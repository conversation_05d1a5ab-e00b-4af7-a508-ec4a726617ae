<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="点位编辑" :visible.sync="dialogVisible" width="1400px" top="0" destroy-on-close @open="getRecord">
      <div class="main-record" v-loading="loading">
        <div class="record-content">
          <baseTitle title="基础信息"></baseTitle>
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
            <el-row>
              <el-col :md="24" :lg="12">
                <el-form-item label="点位名称" prop="name">
                  <el-input
                    v-model="ruleForm.name"
                    placeholder="请输入点位名称"
                    clearable
                    maxlength="32"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="12">
                <el-form-item label="点位编号" prop="code">
                  <el-input
                    v-model="ruleForm.code"
                    placeholder="请输入点位编号"
                    clearable
                    maxlength="32"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="12">
                <el-form-item label="省市区" prop="region">
                  <el-cascader
                    v-model="ruleForm.region"
                    :options="regionOptions"
                    clearable
                    filterable
                    :props="cascaderProps"
                  ></el-cascader>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="12">
                <el-form-item label="详细地址" prop="address">
                  <el-input
                    v-model="ruleForm.address"
                    placeholder="请输入详细地址"
                    clearable
                    maxlength="255"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="12">
                <el-form-item label="经度" prop="longitude">
                  <el-input-number v-model="ruleForm.longitude" :precision="6"></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="12">
                <el-form-item label="纬度" prop="latitude">
                  <div class="location-box">
                    <el-input-number v-model="ruleForm.latitude" :precision="6"></el-input-number>
                    <span class="el-icon-map-location location-icon"></span>
                    <el-button type="text" @click="openDialog">定位调整</el-button>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="12">
                <el-form-item label="点位类型" prop="type">
                  <el-select
                    v-model="ruleForm.type"
                    placeholder="请选择点位类型"
                    clearable
                    filterable
                    @change="changePointType"
                  >
                    <el-option
                      v-for="(item, index) in POINT_TYPE"
                      :key="index"
                      :label="item"
                      :value="index"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="12">
                <el-form-item label="点位收运方式" prop="baggingMethod">
                  <el-select v-model="ruleForm.baggingMethod" placeholder="请选择点位收运方式" clearable filterable>
                    <el-option
                      v-for="(item, index) in POINT_RECEIVING_METHOD"
                      :key="index"
                      :label="item"
                      :value="index"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item label="所属路线" prop="pickupPathId">
                  <el-select v-model="ruleForm.pickupPathId" placeholder="请选择所属路线" clearable filterable>
                    <el-option
                      v-for="item in pathOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <baseTitle title="联络信息"></baseTitle>
            <el-row>
              <el-col :md="24" :lg="12">
                <el-form-item label="联系人名称" prop="contact">
                  <el-input
                    v-model="ruleForm.contact"
                    placeholder="请输入联系人名称"
                    clearable
                    maxlength="32"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="12">
                <el-form-item label="联系电话" prop="contactPhone">
                  <el-input
                    v-model="ruleForm.contactPhone"
                    placeholder="请输入联系电话"
                    clearable
                    maxlength="11"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <baseTitle title="产废信息"></baseTitle>
            <el-row>
              <el-col :md="24" :lg="12">
                <el-form-item label="日排放量（KG）" prop="dailyEmissions">
                  <el-input-number
                    v-model="ruleForm.dailyEmissions"
                    :min="0"
                    :max="99999999"
                    :precision="2"
                  ></el-input-number>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="12">
                <el-form-item label="月排放量（KG）" prop="monthEmissions">
                  <el-input-number
                    v-model="ruleForm.monthEmissions"
                    :min="0"
                    :max="99999999"
                    :precision="2"
                  ></el-input-number>
                </el-form-item>
              </el-col>
            </el-row>
            <baseTitle title="收运信息"></baseTitle>
            <el-row>
              <el-col :md="24" :lg="12">
                <el-form-item label="点位收运周期" prop="period">
                  <el-select v-model="ruleForm.period" placeholder="请选择点位收运周期" clearable filterable>
                    <el-option
                      v-for="(item, index) in COLLECTION_CYCLE"
                      :key="index"
                      :label="item"
                      :value="index"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="12">
                <el-form-item label="点位收运频次" prop="frequency">
                  <el-input-number
                    v-model="ruleForm.frequency"
                    :min="1"
                    :max="99999999"
                    :step="1"
                    step-strictly
                  ></el-input-number>
                  <span class="number-tip">频次</span>
                </el-form-item>
              </el-col>
            </el-row>
            <baseTitle title="关联信息" v-if="ruleForm.type || ruleForm.type === 0"></baseTitle>
            <el-row>
              <el-col>
                <el-form-item label="关联收集柜" prop="holdingTankId" v-if="ruleForm.type === 3">
                  <el-select v-model="ruleForm.holdingTankId" placeholder="请选择关联收集柜" clearable filterable>
                    <el-option
                      v-for="item in collectionOptions"
                      :key="item.id"
                      :label="item.deviceName"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="关联客商" prop="merchantFileId" v-else-if="[0, 1, 2].includes(ruleForm.type)">
                  <el-select v-model="ruleForm.merchantFileId" placeholder="请选择关联客商" clearable filterable>
                    <el-option
                      v-for="item in merchantOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="record-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
        </div>
      </div>
      <positionAdjust :value.sync="showDialog" :formItem="ruleForm" @setPosition="setPosition"></positionAdjust>
    </el-dialog>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun, updateApiFun, getInfoApiFunByParams } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import { POINT_TYPE, COLLECTION_CYCLE, POINT_RECEIVING_METHOD } from "@/enums";
  import positionAdjust from "@/views/archives/receiveShipPoint/components/positionAdjust.vue";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
      value: {
        type: Boolean,
        default: false,
      },
      routeId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    components: {
      baseTitle,
      positionAdjust,
    },
    data() {
      return {
        POINT_TYPE,
        COLLECTION_CYCLE,
        POINT_RECEIVING_METHOD,
        ruleForm: {
          name: "", //点位名称
          code: "", //点位编号
          region: [], //省市区
          address: "", //详细地址
          longitude: "", //经度
          latitude: "", //纬度
          type: "", //点位类型
          pickupPathId: "", //所属路线
          contact: "", //联系人名称
          contactPhone: "", //联系电话
          dailyEmissions: "", //日排放量
          monthEmissions: "", //月排放量
          merchantFileId: "", //关联客商
          holdingTankId: "", //关联收集柜
          period: 1, //点位收运周期
          frequency: 1, //点位收运频次
          baggingMethod: "", //点位收运方式
        },
        rules: {
          name: [{ required: true, message: "请输入点位名称", trigger: "blur" }],
          code: [{ required: true, message: "请输入点位编号", trigger: "blur" }],
          region: [{ required: true, message: "请选择省市区", trigger: "change" }],
          type: [{ required: true, message: "请选择点位类型", trigger: "change" }],
          period: [{ required: true, message: "请选择点位收运周期", trigger: "change" }],
          frequency: [{ required: true, message: "请输入点位收运频次", trigger: "blur" }],
          baggingMethod: [{ required: true, message: "请选择点位收运方式", trigger: "change" }],
        },
        apis: {
          create: "/api/pickup/pickupPoint/create",
          update: "/api/pickup/pickupPoint/update",
          info: "/api/pickup/pickupPoint/get/",
          merchantList: "/api/merchant/merchantFile/list",
          regionList: "/api/region/regionList",
          collectionList: "/api/holding/holdingTank/list",
          pathList: "/api/pickup/pickupPath/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [],
        merchantOptions: [],
        collectionOptions: [],
        regionOptions: [
          {
            code: "440000000000",
            name: "广东省",
            children: [
              { code: "440100000000", name: "广州市", children: [] },
              { code: "441500000000", name: "汕尾市", children: [] },
              { code: "445100000000", name: "潮州市", children: [] },
            ],
          },
        ],
        cascaderProps: {
          value: "code",
          label: "name",
        },
        pathOptions: [],
        oldPath: "",
        showDialog: false,
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    async mounted() {
      await this.getOptions();
      await this.getRegionList();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({}, this.apis.merchantList),
          createApiFun({}, this.apis.collectionList),
          createApiFun({ status: 0 }, this.apis.pathList),
        ];
        let res = await Promise.all(promiseList);
        this.merchantOptions = res[0].data;
        this.collectionOptions = res[1].data;
        this.pathOptions = res[2].data;
      },
      // 获取省市区列表
      async getRegionList() {
        const res = await Promise.all([
          getInfoApiFunByParams({ pid: this.regionOptions[0].children[0].code }, this.apis.regionList),
          getInfoApiFunByParams({ pid: this.regionOptions[0].children[1].code }, this.apis.regionList),
          getInfoApiFunByParams({ pid: this.regionOptions[0].children[2].code }, this.apis.regionList),
        ]);
        if (res[0].success) {
          this.regionOptions[0].children[0].children = res[0].data;
        }
        if (res[1].success) {
          this.regionOptions[0].children[1].children = res[1].data;
        }
        if (res[2].success) {
          this.regionOptions[0].children[2].children = res[2].data;
        }
      },
      // 获取详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.ruleForm.region = [res.data.provinceId, res.data.cityId, res.data.districtId];
          this.ruleForm.contactPhone = this.ruleForm.contactPhone ? this.$sm2Decrypt(this.ruleForm.contactPhone) : "";
          this.oldPath = res.data.pickupPathId;
        }
      },
      // 修改点位类型
      changePointType(value) {
        if (value === 3) {
          this.ruleForm.merchantFileId = "";
        } else {
          this.ruleForm.holdingTankId = "";
        }
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            if (this.oldPath === this.ruleForm.pickupPathId) {
              this.saveFunc();
            } else {
              this.$confirm(`即将要为所选路线增加点位，是否要对该路线的收运点位进行路线优化？`, "提示", {
                confirmButtonText: "去优化",
                cancelButtonText: "保存",
                type: "warning",
              })
                .then(async () => {
                  this.saveFunc(true);
                })
                .catch(() => {
                  this.saveFunc();
                });
            }
          }
        });
      },
      async saveFunc(flag = false) {
        this.loading = true;
        try {
          let params = {
            ...this.ruleForm,
            provinceId: this.ruleForm.region[0],
            cityId: this.ruleForm.region[1],
            districtId: this.ruleForm.region[2],
          };
          let res = this.recordId
            ? await updateApiFun(this.$sm2Encrypt(JSON.stringify(params)), this.apis.update)
            : await createApiFun(this.$sm2Encrypt(JSON.stringify(params)), this.apis.create);
          if (res.success) {
            this.$message.success(`保存成功`);
            let rsp = await getInfoApiFun(this.recordId, this.apis.info);
            if (rsp.success) {
              this.$emit("setPoint", { point: rsp.data, needDelete: this.oldPath !== this.ruleForm.pickupPathId });
            }
            this.dialogVisible = false;
            if (flag) {
              this.$router.push({ name: "receiveRoute", params: { pickupPathId: this.ruleForm.pickupPathId } });
            }
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 打开定位调整弹窗
      openDialog() {
        this.showDialog = true;
      },
      // 设为定位地点
      setPosition(markInfo) {
        this.ruleForm.address = markInfo.address;
        this.ruleForm.longitude = markInfo.longitude;
        this.ruleForm.latitude = markInfo.latitude;
        this.showDialog = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    height: 100%;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding: 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .number-tip {
    margin-left: 10px;
  }
  .location-box {
    display: flex;
    align-items: center;
    .location-icon {
      font-size: 20px;
      color: #909399;
      margin: 0 10px;
      margin-right: 4px;
    }
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
