<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <record
        v-if="showRecord"
        :recordId="recordId"
        :isDisabled="isDisabled"
        @closeRecord="closeRecord"
        @refreshList="initData"
      ></record>
      <!-- <component
        v-else-if="showDetail"
        :is="componentName"
        :detailId="detailId"
        :ruleIndex="ruleIndex"
        :deductSalary="deductSalary"
        :detailType="detailType"
        @closeDetail="showDetail = false"
      /> -->
      <detail
        v-else-if="showDetail"
        :detailId="detailId"
        :deductSalary="deductSalary"
        :detailType="detailType"
        @closeDetail="showDetail = false"
      ></detail>
      <div class="main-index" v-else>
        <el-tabs class="mt-20" :value="activeTab" type="card" @tab-click="handleTabClick">
          <el-tab-pane label="月度考核" name="0"></el-tab-pane>
          <el-tab-pane label="年度考核" name="1"></el-tab-pane>
        </el-tabs>
        <template v-if="activeTab == 1">
          <header class="header">
            <div class="header-left">
              <el-input
                class="w300"
                v-model="keyword"
                placeholder="请输入考核方案名称/被考核人名称"
                clearable
              ></el-input>
            </div>
            <div class="filter-box">
              <el-badge :value="filterNumber" class="filter-badge">
                <el-button type="primary" @click="showFilter = !showFilter">
                  <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                  筛选
                  <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
                </el-button>
              </el-badge>
            </div>
            <el-button @click="resetFilter">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
            <div class="header-right">
              <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
              <el-dropdown
                size="small"
                trigger="click"
                @command="handleDataImport"
                class="dropdown-button"
                placement="bottom-start"
              >
                <el-button size="small" type="primary">
                  <span class="sctmp-iconfont icon-ic_daoru"></span>
                  <span class="ml-5">导入</span>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="1">
                    <div class="has-padding-sm has-border-bottom">导入基础信息</div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button class="mr-10" size="small" type="primary" @click="handleExport">
                <span class="sctmp-iconfont icon-ic_daochu"></span>
                <span class="ml-5">导出</span>
              </el-button>
              <el-popover placement="bottom" width="240" trigger="click" :append-to-body="false">
                <div class="reveal-box">
                  <div class="reveal-header">
                    <div>展示全部</div>
                    <el-switch
                      :value="allItemChecked"
                      active-text="是"
                      inactive-text="否"
                      @change="changeAllReveal"
                    ></el-switch>
                  </div>
                  <ul class="reveal-list">
                    <li class="reveal-item" v-for="(item, index) in itemList" :key="index">
                      <el-checkbox :value="item.value" :disabled="index === 0" @change="changeReveal($event, item)">{{
                        item.label
                      }}</el-checkbox>
                    </li>
                  </ul>
                </div>
                <el-button slot="reference">
                  <div class="btn-inner">
                    <span class="el-icon-s-grid"></span>
                    <span>显示列</span>
                    <span class="el-icon-caret-bottom"></span>
                  </div>
                </el-button>
              </el-popover>
            </div>
          </header>
          <FilterContent label-width="140px" v-show="showFilter">
            <InterviewChannel
              :value.sync="filterForm.channelId"
              :record.sync="channelRecord"
              :needCol="true"
            ></InterviewChannel>
            <el-col :span="8">
              <el-form-item label="考核年度">
                <el-date-picker
                  v-model="filterForm.yearOrMonth"
                  type="month"
                  placeholder="请选择考核年度"
                  value-format="yyyy/MM"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="考核完成日期">
                <el-date-picker
                  v-model="filterForm.completeDate"
                  type="date"
                  placeholder="请选择考核完成日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </FilterContent>
          <main class="main">
            <el-table
              :data="tableData"
              :header-cell-style="{ background: '#F5F7F9' }"
              height="100%"
              border
              ref="tableRef"
            >
              <el-table-column label="考核年度" align="center" v-if="itemList[0].value">
                <template #default="{ row }">{{ row.year }}</template>
              </el-table-column>
              <el-table-column
                prop="completeDate"
                label="考核完成日期"
                align="center"
                v-if="itemList[1].value"
              ></el-table-column>
              <el-table-column
                prop="assessSchemeName"
                label="考核方案"
                align="center"
                v-if="itemList[2].value"
              ></el-table-column>
              <el-table-column
                prop="fullName"
                label="被考核人"
                align="center"
                v-if="itemList[3].value"
              ></el-table-column>
              <el-table-column
                prop="structureName"
                label="所属部门"
                align="center"
                v-if="itemList[4].value"
              ></el-table-column>
              <el-table-column
                prop="totalScore"
                label="总分"
                align="center"
                sortable
                v-if="itemList[5].value"
              ></el-table-column>
              <el-table-column
                prop="assessGrade"
                label="考核等级"
                align="center"
                v-if="itemList[6].value"
              ></el-table-column>
              <el-table-column
                prop="integrate"
                label="本年度绩效积分"
                align="center"
                sortable
                v-if="itemList[7].value"
              ></el-table-column>
              <el-table-column
                prop="accumulativeIntegrate"
                label="累计绩效积分"
                align="center"
                sortable
                v-if="itemList[8].value"
              ></el-table-column>
              <el-table-column
                prop="payIncentives"
                label="年度奖金"
                align="center"
                sortable
                v-if="itemList[9].value"
              ></el-table-column>
              <el-table-column
                prop="deductSalary"
                label="扣款"
                align="center"
                sortable
                v-if="itemList[10].value"
              ></el-table-column>
              <el-table-column prop="channelId" label="渠道名称" align="center" v-if="itemList[11].value">
                <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
              </el-table-column>
              <el-table-column min-width="140" label="操作" align="center">
                <template #default="{ row }">
                  <el-link class="mr-10" type="primary" v-if="row.verifyStatus === 2" @click="editRecord(row, true)"
                    >详情</el-link
                  >
                  <el-popconfirm title="确认删除当前考核记录？" @confirm="deleteRecord(row)" v-else>
                    <el-link type="danger" slot="reference">删除</el-link>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </main>
        </template>
        <template v-else>
          <header class="header">
            <div class="header-left">
              <el-input
                class="w300"
                v-model="keyword"
                placeholder="请输入考核方案名称/被考核人名称"
                clearable
              ></el-input>
            </div>
            <div class="filter-box">
              <el-badge :value="filterNumber" class="filter-badge">
                <el-button type="primary" @click="showFilter = !showFilter">
                  <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                  筛选
                  <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
                </el-button>
              </el-badge>
            </div>
            <el-button @click="resetFilter">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
            <div class="header-right">
              <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
              <el-dropdown
                size="small"
                trigger="click"
                @command="handleDataImport"
                class="dropdown-button"
                placement="bottom-start"
              >
                <el-button size="small" type="primary">
                  <span class="sctmp-iconfont icon-ic_daoru"></span>
                  <span class="ml-5">导入</span>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="1">
                    <div class="has-padding-sm has-border-bottom">导入基础信息</div>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button class="mr-10" size="small" type="primary" @click="handleExport">
                <span class="sctmp-iconfont icon-ic_daochu"></span>
                <span class="ml-5">导出</span>
              </el-button>
              <el-popover placement="bottom" width="240" trigger="click" :append-to-body="false">
                <div class="reveal-box">
                  <div class="reveal-header">
                    <div>展示全部</div>
                    <el-switch
                      :value="allItemChecked"
                      active-text="是"
                      inactive-text="否"
                      @change="changeAllReveal"
                    ></el-switch>
                  </div>
                  <ul class="reveal-list">
                    <li class="reveal-item" v-for="(item, index) in itemList" :key="index">
                      <el-checkbox :value="item.value" :disabled="index === 0" @change="changeReveal($event, item)">{{
                        item.label
                      }}</el-checkbox>
                    </li>
                  </ul>
                </div>
                <el-button slot="reference">
                  <div class="btn-inner">
                    <span class="el-icon-s-grid"></span>
                    <span>显示列</span>
                    <span class="el-icon-caret-bottom"></span>
                  </div>
                </el-button>
              </el-popover>
            </div>
          </header>
          <FilterContent label-width="140px" v-show="showFilter">
            <InterviewChannel
              :value.sync="filterForm.channelId"
              :record.sync="channelRecord"
              :needCol="true"
            ></InterviewChannel>
            <el-col :span="8">
              <el-form-item label="考核月度">
                <el-date-picker
                  v-model="filterForm.yearOrMonth"
                  type="month"
                  placeholder="请选择考核月度"
                  value-format="yyyy/MM"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="考核流程">
                <el-select v-model="filterForm.verifyStatus" placeholder="请选择考核流程" clearable filterable>
                  <el-option v-for="(item, index) in EXAMINE_FLOW" :key="index" :label="item" :value="index">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="考核完成日期">
                <el-date-picker
                  v-model="filterForm.completeDate"
                  type="date"
                  placeholder="请选择考核完成日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </FilterContent>
          <main class="main">
            <el-table
              :data="tableData"
              :header-cell-style="{ background: '#F5F7F9' }"
              height="100%"
              border
              ref="tableRef"
            >
              <el-table-column label="考核月度" align="center" v-if="itemList[0].value">
                <template #default="{ row }"
                  >{{ row.year }}-{{ row.month >= 10 ? row.month : "0" + row.month }}</template
                >
              </el-table-column>
              <el-table-column
                prop="completeDate"
                label="考核完成日期"
                align="center"
                v-if="itemList[1].value"
              ></el-table-column>
              <el-table-column
                prop="assessSchemeName"
                label="考核方案"
                align="center"
                v-if="itemList[2].value"
              ></el-table-column>
              <el-table-column
                prop="fullName"
                label="被考核人"
                align="center"
                v-if="itemList[3].value"
              ></el-table-column>
              <el-table-column
                prop="structureName"
                label="所属部门"
                align="center"
                v-if="itemList[4].value"
              ></el-table-column>
              <el-table-column label="当前流程" align="center" v-if="itemList[5].value">
                <template #default="{ row }">{{
                  [0, 2].includes(row.verifyStatus) ? EXAMINE_FLOW[row.verifyStatus] : ASSESSMENT_FLOWS[row.flowCode]
                }}</template>
              </el-table-column>
              <el-table-column prop="basicPerformance" label="基础绩效" align="center" v-if="itemList[6].value">
                <template #default="{ row }">
                  <el-link type="primary" @click="viewPerformance(row, 0)">
                    <span v-if="row.hasClinicGroup && ((!row.lessType && row.lessType !== 0) || !row.pathFactor)">
                      <span class="el-icon-warning"></span>
                      <span>待选择</span>
                      <template v-if="!row.lessType && row.lessType !== 0">
                        <span>扣款方式</span>
                        <span v-if="!row.pathFactor">及</span>
                      </template>
                      <span v-if="!row.pathFactor">线路系数</span>
                    </span>
                    <span v-else>{{ row.basicPerformance }}</span>
                  </el-link>
                </template>
              </el-table-column>
              <el-table-column label="扣分项" align="center" v-if="itemList[7].value">
                <template #default="{ row }">
                  <span v-if="row.period === 1">-</span>
                  <el-link type="primary" v-else @click="viewDedection(row)">查看扣分项</el-link>
                </template>
              </el-table-column>
              <el-table-column
                prop="totalScore"
                label="总分"
                align="center"
                sortable
                v-if="itemList[8].value"
              ></el-table-column>
              <el-table-column
                prop="assessGrade"
                label="考核等级"
                align="center"
                v-if="itemList[9].value"
              ></el-table-column>
              <el-table-column
                prop="payIncentives"
                label="绩效"
                align="center"
                sortable
                v-if="itemList[10].value"
              ></el-table-column>
              <el-table-column
                prop="deductSalary"
                label="扣款"
                align="center"
                v-if="itemList[11].value"
              ></el-table-column>
              <el-table-column prop="channelId" label="渠道名称" align="center" v-if="itemList[12].value">
                <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
              </el-table-column>
              <el-table-column min-width="140" label="操作" align="center">
                <template #default="{ row }">
                  <template v-if="row.verifyStatus === 2">
                    <el-link class="mr-10" type="primary" @click="editRecord(row, true)">详情</el-link>
                    <el-link class="mr-10" type="primary" @click="viewPerformance(row, 1)">绩效明细</el-link>
                  </template>
                  <el-popconfirm title="确认删除当前考核记录？" @confirm="deleteRecord(row)" v-else>
                    <el-link type="danger" slot="reference">删除</el-link>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </main>
        </template>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
    <deduction :value.sync="showDeduction" :id="deductionId"></deduction>
    <importDialog
      :value.sync="importDialogShow"
      title="考核记录"
      :importDialogType="importDialogType"
      @importSuccess="searchFilter"
    ></importDialog>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import { getListPageApiFun, deleteApiFun, BASE_API_URL } from "@/api/base";
  import record from "./components/record.vue";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import { EXAMINE_PERIOD, PERFORMANCE_RULE, EXAMINE_FLOW, ASSESSMENT_FLOWS } from "@/enums";
  import importDialog from "./components/importDialog";
  import deduction from "./components/deduction.vue";
  import detailComponents from "./componentImport";
  import detail from "./components/detail";
  export default {
    components: {
      defaultPage,
      record,
      importDialog,
      deduction,
      detail,
      ...detailComponents,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/access/record/listPage",
          delete: "/api/access/record/delete/",
          export: "/api/access/record/export",
          initiateAssessment: "/api/assess/scheme/initiateAssessment",
        },
        showRecord: false,
        recordId: "",
        loading: false,
        EXAMINE_PERIOD,
        PERFORMANCE_RULE,
        EXAMINE_FLOW,
        ASSESSMENT_FLOWS,
        importDialogShow: false,
        importDialogType: "",
        isDisabled: false,
        showDeduction: false,
        deductionId: "",
        showFilter: false,
        keyword: "",
        activeTab: "0",
        itemList: [
          { label: "考核月度", value: true },
          { label: "考核完成日期", value: true },
          { label: "考核方案", value: true },
          { label: "被考核人", value: true },
          { label: "所属部门", value: true },
          { label: "当前流程", value: true },
          { label: "基础绩效", value: true },
          { label: "扣分项", value: true },
          { label: "总分", value: true },
          { label: "考核等级", value: true },
          { label: "绩效", value: true },
          { label: "扣款", value: true },
        ],
        itemList0: [
          { label: "考核月度", value: true },
          { label: "考核完成日期", value: true },
          { label: "考核方案", value: true },
          { label: "被考核人", value: true },
          { label: "所属部门", value: true },
          { label: "当前流程", value: true },
          { label: "基础绩效", value: true },
          { label: "扣分项", value: true },
          { label: "总分", value: true },
          { label: "考核等级", value: true },
          { label: "绩效", value: true },
          { label: "扣款", value: true },
          { label: "渠道名称", value: true },
        ],
        itemList1: [
          { label: "考核年度", value: true },
          { label: "考核完成日期", value: true },
          { label: "考核方案", value: true },
          { label: "被考核人", value: true },
          { label: "所属部门", value: true },
          { label: "总分", value: true },
          { label: "考核等级", value: true },
          { label: "本年度绩效积分", value: true },
          { label: "累计绩效积分", value: true },
          { label: "年度奖金", value: true },
          { label: "扣款", value: true },
          { label: "渠道名称", value: true },
        ],
        allItemChecked: true,
        showDetail: false,
        componentName: "",
        detailId: "",
        ruleIndex: 0,
        deductSalary: 0,
        detailType: 0,
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.itemList = this.itemList0;
      this.initData();
    },
    methods: {
      // tab栏点击事件
      handleTabClick(tab) {
        if (tab.name == this.activeTab) return;
        this.activeTab = tab.name;
        this.allItemChecked = true;
        this.itemList = this[`itemList${tab.name}`];
        this.resetFilter();
      },
      // 分页列表数据
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          period: this.activeTab,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showRecord = true;
        this.recordId = "";
        this.isDisabled = false;
      },
      // 编辑
      editRecord(row, flag) {
        this.showRecord = true;
        this.recordId = row.id;
        this.isDisabled = flag;
      },
      closeRecord() {
        this.showRecord = false;
      },
      // 删除
      async deleteRecord(row) {
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            this.page.pageNo = this.pageDropProcess(this.page.pageNo, this.page.pageSize, this.page.total);
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 查看扣分项
      viewDedection(row) {
        this.deductionId = row.id;
        this.showDeduction = true;
      },
      // 导出
      handleExport() {
        this.$confirm(`确认是否以当前筛选条件导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, {
                keyword: this.keyword,
                ...this.filterForm,
              });
              if (res.success) {
                createDownloadEvent(`考核台账${Date.now()}.xlsx`, [res.data]);
                window.ELEMENT.Message.success("导出成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
      // 导入
      handleDataImport(command) {
        this.importDialogType = command;
        this.importDialogShow = true;
      },
      // 修改所有显示列
      changeAllReveal(value) {
        this.allItemChecked = value;
        this.itemList.slice(1, this.itemList.length).forEach((item) => (item.value = value));
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 列点击事件
      changeReveal(value, item) {
        item.value = value;
        let uncheckList = this.itemList.slice(1, this.itemList.length).filter((list) => !list.value);
        if (uncheckList.length === 0) {
          this.allItemChecked = true;
        } else {
          this.allItemChecked = false;
        }
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 查看绩效详情
      viewPerformance(item, type) {
        // switch (item.rule) {
        //   case 0:
        //     this.componentName = `detail0`;
        //     break;
        //   case 1:
        //     this.componentName = `detail1`;
        //     break;
        //   case 2:
        //     this.componentName = `detail2`;
        //     break;
        //   case 3:
        //     this.componentName = `detail2`;
        //     break;
        //   case 4:
        //     this.componentName = `detail4`;
        //     break;
        //   case 5:
        //   case 6:
        //   case 7:
        //     this.componentName = `detail5`;
        //     break;
        //   case 8:
        //     this.componentName = `detail8`;
        //     break;
        // }
        // this.ruleIndex = item.rule;
        this.detailId = item.id;
        this.deductSalary = item.deductSalary;
        this.detailType = type;
        this.showDetail = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .w250 {
    width: 250px;
  }
  .w300 {
    width: 300px;
  }
  .el-col {
    margin-top: 10px;
  }
  .mt-20 {
    margin-top: 20px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
</style>
