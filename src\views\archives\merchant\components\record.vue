<template>
  <div class="main-record micro-app-sctmp_base">
    <div class="record-content">
      <div class="card-header">客商档案详情</div>
      <el-descriptions :labelStyle="labelStyle" :contentStyle="contentStyle" :column="2" border>
        <el-descriptions-item label="客商名称">
          <span class="desc-value">{{ ruleForm.name }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="客商编号">
          <span class="desc-value">{{ ruleForm.code }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="旧编码">
          <span class="desc-value">{{ ruleForm.oldCode }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="曾用名">
          <span class="desc-value">{{ ruleForm.oldName }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="详细地址">
          <span class="desc-value">{{ ruleForm.address }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="法人代表">
          <span class="desc-value">{{ ruleForm.corporateName }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="开票税率">
          <span class="desc-value">{{ ruleForm.billingRate }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="客户类型">
          <span class="desc-value">{{ CUSTOMER_TYPE[ruleForm.type] }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="信用状态">
          <span class="desc-value">{{ CREDIT_STATUS[ruleForm.creditStatus] }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="客商状态">
          <span class="desc-value">{{ CUSTOMER_STATUS[ruleForm.status] }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="医院性质">
          <span class="desc-value">{{ HOSPITAL_NATURE[ruleForm.hospitalNature] }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="登记号">
          <span class="desc-value">{{ ruleForm.registerNumber }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="收款员">
          <span class="desc-value">{{ ruleForm.receivingTeller }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="联系人名称">
          <span class="desc-value">{{ ruleForm.contact }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="联系电话" :span="2">
          <span class="desc-value">{{ ruleForm.contactPhone }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          <span class="desc-value">{{ ruleForm.remark }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="record-footer">
      <el-button @click="$emit('closeRecord')">返回</el-button>
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  import { CUSTOMER_TYPE, CREDIT_STATUS, CUSTOMER_STATUS, HOSPITAL_NATURE } from "@/enums";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        ruleForm: {},
        apis: {
          info: "/api/merchant/merchantFile/get/",
        },
        labelStyle: {
          width: "200px",
          height: "48px",
          fontSize: "14px",
          fontWeight: 400,
          color: "#122131",
          lineHeight: "20px",
        },
        contentStyle: {
          fontSize: "14px",
          fontWeight: 400,
          lineHeight: "20px",
        },
        CUSTOMER_TYPE,
        CREDIT_STATUS,
        CUSTOMER_STATUS,
        HOSPITAL_NATURE,
      };
    },
    mounted() {},
    methods: {
      // 获取用户详情
      async getRecord(id) {
        let res = await getInfoApiFun(id, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.ruleForm.contactPhone = this.ruleForm.contactPhone
            ? this.$sm2Decrypt(this.ruleForm.contactPhone)
            : this.ruleForm.contactPhone;
          for (let key in this.ruleForm) {
            this.ruleForm[key] = this.ruleForm[key] || this.ruleForm[key] === 0 ? this.ruleForm[key] : "-";
          }
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .desc-value {
    color: #46535e;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
</style>
