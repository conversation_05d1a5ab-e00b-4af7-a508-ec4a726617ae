<template>
  <div class="collection-chart">
    <div class="title">{{ chartTitle }}{{ typeName }}今日收运率情况</div>
    <div style="height: 100%; width: 100%" :id="chartId"></div>
    <div class="legend">
      <div
        class="legend-item"
        :class="{ 'legend-first': index === 0 }"
        @click="handleLend(item)"
        v-for="(item, index) in getSeriesData"
        :key="index"
      >
        <div class="legend-color" :style="{ background: colors[index] }"></div>
        <div class="legend-text">{{ item.name }}{{ chartData.typeName }}数量</div>
        <div class="legend-value">{{ item.value }}</div>
      </div>
    </div>
  </div>
</template>

<script>
  import * as echarts from "echarts";

  export default {
    props: {
      chartData: {
        //图表数据
        type: Object,
        default() {
          return {
            percent: 0,
            all: 0,
            xData: [],
            seriesData: [
              { value: 0, name: "已收运" },
              { value: 0, name: "待收运" },
            ],
          };
        },
      },

      chartTitle: {
        type: String,
        default: "",
      },

      typeName: {
        type: String,
        default: "点位",
      },

      chartId: {
        type: String,
        default: '"collectionChartId"',
      },
    },

    computed: {
      getAllCount() {
        return this.chartData.seriesData.reduce((prev, curr) => prev + curr.value, 0);
      },

      getSeriesData() {
        const obj = {
          name: `今日收运`,
          value: this.chartData.all,
          completed: null,
          waybillStatus: undefined,
        };

        return [obj, ...this.chartData.seriesData];
      },
    },

    watch: {
      chartData: {
        handler() {
          this.initChart();
        },
        deep: true,
      },
    },

    data() {
      return {
        chart: null,
        colors: ["transparent", "#63CC7C", "#60B7F7"],
      };
    },

    created() {},

    mounted() {
      this.initChart();
    },

    destroyed() {
      window.removeEventListener("resize", this.resizeChart);
    },

    methods: {
      initChart() {
        let pieOption = {
          tooltip: {
            trigger: "item",
          },
          legend: {
            orient: "vertical",
            top: "20%",
            right: "20%",
            itemWidth: 8,
            itemHeight: 8,
            show: false,
          },
          title: {
            show: false,
          },
          series: [
            {
              name: "",
              type: "pie",
              radius: ["45%", "65%"],
              center: ["30%", "60%"], // 饼图靠左
              label: {
                show: true,
                position: "center",
                formatter: () => `{rate|${this.chartData.percent}%}`,
                rich: {
                  rate: {
                    fontSize: 18,
                    color: "#fff",
                    textVerticalAlign: "middle",
                  },
                },
              },
              labelLine: {
                show: false,
              },
              data: this.chartData.seriesData,
            },
          ],
          color: this.colors.slice(1),
        };

        this.chart = echarts.init(document.getElementById(this.chartId));
        this.chart.setOption(pieOption);
        window.addEventListener("resize", this.resizeChart);
      },

      resizeChart() {
        if (this.chart) {
          this.chart.resize();
        }
      },

      handleLend(item) {
        this.$emit("handleLend", item);
      },
    },
  };
</script>

<style scoped lang="scss">
  .collection-chart {
    position: relative;
    .title {
      font-size: 16px;
      color: #fff;
      padding: 10px 0;
      position: absolute;
      top: 0;
      left: 5px;
    }

    .legend {
      position: absolute;
      top: 63%;
      transform: translateY(-50%);
      left: 249px;
      display: flex;
      flex-direction: column;
      .legend-item {
        cursor: pointer;
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .legend-color {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          margin-right: 5px;
        }

        .legend-text {
          font-size: 18px;
          color: #fff;
          width: 120px;
        }
        .legend-value {
          font-size: 18px;
          color: #fff;
        }
      }

      .legend-first {
        .legend-color {
          width: 0px;
        }

        .legend-text {
          width: 131px;
        }
      }
    }
  }
</style>
