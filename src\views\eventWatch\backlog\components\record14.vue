<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">从业资格证过期待处理</div>
      <el-form label-width="210px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="上报时间" required>
              <el-date-picker
                :value="ruleFormEvent.createTime"
                type="datetime"
                placeholder="请选择上报时间"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                disabled
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="截止日期" required>
              <el-date-picker
                :value="ruleFormEvent.deadline"
                type="date"
                placeholder="请选择截止日期"
                clearable
                value-format="yyyy-MM-dd"
                disabled
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="基础信息"></baseTitle>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="210px" label-suffix="：">
          <el-row>
            <el-col :md="24" :lg="12">
              <el-form-item label="用户名" prop="userName">
                <el-input
                  :value="ruleForm.phone"
                  placeholder="请输入用户名"
                  clearable
                  :maxlength="13"
                  show-word-limit
                  disabled
                  v-if="recordId && !ruleForm.userName"
                ></el-input>
                <el-input
                  v-model="ruleForm.userName"
                  placeholder="请输入用户名"
                  clearable
                  disabled
                  :maxlength="13"
                  show-word-limit
                  v-else
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="真实姓名" prop="fullName">
                <el-input
                  v-model="ruleForm.fullName"
                  placeholder="请输入真实姓名"
                  clearable
                  :maxlength="14"
                  disabled
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="邮箱" prop="email">
                <el-input disabled v-model="ruleForm.email" placeholder="请输入邮箱" clearable></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="性别" prop="sex">
                <el-select disabled v-model="ruleForm.sex" placeholder="请选择性别" clearable filterable>
                  <el-option v-for="(item, index) in SEX_OPTIONS" :key="index" :label="item" :value="index"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="联系电话" prop="phone">
                <el-input
                  disabled
                  v-model="ruleForm.phone"
                  placeholder="请输入联系电话"
                  clearable
                  :maxlength="11"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="工号" prop="jobNo">
                <el-input
                  v-model="ruleForm.jobNo"
                  placeholder="请输入工号"
                  clearable
                  disabled
                  maxlength="20"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="身份证号码" prop="idCard">
                <el-input
                  v-model="ruleForm.idCard"
                  placeholder="身份证号码"
                  clearable
                  disabled
                  maxlength="18"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="所属部门" prop="deptId">
                <el-cascader
                  ref="cascaderRef"
                  disabled
                  v-model="ruleForm.deptId"
                  placeholder="请选择所属部门"
                  filterable
                  clearable
                  :options="deptOptions"
                  :props="deptProps"
                  :show-all-levels="false"
                  @change="changeDept"
                ></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="部门负责人" prop="deptHeadName">
                <el-input
                  disabled
                  v-model="ruleForm.deptHeadName"
                  placeholder="请输入部门负责人"
                  clearable
                  readonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="直属上级" prop="directSuperior">
                <el-select disabled v-model="ruleForm.directSuperior" placeholder="请选择直属上级" clearable filterable>
                  <el-option
                    v-for="item in userOptions"
                    :key="item.lgUnionId"
                    :label="item.fullName"
                    :value="item.lgUnionId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="在职状态" prop="jobStatus">
                <el-select disabled v-model="ruleForm.jobStatus" placeholder="请选择在职状态" clearable filterable>
                  <el-option v-for="(item, index) in JOB_STATUS" :key="index" :label="item" :value="index"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="职位" prop="position">
                <el-select disabled v-model="ruleForm.position" placeholder="请选择职位" clearable filterable>
                  <el-option
                    v-for="item in positionOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="默认驾驶车辆" prop="plateNumber">
                <el-select disabled v-model="ruleForm.plateNumber" placeholder="请选择车牌号" clearable filterable>
                  <el-option
                    v-for="item in carOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="用户头像" prop="avatar">
                <!-- <FileUpload @uploadChange="uploadChangeFileList" :imageList="imageList" :limit="1">
                  <template #tips><el-tag type="warning">请上传用户头像</el-tag></template>
                </FileUpload> -->
                <div v-if="imageList && imageList.length > 0">
                  <el-image
                    :key="index"
                    v-for="(imageObj, index) in imageList"
                    style="width: 100px; height: 100px; margin-right: 20px"
                    :src="imageObj.url"
                    :preview-src-list="[imageObj.url]"
                  >
                  </el-image>
                </div>
                <div v-else>暂无用户头像</div>
              </el-form-item>
            </el-col>
            <el-row>
              <el-col :span="12">
                <el-form-item label="驾驶证编号" prop="drivingCertificateNumber">
                  <el-input
                    disabled
                    class="w-400"
                    v-model="ruleForm.drivingCertificateNumber"
                    placeholder="请输入驾驶证编号"
                    clearable
                    maxlength="100"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="从业资格证编号" prop="qualificationCertificateNumber">
                  <el-input
                    class="w-400"
                    v-model="ruleForm.qualificationCertificateNumber"
                    placeholder="请输入从业资格证编号"
                    clearable
                    maxlength="100"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-col :md="24" :lg="12">
              <el-form-item label="从业资格证有效期开始日期" prop="qualificationCertificateBeginDate">
                <el-date-picker
                  v-model="ruleForm.qualificationCertificateBeginDate"
                  type="date"
                  placeholder="选择有效期开始日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="从业资格证有效期结束日期" prop="qualificationCertificateEndDate">
                <el-date-picker
                  v-model="ruleForm.qualificationCertificateEndDate"
                  type="date"
                  placeholder="选择有效期结束日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="24">
              <el-form-item label="从业资格证登记日期" prop="qualificationCertificateRegistrationDate">
                <el-date-picker
                  v-model="ruleForm.qualificationCertificateRegistrationDate"
                  type="date"
                  placeholder="选择登记日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="从业资格证主页照片" prop="qualificationCertificatePhotoFront">
                <FileUpload
                  @uploadChange="handleUploadChange($event, 'qualificationCertificatePhotoFront')"
                  :imageList="photoImageList['qualificationCertificatePhotoFront']"
                ></FileUpload>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="从业资格证副页照片" prop="qualificationCertificatePhotoBack">
                <FileUpload
                  @uploadChange="handleUploadChange($event, 'qualificationCertificatePhotoBack')"
                  :imageList="photoImageList['qualificationCertificatePhotoBack']"
                ></FileUpload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
      <el-button type="primary" @click="saveRecordThrottling">完成</el-button>
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun, updateApiFun, createApiFun, getListApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import FileUpload from "@/components/FileUpload";
  import { deepClone } from "logan-common/utils";
  import { validPhone } from "@/utils/validate";
  import { SEX_OPTIONS, USER_IDENTITY, USER_TYPE, JOB_STATUS, QUASI_DRIVING_TYPE } from "@/enums";

  export default {
    props: {
      recordItem: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      baseTitle,
      FileUpload,
    },
    computed: {},
    data() {
      return {
        ruleFormEvent: {
          createTime: null,
          completionTime: null,
        },
        ruleForm: {
          userName: "", //用户名
          fullName: "", //真实姓名
          phone: "", //手机号码
          email: "", //邮箱
          sex: "", //性别
          jobNo: "", //工号
          deptId: "", //所在部门
          deptHeadName: "", //部门负责人
          directSuperior: "", //直属上级
          position: "", //职位
          company: "", //所属公司
          userType: 0, //用户类型
          jobStatus: "", //在职状态
          plateNumber: "", //车牌号
          idCard: "", //身份证号码
          qualificationCertificateNumber: "", //从业资格证编号
          qualificationCertificateBeginDate: "", //从业资格证有效期开始时间
          qualificationCertificateEndDate: "", //从业资格证有效期结束时间
          qualificationCertificateRegistrationDate: "", //从业资格证登记日期
          qualificationCertificatePhotoFront: null, //从业资格证主页照片
          qualificationCertificatePhotoBack: null, //从业资格证副页照片
          avatar: "", //用户头像
        },
        rules: {
          fullName: [
            { required: true, message: "请输入真实姓名", trigger: "blur" },
            { min: 2, max: 14, message: "长度在 2 到 14 个字符", trigger: "blur" },
          ],
          phone: [
            {
              required: true,
              validator: this.validatePhone,
              trigger: "blur",
            },
          ],
          sex: [{ required: true, message: "请选择性别", trigger: "change" }],
          // deptId: [{ required: true, message: "请选择所属部门", trigger: "change" }],
          jobNo: [{ required: true, message: "请输入工号", trigger: "blur" }],
          // position: [{ required: true, message: "请选择职位", trigger: "change" }],
          userType: [{ required: true, message: "请选择用户类型", trigger: "change" }],
          jobStatus: [{ required: true, message: "请选择在职状态", trigger: "change" }],
          plateNumber: [{ required: true, message: "请选择车牌号", trigger: "change" }],
          qualificationCertificateNumber: [{ required: true, message: "请输入从业资格证编号", trigger: "blur" }],
          qualificationCertificateBeginDate: [
            { required: true, message: "请选择从业资格证有效期开始时间", trigger: "change" },
          ],
          qualificationCertificateEndDate: [
            { required: true, message: "请选择从业资格证有效期结束时间", trigger: "change" },
          ],
          qualificationCertificateRegistrationDate: [
            { required: true, message: "请选择从业资格证登记日期", trigger: "change" },
          ],
        },
        apis: {
          recordInfo: "/api/task/get/",
          update: "/api/baseuser/update",
          info: "/api/baseuser/get/",
          carList: "/api/vehicle/dossier/list",
          deptList: "/api/company/structure/findIn",
          userList: "/api/baseuser/list",
          positionList: "/api/dict/position/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [],
        carOptions: [],
        photoImageList: {
          qualificationCertificatePhotoFront: [],
          qualificationCertificatePhotoBack: [],
        },
        SEX_OPTIONS,
        USER_IDENTITY,
        USER_TYPE,
        JOB_STATUS,
        QUASI_DRIVING_TYPE,
        deptProps: {
          checkStrictly: true,
          value: "id",
          label: "name",
          emitPath: false,
        },
        deptOptions: [],
        userOptions: [], //用户列表
        positionOptions: [],
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptions();
      if (this.recordItem && this.recordItem.businessId) {
        this.getRecord();
        this.getBusiness();
      }
    },
    methods: {
      // 选择部门
      changeDept(value) {
        if (value) {
          this.ruleForm.deptHeadName = this.$refs.cascaderRef.getCheckedNodes()[0].data.chargeName;
        } else {
          this.ruleForm.deptHeadName = "";
        }
      },
      validatePhone(rule, value, callback) {
        if (!value) {
          callback(new Error("请输入手机号"));
        } else if (!validPhone(value)) {
          callback(new Error("手机号不正确"));
        } else {
          callback();
        }
      },
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getInfoApiFun("", this.apis.deptList),
          getListApiFun({}, this.apis.userList),
          getInfoApiFun("", this.apis.positionList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.deptOptions = res[1].data;
        this.userOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.positionOptions = res[3].data;
      },
      uploadChangeFileList(fileList) {
        if (fileList.length > 0) {
          this.ruleForm.fileList = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
          this.$refs.ruleForm.clearValidate("fileList");
        } else {
          this.ruleForm.fileList = [];
        }
      },
      getCurrentDate() {
        const now = new Date();
        const year = now.getFullYear(); // 获取完整的年份(4位,1970-????)
        const month = String(now.getMonth() + 1).padStart(2, "0"); // 月份是从0开始的，所以要加1
        const day = String(now.getDate()).padStart(2, "0"); // 获取当前月份的日期
        return `${year}-${month}-${day}`;
      },
      // 获取业务详情，取原型需要的字段
      async getBusiness() {
        let res = await getInfoApiFun(this.recordItem.businessId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          // 按要求，调整为不自动清空
          // this.ruleForm.qualificationCertificateBeginDate = null;
          // this.ruleForm.qualificationCertificateEndDate = null;
          // this.ruleForm.qualificationCertificatePhotoFront = null;
          // this.ruleForm.qualificationCertificatePhotoBack = null;

          this.handleBackImage();
          let imageObj = "";
          try {
            imageObj = JSON.parse(this.ruleForm.avatar);
          } catch (error) {
            imageObj = "";
          }
          if (imageObj) {
            this.imageList = [{ url: imageObj.url, file: imageObj }];
          }
          this.ruleForm.phone = this.$sm2Decrypt(this.ruleForm.phone);
          if (this.ruleForm.idCard) {
            this.ruleForm.idCard = this.$sm2Decrypt(this.ruleForm.idCard);
          }
        }
      },
      handleBackImage() {
        for (const key in this.photoImageList) {
          if (Object.hasOwnProperty.call(this.photoImageList, key)) {
            const item = this.ruleForm[key];
            if (this.ruleForm[key]) {
              this.photoImageList[key] = [
                {
                  url: item.url,
                  file: item,
                },
              ];
            }
          }
        }
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await updateApiFun(this.ruleForm, this.apis.update);
              if (res.success) {
                this.$message.success(`处理成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      // 获取待办详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordItem.id, this.apis.recordInfo);
        this.ruleFormEvent = deepClone(res.data);
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("returnIndex");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-box {
    margin-bottom: 20px;
  }
</style>
