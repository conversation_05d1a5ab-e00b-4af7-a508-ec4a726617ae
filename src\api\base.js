import { get, post, put, del, uploadFile } from "@/utils/request";
export const BASE_API_URL = process.env.VUE_APP_BASE_API_URL + "/sctmpbase";
export const DISPATCH_API_URL = process.env.VUE_APP_BASE_API_URL + "/sctmp-dispatch";

export function getListPageApiFun(data, api) {
  return post(`${BASE_API_URL}${api}`, data);
}

export function createApiFun(data, api) {
  return post(`${BASE_API_URL}${api}`, data);
}

export function importApiFun(data, api) {
  return uploadFile(`${BASE_API_URL}${api}`, data);
}

export function updateApiFun(data, api) {
  return put(`${BASE_API_URL}${api}`, data);
}

export function deleteApiFun(data, api) {
  return del(`${BASE_API_URL}${api}${data}`);
}

export function getInfoApiFun(data, api) {
  return get(`${BASE_API_URL}${api}${data}`);
}

export function getInfoApiFunByParams(data, api) {
  return get(`${BASE_API_URL}${api}`, data);
}

export function getListApiFun(data, api) {
  return post(`${BASE_API_URL}${api}`, data);
}

export function postApiFun(data, api) {
  return post(`${BASE_API_URL}${api}`, data);
}

export function delApiFun(data, api, flag = false) {
  return del(`${BASE_API_URL}${api}`, data, flag);
}

export function postApiFunCancel(data, api, config) {
  return post(`${BASE_API_URL}${api}`, data, config);
}
