<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">编辑绩效方案</div>
      <baseTitle title="基础信息"></baseTitle>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="150px" label-suffix="：">
        <el-row>
          <el-col>
            <el-form-item label="绩效方案名称" required>
              <el-input value="桶装/袋装收运月度绩效" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <baseTitle title="桶装绩效规则信息"></baseTitle>
          <el-col :md="12">
            <el-form-item label="绩效计费类型" required>
              <el-input value="重量计费" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="12">
            <el-form-item label="收运方式" required>
              <el-input value="桶装" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="绩效计算公式" required>
              <el-input value="收运单价 × 当月内每日的总收运量 × 考核系数" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="收运单价标准信息" prop="drumPrices">
              <el-table :data="ruleForm.drumPrices" :header-cell-style="{ background: '#F5F7F9' }" border>
                <el-table-column label="收运点数" align="center">
                  <template #default="{ row }">
                    <div>{{ row.min }} {{ "＜" }} x {{ "≤" }} {{ row.max }}</div>
                  </template>
                </el-table-column>
                <el-table-column prop="driverPrice" label="司机收运单价（元/吨）" align="center"></el-table-column>
                <el-table-column
                  prop="supercargoPrice"
                  label="押运工收运单价（元/吨）"
                  align="center"
                ></el-table-column>
                <el-table-column min-width="140" label="操作" align="center">
                  <template #default="{ row, $index }">
                    <el-link
                      class="mr-10"
                      type="primary"
                      @click="editItem('drumPricesItem', 'showDrumPrices', row, $index)"
                      >编辑</el-link
                    >
                    <el-link type="danger" @click="deleteItem('drumPrices', $index)">删除</el-link>
                  </template>
                </el-table-column>
              </el-table>
              <el-button
                class="mt-10"
                type="primary"
                icon="el-icon-plus"
                @click="editItem('drumPricesItem', 'showDrumPrices', '')"
                >新增收运单价</el-button
              >
              <div class="tips">收运单价即，收运对应点位数量后，每吨垃圾对应的绩效金额。</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <baseTitle title="袋装绩效规则信息"></baseTitle>
          <el-col :md="12">
            <el-form-item label="绩效计费类型" required>
              <el-input value="重量计费" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="12">
            <el-form-item label="收运方式" required>
              <el-input value="袋装" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="绩效计算公式" required>
              <el-input
                value="收运单价 × 当月总收运量 × 当月总里程系数 × 当月考核系数"
                placeholder=""
                reanonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="收运单价标准信息" required>
              <el-table :data="[{}]" :header-cell-style="{ background: '#F5F7F9' }" border>
                <el-table-column prop="price" label="司机收运单价（元/吨）" align="center">
                  <template>
                    <el-input-number v-model="ruleForm.driverPrice" step-strictly :min="0"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column prop="factor" label="押运工收运单价（元/吨）" align="center">
                  <template>
                    <el-input-number v-model="ruleForm.supercargoPrice" step-strictly :min="0"></el-input-number>
                  </template>
                </el-table-column>
              </el-table>
              <div class="tips">收运单价即，收运对应点位数量后，每吨垃圾对应的绩效金额。</div>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="里程系数标准信息" prop="mileageFactors">
              <el-table :data="ruleForm.mileageFactors" :header-cell-style="{ background: '#F5F7F9' }" border>
                <el-table-column label="行驶公里范围（公里）" align="center">
                  <template #default="{ row }">
                    <div>{{ row.min }} {{ "＜" }} x {{ "≤" }} {{ row.max }}</div>
                  </template>
                </el-table-column>
                <el-table-column prop="factor" label="里程系数" align="center"></el-table-column>
                <el-table-column min-width="140" label="操作" align="center">
                  <template #default="{ row, $index }">
                    <el-link
                      class="mr-10"
                      type="primary"
                      @click="editItem('mileageFactorsItem', 'showMileageFactors', row, $index)"
                      >编辑</el-link
                    >
                    <el-link type="danger" @click="deleteItem('mileageFactors', $index)">删除</el-link>
                  </template>
                </el-table-column>
              </el-table>
              <el-button
                class="mt-10"
                type="primary"
                icon="el-icon-plus"
                @click="editItem('mileageFactorsItem', 'showMileageFactors', '')"
                >新增里程系数</el-button
              >
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
    <drumPrices
      :value.sync="showDrumPrices"
      :formItem="drumPricesItem"
      :formList="ruleForm.drumPrices"
      :formIndex="formIndex"
      @addList="addList"
      @editList="editList"
    ></drumPrices>
    <mileageFactors
      :value.sync="showMileageFactors"
      :formItem="mileageFactorsItem"
      :formList="ruleForm.mileageFactors"
      :formIndex="formIndex"
      @addList="addList"
      @editList="editList"
    ></mileageFactors>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";
  import { createApiFun, updateApiFun } from "@/api/base";
  import drumPrices from "./drumPrices.vue";
  import mileageFactors from "./mileageFactors.vue";
  export default {
    components: {
      baseTitle,
      drumPrices,
      mileageFactors,
    },
    props: {
      ruleCode: {
        type: String,
        default: "",
      },
      ruleIndex: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        loading: false,
        ruleForm: {
          driverPrice: "",
          supercargoPrice: "",
          drumPrices: [],
          mileageFactors: [],
        },
        rules: {
          drumPrices: [{ required: true, message: "请填写收运单价标准信息", trigger: "change" }],
          mileageFactors: [{ required: true, message: "请填写里程系数标准信息", trigger: "change" }],
        },
        apis: {
          info: "/api/assess/scheme/set/findByRuleCode",
          save: "/api/assess/scheme/set/updateDrumOrBagSet",
        },
        saveRecordThrottling: () => {},
        showDrumPrices: false,
        drumPricesItem: {},
        showMileageFactors: false,
        mileageFactorsItem: {},
        formIndex: 0,
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取详情
      async getRecord() {
        this.loading = true;
        try {
          let res = await createApiFun({ ruleCode: this.ruleCode }, this.apis.info);
          if (res.success) {
            this.ruleForm = res.data;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
          console.log(error);
        }
      },
      // 编辑收运单价 / 标准系数
      editItem(itemField, showField, row, index) {
        this[itemField] = row;
        this[showField] = true;
        this.formIndex = index;
      },
      // 删除收运单价 / 标准系数
      deleteItem(listField, index) {
        if (this.ruleForm[listField].length === 1 || index === this.ruleForm[listField].length - 1) {
          this.ruleForm[listField].splice(index);
        } else {
          this.$confirm(`请注意，调整收运点数区间后将会影响该收运单价数据后的所有关联数据。`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(async () => {
              this.ruleForm[listField].splice(index);
            })
            .catch(() => {});
        }
      },
      // 添加列表数据
      addList(obj) {
        this.ruleForm[obj.listField].push(obj.item);
        this[obj.showfield] = false;
      },
      // 编辑列表数据
      editList(obj) {
        if (this.ruleForm[obj.listField].length !== 1 && obj.index !== this.ruleForm[obj.listField].length - 1) {
          this.$confirm(`请注意，调整收运点数区间后将会影响该收运单价数据后的所有关联数据。`, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(async () => {
              this.$set(this.ruleForm[obj.listField], obj.index, obj.item);
              this.ruleForm[obj.listField].splice(obj.index + 1);
            })
            .catch(() => {});
        } else {
          this.$set(this.ruleForm[obj.listField], obj.index, obj.item);
        }
        this[obj.showfield] = false;
      },
      // 取消
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await updateApiFun(this.ruleForm, this.apis.save);
              if (res.success) {
                this.$message.success(`保存成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-data {
    margin-bottom: 20px;
  }
</style>
