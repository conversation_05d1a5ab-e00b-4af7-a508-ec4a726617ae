<template>
  <div class="container">
    <div class="left">
      <div @click="emitter.emit('open-waybill-dialog', { key: 'showElectronicWaybillDialog', completed: 0 })">
        <div class="left-title">今日有效路线收运单总数</div>
        <div class="left-count">{{ waybillInfo?.waybillNum }}</div>
      </div>
      <div @click="emitter.emit('open-waybill-dialog', { key: 'showElectronicWaybillDialog', completed: 1 })">
        <div class="left-title mt-20">今日已完成路线收运单总数</div>
        <div class="left-count">{{ waybillInfo?.completeBillNum }}</div>
      </div>
      <div @click="emitter.emit('open-waybill-dialog', { key: 'showElectronicWaybillDialog', completed: 2 })">
        <div class="left-title mt-20">今日未完成路线收运单总数</div>
        <div class="left-count">{{ waybillInfo?.unCompleteBillNum }}</div>
      </div>
    </div>
    <div class="right">
      <el-progress
        :width="100"
        type="circle"
        :percentage="roundReserveDecimals((waybillInfo?.completeBillNum / waybillInfo?.waybillNum) * 100) || 0"
        :stroke-width="10"
        define-back-color="#aaa"
        text-color="#fff"
        color="#CAF982"
      ></el-progress>
    </div>
  </div>
</template>

<script>
  import { roundReserveDecimals } from "@/utils";
  import emitter from "@/utils/mitt";
  export default {
    computed: {
      waybillInfo() {
        return this.$store.state.formData.waybillInfo;
      },
    },
    data() {
      return {
        roundReserveDecimals,
        emitter,
      };
    },
  };
</script>

<style lang="scss" scoped>
  .container {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin: 30px 0;
    margin-bottom: 0;
    cursor: pointer;
    pointer-events: auto;
  }
  .left {
    color: #fff;
    .left-title {
      font-size: 16px;
    }
    .left-count {
      font-size: 40px;
      font-weight: bold;
      margin-top: 4px;
    }
  }
  .mt-20 {
    margin-top: 20px;
  }
</style>
