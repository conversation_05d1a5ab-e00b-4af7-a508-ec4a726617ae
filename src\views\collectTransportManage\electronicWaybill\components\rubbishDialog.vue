<template>
  <div class="micro-app-sctmp_base" v-loading="loading">
    <el-dialog title="编辑收运信息" :visible.sync="dialogVisible" width="500px" destroy-on-close @open="initForm">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="170px">
        <template v-if="showInfo">
          <el-form-item label="收运车辆" prop="defaultVehiclePlateNumber">
            <el-select
              v-model="ruleForm.defaultVehiclePlateNumber"
              placeholder="请选择收运车辆"
              clearable
              filterable
              class="w300"
            >
              <el-option
                v-for="item in carOptions"
                :key="item.id"
                :label="item.plateNumber"
                :value="item.plateNumber"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="收运司机" prop="defaultDriverDossierId">
            <el-select
              v-model="ruleForm.defaultDriverDossierId"
              placeholder="请选择收运司机"
              clearable
              filterable
              class="w300"
            >
              <el-option
                v-for="item in driverOptions"
                :key="item.lgUnionId"
                :label="item.fullName"
                :value="item.lgUnionId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="押运工1" prop="supercargoDossierOneId">
            <el-select
              v-model="ruleForm.supercargoDossierOneId"
              placeholder="请选择押运工1"
              clearable
              filterable
              class="w300"
            >
              <el-option
                v-for="item in shipWorkerOptions"
                :key="item.lgUnionId"
                :label="item.fullName"
                :value="item.lgUnionId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="押运工2" prop="supercargoDossierTwoId">
            <el-select
              v-model="ruleForm.supercargoDossierTwoId"
              placeholder="请选择押运工2"
              clearable
              filterable
              class="w300"
            >
              <el-option
                v-for="item in shipWorkerOptions"
                :key="item.lgUnionId"
                :label="item.fullName"
                :value="item.lgUnionId"
              ></el-option>
            </el-select>
          </el-form-item>
        </template>
        <template v-if="rubbishRecord.waybillStatus === 1">
          <el-form-item label="总重量（kg）" required>
            <el-input-number :value="rubbishTotal" :min="0" :max="99999999" :precision="2" disabled></el-input-number>
          </el-form-item>
          <el-form-item :label="`${item.label}（kg）`" :prop="item.key" v-for="item in ruleFormArr" :key="item.key">
            <el-input-number v-model="ruleForm[item.key]" :min="0" :max="99999999" :precision="2"></el-input-number>
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveThrottling">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { floatAdd, isSameMonth, filterObjectByFieldArr } from "@/utils";
  import { createApiFun, getListApiFun } from "@/api/base";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      rubbishRecord: {
        type: Object,
        default: () => {},
      },
      showInfo: {
        type: Boolean,
        default: false,
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
      rubbishTotal() {
        return this.ruleFormArr.reduce((acc, item) => floatAdd(acc, this.ruleForm[item.key]), 0);
      },
    },
    data() {
      return {
        apis: {
          submit: "/api/waybill/detail/updateRecord/submit",
          carList: "/api/vehicle/dossier/getFreeList",
          userList: "/api/baseuser/list",
        },
        saveThrottling: () => {},
        loading: false,
        ruleForm: {
          defaultVehiclePlateNumber: "", //收运车辆
          defaultDriverDossierId: "", //司机
          supercargoDossierOneId: "", //押运工1
          supercargoDossierTwoId: "", //押运工2
          rubbishTotal: "", //总重量
          infectiousWaste: "", //感染性重量
          damagingWaste: "", //损伤性重量
          pharmaceuticalWaste: "", //药物性重量
          pathologicalWaste: "", //病理性重量
          chemicalWaste: "", //化学性重量
          sludge: "", //感染性-污泥重量
        },
        rules: {
          defaultVehiclePlateNumber: [{ required: true, message: "请选择收运车辆", trigger: "change" }],
          defaultDriverDossierId: [{ required: true, message: "请选择司机", trigger: "change" }],
          infectiousWaste: [{ required: true, message: "请输入感染性重量", trigger: "blur" }],
          damagingWaste: [{ required: true, message: "请输入损伤性重量", trigger: "blur" }],
          pharmaceuticalWaste: [{ required: true, message: "请输入药物性重量", trigger: "blur" }],
          pathologicalWaste: [{ required: true, message: "请输入病理性重量", trigger: "blur" }],
          chemicalWaste: [{ required: true, message: "请输入化学性重量", trigger: "blur" }],
          sludge: [{ required: true, message: "请输入感染性-污泥重量", trigger: "blur" }],
        },
        ruleFormArr: [
          { key: "infectiousWaste", label: "感染性重量" },
          { key: "damagingWaste", label: "损伤性重量" },
          { key: "pharmaceuticalWaste", label: "药物性重量" },
          { key: "pathologicalWaste", label: "病理性重量" },
          { key: "chemicalWaste", label: "化学性重量" },
          { key: "sludge", label: "感染性-污泥重量" },
        ],
        carOptions: [],
        driverOptions: [],
        shipWorkerOptions: [],
        infoRecordArr: [
          "defaultVehiclePlateNumber",
          "defaultDriverDossierId",
          "supercargoDossierOneId",
          "supercargoDossierTwoId",
        ],
        rubbishRecordArr: [
          "rubbishTotal",
          "infectiousWaste",
          "damagingWaste",
          "pharmaceuticalWaste",
          "pathologicalWaste",
          "chemicalWaste",
          "sludge",
        ],
      };
    },
    created() {
      this.saveThrottling = this.$throttling(this.saveRubbish, 500);
    },
    methods: {
      async initForm() {
        if (
          this.showInfo &&
          (isSameMonth(this.rubbishRecord.effectiveDate) || isSameMonth(this.rubbishRecord.endDate))
        ) {
          const { defaultVehiclePlateNumber, defaultDriverDossierId, supercargoDossierOneId, supercargoDossierTwoId } =
            this.rubbishRecord;
          Object.assign(this.ruleForm, {
            defaultVehiclePlateNumber,
            defaultDriverDossierId,
            supercargoDossierOneId,
            supercargoDossierTwoId,
          });
        }
        if (this.rubbishRecord.waybillStatus === 1 && isSameMonth(this.rubbishRecord.waybillTime)) {
          const { infectiousWaste, damagingWaste, pharmaceuticalWaste, pathologicalWaste, chemicalWaste, sludge } =
            this.rubbishRecord;
          Object.assign(this.ruleForm, {
            infectiousWaste,
            damagingWaste,
            pharmaceuticalWaste,
            pathologicalWaste,
            chemicalWaste,
            sludge,
          });
        }
        // 获取数据列表
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getListApiFun({ userIdentity: 4 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipWorkerOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },
      // 编辑
      async saveRubbish() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let infoRecord = {};
              let rubbishRecord = {};
              if (
                this.showInfo &&
                (isSameMonth(this.rubbishRecord.effectiveDate) || isSameMonth(this.rubbishRecord.endDate))
              ) {
                infoRecord = filterObjectByFieldArr(this.ruleForm, this.infoRecordArr);
              }
              if (this.rubbishRecord.waybillStatus === 1 && isSameMonth(this.rubbishRecord.waybillTime)) {
                rubbishRecord = filterObjectByFieldArr(this.ruleForm, this.rubbishRecordArr);
              }
              let res = await createApiFun(
                { waybillDetailId: this.rubbishRecord.id, ...infoRecord, ...rubbishRecord },
                this.apis.submit,
              );
              if (res.success) {
                this.$message.success("编辑成功");
                this.dialogVisible = false;
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
  }
</style>
