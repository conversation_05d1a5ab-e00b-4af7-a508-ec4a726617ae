<template>
  <div class="container container-position">
    <div class="title"
      >收运单情况
      <div class="legend">
        <div class="legend-flex-wrapper"><div class="dot-green"></div> 当日总收运单</div>
        <div class="legend-flex-wrapper"><div class="dot-yellow"></div>当日已完成收运单</div>
      </div>
    </div>

    <div class="row header">
      <div class="col-top" v-for="(day, index) in days" :key="index">
        <div class="grid-content bg-purple">{{ day }}</div>
      </div>
    </div>
    <div class="calendar-body">
      <div class="row" v-for="(week, weekIndex) in weeks" :key="weekIndex">
        <div
          :class="day.isCurrentMonth ? 'bg-value' : 'bg-value-none'"
          class="col"
          v-for="(day, dayIndex) in week"
          :key="`${weekIndex}-${dayIndex}`"
        >
          <div class="grid-content" :class="day.isCurrentMonth ? 'bg-purple' : 'bg-none'">
            <div class="dayday">{{ day.day }}</div>
            <div class="dayvalue">
              <template v-if="day.day"> <span class="green">45</span>/<span class="yellow">20</span> </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    components: {},
    props: {},
    data() {
      return {
        days: ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"],
        weeks: [
          // 9月第一周
          [
            { day: null, isCurrentMonth: false },
            { day: null, isCurrentMonth: false },
            { day: null, isCurrentMonth: false },
            { day: null, isCurrentMonth: false },
            { day: null, isCurrentMonth: false },
            { day: null, isCurrentMonth: false },
            { day: 1, isCurrentMonth: true },
          ],
          // 9月第二周
          [
            { day: 2, isCurrentMonth: true },
            { day: 3, isCurrentMonth: true },
            { day: 4, isCurrentMonth: true },
            { day: 5, isCurrentMonth: true },
            { day: 6, isCurrentMonth: true },
            { day: 7, isCurrentMonth: true },
            { day: 8, isCurrentMonth: true },
          ],
          // 9月第三周
          [
            { day: 9, isCurrentMonth: true },
            { day: 10, isCurrentMonth: true },
            { day: 11, isCurrentMonth: true },
            { day: 12, isCurrentMonth: true },
            { day: 13, isCurrentMonth: true },
            { day: 14, isCurrentMonth: true },
            { day: 15, isCurrentMonth: true },
          ],
          // 9月第四周
          [
            { day: 16, isCurrentMonth: true },
            { day: 17, isCurrentMonth: true },
            { day: 18, isCurrentMonth: true },
            { day: 19, isCurrentMonth: true },
            { day: 20, isCurrentMonth: true },
            { day: 21, isCurrentMonth: true },
            { day: 22, isCurrentMonth: true },
          ],
          // 9月第五周
          [
            { day: 23, isCurrentMonth: true },
            { day: 24, isCurrentMonth: true },
            { day: 25, isCurrentMonth: true },
            { day: 26, isCurrentMonth: true },
            { day: 27, isCurrentMonth: true },
            { day: 28, isCurrentMonth: true },
            { day: 29, isCurrentMonth: true },
          ],
          [
            { day: 30, isCurrentMonth: true },
            { day: null, isCurrentMonth: false },
            { day: null, isCurrentMonth: false },
            { day: null, isCurrentMonth: false },
            { day: null, isCurrentMonth: false },
            { day: null, isCurrentMonth: false },
            { day: null, isCurrentMonth: false },
          ],
        ],
      };
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {},
    watch: {},
  };
</script>

<style scoped lang="scss">
  .container-position {
    width: 440px;
    height: 320px;
    position: absolute;
    z-index: 1;
    top: 730px;
    left: 1440px;
    background-color: #000;
    padding: 20px;
  }
  .legend {
    top: 0;
    right: 0;
    position: absolute;
    display: flex;
  }
  .title {
    position: relative;
    font-size: 14px;
    color: #fff;
  }

  .row {
    display: flex;
    justify-content: space-between;
  }

  .col {
    flex: 1;
    margin-right: 10px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .col-top {
    flex: 1;
    margin-right: 10px;
    margin-top: 10px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .col:last-child {
    margin-right: 0;
  }

  .grid-content {
    position: relative;
    height: 100%;
    width: 100%;
  }
  .dayday {
    position: absolute;
    top: 4px;
    left: 0;
  }
  .dayvalue {
    font-size: 14px;
    position: absolute;
    bottom: 0;
    left: 0;
  }
  .bg-purple {
    background-color: #000;
    color: #fff;
  }
  .bg-value {
    background-color: #000;
    color: #fff;
    border-bottom: 2px solid #fff;
  }
  .bg-value-none {
    background-color: #000;
    color: #000;
  }
  .bg-none {
    background-color: #000;
    color: #000;
  }
  .green {
    color: #11e48a;
  }
  .yellow {
    color: #ffff00;
  }
  .dot-green {
    width: 12px;
    height: 12px;
    background-color: #11e48a;
    margin-right: 8px;
  }
  .dot-yellow {
    width: 12px;
    height: 12px;
    background-color: #ffff00;
    margin-right: 8px;
  }
  .legend-flex-wrapper {
    display: flex;
    align-items: center;
    margin-left: 10px;
  }
</style>
