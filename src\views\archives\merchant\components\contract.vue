<template>
  <div class="main-record micro-app-sctmp_base">
    <record v-show="showRecord" @closeRecord="closeRecord" ref="record"></record>
    <div class="main-index" v-show="!showRecord">
      <backButton @closeRecord="closeRecord"></backButton>
      <div class="card-header">合同信息</div>
      <div class="record-content">
        <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" style="width: 100%" border>
          <el-table-column type="index" width="55" align="center"></el-table-column>
          <el-table-column prop="code" label="合同编号" align="center"> </el-table-column>
          <el-table-column prop="typeName" label="合同类型" align="center"> </el-table-column>
          <el-table-column prop="statusName" label="合同状态" align="center"></el-table-column>
          <el-table-column prop="effectiveDate" label="生效日期" align="center"></el-table-column>
          <el-table-column prop="expiryDate" label="失效日期" align="center"></el-table-column>
          <el-table-column prop="payDate" label="已缴费期间" align="center"> </el-table-column>
          <el-table-column prop="merchantFileName" label="客商名称" align="center"> </el-table-column>
          <el-table-column prop="dailyEmissions" label="日计量（KG）" align="center"> </el-table-column>
          <el-table-column prop="monthEmissions" label="月计量（KG）" align="center"> </el-table-column>
          <el-table-column prop="districtName" label="地区" align="center"> </el-table-column>
          <el-table-column min-width="140" label="操作" align="center">
            <template #default="{ row }">
              <el-link class="mr-10" type="primary" @click="editRecord(row)">详情</el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <Pagination :page="page" @pageChange="pageChange"></Pagination>
    </div>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import record from "@/views/contractManage/contractInfo/components/record.vue";
  import backButton from "@/components/backButton";
  export default {
    components: {
      record,
      backButton,
    },
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        ruleForm: {},
        apis: {
          listPage: "/api/contract/contractinfo/listPage",
        },
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        showRecord: false,
      };
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          merchantFileId: this.recordId,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 关闭弹窗
      closeRecord() {
        if (this.showRecord) {
          this.showRecord = false;
          return;
        }
        this.$emit("closeRecord");
      },
      editRecord(row) {
        this.showRecord = true;
        this.$refs.record.getRecord(row.id);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .main-index {
    height: 100%;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin: 20px 0;
  }
  .desc-value {
    color: #46535e;
  }
  .card-description {
    margin-bottom: 24px;
  }
</style>
