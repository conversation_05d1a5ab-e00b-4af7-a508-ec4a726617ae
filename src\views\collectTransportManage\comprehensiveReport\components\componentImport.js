let driverShipDialog = () => import("@/views/collectTransportManage/todayReport/components/driverShipDialog");
let vehicleDialog = () => import("@/views/collectTransportManage/todayReport/components/vehicleDialog");
let pointDialog = () => import("@/views/collectTransportManage/todayReport/components/pointDialog");
let exceptReportDialog = () => import("@/views/collectTransportManage/todayReport/components/exceptReport/dialog.vue");
let pointRecordDialog = () => import("@/views/collectTransportManage/todayReport/components/pointRecordDialog");
let card = () => import("@/views/collectTransportManage/todayReport/components/card.vue");
let chart = () => import("@/views/collectTransportManage/todayReport/components/chart.vue");
let chart2 = () => import("@/views/collectTransportManage/todayReport/components/chart2.vue");
let waybillDialog = () => import("@/views/collectTransportManage/todayReport/components/waybillDialog");
let routeDialog = () => import("@/views/collectTransportManage/todayReport/components/routeDialog");
let dangerDialog = () => import("@/views/collectTransportManage/todayReport/components/dangerDialog");
let exceptDetail = () => import("@/views/collectTransportManage/todayReport/components/exceptDetail");
let weightChart = () => import("@/views/collectTransportManage/todayReport/components/weightChart.vue");

export default {
  driverShipDialog,
  vehicleDialog,
  pointDialog,
  exceptReportDialog,
  pointRecordDialog,
  card,
  chart,
  chart2,
  waybillDialog,
  routeDialog,
  dangerDialog,
  exceptDetail,
  weightChart,
};
