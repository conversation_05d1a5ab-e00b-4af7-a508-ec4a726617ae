<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="80%"
      :show-close="true"
      @open="initData"
      :before-close="closeDialog"
      :modal-append-to-body="false"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <PointTable
        :recordId="recordId"
        :recordForm="recordForm"
        v-if="showRecord"
        :query="query"
        @closeRecord="showRecord = false"
        height="700px"
      ></PointTable>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input
              class="w400 mr-10"
              v-model="keyword"
              placeholder="请输入路线名称/收运车牌号/司机姓名/押运工姓名"
              clearable
            ></el-input>
          </div>
          <el-button @click="initData">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
        </header>
        <main class="main">
          <el-table
            ref="tableRef"
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            border
            height="710px"
            v-loading="loading"
          >
            <el-table-column prop="name" label="路线名称" align="center"></el-table-column>
            <el-table-column prop="date" label="日期" align="center"></el-table-column>
            <el-table-column prop="plateNumber" label="收运车辆" align="center"></el-table-column>
            <el-table-column prop="driverName" label="收运司机" align="center"></el-table-column>
            <el-table-column prop="cargoOneName" label="押运员1" align="center"></el-table-column>
            <el-table-column prop="cargoTwoName" label="押运员2" align="center"></el-table-column>
            <el-table-column prop="total" label="应收点位" align="center"></el-table-column>
            <el-table-column prop="collectNum" label="已收点位" align="center"></el-table-column>
            <el-table-column prop="unCollectNum" label="未收点位" align="center"></el-table-column>

            <el-table-column width="180" label="操作" align="center">
              <template #default="{ row }">
                <el-link type="primary" @click="checkRecord(row)">查看详情</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import { RECEIVING_CONDITION, POINT_TASK_TYPE, IS_NORMAL_OPERATION, BARRELS_BAGS, VERIFY_STATUS } from "@/enums";
  import moment from "moment";
  import PointTable from "./PointTable.vue";

  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      completed: {
        type: Boolean,
        default: true,
      },

      query: {
        type: Object,
        default: () => {},
      },

      title: {
        type: String,
        default: "",
      },
    },
    components: {
      PointTable,
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/logisticsReport/waybill/listPage",
        },
        RECEIVING_CONDITION,
        POINT_TASK_TYPE,
        IS_NORMAL_OPERATION,
        BARRELS_BAGS,
        VERIFY_STATUS,
        loading: false,
        keyword: "",
        showRecord: false,
        recordId: "",
        recordForm: {},
        maxHeight: 500,
      };
    },
    methods: {
      initData() {
        this.showRecord = false;
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.page.pageSize = 10;
        this.getDataList();
      },
      async getDataList() {
        try {
          this.loading = true;
          let params = {
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            keyword: this.keyword,
            beginStatisticsDate: moment().format("YYYY-MM-DD"),
            endStatisticsDate: moment().format("YYYY-MM-DD"),
            completed: this.query.completed,
            districtId: this.query.districtId,
            effectiveEndBeginTime: this.query.effectiveEndBeginTime,
            effectiveEndEndTime: this.query.effectiveEndEndTime,
            type: this.query.type,
          };
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
          this.loading = false;
        } finally {
          this.loading = false;
        }
      },

      setHeight() {
        this.$nextTick(() => {
          const wrap = document.querySelector(".screen-wrapper");
          const scale = this.getScale(wrap);
          const h = document.querySelector(".el-dialog__wrapper").offsetHeight;
          const scaleH = h * scale.scaleX - 300;
          this.maxHeight = scaleH < 600 ? 600 : scaleH;
        });
      },

      getScale(element) {
        // 获取元素的所有计算样式
        const style = window.getComputedStyle(element);

        // 获取 transform 属性值
        const transform = style.getPropertyValue("transform");

        // 如果没有 transform 或者 transform 是 none，则返回默认缩放值 1
        if (transform === "none") {
          return { scaleX: 1, scaleY: 1 };
        }

        // 解析 transform matrix
        let matrix;
        try {
          // 尝试将 transform 解析为 matrix 或 matrix3d
          matrix = new DOMMatrix(transform);
        } catch (e) {
          console.error("Failed to parse transform matrix:", e);
          return { scaleX: 1, scaleY: 1 };
        }

        // 返回 scaleX 和 scaleY
        return {
          scaleX: matrix.a, // 对于 2D 变换，matrix.a 是 scaleX
          scaleY: matrix.d, // 对于 2D 变换，matrix.d 是 scaleY
        };
      },

      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.getDataList();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.getDataList();
      },
      // 查看详情
      checkRecord(row) {
        this.recordId = row.id;
        this.recordForm = row;
        this.showRecord = true;
      },

      closeDialog() {
        this.dialogVisible = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
      position: relative;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .w400 {
    width: 400px;
  }
  .picture-img {
    width: 60px;
    height: 60px;
  }
  ::v-deep .el-dialog__wrapper {
    overflow: hidden;

    .el-dialog {
      margin-top: 4vh !important;
    }
    &::-webkit-scrollbar {
      width: 0 !important; /* 隐藏滚动条 */
      height: 0 !important; /* 隐藏滚动条 */
    }
    /* 隐藏滚动条（WebKit） */
    &::-webkit-scrollbar {
      display: none;
    }

    /* 隐藏滚动条（Firefox） */
    @-moz-document url-prefix() {
      scrollbar-width: thin;
    }
    /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
    -ms-overflow-style: none;
    scrollbar-width: none; /* Firefox 64+ */
    scrollbar-color: transparent transparent;

    .el-table__body-wrapper {
      scrollbar-width: initial; /* Firefox 64+ */
      scrollbar-color: initial;
    }
  }

  ::v-deep .el-table {
    min-height: 200px;
  }
</style>
