<template>
  <div class="sub-title">
    <div class="title">{{ title }}</div>
  </div>
</template>

<script>
  export default {
    props: {
      title: {
        type: [String, Number],
        default: "标题",
      },
    },
    data() {
      return {
        iconData: {
          bg: require("@/assets/images/title-bg.png"),
        },
      };
    },
  };
</script>

<style lang="scss" scoped>
  .sub-title {
    width: 200px;
    height: 22px;
    padding: 0 10px;
    .title {
      position: relative;
      font-weight: 500;
      font-size: 16px;
      color: #c7f1e2;
      padding-left: 10px;
    }
  }
  .title::after {
    content: "";
    position: absolute;
    left: 0;
    top: 4px;
    width: 3px;
    height: 14px;
    background: #c7f1e2;
  }
</style>
