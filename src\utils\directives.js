export const resizeDirective = {
  update(el, binding) {
    if (binding.value !== binding.oldValue && binding.value) {
      el.__resizeHandler__ = () => {
        if (binding.value) {
          binding.value.resize();
        }
      };
      window.addEventListener("resize", el.__resizeHandler__);
    }
  },
  unbind(el) {
    window.removeEventListener("resize", el.__resizeHandler__);
    delete el.__resizeHandler__;
  },
};
