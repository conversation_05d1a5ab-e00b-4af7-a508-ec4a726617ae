let treeOptions = {
  tooltip: {
    trigger: "item",
    triggerOn: "mousemove",
  },
  series: [
    {
      type: "tree",
      data: [],
      top: "1%",
      left: "7%",
      bottom: "1%",
      right: "20%",
      symbolSize: 7,
      layout: "orthogonal",
      label: {
        position: "left",
        verticalAlign: "middle",
        align: "right",
        fontSize: 9,
      },
      leaves: {
        label: {
          position: "right",
          verticalAlign: "middle",
          align: "left",
        },
      },
      emphasis: {
        focus: "descendant",
      },
      expandAndCollapse: true,
      animationDuration: 550,
      animationDurationUpdate: 750,
    },
  ],
  color: ["#60B7F7", "#FABB28", "#FA8128", "#63CC7C", "#9669F5", "#4DCCCB"],
};

export default treeOptions;
