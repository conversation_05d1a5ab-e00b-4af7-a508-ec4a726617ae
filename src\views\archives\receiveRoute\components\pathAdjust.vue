<template>
  <div class="adjust-record micro-app-sctmp_base" v-loading="loading">
    <div class="adjust-content">
      <div class="adjust-header">
        <div>路径调整</div>
        <el-button @click="pathOptimize">路径优化</el-button>
      </div>
      <div class="tab-container">
        <div class="tab-box">
          <el-tabs v-model="activeStrategy" type="card" @tab-click="handleTabClick">
            <el-tab-pane label="速度优先" name="0"></el-tab-pane>
            <el-tab-pane label="距离优先" name="2"></el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="adjust-map">
        <div class="map-box">
          <mapContainer mapId="originalMap" @initMap="initOriginalMap"></mapContainer>
          <div class="map-title">原路线</div>
          <div class="map-info">
            <div class="info-text">路线点位数：{{ pointTotal }}</div>
            <div class="info-text">路线总用时：{{ originalRouteForm.time }}</div>
            <div class="info-text">路线总长度：{{ originalRouteForm.distance }}km</div>
          </div>
        </div>
        <div class="map-box">
          <mapContainer mapId="newMap" @initMap="initNewMap"></mapContainer>
          <div class="map-title">调整后</div>
          <div class="map-info">
            <div class="info-text">路线点位数：{{ pointTotal }}</div>
            <div class="info-text">路线总用时：{{ newRouteForm.time }}</div>
            <div class="info-text">路线总长度：{{ newRouteForm.distance }}km</div>
          </div>
        </div>
      </div>
      <div class="adjust-table">
        <el-table
          :data="ruleForm.tableData"
          :header-cell-style="{ background: '#F5F7F9' }"
          style="width: 100%"
          border
          row-key="id"
          row-class-name="table-row"
        >
          <el-table-column type="index" label="收运顺序" align="center" width="120"></el-table-column>
          <el-table-column prop="name" label="点位名称" align="center"></el-table-column>
          <el-table-column prop="code" label="点位编号" align="center"></el-table-column>
          <el-table-column prop="address" label="点位地址" align="center"></el-table-column>
          <el-table-column prop="contact" label="点位联系人" align="center"></el-table-column>
          <el-table-column prop="contactPhone" label="点位联系方式" align="center"></el-table-column>
          <el-table-column prop="type" label="点位类型" align="center">
            <template #default="{ row }">{{ POINT_TYPE[row.type] }}</template>
          </el-table-column>
          <el-table-column prop="contractStatusName" label="合同状态" align="center"></el-table-column>
          <el-table-column prop="deviceStatusName" label="设备状态" align="center"></el-table-column>
          <el-table-column prop="isUndock" label="移除状态" align="center">
            <template #default="{ row }">{{ REMOVE_STATUS[row.isUndock] }}</template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="adjust-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="refreshPath">刷新路线</el-button>
      <el-button type="primary" @click="compareThrottling">应用路线</el-button>
    </div>
    <el-dialog
      title="备注"
      :visible.sync="showRemark"
      width="500px"
      destroy-on-close
      :close-on-click-modal="false"
      v-loading="loading"
    >
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
        <el-form-item label="修改备注" prop="historyRemark">
          <el-input
            v-model="ruleForm.historyRemark"
            placeholder="请输入备注"
            clearable
            type="textarea"
            rows="10"
            maxlength="500"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showRemark = false">取消</el-button>
        <el-button type="primary" @click="saveThrottling">完成</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun, updateApiFun, DISPATCH_API_URL } from "@/api/base";
  import Sortable from "sortablejs";
  import { POINT_TYPE, REMOVE_STATUS } from "@/enums";
  import mapContainer from "@/components/mapContainer";
  import { post } from "@/utils/request";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      mapContainer,
    },
    data() {
      return {
        POINT_TYPE,
        REMOVE_STATUS,
        apis: {
          info: "/api/pickup/pickupPath/get/",
          getStartPoint: "/api/pickup/pickupPoint/getStartPoint",
          compare: "/api/pickup/pickupPath/compare",
          update: "/api/pickup/pickupPath/update",
          optimize: "/api/tsp/path/optimize",
        },
        ruleForm: {
          name: "", //路线名称
          code: "", //路线编号
          status: "", //路线状态
          districtId: "", //所属区域id
          type: "", //路线属性
          strategy: 0, //路线策略（默认为0-速度优先）
          defaultVehiclePlateNumber: "", //默认收运车辆
          defaultDriverDossierId: "", //默认驾驶司机
          defaultDriverDossierPhone: "", //司机联系方式
          supercargoDossierOneId: "", //默认押运工1
          supercargoDossierOnePhone: "", //押运工联系方式
          supercargoDossierTwoId: "", //默认押运工2
          supercargoDossierTwoPhone: "", //押运工联系方式
          tableData: [], //点位列表
          historyRemark: "", //备注
          versionNumber: 1,
        },
        loading: false,
        originalMap: "",
        newMap: "",
        showRemark: false,
        compareThrottling: () => {},
        saveThrottling: () => {},
        startEndPoint: "",
        originalRoutePath: [],
        newRoutePath: [],
        rules: {
          historyRemark: [{ required: true, message: "请输入备注", trigger: "blur" }],
        },
        pointTotal: 0,
        originalRouteForm: {
          time: 0,
          distance: 0,
        },
        newRouteForm: {
          time: 0,
          distance: 0,
        },
        isOptimize: false,
        optimizeLoad: false,
        shortestPath: [],
        leastTimePath: [],
      };
    },
    computed: {
      activeStrategy: {
        get() {
          return this.ruleForm.strategy.toString();
        },
        set(newValue) {
          this.ruleForm.strategy = Number(newValue);
        },
      },
    },
    created() {
      document.body.ondrop = (event) => {
        event.preventDefault();
        event.stopPropagation();
      };
      this.compareThrottling = this.$throttling(this.compareRecord, 500);
      this.saveThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.initSortable();
    },
    methods: {
      initSortable() {
        this.$nextTick(() => {
          const el = this.$el.querySelector(".el-table__body-wrapper tbody");
          Sortable.create(el, {
            onEnd: (event) => {
              const { oldIndex, newIndex } = event;
              this.updateRowOrder(oldIndex, newIndex);
            },
          });
        });
      },
      // 列表行拖拽顺序调整
      updateRowOrder(oldIndex, newIndex) {
        this.isOptimize = false;
        let arr = this.ruleForm.tableData;
        arr.splice(newIndex, 0, arr.splice(oldIndex, 1)[0]);
        this.$nextTick(() => {
          this.ruleForm.tableData = arr;
        });
      },
      // 获取起终点坐标
      async getStartAndEndPoint() {
        let res = await getInfoApiFun("", this.apis.getStartPoint);
        if (res.success) {
          this.startEndPoint = res.data;
          this.drawStartEndPoint("originalMap");
          this.drawStartEndPoint("newMap");
        }
      },
      // 获取收运路线档案详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.ruleForm.tableData = res.data.pickupPointList.map((item) => {
            return {
              ...item,
              contactPhone: item.contactPhone ? this.$sm2Decrypt(item.contactPhone) : "",
            };
          });
          this.pointTotal = this.ruleForm.tableData.length;
          this.drawMarker(res.data.pickupPointList, "originalMap");
          this.drawMarker(res.data.pickupPointList, "newMap");
          let pathList = res.data.pickupPointList.map((dt) => {
            return [dt.longitude, dt.latitude];
          });
          pathList.push([this.startEndPoint.longitude, this.startEndPoint.latitude]);
          let longLatList = this.cutArray(pathList);
          longLatList.forEach((list) => {
            this.handleDrive(
              list[0],
              list[list.length - 1],
              list.slice(1, list.length - 1),
              this.ruleForm.strategy,
              "originalRoutePath",
              "originalMap",
            );
            this.handleDrive(
              list[0],
              list[list.length - 1],
              list.slice(1, list.length - 1),
              this.ruleForm.strategy,
              "newRoutePath",
              "newMap",
            );
          });
        }
      },
      // 初始化原路线地图
      initOriginalMap(map) {
        if (map) {
          this.originalMap = map;
          this.getStartAndEndPoint();
          this.getRecord();
        }
      },
      // 初始化新路线地图
      initNewMap(map) {
        if (map) {
          this.newMap = map;
        }
      },
      // 关闭
      closeRecord() {
        this.showRemark = false;
        this.$emit("closeRecord");
      },
      // tab栏切换事件回调
      handleTabClick() {
        if (this.isOptimize) {
          this.handleTableDataByStrategy();
        } else {
          this.refreshPath();
        }
      },
      // 路径优化
      pathOptimize() {
        this.$confirm(`系统将按照算法对当前的点位顺序进行优化，路径调整下方展示的点位顺序将会产生变化。`, "路径优化", {
          confirmButtonText: "优化",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.isOptimize = true;
            if (this.optimizeLoad) {
              this.handleTableDataByStrategy();
            } else {
              this.loading = true;
              try {
                let res = await post(DISPATCH_API_URL + this.apis.optimize, { pathId: this.recordId });
                if (res.success) {
                  this.shortestPath = res.data.shortestPath.points
                    .slice(1, res.data.shortestPath.points.length - 1)
                    .map((item) => item.uuid);
                  this.leastTimePath = res.data.leastTimePath.points
                    .slice(1, res.data.leastTimePath.points.length - 1)
                    .map((item) => item.uuid);
                  this.handleTableDataByStrategy();
                }
                this.loading = false;
              } catch (error) {
                this.loading = false;
                console.warn(error);
              }
            }
          })
          .catch(() => {});
      },
      handleTableDataByStrategy() {
        let orderMap = [];
        switch (this.ruleForm.strategy) {
          case 0:
            orderMap = this.leastTimePath.map((id, index) => [id, index]);
            this.ruleForm.tableData.sort(
              (a, b) => orderMap.find(([id]) => id === a.id)[1] - orderMap.find(([id]) => id === b.id)[1],
            );
            break;
          case 2:
            orderMap = this.shortestPath.map((id, index) => [id, index]);
            this.ruleForm.tableData.sort(
              (a, b) => orderMap.find(([id]) => id === a.id)[1] - orderMap.find(([id]) => id === b.id)[1],
            );
            break;
        }
        this.refreshPath();
      },
      // 比较
      async compareRecord() {
        this.loading = true;
        this.ruleForm.pickupPointIds = this.ruleForm.tableData.map((data) => data.id);
        try {
          let res = await createApiFun(this.ruleForm, this.apis.compare);
          if (res.success) {
            if (res.data.point) {
              this.showRemark = true;
            } else {
              this.closeRecord();
            }
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 刷新路线
      refreshPath() {
        this.newMap.clearMap();
        this.newMap.clearInfoWindow();
        this.newRoutePath = [];
        this.drawStartEndPoint("newMap");
        this.drawMarker(this.ruleForm.tableData, "newMap");
        let pathList = this.ruleForm.tableData.map((dt) => {
          return [dt.longitude, dt.latitude];
        });
        pathList.push([this.startEndPoint.longitude, this.startEndPoint.latitude]);
        let longLatList = this.cutArray(pathList);
        longLatList.forEach((list) => {
          this.handleDrive(
            list[0],
            list[list.length - 1],
            list.slice(1, list.length - 1),
            this.ruleForm.strategy,
            "newRoutePath",
            "newMap",
          );
        });
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            this.ruleForm.pickupPointIds = this.ruleForm.tableData.map((data) => data.id);
            try {
              let res = await updateApiFun(this.$sm2Encrypt(JSON.stringify(this.ruleForm)), this.apis.update);
              if (res.success) {
                this.$message.success(`应用路线成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      // 绘制起终点坐标
      drawStartEndPoint(mapRef) {
        // 设置起始/终点坐标点位图标
        const startIcon = new window.AMap.Icon({
          // 图标尺寸
          size: new window.AMap.Size(25, 34),
          // 图标的取图地址
          image: "https://a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png",
          // 图标所用图片大小
          imageSize: new window.AMap.Size(135, 40),
          // 图标取图偏移量
          imageOffset: new window.AMap.Pixel(-9, -3),
        });
        let startMarker = new window.AMap.Marker({
          position: new window.AMap.LngLat(this.startEndPoint.longitude, this.startEndPoint.latitude),
          icon: startIcon,
          offset: new window.AMap.Pixel(-13, -30),
        });

        const endIcon = new window.AMap.Icon({
          size: new window.AMap.Size(25, 34),
          image: "https://a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png",
          imageSize: new window.AMap.Size(135, 40),
          imageOffset: new window.AMap.Pixel(-95, -3),
        });
        // 将 icon 传入 marker
        let endMarker = new window.AMap.Marker({
          position: new window.AMap.LngLat(this.startEndPoint.longitude, this.startEndPoint.latitude),
          icon: endIcon,
          offset: new window.AMap.Pixel(-13, -30),
        });

        startMarker.content = `当前地点为：${this.startEndPoint.address}`;
        startMarker.on("click", mapRef === "originalMap" ? this.originalMarkerClick : this.newMarkerClick);
        endMarker.content = `当前地点为：${this.startEndPoint.address}`;
        endMarker.on("click", mapRef === "originalMap" ? this.originalMarkerClick : this.newMarkerClick);

        // 将 markers 添加到地图
        this[mapRef].add([startMarker, endMarker]);
      },
      // 绘制点位
      drawMarker(dataList, mapRef) {
        // 添加点位标记
        dataList.forEach((data, index) => {
          let content = `
            <div class="marker-icon" style="width:20px;height:20px;overflow:hidden;background-color:#D9001B;color:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:12px">${
              index + 1
            }</div>
            `;
          let marker = new window.AMap.Marker({
            content,
            map: this[mapRef],
            position: [data.longitude, data.latitude],
          });
          marker.content = `当前地点为：${data.address}`;
          marker.on("click", mapRef === "originalMap" ? this.originalMarkerClick : this.newMarkerClick);
        });
      },
      // 点标记点击事件
      originalMarkerClick(e) {
        let infoWindow = new window.AMap.InfoWindow({ offset: new window.AMap.Pixel(4, -4) });
        infoWindow.setContent(e.target.content);
        infoWindow.open(this.originalMap, e.target.getPosition());
      },
      // 点标记点击事件
      newMarkerClick(e) {
        let infoWindow = new window.AMap.InfoWindow({ offset: new window.AMap.Pixel(4, -4) });
        infoWindow.setContent(e.target.content);
        infoWindow.open(this.newMap, e.target.getPosition());
      },
      // 调用高德地图jsApi获取驾驶路线
      handleDrive(start, end, points, strategy, routePathField, mapRef) {
        window.AMap.plugin("AMap.Driving", () => {
          let driving = new window.AMap.Driving({
            // map: this[mapRef],
            policy: strategy, //驾车路线规划策略，0是速度优先的策略
            // panel: "container",
          });
          driving.search(start, end, { waypoints: points }, (status, result) => {
            //status：complete 表示查询成功，no_data 为查询无结果，error 代表查询错误
            //查询成功时，result 即为对应的驾车导航信息
            if (status === "complete") {
              if (mapRef === "originalMap") {
                this.originalRouteForm.time = `${Math.floor(result.routes[0].time / 3600)}h${Math.floor(
                  (result.routes[0].time % 3600) / 60,
                )}m`;
                this.originalRouteForm.distance = (result.routes[0].distance / 1000).toFixed(2);
              } else {
                this.newRouteForm.time = `${Math.floor(result.routes[0].time / 3600)}h${Math.floor(
                  (result.routes[0].time % 3600) / 60,
                )}m`;
                this.newRouteForm.distance = (result.routes[0].distance / 1000).toFixed(2);
              }
              this.drawRoute(result.routes[0], routePathField, mapRef);
            }
          });
        });
      },
      drawRoute(route, routePathField, mapRef) {
        let path = this.parseRouteToPath(route);
        this[routePathField] = this[routePathField].concat(path);
        // 绘制轨迹
        new window.AMap.Polyline({
          map: this[mapRef],
          path,
          showDir: true,
          strokeColor: "#28F", //线颜色
          strokeWeight: 6, //线宽
        });
        this[mapRef].setFitView();
      },
      // 解析DrivingRoute对象，构造成AMap.Polyline的path参数需要的格式
      parseRouteToPath(route) {
        let path = [];
        for (let i = 0, l = route.steps.length; i < l; i++) {
          let step = route.steps[i];
          for (let j = 0, n = step.path.length; j < n; j++) {
            path.push(step.path[j]);
          }
        }
        return path;
      },
      // 按照数组长度为16切割，多余的数据存进最后一个数组
      cutArray(array) {
        let finalArray = [];
        // 商
        let quotient = Math.floor(array.length / 16);
        // 余数
        let remainder = array.length % 16;
        // 是否为16的倍数
        if (quotient > 0) {
          for (let i = 0; i < quotient; i++) {
            finalArray.push(array.slice(i * 16, (i + 1) * 16));
          }
          if (remainder > 0) {
            finalArray.push(array.slice(16 * quotient, array.length));
          }
          // 需要插入数组的索引数组
          let indexArr = [];
          // 需要插入数组的数据数组
          let valueArr = [];
          finalArray.forEach((arr, index) => {
            if (index >= 0 && index < finalArray.length - 1) {
              indexArr.push(index);
              valueArr.push([arr[15], finalArray[index + 1][0]]);
            }
          });
          // 将每个数组的终点坐标和起点坐标连接成数组并插入对应的位置
          indexArr.forEach((item, number) => {
            if (number >= 0 && number < indexArr.length) {
              finalArray.splice(item + number + 1, 0, valueArr[number]);
            }
          });
          finalArray = finalArray.filter((list) => list.length > 1);
        } else {
          finalArray = [array];
        }
        return finalArray;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .adjust-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .adjust-content {
    flex: 1;
    overflow: auto;
  }
  .adjust-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 20px;
    font-weight: bold;
    margin: 16px 0;
  }
  .tab-container {
    display: flex;
    justify-content: flex-end;
    .tab-box {
      width: 50%;
    }
  }
  .adjust-map {
    display: flex;
    width: 100%;
    justify-content: space-between;
    height: 600px;
    margin-bottom: 20px;
    .map-box {
      width: calc(50% - 10px);
      height: 100%;
      position: relative;
      .map-title {
        background-color: white;
        padding: 10px 16px;
        border-radius: 4px;
        box-shadow: 0 2px 6px 0 rgba(114, 124, 245, 0.5);
        position: absolute;
        top: 0;
        right: 0;
      }
      .map-info {
        background-color: white;
        padding: 10px 16px;
        border-radius: 4px;
        box-shadow: 0 2px 6px 0 rgba(114, 124, 245, 0.5);
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }
  }
  .strategy-tip {
    font-size: 12px;
    color: #909399;
  }
  .adjust-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .mb-10 {
    margin-bottom: 10px;
  }
  .dialog-footer {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  ::v-deep .table-row {
    cursor: move;
  }
</style>
