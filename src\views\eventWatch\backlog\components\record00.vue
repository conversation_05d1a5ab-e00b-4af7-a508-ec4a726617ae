<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">车辆待保养处理</div>
      <el-form label-width="180px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="上报时间" required>
              <el-date-picker
                :value="ruleFormEvent.createTime"
                type="datetime"
                placeholder="请选择上报时间"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                disabled
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="截止日期" required>
              <el-date-picker
                :value="ruleFormEvent.deadline"
                type="date"
                placeholder="请选择截止日期"
                clearable
                value-format="yyyy-MM-dd"
                disabled
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="基础信息"></baseTitle>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
          <el-row>
            <el-col :md="24" :lg="12">
              <el-form-item label="车牌号" prop="plateNumber">
                <el-input
                  v-model="ruleForm.plateNumber"
                  placeholder="请输入车牌号"
                  clearable
                  disabled
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="维保类型" prop="type">
                <el-select
                  v-model="ruleForm.type[0]"
                  placeholder="请选择维保类型"
                  clearable
                  filterable
                  disabled
                  class="w-300"
                >
                  <el-option v-for="item in typeOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="经办人" :prop="`dataList.${0}.operatorName`" :rules="rules.operatorName">
                <el-input
                  v-model="ruleForm.dataList[0].operatorName"
                  placeholder="请输入经办人姓名"
                  clearable
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="保养单位"
                :prop="`dataList.${0}.organizationName`"
                :rules="[{ required: true, message: '请输入保养单位', trigger: 'blur' }]"
              >
                <el-input
                  v-model="ruleForm.dataList[0].organizationName"
                  placeholder="请输入保养单位"
                  clearable
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="最近保养日期"
                :prop="`dataList.${0}.recentMaintenanceTime`"
                :rules="[{ required: true, message: `请选择最近保养日期`, trigger: 'change' }]"
              >
                <el-date-picker
                  v-model="ruleForm.dataList[0].recentMaintenanceTime"
                  type="date"
                  placeholder="请选择最近保养日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="下次保养日期" prop="dataList[0].nextMaintenanceTime">
                <el-date-picker
                  v-model="ruleForm.dataList[0].nextMaintenanceTime"
                  type="date"
                  placeholder="请选择下次保养日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="行驶总里程(Km)"
                :prop="`dataList.${0}.driveMileage`"
                :rules="[{ required: true, message: `请输入行驶总里程`, trigger: 'change' }]"
              >
                <el-input-number
                  v-model="ruleForm.dataList[0].driveMileage"
                  :precision="2"
                  :min="0"
                  :max="99999999999999"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="下次保养里程(Km)"
                :prop="`dataList.${0}.upkeepMileage`"
                :rules="[{ required: true, message: `请输入下次保养里程`, trigger: 'change' }]"
              >
                <el-input-number
                  v-model="ruleForm.dataList[0].upkeepMileage"
                  :precision="2"
                  :min="0"
                  :max="99999999999999"
                ></el-input-number>
                <div class="date-tips"
                  >合理保养里程范围为当前行驶里程+5000~10000KM，之后将会根据此次输入的里程数在即将到期的时候发出提醒。</div
                >
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="保养费用(元)"
                :prop="`dataList.${0}.costs`"
                :rules="[{ required: true, message: `请输入保养费用`, trigger: 'change' }]"
              >
                <el-input-number
                  v-model="ruleForm.dataList[0].costs"
                  :precision="2"
                  :min="0"
                  :max="99999999999999"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item
                label="凭证"
                :prop="`dataList.${0}.vehicleFileList`"
                :rules="[{ required: true, message: `请上传保养凭证`, trigger: 'change' }]"
              >
                <FileUpload @uploadChange="uploadChange($event, 0)" :imageList="imageList[0]" :limit="5">
                  <template #tips><el-tag type="warning">请上传保养凭证</el-tag></template>
                </FileUpload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
      <el-button type="primary" @click="saveRecordThrottling">完成</el-button>
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import FileUpload from "@/components/FileUpload";
  import { MAINTENANCE_TYPE } from "@/enums";
  import { deepClone } from "logan-common/utils";

  export default {
    props: {
      recordItem: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      baseTitle,
      FileUpload,
    },
    computed: {
      typeOptions() {
        let options = [];
        if (this.operationalNature == 1) {
          options = this.maintenanceTypeOptions.filter((item) => item.id != 1);
        } else {
          options = this.maintenanceTypeOptions;
        }
        return options;
      },
    },
    data() {
      return {
        ruleFormEvent: {
          createTime: null,
          completionTime: null,
        },
        ruleForm: {
          plateNumber: "",
          type: [0], //维保类型
          dataList: [
            {
              type: 0,
              operatorName: "", //经办人
              organizationName: "", //保养单位
              recentMaintenanceTime: "", //最近保养日期
              nextMaintenanceTime: "", //下次保养日期
              driveMileage: "", //行驶总里程
              upkeepMileage: "", //下次保养里程
              costs: "", //保养费用
              vehicleFileList: "", //保养凭证
            },
          ],
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号", trigger: "change" }],
          type: [{ required: true, message: "请选择维保类型", trigger: "change" }],
          operatorName: [{ required: true, message: "请输入经办人姓名", trigger: "blur" }],
        },
        apis: {
          recordInfo: "/api/task/get/",
          create: "/api/vehicleMaintenance/createBatch",
          update: "/api/vehicleMaintenance/update",
          info: "/api/vehicleMaintenance/get/",
          carList: "/api/vehicle/dossier/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [[], [], []],
        MAINTENANCE_TYPE,
        carOptions: [],
        // 0-常规保养 1-二级维护 2-维修
        maintenanceTypeOptions: [
          {
            id: 0,
            name: "常规保养",
          },
          {
            id: 1,
            name: "二级维护",
          },
          {
            id: 2,
            name: "维修",
          },
        ],
        operationalNature: "",
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptions();
      if (this.recordItem && this.recordItem.businessId) {
        this.getRecord();
        this.getBusiness();
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({ statusList: [0, 1] }, this.apis.carList)];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
      },
      uploadChange(fileList, index) {
        if (fileList.length > 0) {
          this.ruleForm.dataList[index].vehicleFileList = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
          this.$refs.ruleForm.clearValidate(`dataList.${index}.vehicleFileList`);
        } else {
          this.ruleForm.dataList[index].vehicleFileList = [];
        }
      },
      getCurrentDate() {
        const now = new Date();
        const year = now.getFullYear(); // 获取完整的年份(4位,1970-????)
        const month = String(now.getMonth() + 1).padStart(2, "0"); // 月份是从0开始的，所以要加1
        const day = String(now.getDate()).padStart(2, "0"); // 获取当前月份的日期
        return `${year}-${month}-${day}`;
      },
      // 获取业务详情，取原型需要的字段
      async getBusiness() {
        let res = await getInfoApiFun(this.recordItem.businessId, this.apis.info);
        if (res.success) {
          // 00 为 常规保养
          let obj = {
            plateNumber: res.data.plateNumber,
            type: [0], //维保类型
            dataList: [
              {
                type: 0,
                operatorName: "", //经办人
                organizationName: "", //保养单位
                recentMaintenanceTime: this.getCurrentDate(), //最近保养日期
                nextMaintenanceTime: "", //下次保养日期
                driveMileage: "", //行驶总里程
                upkeepMileage: "", //下次保养里程
                costs: "", //保养费用
                vehicleFileList: "", //保养凭证
              },
            ],
          };
          this.ruleForm = deepClone(obj);
        }
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            let params = {};
            if (this.recordId) {
              params = {
                id: this.ruleForm.id,
                plateNumber: this.ruleForm.plateNumber,
                ...this.ruleForm.dataList[this.ruleForm.type[0]],
              };
            } else {
              let dataList = this.ruleForm.dataList.filter((list) => this.ruleForm.type.includes(list.type));
              params = this.ruleForm;
              params.dataList = dataList;
            }
            try {
              let res = await createApiFun(params, this.apis.create);
              if (res.success) {
                this.$message.success(`处理成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      // 获取待办详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordItem.id, this.apis.recordInfo);
        this.ruleFormEvent = deepClone(res.data);
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("returnIndex");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-box {
    margin-bottom: 20px;
  }
</style>
