<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="生成收运统计表" :visible.sync="dialogVisible" width="600px" destroy-on-close>
      <div v-loading="loading">
        <header class="header">
          <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-suffix=":" label-width="200px">
            <el-form-item label="请选择生成统计的日期范围" prop="statisticalDate">
              <el-date-picker
                v-model="ruleForm.statisticalDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
        </header>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">返回</el-button>
          <el-button type="primary" @click="saveRecordThrottling">确认</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        loading: false,
        apis: {
          create: "/api/logisticsReport/generate",
        },
        saveRecordThrottling: () => {},
        ruleForm: {
          statisticalDate: "",
        },
        rules: {
          statisticalDate: [{ required: true, message: "请选择生成统计的日期范围", trigger: "change" }],
        },
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now();
          },
        },
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    methods: {
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            let params = {
              beginStatisticsDate: this.ruleForm.statisticalDate[0],
              endStatisticsDate: this.ruleForm.statisticalDate[1],
            };
            this.loading = true;
            try {
              let res = await createApiFun(params, this.apis.create);
              if (res.success) {
                this.$message.success("生成收运统计表成功");
                this.dialogVisible = false;
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 40px;
  }
  .header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .header-input {
      margin-right: 10px;
    }
  }
  .w250 {
    width: 250px;
  }
  ::v-deep .header .el-form-item {
    margin-bottom: 0;
  }
</style>
