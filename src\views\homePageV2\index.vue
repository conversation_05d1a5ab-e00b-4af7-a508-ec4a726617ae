<template>
  <v-scale-screen width="1920" height="1080" class="dp-first-content">
    <div class="bg-img" :style="{ 'background-image': 'url(' + iconData.bg + ')' }"></div>
    <Header></Header>
    <Statistic :numList="numList"></Statistic>
    <AbnormalInfoList :listData="summaryData.abnormalInfoList"></AbnormalInfoList>
    <AttendInfoList :listData="summaryData.attendInfoList"></AttendInfoList>
    <mapContainer class="mapContainer"></mapContainer>
    <CheckAbnormalNum :num="summaryData.checkVehicleList.length"></CheckAbnormalNum>
    <CheckVehicleList :listData="summaryData.checkVehicleList"></CheckVehicleList>
    <UnAttendVehicle :listData="summaryData.unAttendVehicle"></UnAttendVehicle>
  </v-scale-screen>
</template>

<script>
  import VScaleScreen from "v-scale-screen";
  import Header from "./components/Header.vue";
  import Statistic from "./components/Statistic.vue";
  import AbnormalInfoList from "./components/AbnormalInfoList.vue";
  import AttendInfoList from "./components/AttendInfoList.vue";
  import mapContainer from "./components/mapContainer";
  import CheckAbnormalNum from "./components/CheckAbnormalNum.vue";
  import UnAttendVehicle from "./components/UnAttendVehicle.vue";
  import CheckVehicleList from "./components/CheckVehicleList.vue";

  import { getInfoApiFunByParams } from "@/api/base.js";

  export default {
    components: {
      VScaleScreen,
      Header,
      Statistic,
      AbnormalInfoList,
      AttendInfoList,
      mapContainer,
      CheckAbnormalNum,
      UnAttendVehicle,
      CheckVehicleList,
    },
    data() {
      return {
        summaryData: {
          checkAbnormalNum: 0,
          unAttendVehicle: [],
          abnormalInfoList: [],
          attendInfoList: [],
          checkVehicleList: [],
        },
        apis: {
          getData: "/api/monitor/large-screen",
        },
        iconData: {
          bg: require("@/assets/images/<EMAIL>"),
        },
        numList: [
          {
            title: "车辆总数",
            icon: require("./images/ic_carNum.png"),
            num: 0,
            subtext: "(辆)",
          },
          {
            title: "今日收运车辆",
            icon: require("./images/ic_carNum.png"),
            num: 0,
            subtext: "(辆)",
          },
          {
            title: "异常上报数量",
            icon: require("./images/ic_warn.png"),
            num: 0,
          },
          {
            title: "异常驾驶状态",
            icon: require("./images/ic_warn.png"),
            num: 0,
          },
        ],
      };
    },
    created() {
      this.getData();
    },
    methods: {
      async getData() {
        try {
          let res = await getInfoApiFunByParams("", this.apis.getData);
          this.summaryData = res.data;
          this.numList[0].num = res.data.statistic.vehicleNum;
          this.numList[1].num = res.data.statistic.attendNum;
          this.numList[2].num = res.data.statistic.abnormalNum;
          this.numList[3].num = "-";
        } catch (error) {
          console.log(error);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .bg-img {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    position: absolute;
    z-index: 0;
  }
  .mapContainer {
    position: absolute;
    top: 128px;
    left: 540px;
    z-index: 1;
    width: 840px;
    height: 912px;
  }
</style>
