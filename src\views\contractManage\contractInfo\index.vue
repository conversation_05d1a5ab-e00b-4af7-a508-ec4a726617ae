<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage :topInfo="topInfo" @closeRecord="closeRecord">
      <record v-show="showRecord" @closeRecord="closeRecord" ref="record"></record>
      <div class="main-index" v-show="!showRecord">
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="keyword" placeholder="请输入客商名称/合同编号" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <!-- <el-button type="primary" size="small" icon="el-icon-folder-opened">同步</el-button> -->
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <el-col :span="12">
            <el-form-item label="合同状态">
              <el-select v-model="filterForm.status" placeholder="请选择合同状态" clearable filterable>
                <el-option v-for="item in statusOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同类型">
              <el-select v-model="filterForm.type" placeholder="请选择合同类型" clearable filterable>
                <el-option v-for="item in typeOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="生效日期范围">
              <el-date-picker
                v-model="filterForm.effectiveDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="失效日期范围">
              <el-date-picker
                v-model="filterForm.expiryDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" width="55" align="center"></el-table-column>
            <el-table-column prop="code" label="合同编号" align="center"> </el-table-column>
            <el-table-column prop="typeName" label="合同类型" align="center"> </el-table-column>
            <el-table-column prop="statusName" label="合同状态" align="center"></el-table-column>
            <el-table-column prop="effectiveDate" label="生效日期" align="center"></el-table-column>
            <el-table-column prop="expiryDate" label="失效日期" align="center"></el-table-column>
            <el-table-column prop="payDate" label="已缴费期间" align="center"> </el-table-column>
            <el-table-column prop="merchantFileName" label="客商名称" align="center"> </el-table-column>
            <el-table-column prop="dailyEmissions" label="日计量（KG）" align="center"> </el-table-column>
            <el-table-column prop="monthEmissions" label="月计量（KG）" align="center"> </el-table-column>
            <el-table-column prop="districtName" label="地区" align="center"> </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row)">详情</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, getInfoApiFun } from "@/api/base";
  import record from "./components/record.vue";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
    },
    data() {
      return {
        topInfo: {
          buttonName: "新增",
          subTitle: "列表",
          buttonShow: true,
          buttonPermission: "-1",
        },
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/contract/contractinfo/listPage",
          contractStatusList: "/api/dict/contractStatus/list",
          contractTypeList: "/api/dict/contractType/list",
        },
        showRecord: false,
        loading: false,
        statusOptions: [],
        typeOptions: [],
        keyword: "",
        showFilter: false,
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.getOptions();
      this.initData();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          getInfoApiFun("", this.apis.contractStatusList),
          getInfoApiFun("", this.apis.contractTypeList),
        ];
        let res = await Promise.all(promiseList);
        this.statusOptions = res[0].data;
        this.typeOptions = res[1].data;
      },
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          effectiveBeginDate: this.filterForm.effectiveDate ? this.filterForm.effectiveDate[0] : "",
          effectiveEndDate: this.filterForm.effectiveDate ? this.filterForm.effectiveDate[1] : "",
          expiryBeginDate: this.filterForm.expiryDate ? this.filterForm.expiryDate[0] : "",
          expiryEndDate: this.filterForm.expiryDate ? this.filterForm.expiryDate[1] : "",
          status: this.filterForm.status,
          type: this.filterForm.type,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.topInfo.buttonShow = false;
        this.topInfo.subTitle = "详情";
        this.$refs.record.getRecord(row.id);
      },
      closeRecord() {
        this.showRecord = false;
        this.topInfo.buttonShow = true;
        this.topInfo.subTitle = "列表";
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
