<template>
  <ul class="card-list">
    <li class="card-item" v-for="(item, index) in cardList" :key="index" @click="itemClick(index)">
      <div class="item-left">
        <div class="item-title">{{ item.title }}</div>
        <div class="item-bottom">
          <div class="item-count">{{ item.count }}</div>
          <div class="item-unit" v-if="item.unit">{{ item.unit }}</div>
        </div>
      </div>
      <div class="item-right" v-if="index < cardList.length - 1"></div>
    </li>
  </ul>
</template>

<script>
  export default {
    props: {
      cardList: {
        type: Array,
        default: () => [],
      },
    },
    methods: {
      itemClick(index) {
        this.$emit("itemClick", index);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .card-list {
    width: auto;
    padding: 24px 24px 28px 24px;
    background-color: #f7faf9;
    border-radius: 3px;
    display: flex;
    align-items: center;
  }
  .card-item {
    display: flex;
    align-items: center;
    cursor: pointer;
    .item-left {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .item-title {
      font-weight: 400;
      font-size: 14px;
      color: #5c6663;
      line-height: 16px;
      margin-bottom: 4px;
    }
    .item-bottom {
      display: flex;
      align-items: baseline;
      .item-count {
        font-weight: 500;
        font-size: 20px;
        color: #1d2925;
        line-height: 23px;
      }
      .item-unit {
        font-weight: 500;
        font-size: 14px;
        color: #1d2925;
        line-height: 16px;
      }
    }
    .item-right {
      width: 1px;
      min-height: 34px;
      background-color: #d4d7d6;
      margin: 0 28px;
    }
  }
</style>
