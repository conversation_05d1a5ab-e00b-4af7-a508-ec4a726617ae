<template>
  <div class="container">
    <div class="box" @click="emitter.emit('open-dialog', 'showDriverShip')">
      <div class="top">
        <el-progress
          :width="100"
          type="circle"
          :percentage="
            roundReserveDecimals(
              (statistic?.departureDriverInfo.departureDriverNum / statistic?.departureDriverInfo.total) * 100,
            ) || 0
          "
          :show-text="false"
          :stroke-width="10"
          define-back-color="#F7D84D"
          text-color="#00FFFC"
          color="#00FFFC"
        ></el-progress>
        <div class="top-right">
          <div class="title">司机总数</div>
          <div>
            <div class="count-box" v-for="(item, index) in statistic?.departureDriverInfo.totalList" :key="index">
              <div class="count-value">{{ item }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="inactive-box">
          <div class="inactive-square"></div>
          <div class="bottom-text">空闲司机{{ statistic?.departureDriverInfo.unDepartureDriverNum }}</div>
        </div>
        <div class="active-box">
          <div class="active-square"></div>
          <div class="bottom-text">出车司机{{ statistic?.departureDriverInfo.departureDriverNum }}</div>
        </div>
      </div>
    </div>
    <div class="box" @click="emitter.emit('open-dialog', 'showDriverShip')">
      <div class="top">
        <el-progress
          :width="100"
          type="circle"
          :percentage="
            roundReserveDecimals(
              (statistic?.departureEscortInfo.departureEscortNum / statistic?.departureEscortInfo.total) * 100,
            ) || 0
          "
          :show-text="false"
          :stroke-width="10"
          define-back-color="#F7D84D"
          text-color="#00FFFC"
          color="#00FFFC"
        ></el-progress>
        <div class="top-right">
          <div class="title">押运工总数</div>
          <div>
            <div class="count-box" v-for="(item, index) in statistic?.departureEscortInfo.totalList" :key="index">
              <div class="count-value">{{ item }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="inactive-box">
          <div class="inactive-square"></div>
          <div class="bottom-text">空闲押运{{ statistic?.departureEscortInfo.unDepartureScortNum }}</div>
        </div>
        <div class="active-box">
          <div class="active-square"></div>
          <div class="bottom-text">出车押运{{ statistic?.departureEscortInfo.departureEscortNum }}</div>
        </div>
      </div>
    </div>
    <div class="box" @click="emitter.emit('open-dialog', 'showVehicleDialog')">
      <div class="top">
        <el-progress
          :width="100"
          type="circle"
          :percentage="
            roundReserveDecimals(
              (statistic?.departureVehicleInfo.unOperationsNum / statistic?.departureVehicleInfo.departureVehicleNum) *
                100,
            ) || 0
          "
          :show-text="false"
          :stroke-width="10"
          define-back-color="#F7D84D"
          text-color="#00FFFC"
          color="#00FFFC"
        ></el-progress>
        <div class="top-right">
          <div class="title">今日出车数</div>
          <div>
            <div class="count-box" v-for="(item, index) in statistic?.departureVehicleInfo.totalList" :key="index">
              <div class="count-value">{{ item }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="inactive-box">
          <div class="inactive-square"></div>
          <div class="bottom-text">危运{{ statistic?.departureVehicleInfo.operationsNum }}</div>
        </div>
        <div class="active-box">
          <div class="active-square"></div>
          <div class="bottom-text">非危运{{ statistic?.departureVehicleInfo.unOperationsNum }}</div>
        </div>
      </div>
    </div>
    <div class="box" @click="emitter.emit('open-dialog', 'showVehicleDialog')">
      <div class="top">
        <el-progress
          :width="100"
          type="circle"
          :percentage="
            roundReserveDecimals(
              (statistic?.unDepartureVehicleInfo.unOperationsNum /
                statistic?.unDepartureVehicleInfo.unDepartureVehicleNum) *
                100,
            ) || 0
          "
          :show-text="false"
          :stroke-width="10"
          define-back-color="#F7D84D"
          text-color="#00FFFC"
          color="#00FFFC"
        ></el-progress>
        <div class="top-right">
          <div class="title">今日机动车辆</div>
          <div>
            <div class="count-box" v-for="(item, index) in statistic?.unDepartureVehicleInfo.totalList" :key="index">
              <div class="count-value">{{ item }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="inactive-box">
          <div class="inactive-square"></div>
          <div class="bottom-text">危运{{ statistic?.unDepartureVehicleInfo.operationsNum }}</div>
        </div>
        <div class="active-box">
          <div class="active-square"></div>
          <div class="bottom-text">非危运{{ statistic?.unDepartureVehicleInfo.unOperationsNum }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { roundReserveDecimals } from "@/utils";
  import emitter from "@/utils/mitt";
  export default {
    computed: {
      statistic() {
        return this.$store.state.formData.statistic;
      },
    },
    data() {
      return {
        apis: {
          person: "/api/monitor/departure/driver",
        },
        emitter,
        roundReserveDecimals,
      };
    },
    methods: {
      openDialog(value) {
        this.$emit("openDialog", value);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .container {
    margin: 16px 0;
    display: grid;
    grid-gap: 30px;
    grid-template-columns: repeat(2, 1fr);
    margin-bottom: 40px;
    pointer-events: auto;
    cursor: pointer;
  }
  .top {
    display: flex;
  }
  .top-right {
    display: flex;
    flex-direction: column;
    margin-left: 12px;
    .title {
      font-size: 14px;
      color: #fff;
      margin-bottom: 20px;
    }
    .count-box {
      display: inline-block;
      border: 1px solid #00fffc;
      margin-right: 4px;
      border-radius: 4px;
      .count-value {
        font-size: 40px;
        font-weight: bold;
        color: #fff;
        padding: 0 4px;
        line-height: 44px;
      }
    }
  }
  .bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
  }
  .inactive-box,
  .active-box {
    display: flex;
    align-items: center;
  }
  .inactive-square {
    width: 14px;
    height: 14px;
    background-color: #f7d84d;
  }
  .active-square {
    width: 14px;
    height: 14px;
    background-color: #56f1f1;
  }
  .bottom-text {
    font-size: 14px;
    color: #fff;
    margin-left: 4px;
  }
</style>
