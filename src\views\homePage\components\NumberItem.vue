<template>
  <div class="NumberItem-wrapper">
    <img class="urlImg" :src="url" alt="" />
    <div>
      <div class="text">{{ text }}</div>
      <div class="number">
        <span>{{ number }}</span>
        <span v-if="beside" class="beside">{{ beside }}</span>
        <span v-if="beside" class="besideValue">{{ besideValue }}</span>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    components: {},
    props: ["text", "number", "url", "beside", "besideValue"],
    data() {
      return {};
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {},
    watch: {},
  };
</script>
<style scoped lang="scss">
  .urlImg {
    width: 48px;
    height: 48px;
    margin-right: 12px;
  }
  .NumberItem-wrapper {
    display: flex;
    cursor: pointer;
  }
  .text {
    min-width: 180px;
    font-weight: 400;
    font-size: 16px;
    color: #5c6663;
    line-height: 22px;
  }
  .number {
    font-size: 20px;
    color: #1d2925;
    line-height: 28px;
    font-weight: 500;
  }
  .beside {
    color: #939c99;
    line-height: 17px;
    text-align: left;
    font-size: 12px;
    margin-left: 8px;
  }
  .besideValue {
    color: #f53f3f;
    font-size: 14px;
    line-height: 20px;
    font-weight: 400;
    text-align: left;
    margin-left: 8px;
  }
</style>
