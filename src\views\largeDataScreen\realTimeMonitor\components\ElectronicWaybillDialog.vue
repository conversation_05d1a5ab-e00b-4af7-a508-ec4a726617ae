<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      width="1400px"
      top="0"
      destroy-on-close
      @open="initData"
      :before-close="closeRecord"
    >
      <template #title>
        <header class="header-title">{{
          recordType ? (recordType == 1 ? "电子收运单详情" : "临时调整") : "今日电子收运单管理"
        }}</header>
      </template>
      <record
        v-if="recordType === 1"
        :recordId="recordId"
        :recordForm="recordForm"
        @closeRecord="closeRecord"
        @refreshList="getDataList"
      ></record>
      <temporary
        v-else-if="recordType === 2"
        :recordForm="recordForm"
        @closeRecord="closeRecord"
        @refreshList="getDataList"
      ></temporary>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-form ref="filterForm" inline :model="filterForm" label-suffix=":" label-width="140px" @submit.prevent>
              <el-form-item label="收运单编号">
                <el-input v-model="filterForm.waybillCode" placeholder="请输入收运单编号" clearable></el-input>
              </el-form-item>
              <el-form-item label="路线编号">
                <el-input v-model="filterForm.code" placeholder="请输入路线编号" clearable></el-input>
              </el-form-item>
              <el-form-item label="路线名称">
                <el-input v-model="filterForm.name" placeholder="请输入路线名称" clearable></el-input>
              </el-form-item>
              <el-form-item label="默认车辆车牌号">
                <el-input
                  v-model="filterForm.defaultVehiclePlateNumber"
                  placeholder="请输入车辆车牌号"
                  clearable
                ></el-input>
              </el-form-item>
            </el-form>
          </div>
          <div class="header-right">
            <el-button @click="resetFilter">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
          </div>
        </header>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column prop="waybillCode" label="收运单编号" align="center" min-width="100"></el-table-column>
            <el-table-column
              prop="effectiveDate"
              label="收运单生效日期"
              align="center"
              min-width="160"
            ></el-table-column>
            <el-table-column prop="isTemp" label="临时收运单" align="center" min-width="100">
              <template #default="{ row }">{{ IS_TEMPORARY[row.isTemp] }}</template>
            </el-table-column>
            <el-table-column prop="name" label="路线名称" align="center" min-width="120"></el-table-column>
            <el-table-column prop="code" label="路线编号" align="center" min-width="120"></el-table-column>
            <el-table-column prop="districtName" label="所属区域" align="center"></el-table-column>
            <el-table-column prop="type" label="路线属性" align="center" min-width="120">
              <template #default="{ row }">{{ ROUTE_PROPERTY[row.type] }}</template>
            </el-table-column>
            <el-table-column
              prop="defaultVehiclePlateNumber"
              label="默认车辆"
              align="center"
              min-width="120"
            ></el-table-column>
            <el-table-column prop="defaultDriverDossierName" label="默认司机" align="center"></el-table-column>
            <el-table-column
              prop="defaultDriverDossierPhone"
              label="司机联系方式"
              align="center"
              min-width="120"
            ></el-table-column>
            <el-table-column prop="supercargoDossierOneName" label="押运工" align="center"></el-table-column>
            <el-table-column
              prop="supercargoDossierOnePhone"
              label="押运工联系方式"
              align="center"
              min-width="120"
            ></el-table-column>
            <el-table-column prop="supercargoDossierTwoName" label="押运工2" align="center"></el-table-column>
            <el-table-column
              prop="supercargoDossierTwoPhone"
              label="押运工2联系方式"
              align="center"
              min-width="140"
            ></el-table-column>
            <el-table-column prop="versionNumber" label="路线版本号" align="center" min-width="100">
              <template #default="{ row }">v{{ row.versionNumber.toFixed(1) }}</template>
            </el-table-column>
            <el-table-column prop="waybillType" label="收运方式" align="center">
              <template #default="{ row }">{{ WAYBILL_TYPE[row.waybillType] }}</template>
            </el-table-column>
            <el-table-column prop="pointNumber" label="点位数量" align="center"></el-table-column>
            <el-table-column prop="completeCount" label="已收运点位数" align="center"></el-table-column>
            <el-table-column prop="updateFullname" label="最近修改人" align="center" min-width="140"></el-table-column>
            <el-table-column prop="updateTime" label="最近修改时间" align="center" min-width="160"></el-table-column>
            <el-table-column label="操作" align="center" fixed="right" min-width="200">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row, 1)">详情</el-link>
                <el-link class="mr-10" type="primary" @click="editRecord(row, 2)" v-if="row.isOperateTemp === 0"
                  >临时调整</el-link
                >
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import { IS_TEMPORARY, ROUTE_PROPERTY, ROUTE_STATUS, WAYBILL_TYPE } from "@/enums";
  import record from "@/views/collectTransportManage/electronicWaybill/components/record";
  import temporary from "@/views/collectTransportManage/electronicWaybill/components/temporary";
  export default {
    components: {
      record,
      temporary,
    },
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      completed: {
        type: [Number],
        default: 0,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        filterForm: {},
        IS_TEMPORARY,
        ROUTE_PROPERTY,
        ROUTE_STATUS,
        WAYBILL_TYPE,
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/monitor/waybill/listPage",
          export: "",
        },
        recordType: 0,
        recordId: "",
        loading: false,
        recordForm: {},
      };
    },
    methods: {
      initData() {
        this.recordType = 0;
        this.showRecord = false;
        this.filterForm = {};
        this.page = { pageNo: 1, pageSize: 10, total: 0 };
        this.getDataList();
      },
      async getDataList() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          issueStatus: 1,
          completed: this.completed == 0 ? "" : this.completed == 1 ? true : false,
          channelId: this.channelId,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas.map((item) => {
            return {
              ...item,
              defaultDriverDossierPhone: item.defaultDriverDossierPhone
                ? this.$sm2Decrypt(item.defaultDriverDossierPhone)
                : "",
              supercargoDossierOnePhone: item.supercargoDossierOnePhone
                ? this.$sm2Decrypt(item.supercargoDossierOnePhone)
                : "",
              supercargoDossierTwoPhone: item.supercargoDossierTwoPhone
                ? this.$sm2Decrypt(item.supercargoDossierTwoPhone)
                : "",
            };
          });
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.getDataList();
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.page.pageNo = 1;
        this.getDataList();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.getDataList();
      },
      // 编辑
      editRecord(row, type) {
        this.recordType = type;
        this.recordId = row.id;
        this.recordForm = row;
      },
      closeRecord(done) {
        if (this.recordType != 0) {
          this.recordType = 0;
          return;
        }
        done();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    .header-left {
      flex: 1;
    }
    .header-right {
      margin-top: 20px;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .mrl-10 {
    margin: 0 10px;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
