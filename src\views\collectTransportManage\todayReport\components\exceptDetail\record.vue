<template>
  <div class="micro-app-sctmp_base record-box">
    <div class="main-record" v-loading="loading">
      <div class="record-content">
        <div class="card-header">异常事件上报详情</div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px">
          <el-row>
            <el-col :md="24" :lg="12">
              <el-form-item label="上报时间" prop="reportingTime">
                <el-date-picker
                  disabled
                  v-model="ruleForm.reportingTime"
                  type="datetime"
                  placeholder="请选择上报时间"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <baseTitle title="基础信息"></baseTitle>
          <el-row>
            <el-col :md="24" :lg="12">
              <el-form-item label="上报人" prop="reportPerson">
                <el-input
                  disabled
                  v-model="ruleForm.reportPerson"
                  placeholder="请输入上报人"
                  clearable
                  maxlength="20"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :md="24" :lg="12">
              <el-form-item label="异常事件类型" prop="exceptionType">
                <el-select disabled v-model="ruleForm.exceptionType" placeholder="请选择处理情况" clearable filterable>
                  <el-option
                    v-for="(item, index) in ERROR_STATUS"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="12" :lg="12">
              <el-form-item label="是否可以继续收运" prop="continueCarrying">
                <el-select
                  disabled
                  v-model="ruleForm.continueCarrying"
                  placeholder="请选择处理情况"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in CONTINUECARRYING_STATUS"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="上报地点" prop="address">
                <div class="location-box">
                  <el-input
                    class="w-300"
                    disabled
                    v-model="ruleForm.address"
                    placeholder="请输入上报地点"
                    clearable
                  ></el-input>
                  <template v-if="ruleForm.address && ruleForm.longitude && ruleForm.latitude">
                    <span class="el-icon-map-location location-icon"></span>
                    <el-button type="text" @click="showDialog = true">查看定位</el-button>
                  </template>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <baseTitle title="异常描述"></baseTitle>
          <el-row>
            <el-col>
              <el-form-item label="异常描述内容" prop="exceptionContent">
                <el-input
                  disabled
                  v-model="ruleForm.exceptionContent"
                  placeholder="无"
                  clearable
                  type="textarea"
                  rows="6"
                  maxlength="500"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="record-footer">
        <el-button @click="$emit('closeRecord')">返回</el-button>
      </div>
    </div>
    <pointRecord :value.sync="showDialog" :formItem="ruleForm"></pointRecord>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";
  import { getInfoApiFun } from "@/api/base";
  import { CONTINUECARRYING_STATUS, ERROR_STATUS } from "@/enums";
  import pointRecord from "@/views/eventWatch/errorManager/components/pointRecord.vue";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      baseTitle,
      pointRecord,
    },
    data() {
      return {
        ruleForm: {
          id: "",
          exceptionType: "",
          continueCarrying: "",
          exceptionContent: "",
          reportingTime: "",
          reportPerson: "",
        },
        rules: {},
        apis: {
          info: "/api/abnormalreporting/get/",
        },
        CONTINUECARRYING_STATUS,
        ERROR_STATUS,
        loading: false,
        showDialog: false,
      };
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        if (this.recordId) {
          try {
            let res = await getInfoApiFun(this.recordId, this.apis.info);
            if (res.success) {
              this.ruleForm = res.data;
            }
          } catch (error) {
            console.log(error);
          }
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .record-box {
    height: 100%;
  }
  .main-record {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .w-400 {
    width: 400px;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .w-300 {
    width: 300px;
  }
  .location-box {
    display: flex;
    align-items: center;
    .location-icon {
      font-size: 20px;
      color: #909399;
      margin: 0 10px;
      margin-right: 4px;
    }
  }
  // 测试反馈 字数限制提示挡住内容
  ::v-deep .el-textarea__inner {
    padding-right: 40px !important;
  }
</style>
