<template>
  <div v-if="completed">
    <!-- <el-button @click="changeData">change</el-button> -->
    <v-scale-screen width="1920" height="1080" class="dp-first-content">
      <div class="bg-img" :style="{ 'background-image': 'url(' + iconData.bg + ')' }"></div>
      <Header
        :channelList="channelList"
        :channel="channel"
        :value.sync="channelId"
        :showChannel="showChannel"
        @toggleChannel="toggleChannel"
      ></Header>
      <!-- 各区收运量排名统计 -->
      <JobRankingStatistics
        :jobRankingData="jobRankingData"
        @changeAreaByFromJobRanking="changeAreaByFromJobRanking"
        ref="JobRankingStatisticsRef"
      ></JobRankingStatistics>
      <!-- 今日路线收运量排名统计 -->
      <AbnormalCondition
        :waybillCollectRankData="waybillCollectRankData"
        :waybillCollectRankDataChid="waybillCollectRankDataChid"
        :areaId="areaId"
        @drawLine="drawLine"
        ref="AbnormalConditionRef"
      ></AbnormalCondition>
      <!-- 中间地图 -->
      <mapChartsVue
        ref="mapChartsRef"
        :additiveLabel="additiveLabel"
        :areaData="areaData"
        :channelList="channelList"
        :channelId="channelId"
        @changeChannel="changeChannel"
        @changeMap="changeMap"
        @resetActiveFromMapCharts="resetActiveFromMapCharts"
        @changePointType="changePointType"
        @showPointTaskDialog="showPointTaskDialog"
      ></mapChartsVue>
      <!-- 右侧 -->
      <CollectionData
        :channelId="channelId"
        ref="CollectionDataRef"
        @changeType="changeType"
        @sendSocketMsg="sendSocketMsg"
      ></CollectionData>

      <!-- 收运任务待处理 -->
      <PointTaskDialog :value.sync="showPointDialog" :title="pointTaskTitle" :pointTaskData="pointTaskData">
      </PointTaskDialog>
    </v-scale-screen>
  </div>
</template>

<script>
  import {
    areaCollectRankDay,
    waybillCollectRankDay,
    areaTotal,
    waybillCollectRankDayChild,
  } from "@/api/OperationManagement";
  import VScaleScreen from "v-scale-screen";
  import Header from "./components/Header.vue";
  import JobRankingStatistics from "./components/JobRankingStatistics.vue";
  import AbnormalCondition from "./components/AbnormalCondition.vue";
  import mapChartsVue from "./components/mapCharts/mapCharts.vue";
  import CollectionData from "./components/collectionData/index.vue";
  import websocket from "@/utils/websocket";
  import PointTaskDialog from "./components/PointTaskDialog/index.vue";
  import { getLocalStorage } from "@/utils/storage";
  import { INTERVIEW_CHANNEL } from "@/utils/commonValue"; //访问渠道
  import { getInfoApiFun } from "@/api/base";
  export default {
    mixins: [websocket],

    name: "VehiclesData",

    components: {
      Header,
      VScaleScreen,
      mapChartsVue,
      JobRankingStatistics,
      AbnormalCondition,
      CollectionData,
      PointTaskDialog,
    },

    data() {
      return {
        jobRankingData: [],
        waybillCollectRankData: [],
        areaPointTotalData: [],
        rubbishWeightStatisticsData: [],
        baseInfoData: [],
        areaData: [],
        waybillCollectRankDataChid: [],
        iconData: {
          bg: require("@/assets/images/<EMAIL>"),
        },
        //地图的自定义对象，可以扩展地图
        additiveLabel: {
          show: true,
          padding: [10, 20],
          color: "#fff",
          borderColor: "#44FFF5",
          borderWidth: 1,
          borderRadius: 6,
          textStyle: {
            align: "left",
            lineHeight: 26,
          },
          width: 100,
          backgroundColor: {
            image: require("@/assets/images/labelBg.png"),
          },
          formatter: (params) => {
            // let arr = [params.name, "客商数量：" + 0, "点位数量：" + 0, "路线数量：" + 0];
            let arr = [params.name, "点位数量：" + 0, "路线数量：" + 0];
            return arr.join("\n");
          },
        },

        websocketUrl: `${process.env.VUE_APP_WSS_URL}/sctmpbase/api/screen/real-time?location=2`,
        // websocketUrl: `ws://192.168.89.249:9190/api/screen/real-time?location=2`,
        areaId: "",
        showPointDialog: false,
        pointTaskTitle: "",
        pointTaskData: {},
        apis: { channelList: "/api/base/dna/listByDna/" },
        channelId: "",
        channelList: [],
        channel: "",
        completed: false,
        showChannel: true,
      };
    },
    async created() {
      this.channel = getLocalStorage(INTERVIEW_CHANNEL);
      await this.getChannelList();
      this.initData();
      let token = await window.LOGAN.getToken();
      this.initWebSocket(this.websocketUrl + `&token=${token}`);
      let socketData = [
        {
          propName: "getTodayWaybill",
          params: [null, null, null, null, this.channelId],
        },
        {
          propName: "getTodayRubbishTotalInfo",
          params: [null, null, null, null, this.channelId],
        },
        {
          propName: "getPathWaybill",
          params: [null, null, this.channelId],
        },
      ];
      this.sendSocketMessage(socketData);
    },
    mounted() {},
    beforeDestroy() {
      this.setOncloseMessage();
    },
    methods: {
      // 获取渠道列表
      async getChannelList() {
        let res = await getInfoApiFun(this.$DNACONFIG.dna, this.apis.channelList);
        this.channelList = this.channelList.concat(res.data);
        if (this.channel) {
          this.channelId = this.channel;
        } else {
          this.channelId = this.channelList[0].id;
        }
      },
      changeChannel(flag) {
        this.showChannel = flag;
      },
      toggleChannel() {
        this.initData();
        let socketData = [
          {
            propName: "getTodayWaybill",
            params: [null, null, null, null, this.channelId],
          },
          {
            propName: "getTodayRubbishTotalInfo",
            params: [null, null, null, null, this.channelId],
          },
          {
            propName: "getPathWaybill",
            params: [null, null, this.channelId],
          },
        ];
        this.sendSocketMessage(socketData);
      },
      async initData() {
        try {
          this.loading = true;
          const res = await Promise.allSettled([
            areaCollectRankDay({ channelId: this.channelId }),
            waybillCollectRankDay({ channelId: this.channelId }),
            // rubbishWeightStatistics(),
            areaTotal({ channelId: this.channelId }),
          ]);

          if (res[0].status == "fulfilled") {
            // 各区收运量排名统计
            this.jobRankingData = res[0]?.value?.data || [];
          }

          if (res[1].status == "fulfilled") {
            // 今日路线收运量排名统计
            this.waybillCollectRankData = res[1]?.value?.data || [];
          }

          if (res[2].status == "fulfilled") {
            // 地图数据
            this.areaData = res[2]?.value?.data || [];
            this.additiveLabel.formatter = this.mapAddFormatter;
          }
          this.completed = true;
        } catch (error) {
          this.completed = true;
        }
      },

      mapAddFormatter(params) {
        //这里params拿到的是整个地图数据
        //this.areaData是图例数据
        //将当前的params对象对应的图例对象筛选出来（根据名字筛选）
        //然后拼接数据
        let info = this.areaData.filter((i) => i.areaName == params.name);
        let arr = [
          params.name,
          // "客商数量：" + info?.[0]?.merchantQuantity,
          "点位数量：" + (info.length > 0 ? info?.[0]?.pointQuantity || 0 : 0),
          "路线数量：" + (info.length > 0 ? info?.[0]?.pathQuantity || 0 : 0),
        ];
        return arr.join("\n");
      },

      // websocket接收消息
      async setOnmessageMessage(res) {
        if (res.data === "token过期") {
          this.setOncloseMessage();
          await this.$store.dispatch("refreshAuthToken");
          let token = await window.LOGAN.getToken();
          this.initWebSocket(this.websocketUrl + `&token=${token}`);
          let socketData = [
            {
              propName: "getTodayWaybill",
              params: [null, null, null, null, this.channelId],
            },
            {
              propName: "getTodayRubbishTotalInfo",
              params: [null, null, null, null, this.channelId],
            },
            {
              propName: "getPathWaybill",
              params: [null, null, this.channelId],
            },
          ];
          this.sendSocketMessage(socketData);
          return;
        }
        let rsp = "";
        try {
          rsp = JSON.parse(res.data);
        } catch (error) {
          rsp = "";
        }
        if (rsp) {
          for (let key in rsp) {
            // 今日各区收运量排名统计
            if (key == "getTodayDistrictRubbishTotalInfo") {
              this.jobRankingData = rsp[key] || [];
            }

            // 今日路线收运量排名统计
            if (key == "getTodayPathRubbishTotalInfo") {
              this.waybillCollectRankData = rsp[key] || [];
            }

            // 地图数据
            if (key == "getAreaInfo") {
              this.areaData = rsp[key] || [];
              this.additiveLabel.formatter = this.mapAddFormatter;
            }

            // 更新右侧数据
            if (["getTodayWaybill", "getTodayRubbishTotalInfo", "getPathWaybill"].includes(key)) {
              this.$refs["CollectionDataRef"].refreshData(key, rsp[key]);
            }
          }
        }
      },

      // 切换子级地图，获取子级运量数据
      changeMap(areaId) {
        this.areaId = areaId;

        if (!areaId) {
          this.waybillCollectRankDataChid = [];
          return;
        }

        waybillCollectRankDayChild({ districtId: areaId }).then((res) => {
          if (res.status == 200) {
            this.waybillCollectRankDataChid = res.data || [];
          }
        });
      },

      // 点击左边区域,切换地图和获取路线数据
      changeAreaByFromJobRanking(data) {
        this.$refs["mapChartsRef"].jumpChild({
          name: data.areaName,
        });

        // this.changeMap(data.districtId);
      },

      // 点击左边路线,通知地图连线 AbnormalConditionRef -> mapChart
      drawLine(data) {
        this.$refs["mapChartsRef"].drawLine(data);
      },

      // 地图通知高亮或者重置高亮 mapChart -> JobRankingStatisticsRef/AbnormalConditionRef
      resetActiveFromMapCharts({ type, id }) {
        switch (type) {
          case "area":
            this.$refs["JobRankingStatisticsRef"].setActiveId(id);
            break;
          case "line":
            this.$refs["AbnormalConditionRef"].setActiveId(id);
            break;
        }
      },

      // 点位类型切换 mapChart -> collectionData
      changePointType(data) {
        this.$refs["CollectionDataRef"].toggleTab(data, true);
      },

      // 点击右边类型切换,地图的类型和点位也要联动变化 collectionData -> mapChart
      changeType(data) {
        this.$refs["mapChartsRef"].handleMarkerType(data, true);
      },

      showPointTaskDialog(data) {
        this.pointTaskData = data;
        this.showPointDialog = true;
      },

      sendSocketMsg(data) {
        if (this.socket) {
          this.sendSocketMessage(data);
        }
      },

      changeData() {
        const data = {
          completePickupPointRate: 49.38,
          pickupPathCount: 226,
          completePickupCount: 9,
          noCompletePickupPointCount: 245,
          completePickupRate: 23.08,
          noCompletePickupCount: 30,
          completePickupPointCount: 239,
          pickupPointCount: 484,
        };
        this.$refs["CollectionDataRef"].refreshData("getPathWaybill", data);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .bg-img {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    position: absolute;
    z-index: 0;
  }
</style>
