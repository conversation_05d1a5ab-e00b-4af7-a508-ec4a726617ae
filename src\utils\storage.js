export function getLocalStorage(key) {
  //获取localStorage值
  try {
    return JSON.parse(window.localStorage.getItem(key));
  } catch (error) {
    return window.localStorage.getItem(key);
  }
}

export function setLocalStorage(key, value) {
  //设置localStorage值
  try {
    return window.localStorage.setItem(key, JSON.stringify(value));
  } catch {
    return window.localStorage.setItem(key, value);
  }
}

export function removeLocalStorage(key) {
  //移除localStorage值
  return window.localStorage.removeItem(key);
}

export function clearLocalStorage() {
  //清空localStorage值
  return window.localStorage.clear();
}

/**
 * sessionStorage 相关操作
 */

export function getSessionStorage(key) {
  //获取sessionStorage值
  try {
    return JSON.parse(window.sessionStorage.getItem(key));
  } catch (error) {
    return window.sessionStorage.getItem(key);
  }
}

export function setSessionStorage(key, value) {
  //设置sessionStorage值
  try {
    return window.sessionStorage.setItem(key, JSON.stringify(value));
  } catch {
    return window.sessionStorage.setItem(key, value);
  }
}

export function removeSessionStorage(key) {
  //移除sessionStorage值
  return window.sessionStorage.removeItem(key);
}

export function clearSessionStorage() {
  //清空sessionStorage值
  return window.sessionStorage.clear();
}
