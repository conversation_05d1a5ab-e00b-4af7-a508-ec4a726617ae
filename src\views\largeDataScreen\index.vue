<template>
  <div class="micro-app-sctmp_base screen-container full-height" v-loading="loading">
    <div class="main-index pt-48">
      <img class="logo" src="@/assets/images/logo.png" alt="" srcset="" />
      <div class="name mb-36">医疗废物智慧收运管理平台</div>
      <!-- <el-button type="primary" @click="routerGo('/sctmp-data/notBar/CommandData')">指挥调度大屏</el-button> -->
      <el-button type="primary" @click="routerGo('/sctmp_base/realTimeMonitor')">实时监控大屏</el-button>
      <!-- <el-button type="primary" @click="routerGo('/sctmp-data/notBar/OperationManagement')">运营管理大屏</el-button> -->
      <el-button type="primary" @click="routerGo('/sctmp_base/notBar/operationManagement')">运营管理大屏</el-button>
      <el-button type="primary" @click="routerGo('/sctmp_base/notBar/vehiclesData')">车辆管理大屏</el-button>
      <!-- <el-button type="primary" @click="routerGo('/sctmp_base/RTMLargeScreenV3')">处置中心大屏</el-button> -->
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        loading: false,
      };
    },
    methods: {
      routerGo(path) {
        const { href } = this.$router.resolve({
          path, // 这里写的是要跳转的路由地址
        });
        window.open(href, "_blank");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .screen-container {
    width: 100%;
    padding: 20px;
  }
  .main-index {
    height: 100%;
    background-color: #fff;
    text-align: center;
  }
  .logo {
    width: 80px;
    height: 80px;
  }

  .name {
    font-size: 20px;
    color: #122131;
    font-weight: bold;
  }

  .pt-48 {
    padding-top: 48px;
  }

  .mb-36 {
    margin-bottom: 36px;
  }
</style>
