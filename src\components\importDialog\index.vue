<template>
  <el-dialog :title="'批量导入' + title" :visible.sync="dialogVisible" width="60%" class="import-dialog">
    <div v-loading="loading">
      <div class="download-template"><el-button type="text" @click="downloadTemplate">下载模板</el-button></div>
      <el-form :model="importForm" :rules="importRules" ref="importForm" label-width="100px" label-suffix="：">
        <el-form-item label="所属部门" prop="deptId" v-if="hasDept">
          <el-cascader
            v-model="importForm.deptId"
            placeholder="请选择所属部门"
            filterable
            clearable
            :options="deptOptions"
            :props="deptProps"
            :show-all-levels="false"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="导入文件" prop="file">
          <el-upload
            drag
            action="custom"
            :http-request="httpRequest"
            :accept="suffix"
            :limit="limit"
            :file-list="importForm.file"
            :on-change="handleChange"
            :on-remove="handleRemove"
            :before-upload="beforeAvatarUpload"
            :on-exceed="handleExceed"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖拽至此区域或<em>选择文件</em></div>
            <div>支持格式：xls、xlsx</div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="importFileThrottling">确 定</el-button>
      </span>
    </div>
  </el-dialog>
</template>

<script>
  import { uploadFile, getFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import { BASE_API_URL } from "@/api/base";
  export default {
    name: "importDialog",
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      title: {
        type: String,
        default: "",
      },
      // 导入接口
      importApi: {
        type: String,
        default: "",
      },
      // 下载模板接口
      templateApi: {
        type: String,
        default: "",
      },
      // 导入类型
      importDialogType: {
        type: String,
        default: "",
      },
      // 下载模板是否为接口方式
      isTemplateApi: {
        type: Boolean,
        default: false,
      },
      // 是否需要选择部门
      hasDept: {
        type: Boolean,
        default: false,
      },
      // 部门列表
      deptOptions: {
        type: Array,
        default: () => [],
      },
      // 部门字段
      deptField: {
        type: String,
        default: "deptId",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.importForm.deptId = "";
          this.importForm.file = [];
          this.$emit("update:value", e);
        },
      },
    },
    created() {
      this.importFileThrottling = this.$throttling(this.importFile, 500);
    },
    data() {
      return {
        loading: false,
        importForm: {
          deptId: "",
          file: [],
        },
        importRules: {
          file: [{ required: true, message: "请导入文件", trigger: "change" }],
        },
        limit: 1,
        maxSize: 10,
        suffix: ".xlsx,.xls",
        deptProps: {
          checkStrictly: true,
          value: "id",
          label: "name",
          emitPath: false,
        },
        importFileThrottling: () => {},
      };
    },
    methods: {
      handleExceed() {
        this.$message.warning(`请最多上传 ${this.limit} 个文件。`);
      },
      handleRemove(file, fileList) {
        this.importForm.file = fileList;
      },
      handleChange(file, fileList) {
        this.importForm.file = fileList;
        this.$refs.importForm.validateField("file");
      },
      //上传之前
      beforeAvatarUpload(file) {
        //文件大小判断
        const isLimit = file.size / 1024 / 1024 < this.maxSize;
        if (!isLimit) {
          this.$message.warning(`上传文件大小不能超过 ${this.maxSize}MB!`);
          return false;
        }
        //后缀名判断
        if (this.suffix) {
          if (this.suffix === "*") return true;
          let suffix = file.name.slice(file.name.lastIndexOf("."));
          let suffixList = this.suffix.replace(/\s/g, "").split(",");
          console.log(suffixList);
          if (!suffixList.includes(suffix)) {
            this.$message.warning(`上传文件允许格式为${this.suffix}`);
            return false;
          }
        }
        return true;
      },
      // 下载模板
      async downloadTemplate() {
        if (this.templateApi) {
          if (this.isTemplateApi) {
            this.loading = true;
            try {
              let res = await getFile(BASE_API_URL + this.templateApi);
              if (res.success) {
                createDownloadEvent(`${this.title}导入模板.xlsx`, [res.data]);
                window.ELEMENT.Message.success("下载成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          } else {
            window.open(this.templateApi);
          }
        }
      },
      // 导入文件
      importFile() {
        this.$refs.importForm.validate(async (valid) => {
          if (valid) {
            if (!this.importApi) return;
            let formData = new FormData();
            if (this.hasDept && this.importForm.deptId) {
              formData.append(this.deptField, this.importForm.deptId);
            }
            formData.append("file", this.importForm.file[0].raw);
            this.loading = true;
            try {
              let res = await uploadFile(BASE_API_URL + this.importApi, formData);
              if (res.success) {
                window.ELEMENT.Message.success("导入成功");
                this.dialogVisible = false;
                this.$emit("importSuccess");
              }
              this.loading = false;
            } catch (error) {
              console.warn(error);
              this.loading = false;
            }
          }
        });
      },
      httpRequest() {},
    },
  };
</script>

<style lang="scss">
  .import-dialog {
    .el-upload {
      width: 100%;
    }
    .el-upload-dragger {
      width: 100%;
    }
  }
  .el-upload-style {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .dialog-footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .download-template {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
</style>
