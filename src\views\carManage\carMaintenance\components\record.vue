<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="车牌号" prop="plateNumber">
              <el-select
                v-model="ruleForm.plateNumber"
                placeholder="请选择车牌号"
                clearable
                filterable
                :disabled="recordId ? true : false"
                @change="changePlateNumber"
              >
                <el-option v-for="item in carOptions" :key="item.id" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="维保类型" prop="type">
              <el-select
                v-model="ruleForm.type"
                placeholder="请选择维保类型"
                clearable
                filterable
                multiple
                :disabled="recordId || !ruleForm.plateNumber ? true : false"
                class="w-300"
              >
                <el-option v-for="item in typeOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-card class="type-card" v-if="ruleForm.type.includes(0)">
            <div slot="header" class="card-header">常规保养记录</div>
            <el-col :md="24" :lg="12">
              <el-form-item label="经办人" :prop="`dataList.${0}.operatorName`" :rules="rules.operatorName">
                <el-input
                  v-model="ruleForm.dataList[0].operatorName"
                  placeholder="请输入经办人姓名"
                  clearable
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="保养单位"
                :prop="`dataList.${0}.organizationName`"
                :rules="[{ required: true, message: '请输入保养单位', trigger: 'blur' }]"
              >
                <el-input
                  v-model="ruleForm.dataList[0].organizationName"
                  placeholder="请输入保养单位"
                  clearable
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="最近保养日期"
                :prop="`dataList.${0}.recentMaintenanceTime`"
                :rules="[{ required: true, message: `请选择最近保养日期`, trigger: 'change' }]"
              >
                <el-date-picker
                  v-model="ruleForm.dataList[0].recentMaintenanceTime"
                  type="date"
                  placeholder="请选择最近保养日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="下次保养日期" prop="dataList[0].nextMaintenanceTime">
                <el-date-picker
                  v-model="ruleForm.dataList[0].nextMaintenanceTime"
                  type="date"
                  placeholder="请选择下次保养日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="行驶总里程(Km)"
                :prop="`dataList.${0}.driveMileage`"
                :rules="[{ required: true, message: `请输入行驶总里程`, trigger: 'change' }]"
              >
                <el-input-number
                  v-model="ruleForm.dataList[0].driveMileage"
                  :precision="2"
                  :min="0"
                  :max="99999999999999"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="下次保养里程(Km)"
                :prop="`dataList.${0}.upkeepMileage`"
                :rules="[{ required: true, message: `请输入下次保养里程`, trigger: 'change' }]"
              >
                <el-input-number
                  v-model="ruleForm.dataList[0].upkeepMileage"
                  :precision="2"
                  :min="0"
                  :max="99999999999999"
                ></el-input-number>
                <div class="date-tips"
                  >合理保养里程范围为当前行驶里程+5000~10000KM，之后将会根据此次输入的里程数在即将到期的时候发出提醒。</div
                >
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="保养费用(元)"
                :prop="`dataList.${0}.costs`"
                :rules="[{ required: true, message: `请输入保养费用`, trigger: 'change' }]"
              >
                <el-input-number
                  v-model="ruleForm.dataList[0].costs"
                  :precision="2"
                  :min="0"
                  :max="99999999999999"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item
                label="凭证"
                :prop="`dataList.${0}.vehicleFileList`"
                :rules="[{ required: true, message: `请上传保养凭证`, trigger: 'change' }]"
              >
                <FileUpload @uploadChange="uploadChange($event, 0)" :imageList="imageList[0]" :limit="5">
                  <template #tips><el-tag type="warning">请上传保养凭证</el-tag></template>
                </FileUpload>
              </el-form-item>
            </el-col>
          </el-card>
          <el-card class="type-card" v-if="ruleForm.type.includes(1)">
            <div slot="header" class="card-header">二级维护记录</div>
            <el-col :md="24" :lg="12">
              <el-form-item label="经办人" :prop="`dataList.${1}.operatorName`" :rules="rules.operatorName">
                <el-input
                  v-model="ruleForm.dataList[1].operatorName"
                  placeholder="请输入经办人姓名"
                  clearable
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="维护单位"
                :prop="`dataList.${1}.organizationName`"
                :rules="[{ required: true, message: '请输入维护单位', trigger: 'blur' }]"
              >
                <el-input
                  v-model="ruleForm.dataList[1].organizationName"
                  placeholder="请输入维护单位"
                  clearable
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="最近二级维护日期"
                :prop="`dataList.${1}.recentMaintenanceTime`"
                :rules="[{ required: true, message: `请选择最近二级维护日期`, trigger: 'change' }]"
              >
                <el-date-picker
                  v-model="ruleForm.dataList[1].recentMaintenanceTime"
                  type="date"
                  placeholder="请选择最近二级维护日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="下次二级维护日期"
                prop="dataList[1].nextMaintenanceTime"
                :rules="[{ required: true, message: `请选择下次二级维护日期`, trigger: 'change' }]"
              >
                <el-date-picker
                  v-model="ruleForm.dataList[1].nextMaintenanceTime"
                  type="date"
                  placeholder="请选择下次二级维护日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="二级维护费用(元)"
                :prop="`dataList.${1}.costs`"
                :rules="[{ required: true, message: `请输入二级维护费用`, trigger: 'change' }]"
              >
                <el-input-number
                  v-model="ruleForm.dataList[1].costs"
                  :precision="2"
                  :min="0"
                  :max="99999999999999"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item
                label="凭证"
                :prop="`dataList.${1}.vehicleFileList`"
                :rules="[{ required: true, message: `请上传二级维护凭证`, trigger: 'change' }]"
              >
                <FileUpload @uploadChange="uploadChange($event, 1)" :imageList="imageList[1]" :limit="5">
                  <template #tips><el-tag type="warning">请上传二级维护凭证</el-tag></template>
                </FileUpload>
              </el-form-item>
            </el-col>
          </el-card>
          <el-card class="type-card" v-if="ruleForm.type.includes(2)">
            <div slot="header" class="card-header">维修记录</div>
            <el-col :md="24" :lg="12">
              <el-form-item label="经办人" :prop="`dataList.${2}.operatorName`" :rules="rules.operatorName">
                <el-input
                  v-model="ruleForm.dataList[2].operatorName"
                  placeholder="请输入经办人姓名"
                  clearable
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="维修单位"
                :prop="`dataList.${2}.organizationName`"
                :rules="[{ required: true, message: '请输入维修单位', trigger: 'blur' }]"
              >
                <el-input
                  v-model="ruleForm.dataList[2].organizationName"
                  placeholder="请输入维修单位"
                  clearable
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="最近维修日期"
                :prop="`dataList.${2}.recentMaintenanceTime`"
                :rules="[{ required: true, message: `请选择最近维修日期`, trigger: 'change' }]"
              >
                <el-date-picker
                  v-model="ruleForm.dataList[2].recentMaintenanceTime"
                  type="date"
                  placeholder="请选择最近维修日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="维修费用(元)"
                :prop="`dataList.${2}.costs`"
                :rules="[{ required: true, message: `请输入维修费用`, trigger: 'change' }]"
              >
                <el-input-number
                  v-model="ruleForm.dataList[2].costs"
                  :precision="2"
                  :min="0"
                  :max="99999999999999"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item
                label="凭证"
                :prop="`dataList.${2}.vehicleFileList`"
                :rules="[{ required: true, message: `请上传维修凭证`, trigger: 'change' }]"
              >
                <FileUpload @uploadChange="uploadChange($event, 2)" :imageList="imageList[2]" :limit="5">
                  <template #tips><el-tag type="warning">请上传维修凭证</el-tag></template>
                </FileUpload>
              </el-form-item>
            </el-col>
          </el-card>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import FileUpload from "@/components/FileUpload";
  import { MAINTENANCE_TYPE } from "@/enums";
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  export default {
    components: { FileUpload },
    props: {
      recordId: {
        type: String,
        default: "",
      },
      positionOptions: {
        type: Array,
        default: () => [],
      },
      roleOptions: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        ruleForm: {
          plateNumber: "",
          type: [], //维保类型
          dataList: [
            {
              type: 0,
              operatorName: "", //经办人
              organizationName: "", //保养单位
              recentMaintenanceTime: "", //最近保养日期
              nextMaintenanceTime: "", //下次保养日期
              driveMileage: "", //行驶总里程
              upkeepMileage: "", //下次保养里程
              costs: "", //保养费用
              vehicleFileList: "", //保养凭证
            },
            {
              type: 1,
              operatorName: "", //经办人
              organizationName: "", //维护单位
              recentMaintenanceTime: "", //最近二级维护日期
              nextMaintenanceTime: "", //下次二级维护日期
              costs: "", //二级维护费用
              vehicleFileList: "", //二级维护凭证
            },
            {
              type: 2,
              operatorName: "", //经办人
              organizationName: "", //维修单位
              recentMaintenanceTime: "", //最近维修日期
              costs: "", //维修费用
              vehicleFileList: "", //维修凭证
            },
          ],
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号", trigger: "change" }],
          type: [{ required: true, message: "请选择维保类型", trigger: "change" }],
          operatorName: [{ required: true, message: "请输入经办人姓名", trigger: "blur" }],
        },
        options: [], //加油类型
        apis: {
          create: "/api/vehicleMaintenance/createBatch",
          update: "/api/vehicleMaintenance/update",
          info: "/api/vehicleMaintenance/get/",
          carList: "/api/vehicle/dossier/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [[], [], []],
        MAINTENANCE_TYPE,
        carOptions: [],
        // 0-常规保养 1-二级维护 2-维修
        maintenanceTypeOptions: [
          {
            id: 0,
            name: "常规保养",
          },
          {
            id: 1,
            name: "二级维护",
          },
          {
            id: 2,
            name: "维修",
          },
        ],
        operationalNature: "",
      };
    },
    computed: {
      typeOptions() {
        let options = [];
        if (this.operationalNature == 1) {
          options = this.maintenanceTypeOptions.filter((item) => item.id != 1);
        } else {
          options = this.maintenanceTypeOptions;
        }
        return options;
      },
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptions();
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({ statusList: [0, 1] }, this.apis.carList)];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
      },
      uploadChange(fileList, index) {
        if (fileList.length > 0) {
          this.ruleForm.dataList[index].vehicleFileList = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
          this.$refs.ruleForm.clearValidate(`dataList.${index}.vehicleFileList`);
        } else {
          this.ruleForm.dataList[index].vehicleFileList = [];
        }
      },
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          if (res.data.vehicleFileList) {
            this.imageList[res.data.type] = res.data.vehicleFileList.map((i) => ({
              url: i.url,
              file: i,
            }));
          }
          this.ruleForm.type = [res.data.type];
          this.ruleForm.plateNumber = res.data.plateNumber;
          this.ruleForm.id = res.data.id;
          let dataType = Object.assign({}, res.data);
          delete dataType.plateNumber;
          delete dataType.id;
          this.$set(this.ruleForm.dataList, res.data.type, dataType);
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            let params = {};
            if (this.recordId) {
              params = {
                id: this.ruleForm.id,
                plateNumber: this.ruleForm.plateNumber,
                ...this.ruleForm.dataList[this.ruleForm.type[0]],
              };
            } else {
              let dataList = this.ruleForm.dataList.filter((list) => this.ruleForm.type.includes(list.type));
              params = this.ruleForm;
              params.dataList = dataList;
            }
            try {
              let res = this.recordId
                ? await updateApiFun(params, this.apis.update)
                : await createApiFun(params, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}维保记录成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      // 修改车牌号时获取车辆营运性质
      changePlateNumber(value) {
        if (!value) {
          this.ruleForm.type = "";
          return;
        }
        let item = this.carOptions.filter((car) => car.name === value)[0];
        this.operationalNature = item.operationalNature;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 16px;
  }
  .type-card {
    margin-bottom: 20px;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
  }
  ::v-deep .el-transfer-panel__body {
    height: 400px;
  }
  .w-300 {
    width: 300px;
  }
  .w400 {
    width: 400px;
  }
  .w200 {
    width: 200px;
  }
</style>
