<template>
  <div :id="mapId" class="map-container"></div>
</template>
<script>
  import AMapLoader from "@amap/amap-jsapi-loader";
  export default {
    name: "map-view",
    props: {
      config: {
        type: Object,
        default: () => {
          return {
            viewMode: "3D",
            zoom: 14, //地图级别
            center: [113.2744826, 23.1820811], //地图中心点
          };
        },
      },
      mapId: {
        type: String,
        default: "mapContainer",
      },
    },
    mounted() {
      this.initAMap();
    },
    beforeDestroy() {
      this.map?.destroy();
      this.$emit("initMap", "");
    },
    data() {
      return {};
    },
    methods: {
      initAMap() {
        AMapLoader.load({
          key: "35d693226b39bc43fd408e883f75f127", // 申请好的Web端开发者Key，首次调用 load 时必填
          version: "2.0", // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
          plugins: [], // 需要使用的的插件列表，如比例尺'AMap.Scale'等
        })
          .then((AMap) => {
            this.map = new AMap.Map(this.mapId, this.config);
            this.map.on("complete", () => {
              this.$emit("initMap", this.map);
            });
          })
          .catch((e) => {
            console.log(e);
          });
      },
    },
  };
</script>
<style scoped>
  .map-container {
    width: 100%;
    height: 100%;
  }
</style>
