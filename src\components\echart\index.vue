<template>
  <div style="height: 100%; width: 100%" :ref="echart<PERSON>ey"></div>
</template>

<script>
  // 引入 echarts 核心模块，核心模块提供了 echarts 使用必须要的接口。
  import * as echarts from "echarts/core";
  // 引入柱状图图表，图表后缀都为 Chart
  import { BarChart, PieChart, GaugeChart, LineChart, RadarChart } from "echarts/charts";
  // 引入提示框，标题，直角坐标系组件，组件后缀都为 Component
  import {
    LegendComponent,
    TitleComponent,
    TooltipComponent,
    GridComponent,
    MarkLineComponent,
    VisualMapComponent,
  } from "echarts/components";
  // 引入 Canvas 渲染器，注意引入 CanvasRenderer 或者 SVGRenderer 是必须的一步
  import { CanvasRenderer } from "echarts/renderers";

  // 注册必须的组件
  echarts.use([
    LegendComponent,
    TitleComponent,
    TooltipComponent,
    Grid<PERSON>omponent,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>ponent,
    VisualMapComponent,
  ]);

  import barOption from "./barOption";
  import barOptionTurn from "./barOptionTurn";
  import pieOption from "./pieOption";
  import gaugeOption from "./gaugeOption";
  import barNegativeOption from "./barNegativeOption";
  import radarOptions from "./radarOption";
  import treeOptions from "./treeOptions";
  import { deepMerge } from "@/utils";

  export default {
    props: {
      chartType: {
        //图表类型，bar柱状图/条形图，barTurn柱状图横向，barNegative正负条形图，line：折线/面积图， pie：饼图，gauge 仪表盘,radar雷达图
        type: [String, Number],
        default: "bar",
      },

      chartData: {
        //图表数据
        type: Object,
        default() {
          return {
            xData: [],
            series: [],
          };
        },
      },

      isPieTooltip: {
        //是否显示饼图图例
        type: Boolean,
        default: false,
      },

      height: {
        type: String,
        default: null,
      },
    },

    watch: {
      chartData: {
        handler() {
          setTimeout(() => {
            this.initChart();
            this.resizeChart();
          }, 300);
        },
        immediate: true,
        deep: true,
      },
    },
    data() {
      return {
        echartKey: "echart-" + Date.now() + "" + Math.floor(Math.random() * 10000),
        echart: null,
        options: {}, //图表初始化配置
        barOption: {}, //柱形图-竖向
        barOptionTurn: {}, //柱形图-横向
        barNegativeOption: {}, //正负条形图
        pieOption: {}, //饼图
        gaugeOption: {}, //仪表盘
        radarOptions: {}, //雷达图
        treeOptions: {}, //树
      };
    },

    mounted() {
      this.init();
    },

    destroyed() {
      window.removeEventListener("resize", this.resizeChart);
    },

    methods: {
      /**
       * @Author: 赵锦俊
       * @Date: 2022-06-09 11:05:43
       * @return {*}
       * @description: 初始化
       */
      init() {
        this.barOption = barOption;
        this.barOptionTurn = barOptionTurn;
        this.pieOption = pieOption;
        this.gaugeOption = gaugeOption;
        this.barNegativeOption = barNegativeOption;
        this.radarOptions = radarOptions;
        this.treeOptions = treeOptions;
        window.addEventListener("resize", this.resizeChart);
      },

      initChart() {
        this.options = {};
        switch (this.chartType) {
          case "bar": //柱状图/条形图
            this.options = deepMerge(this.chartData, this.barOption);
            break;
          case "barTurn": //柱状图-横向
            this.options = deepMerge(this.chartData, this.barOptionTurn);
            break;
          case "barNegative": //正负条形图
            this.options = deepMerge(this.chartData, this.barNegativeOption);
            break;
          case "line": //折线/面积图
            this.options = deepMerge(this.chartData, this.barOption);
            break;
          case "pie": //饼图
            this.options = deepMerge(this.chartData, this.pieOption);
            break;
          case "gauge": //仪表盘
            this.options = deepMerge(this.chartData, this.gaugeOption);
            break;
          case "radar": //雷达图
            this.options = deepMerge(this.chartData, this.radarOptions);
            break;
          case "tree": //树
            this.options = deepMerge(this.chartData, this.treeOptions);
            break;
          default:
            this.options = this.chartData;
            console.log("其它图表");
            break;
        }

        if (this.echart) {
          this.echart.setOption(this.options, true);
        } else {
          this.echart = echarts.init(this.$refs[this.echartKey]);
          this.echart.setOption(this.options, true);
        }
        this.echart.on("click", (params) => {
          this.$emit("echartClick", params);
        });
      },

      resizeChart() {
        //图表调整自适应
        if (this.height) {
          if (this.echart) {
            this.echart.resize({ height: this.height });
          }
        } else {
          if (this.echart) {
            this.echart.resize();
          }
        }
      },
    },
  };
</script>

<style lang="scss" scoped></style>
