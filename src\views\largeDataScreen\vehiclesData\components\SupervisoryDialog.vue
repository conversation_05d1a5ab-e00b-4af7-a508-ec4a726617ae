<template>
  <div class="micro-app-sctmp_base" v-loading="loading">
    <el-dialog
      title="监督抽查记录"
      :visible.sync="dialogVisible"
      width="1400px"
      top="0"
      destroy-on-close
      :modal-append-to-body="false"
      :before-close="closeDialog"
      @open="initData"
    >
      <div class="full-height" v-loading="loading">
        <div class="main-index">
          <!-- <header class="header">
            <div class="header-left mr-10">
              <el-input class="w400" v-model="keyword" placeholder="请输入经办人名称" clearable></el-input>
            </div>
            <el-button @click="resetFilter">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
          </header> -->
          <main class="main">
            <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
              <el-table-column type="index" align="center"></el-table-column>
              <el-table-column prop="operatorName" label="经办人" align="center"></el-table-column>
              <el-table-column prop="conditionTime" label="监督抽查日期" align="center"></el-table-column>
              <el-table-column prop="status" label="抽查结果" align="center">
                <template #default="{ row }">{{ EVALUATE_STATUS[row.status] }}</template>
              </el-table-column>
            </el-table>
          </main>
          <Pagination :page="page" @pageChange="pageChange"></Pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import moment from "moment";
  import { EVALUATE_STATUS } from "@/enums";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      secureSize: {
        type: Number,
        default: 0,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        page: {
          pageNo: 1,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/vehicle/check/listPage",
        },
        loading: false,
        keyword: "",
        EVALUATE_STATUS,
      };
    },
    methods: {
      initData() {
        this.keyword = "";
        this.page.pageNo = 1;
        this.getDataList();
      },
      async getDataList() {
        this.loading = true;
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.secureSize,
          operatorName: this.keyword,
          conditionBeginTime: moment().format("YYYY-MM-DD"),
          conditionEndTime: moment().format("YYYY-MM-DD"),
          channelId: this.channelId,
        };
        try {
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = this.secureSize;
          }
          this.loading = false;
        } catch (_) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.getDataList();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.getDataList();
      },
      closeDialog() {
        this.dialogVisible = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w400 {
    width: 400px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
