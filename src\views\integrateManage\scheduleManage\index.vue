<template>
  <div class="calendar-container" v-loading="loading">
    <record v-if="showRecord" :recordItem="recordItem" :channelId="channelId" @closeRecord="closeRecord"></record>
    <div class="calendar-wrapper" v-show="!showRecord">
      <div class="page-header">
        <div class="page-header-date">
          <el-date-picker
            v-model="currentDate"
            type="month"
            placeholder="选择月"
            :clearable="false"
            @change="handleDateChange"
            size="large"
          ></el-date-picker>
        </div>
        <div class="left-channel" v-if="!channel">
          <el-radio-group v-model="channelId" @change="toggleChannel">
            <el-radio-button :label="label.id" v-for="label in channelList" :key="label.id">{{
              label.name
            }}</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="calendar-body">
        <div class="weekday-header">
          <div class="weekday-item" v-for="day in weekDays" :key="day">{{ day }}</div>
        </div>
        <div class="date-grid">
          <div
            v-for="(cell, index) in calendarList"
            :key="index"
            class="date-cell"
            :class="{
              holiday: cell.isHolidays || cell.isWeek,
              active: nowDate === cell.day,
              'other-month': cell.isOtherMonth,
            }"
            @click="viewRecord(cell)"
          >
            <div class="cell-content">
              <div class="date-number">
                <div class="flex-1">{{ cell.date }}</div>
                <div class="date-name" v-if="cell.name">{{ cell.name }}</div>
              </div>
              <div class="work-status">{{ cell.isHoliday ? "休息" : "工作" }}</div>
              <template v-if="dutyplanObj && dutyplanObj[cell.day]">
                <div class="work-status">收运单数量：{{ dutyplanObj[cell.day]?.pickupPathCount || 0 }}</div>
                <div class="user-box">
                  <div
                    >出勤司机 {{ dutyplanObj[cell.day]?.workDriverCount || 0 }}&nbsp;&nbsp;空闲司机
                    {{ dutyplanObj[cell.day]?.freeDriverCount || 0 }}</div
                  >
                  <div>换班司机 {{ dutyplanObj[cell.day]?.changeDriverCount || 0 }}</div>
                </div>
                <div class="user-box">
                  <div
                    >出勤押运工 {{ dutyplanObj[cell.day]?.workSupercargoCount || 0 }}&nbsp;&nbsp;空闲押运工
                    {{ dutyplanObj[cell.day]?.freeSupercargoCount || 0 }}</div
                  >
                  <div>换班押运工 {{ dutyplanObj[cell.day]?.changeSupercargoCount || 0 }}</div>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import moment from "moment";
  import { createApiFun, getInfoApiFun } from "@/api/base";
  import record from "./components/Record.vue";
  import { getLocalStorage } from "@/utils/storage";
  import { INTERVIEW_CHANNEL } from "@/utils/commonValue"; //访问渠道
  export default {
    name: "WorkCalendar",
    components: {
      record,
    },
    data() {
      return {
        currentDate: moment().format("YYYY-MM"),
        weekDays: ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"],
        apis: {
          holidayList: "/api/holiday/list",
          dutyplanList: "/api/dutyplan/list",
          channelList: "/api/base/dna/listByDna/",
        },
        nowYear: moment().format("YYYY"),
        nowDate: moment().format("YYYY-MM-DD"),
        showDialog: false,
        holidayObj: {},
        dialogForm: {},
        loading: false,
        calendarList: [],
        dutyplanObj: {},
        showRecord: false,
        recordItem: {},
        channelId: "",
        channelList: [],
        channel: "",
      };
    },
    async created() {
      this.channel = getLocalStorage(INTERVIEW_CHANNEL);
      if (!this.channel) {
        await this.getChannelList();
      }
      this.refreshCalendar();
    },
    methods: {
      // 获取渠道列表
      async getChannelList() {
        let res = await getInfoApiFun(this.$DNACONFIG.dna, this.apis.channelList);
        this.channelList = this.channelList.concat(res.data);
        this.channelId = this.channelList[0].id;
      },
      toggleChannel() {
        this.refreshCalendar();
      },
      // 刷新数据
      async refreshCalendar() {
        await this.getHolidayList();
        this.getDutyplanList();
        this.generateCalendar();
      },
      // 获取节假日列表
      async getHolidayList() {
        this.loading = true;
        try {
          let res = await createApiFun({}, this.apis.holidayList);
          res.data.forEach((item) => {
            this.holidayObj[item.day] = item;
          });
          this.loading = false;
        } catch (_) {
          this.loading = false;
        }
      },
      async getDutyplanList() {
        this.loading = true;
        try {
          let res = await createApiFun(
            {
              year: moment(this.currentDate).format("YYYY"),
              month: moment(this.currentDate).format("M"),
              channelId: this.channelId,
            },
            this.apis.dutyplanList,
          );
          this.dutyplanObj = {};
          if (res.data.length > 0) {
            res.data.forEach((item) => {
              this.dutyplanObj[item.dutyDate] = item;
            });
          }
          this.loading = false;
        } catch (_) {
          this.loading = false;
        }
      },
      // 生成月份日历列表
      generateCalendar() {
        const cells = [];
        const currentMoment = moment(this.currentDate);
        const firstDay = moment(currentMoment).startOf("month");
        const lastDay = moment(currentMoment).endOf("month");

        // 获取上个月需要显示的天数
        let firstDayWeekday = firstDay.day() || 7; // 转换周日的0为7
        const prevMonthDays = firstDayWeekday - 1;

        // 添加上个月的日期
        const prevMonth = moment(firstDay).subtract(1, "month");
        const prevMonthLastDay = prevMonth.daysInMonth();
        for (let i = prevMonthDays - 1; i >= 0; i--) {
          const date = prevMonthLastDay - i;
          const currentDate = moment(prevMonth).date(date);
          cells.push({
            date,
            day: currentDate.format("YYYY-MM-DD"),
            isOtherMonth: true,
          });
        }

        // 添加当前月的日期
        for (let date = 1; date <= lastDay.date(); date++) {
          const currentDate = moment(this.currentDate).date(date);
          const dateStr = currentDate.format("YYYY-MM-DD");
          const holiday = this.holidayObj[dateStr];

          cells.push({
            date,
            day: dateStr,
            isOtherMonth: false,
            isHoliday: holiday || this.isWeekend(currentDate),
            isSpecial: holiday?.isSpecial,
            holidayName: holiday?.name,
          });
        }

        // 添加下个月的日期
        const nextMonth = moment(lastDay).add(1, "month");
        const remainingCells = 42 - cells.length; // 保持6行
        for (let i = 1; i <= remainingCells; i++) {
          const currentDate = moment(nextMonth).date(i);
          cells.push({
            date: i,
            day: currentDate.format("YYYY-MM-DD"),
            isOtherMonth: true,
          });
        }
        let calendarList = [];
        cells.forEach((item) => {
          let obj = item;
          if (this.holidayObj[item.day]) {
            obj = Object.assign(obj, this.holidayObj[item.day]);
          }
          calendarList.push(obj);
        });
        this.calendarList = calendarList;
      },
      handleDateChange(date) {
        this.currentDate = date;
        this.generateCalendar();
        this.getDutyplanList();
      },
      isWeekend(date) {
        const day = moment(date).day();
        return day === 0 || day === 6;
      },
      // 查看详情
      viewRecord(cell) {
        this.recordItem = this.dutyplanObj[cell.day];
        if (!this.recordItem) return;
        this.showRecord = true;
      },
      // 关闭
      closeRecord() {
        this.showRecord = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .calendar-container {
    height: 100%;
    background: #f5f5f5;
    padding: 16px;
    overflow-y: auto;
  }
  .calendar-wrapper {
    background-color: #fff;
    padding-top: 10px;
  }
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding: 0 8px;
    .page-header-date {
      flex: 1;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .calendar-body {
    background: #fff;
    border-radius: 4px;
    padding: 16px;
  }
  .weekday-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    margin-bottom: 8px;
    .weekday-item {
      padding: 12px;
      text-align: center;
      font-weight: normal;
      color: #333;
      background: #f5f7fa;
      border-radius: 8px;
      font-size: 14px;
    }
  }
  .date-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
  }
  .date-cell {
    background: #fff;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    min-height: 100px;
    position: relative;
    transition: all 0.3s;
    cursor: pointer;
    overflow: hidden;
    &.holiday {
      background-color: #fff1f0;
      .date-number,
      .work-status,
      .user-box {
        color: #ff4d4f;
      }
    }
    &.active {
      background-color: var(--color-primary);
      .date-number,
      .work-status,
      .user-box {
        color: #fff;
      }
    }
    &.other-month {
      opacity: 0.5;
    }
    .cell-content {
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    .date-number {
      font-size: 16px;
      color: #333;
      font-weight: 500;
      display: flex;
      align-items: center;
    }
    .work-status {
      font-size: 14px;
      color: #666;
    }
  }
  .date-name {
    font-size: 14px;
  }
  .user-box {
    padding: 10px;
    font-size: 14px;
    color: #666;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    border-radius: 4px;
  }
</style>
