<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div>
        <div style="width: 100%; align-items: center; text-align: center; line-height: 36px; font-size: 16px">
          车辆日常安检异常
        </div>
        <el-form :model="ruleFormEvent" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
          <el-row>
            <el-col :span="12">
              <el-form-item label="上报时间" prop="createTime">
                <el-date-picker
                  disabled
                  v-model="ruleFormEvent.createTime"
                  type="datetime"
                  placeholder="请选择上报时间"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="截止日期" prop="completionTime">
                <el-date-picker
                  disabled
                  v-model="ruleFormEvent.completionTime"
                  type="datetime"
                  placeholder="请选择截止日期"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
        <baseTitle title="基础信息"></baseTitle>
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="自检时间" prop="inspectTime">
              <el-date-picker
                disabled
                v-model="ruleForm.inspectTime"
                type="datetime"
                placeholder="请选择自检时间"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="日常安检异常类型" prop="type">
              <el-select v-model="ruleForm.type" placeholder="请选择自检类型" filterable disabled class="w-300">
                <el-option
                  v-for="(item, index) in SELFTEST_STATUS"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="收运车辆" prop="plateNumber">
              <el-select v-model="ruleForm.plateNumber" placeholder="请选择收运车辆" clearable filterable disabled>
                <el-option v-for="item in carOptions" :key="item.id" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="安检司机" prop="driverDossierId">
              <el-select v-model="ruleForm.driverDossierId" placeholder="请选择安检司机" clearable filterable disabled>
                <el-option
                  v-for="item in driverOptions"
                  :key="item.id"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-card class="type-card" v-if="ruleForm.type || ruleForm.type === 0">
            <div slot="header" class="card-header">{{ SELFTEST_TITLE[ruleForm.type] }}</div>
            <el-col>
              <el-form-item :label="`${SELFTEST_STATUS[ruleForm.type]}明细`" prop="detailList" required>
                <el-table
                  :data="ruleForm.detailList"
                  :header-cell-style="{ background: '#F5F7F9' }"
                  style="width: 100%"
                  border
                >
                  <el-table-column type="index" align="center" label="序号" width="55"></el-table-column>
                  <el-table-column prop="configName" label="评价项目"></el-table-column>
                  <el-table-column prop="status" label="项目状态">
                    <template #default="{ row, $index }">
                      <el-form-item :prop="`detailList.${$index}.status`" :rules="detailRules.status">
                        <el-switch
                          disabled
                          v-model="row.status"
                          active-text="正常"
                          inactive-text="异常"
                          :active-value="0"
                          :inactive-value="1"
                        ></el-switch>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="检查状态" prop="status">
                <el-select v-model="ruleForm.status" placeholder="请选择检查状态" clearable filterable disabled>
                  <el-option
                    v-for="(item, index) in EVALUATE_STATUS"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="车辆照片" prop="fileList">
                <div v-if="imageList && imageList.length > 0">
                  <el-image
                    :key="index"
                    v-for="(imageObj, index) in imageList"
                    style="width: 100px; height: 100px; margin-right: 20px"
                    :src="imageObj.url"
                    :preview-src-list="[imageObj.url]"
                  >
                  </el-image>
                </div>
                <div v-else>暂无车辆照片</div>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <!-- TODO未对接字段 -->
              <el-form-item label="是否可以继续收运" prop="influencePickup">
                <el-select v-model="ruleForm.influencePickup" placeholder="未设置" clearable filterable disabled>
                  <el-option
                    v-for="item in INFLUENCEPICKUP_STATUS"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-card>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
    </div>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";

  import { SELFTEST_STATUS, SELFTEST_TITLE, EVALUATE_STATUS, INFLUENCEPICKUP_STATUS } from "@/enums";
  import { getInfoApiFun, createApiFun, updateApiFun, getListApiFun } from "@/api/base";
  import { deepClone } from "logan-common/utils";

  export default {
    props: {
      businessId: {
        type: String,
        default: "",
      },
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      baseTitle,
    },
    data() {
      return {
        INFLUENCEPICKUP_STATUS,
        ruleForm: {
          plateNumber: "", //车牌号
          driverDossierId: "", //司机
          type: "", //自检类型
          inspectTime: "", //车前检自检时间
          detailList: [],
          influencePickup: null,
        },
        ruleFormEvent: {
          createTime: null,
          completionTime: null,
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号", trigger: "change" }],
          driverDossierId: [{ required: true, message: "请选择驾驶司机", trigger: "change" }],
          type: [{ required: true, message: "请选择自检类型", trigger: "change" }],
          inspectTime: [{ required: true, message: "请选择自检时间", trigger: "change" }],
          detailList: [{ required: true, message: "请填写明细", trigger: "change" }],
        },
        detailRules: {
          status: [{ required: true, message: "请选择项目状态", trigger: "change" }],
        },
        apis: {
          create: "/api/vehicle/inspect/create",
          update: "/api/vehicle/inspect/update",
          info: "/api/vehicle/inspect/get/",
          recordInfo: "/api/task/get/",
          carList: "/api/vehicle/dossier/list",
          userList: "/api/baseuser/list",
          configList: "/api/vehicle/evaluationConfig/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [],
        SELFTEST_STATUS,
        SELFTEST_TITLE,
        EVALUATE_STATUS,
        carOptions: [],
        driverOptions: [],
        pickerOptions: {
          disabledDate: (time) => time.getTime() > new Date(new Date().setHours(23, 59, 59)),
        },
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    async mounted() {
      this.getOptions();
      await this.getBusiness();
      this.getRecord();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },
      // 获取业务详情
      async getBusiness() {
        let res = await getInfoApiFun(this.businessId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          if (this.ruleForm.fileList?.length > 0) {
            this.imageList = this.ruleForm.fileList.map((list) => {
              return {
                url: list.url,
                file: list,
              };
            });
          }
        }
      },
      // 获取告警详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.recordInfo);
        if (res.success) {
          this.ruleFormEvent = deepClone(res.data);
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = this.businessId
                ? await updateApiFun(this.ruleForm, this.apis.update)
                : await createApiFun(this.ruleForm, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.businessId ? "修改" : "新增"}成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      uploadChangeFileList(fileList) {
        if (fileList.length > 0) {
          this.ruleForm.fileList = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
        } else {
          this.ruleForm.fileList = [];
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 16px;
  }
  .type-card {
    margin-bottom: 20px;
  }
  .w-300 {
    width: 300px;
  }
</style>
