<template>
  <div class="detail-container">
    <waybillRecord
      v-if="showRecord"
      :recordId="recordId"
      :recordForm="recordForm"
      @closeRecord="closeRecord"
    ></waybillRecord>
    <div class="main-record micro-app-sctmp_base" v-else v-loading="loading">
      <div class="record-content">
        <div class="card-header">{{ detailType === 1 ? "月度绩效明细" : "基础绩效明细" }}</div>
        <baseTitle title="绩效明细"></baseTitle>
        <el-form :model="ruleForm" ref="ruleForm" label-width="240px" label-suffix="：">
          <el-row>
            <template v-if="detailType === 1">
              <el-col>
                <el-form-item label="绩效方案名称">
                  <el-input :value="PERFORMANCE_RULE[ruleIndex]" placeholder="" reanonly></el-input>
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item label="绩效计算公式">
                  <el-input
                    :value="`（小型床位总收运点数 × 当月小型床位各点提成标准 + ${ruleObj[ruleIndex]}绩效工资标准 × ${ruleObj[ruleIndex]}线路系数）x 考核系数 - 扣款金额`"
                    placeholder=""
                    reanonly
                  ></el-input>
                </el-form-item>
              </el-col>
            </template>
            <el-col>
              <el-form-item label="点位收运绩效明细"></el-form-item>
              <el-table
                class="table-data"
                :data="ruleForm.detailInfo"
                :header-cell-style="{ background: '#F5F7F9' }"
                border
              >
                <el-table-column prop="date" label="日期" align="center"></el-table-column>
                <el-table-column label="小型床位点位收运情况" align="center">
                  <el-table-column prop="bucketCount" label="桶装点位收运数量" align="center"></el-table-column>
                  <el-table-column prop="bagCount" label="袋装点位收运数量" align="center"></el-table-column>
                </el-table-column>
                <el-table-column label="诊所点位收运情况" align="center">
                  <el-table-column prop="dayPoint" label="诊所点位收运数量" align="center"></el-table-column>
                  <el-table-column
                    prop="monthTotalPoint"
                    label="当月累计诊所点位收运数量"
                    align="center"
                  ></el-table-column>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="12">
              <el-form-item label="桶装点位（小型床位）绩效总计（元）">
                <el-input
                  :value="`${ruleForm.bucketCount} x ${ruleForm.bucketUnitPrice || 0} = ${ruleForm.bucketPrice}（元）`"
                  placeholder=""
                  reanonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="袋装点位（小型床位）绩效总计（元）">
                <el-input
                  :value="`${ruleForm.bagCount} x ${ruleForm.bagUnitPrice || 0} = ${ruleForm.bagPrice}（元）`"
                  placeholder=""
                  reanonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="诊所点位总收运数（个）">
                <el-input :value="ruleForm.totalPoint" placeholder="" reanonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="诊所线路系数" v-if="ruleIndex != 7">
                <el-input :value="ruleForm.factor" placeholder="" reanonly></el-input>
              </el-form-item>
            </el-col>
            <template v-if="detailType === 1">
              <el-col>
                <el-form-item label="考核系数">
                  <el-input :value="ruleForm.ratio" placeholder="" reanonly></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="扣款金额">
                  <el-input :value="deductSalary" placeholder="" reanonly></el-input>
                </el-form-item>
              </el-col>
            </template>
            <el-col v-if="detailType === 1">
              <el-form-item label="绩效工资（元）">
                <el-input
                  :value="`（${ruleForm.pointPrice} + ${ruleForm.clinicGroupPrice}）x ${ruleForm.ratio} - ${deductSalary} = ${ruleForm.payIncentives}`"
                  placeholder=""
                  reanonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col v-else>
              <el-form-item label="基础绩效（元）">
                <el-input
                  :value="`${ruleForm.pointPrice} + ${ruleForm.clinicGroupPrice} = ${ruleForm.basicPerformance}`"
                  placeholder=""
                  reanonly
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <baseTitle title="收运明细"></baseTitle>
        <waybillTable :waybillInfo="ruleForm.waybillInfo" @editRecord="editRecord"></waybillTable>
      </div>
      <div class="record-footer">
        <el-button @click="closeDetail">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";
  import { PERFORMANCE_RULE } from "@/enums";
  import waybillRecord from "@/views/collectTransportManage/electronicWaybill/components/record.vue";
  import waybillTable from "./waybillTable.vue";
  import { getInfoApiFun } from "@/api/base";
  export default {
    components: {
      baseTitle,
      waybillRecord,
      waybillTable,
    },
    props: {
      detailId: {
        type: String,
        default: "",
      },
      ruleIndex: {
        type: Number,
        default: 0,
      },
      deductSalary: {
        type: Number,
        default: 0,
      },
      detailType: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        PERFORMANCE_RULE,
        loading: false,
        ruleForm: {},
        bagData: [],
        showRecord: false,
        recordId: "",
        recordForm: {},
        apis: {
          info: "/api/access/record/detail/",
        },
        ruleObj: {
          5: "诊所组",
          6: "诊所组（南沙）",
          7: "特殊诊所组",
        },
      };
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取详情
      async getRecord() {
        try {
          let res = await getInfoApiFun(this.detailId, this.apis.info);
          if (res.success) {
            this.ruleForm = res.data;
            this.bagData = [this.ruleForm.bagTotal];
          }
        } catch (error) {
          console.log(error);
        }
      },
      // 返回
      closeDetail() {
        this.$emit("closeDetail");
      },
      editRecord(row) {
        this.recordId = row.id;
        this.recordForm = row;
        this.showRecord = true;
      },
      closeRecord() {
        this.showRecord = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .detail-container {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-data {
    margin-bottom: 20px;
  }
</style>
