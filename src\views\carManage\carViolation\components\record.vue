<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="车牌号" prop="plateNumber">
              <el-select v-model="ruleForm.plateNumber" placeholder="请选择车牌号" clearable filterable>
                <el-option v-for="item in carOptions" :key="item.id" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="司机" prop="driverId">
              <el-select v-model="ruleForm.driverId" placeholder="请选择司机" clearable filterable>
                <el-option
                  v-for="item in userOptions"
                  :key="item.id"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="发生时间" prop="occurrenceTime">
              <el-date-picker
                v-model="ruleForm.occurrenceTime"
                type="datetime"
                placeholder="请选择发生时间"
                align="right"
                value-format="yyyy-MM-dd HH:mm"
                format="yyyy-MM-dd HH:mm"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="发生地点" prop="location">
              <el-input
                v-model="ruleForm.location"
                placeholder="请输入发生地点"
                clearable
                :maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="罚款金额(元)" prop="penaltyAmount">
              <el-input-number
                v-model="ruleForm.penaltyAmount"
                :precision="2"
                :min="0"
                :max="99999999999999"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="扣分" prop="deductionPoint">
              <el-input-number
                v-model="ruleForm.deductionPoint"
                :step="1"
                step-strictly
                :min="0"
                :max="99999999999999"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆损坏程度" prop="degree">
              <el-select v-model="ruleForm.degree" placeholder="请选择车辆损坏程度" clearable filterable>
                <el-option v-for="(item, index) in DEGREE_LIST" :key="index" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="处理状态" prop="status">
              <el-select v-model="ruleForm.status" placeholder="请选择处理状态" clearable filterable>
                <el-option v-for="(item, index) in STATUS_LIST" :key="index" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="24">
            <el-form-item label="事故描述" prop="description">
              <el-input
                type="textarea"
                placeholder="请输入事故描述"
                v-model="ruleForm.description"
                :maxlength="200"
                show-word-limit
                :autosize="{ minRows: 4 }"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8">
            <el-form-item label="凭证" prop="violationPhoto">
              <FileUpload
                @uploadChange="handleUploadChange($event, 'violationPhoto')"
                :imageList="imageList['violationPhoto']"
              >
                <template #tips><el-tag type="warning">请上传违章事故现场照片</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8">
            <el-form-item prop="violationPenaltyCertificate">
              <FileUpload
                @uploadChange="handleUploadChange($event, 'violationPenaltyCertificate')"
                :imageList="imageList['violationPenaltyCertificate']"
              >
                <template #tips><el-tag type="warning">请上传违章事故处罚单凭证</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8">
            <el-form-item prop="violationPayCertificate">
              <FileUpload
                @uploadChange="handleUploadChange($event, 'violationPayCertificate')"
                :imageList="imageList['violationPayCertificate']"
              >
                <template #tips><el-tag type="warning">请上传违章事故处罚缴费凭证</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import FileUpload from "@/components/FileUpload";
  import { STATUS_LIST, DEGREE_LIST } from "@/enums";
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  export default {
    components: { FileUpload },
    props: {
      recordId: {
        type: String,
        default: "",
      },
      positionOptions: {
        type: Array,
        default: () => [],
      },
      roleOptions: {
        type: Array,
        default: () => [],
      },
      userOptions: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        STATUS_LIST,
        DEGREE_LIST,
        ruleForm: {
          plateNumber: null,
          driverName: null,
          driverId: "",
          occurrenceTime: null,
          location: null,
          penaltyAmount: null,
          deductionPoint: null,
          degree: null,
          status: null,
          description: null,
          violationPhoto: null,
          violationPenaltyCertificate: null,
          violationPayCertificate: null,
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号", trigger: "change" }],
          driverId: [{ required: true, message: "请选择司机", trigger: "change" }],
          occurrenceTime: [{ required: true, message: "请选择发生时间", trigger: "change" }],
          location: [{ required: true, message: "请输入发生地点", trigger: "change" }],
          penaltyAmount: [{ required: true, message: "请选择罚款金额", trigger: "change" }],
          deductionPoint: [{ required: true, message: "请输入扣分数", trigger: "change" }],
          degree: [{ required: true, message: "请选择车辆损坏程度", trigger: "change" }],
          status: [{ required: true, message: "请选择处理状态", trigger: "change" }],
          description: [{ required: true, message: "请输入事故描述", trigger: "change" }],
          violationPhoto: [{ required: true, message: "请上传违章事故现场照片", trigger: "change" }],
          violationPenaltyCertificate: [{ required: true, message: "请上传违章事故处罚单凭证", trigger: "change" }],
          violationPayCertificate: [{ required: true, message: "请上传违章事故处罚缴费凭证", trigger: "change" }],
        },
        options: [], //加油类型
        apis: {
          vehicleFuelList: "/api/dict/refuelType/list",
          create: "/api/vehicle/illegal/create",
          update: "/api/vehicle/illegal/update",
          inofo: "/api/vehicle/illegal/get/",
          carList: "/api/vehicle/dossier/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: {
          violationPhoto: [],
          violationPenaltyCertificate: [],
          violationPayCertificate: [],
        },
        carOptions: [],
        pickerOptions: {
          disabledDate: (time) => time.getTime() > new Date(new Date().setHours(23, 59, 59)),
        },
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptions();
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          getInfoApiFun("", this.apis.vehicleFuelList),
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
        ];
        let res = await Promise.all(promiseList);
        this.options = res[0].data;
        this.carOptions = res[1].data;
      },
      handleUploadChange(fileList, field) {
        if (fileList.length && fileList[0].file) {
          this.ruleForm[field] = { name: fileList[0].name, size: fileList[0].size, ...fileList[0].file };
          this.$refs.ruleForm.clearValidate(field);
        } else {
          this.ruleForm[field] = null;
        }
      },
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.inofo);
        if (res.success) {
          this.ruleForm = res.data;
          this.handleBackImage();
        }
      },
      handleBackImage() {
        for (const key in this.imageList) {
          if (Object.hasOwnProperty.call(this.imageList, key)) {
            const item = this.ruleForm[key];
            if (this.ruleForm[key]) {
              this.imageList[key] = [
                {
                  url: item.url,
                  file: item,
                },
              ];
            }
          }
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = this.recordId
                ? await updateApiFun(this.ruleForm, this.apis.update)
                : await createApiFun(this.ruleForm, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}违章事故记录成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
  }
  ::v-deep .el-transfer-panel__body {
    height: 400px;
  }
  .w400 {
    width: 400px;
  }

  .w200 {
    width: 200px;
  }
</style>
