<template>
  <div class="detail-container">
    <waybillRecord
      v-if="showRecord"
      :recordId="recordId"
      :recordForm="recordForm"
      @closeRecord="closeRecord"
    ></waybillRecord>
    <div class="main-record micro-app-sctmp_base" v-else v-loading="loading">
      <div class="record-content">
        <div class="card-header">{{ detailType === 1 ? "月度绩效明细" : "基础绩效明细" }}</div>
        <baseTitle title="绩效明细"></baseTitle>
        <el-form :model="ruleForm" ref="ruleForm" label-width="240px" label-suffix="：">
          <el-row>
            <el-col v-if="detailType === 1">
              <el-form-item label="绩效方案名称">
                <el-input value="顶班司机绩效工资" placeholder="" reanonly></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="点位收运绩效明细"></el-form-item>
              <el-table
                class="table-data"
                :data="ruleForm.detailInfo"
                :header-cell-style="{ background: '#F5F7F9' }"
                border
              >
                <el-table-column prop="date" label="日期" align="center"></el-table-column>
                <el-table-column label="按重量计费点位收运情况" align="center">
                  <el-table-column prop="bucketCount" label="桶装点位收运数量" align="center"></el-table-column>
                  <el-table-column
                    prop="bucketRubbishTotal"
                    label="桶装垃圾收运重量（kg）"
                    align="center"
                  ></el-table-column>
                  <el-table-column prop="bagCount" label="袋装点位收运数量" align="center"></el-table-column>
                  <el-table-column
                    prop="bagRubbishTotal"
                    label="袋装垃圾收运重量（kg）"
                    align="center"
                  ></el-table-column>
                </el-table-column>
                <el-table-column label="小型床位点位收运情况" align="center">
                  <el-table-column
                    prop="collectionBucketCount"
                    label="桶装点位收运数量"
                    align="center"
                  ></el-table-column>
                  <el-table-column prop="collectionBagCount" label="袋装点位收运数量" align="center"></el-table-column>
                </el-table-column>
                <el-table-column label="诊所点位收运情况" align="center">
                  <el-table-column
                    prop="clinicGroupPointCount"
                    label="诊所点位收运数量"
                    align="center"
                  ></el-table-column>
                  <el-table-column
                    prop="nsClinicGroupPointCount"
                    label="南沙点位收运数量"
                    align="center"
                  ></el-table-column>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col>
              <el-form-item label="桶装垃圾收运绩效总计（元）">
                <el-input :value="ruleForm.bucketTotalPrice" placeholder="" reanonly></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="袋装垃圾收运绩效总计（元）">
                <el-table :data="bagData" :header-cell-style="{ background: '#F5F7F9' }" border>
                  <el-table-column prop="date" label="月份" align="center"></el-table-column>
                  <el-table-column prop="bagPrice" label="袋装垃圾收运单价（元/吨）" align="center"></el-table-column>
                  <el-table-column
                    prop="bagRubbishTotal"
                    label="当月袋装垃圾收运总量（kg）"
                    align="center"
                  ></el-table-column>
                  <el-table-column prop="factor" label="当月里程系数" align="center"></el-table-column>
                  <el-table-column prop="bagTotalPrice" label="本月袋装垃圾绩效（元）" align="center"></el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="桶装点位（小型床位）绩效总计（元）">
                <el-input
                  :value="`${ruleForm.bucketTotalCount} x ${ruleForm.bucketPrice} = ${ruleForm.bucketTotalPrice}（元）`"
                  placeholder=""
                  reanonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="袋装点位（小型床位）绩效总计（元）">
                <el-input
                  :value="`${ruleForm.bagTotalCount} x ${ruleForm.bagPrice} = ${ruleForm.bagTotalPrice}（元）`"
                  placeholder=""
                  reanonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="诊所点位总收运数（个）">
                <el-input :value="ruleForm.totalPoint" placeholder="" reanonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="诊所线路系数">
                <el-input :value="ruleForm.factor" placeholder="" reanonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="南沙诊所点位总收运数（个）">
                <el-input :value="ruleForm.nsTotalPoint" placeholder="" reanonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="南沙诊所线路系数">
                <el-input :value="ruleForm.nsFactor" placeholder="" reanonly></el-input>
              </el-form-item>
            </el-col>
            <template v-if="detailType === 1">
              <el-col>
                <el-form-item label="考核系数">
                  <el-input :value="ruleForm.ratio" placeholder="" reanonly></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="扣款金额">
                  <el-input :value="deductSalary" placeholder="" reanonly></el-input>
                </el-form-item>
              </el-col>
            </template>
            <el-col v-if="detailType === 1">
              <el-form-item label="绩效工资（元）">
                <el-input
                  :value="`${ruleForm.totalPrice} x ${ruleForm.ratio} - ${deductSalary} = ${ruleForm.payIncentives}`"
                  placeholder=""
                  reanonly
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col v-else>
              <el-form-item label="基础绩效（元）">
                <el-input :value="ruleForm.basicPerformance" placeholder="" reanonly></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <baseTitle title="收运明细"></baseTitle>
        <waybillTable :waybillInfo="ruleForm.waybillInfo" @editRecord="editRecord"></waybillTable>
      </div>
      <div class="record-footer">
        <el-button @click="closeDetail">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";
  import waybillRecord from "@/views/collectTransportManage/electronicWaybill/components/record.vue";
  import waybillTable from "./waybillTable.vue";
  import { getInfoApiFun } from "@/api/base";
  export default {
    components: {
      baseTitle,
      waybillRecord,
      waybillTable,
    },
    props: {
      detailId: {
        type: String,
        default: "",
      },
      ruleIndex: {
        type: Number,
        default: 0,
      },
      deductSalary: {
        type: Number,
        default: 0,
      },
      detailType: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        loading: false,
        ruleForm: {},
        showRecord: false,
        recordId: "",
        recordForm: {},
        apis: {
          info: "/api/access/record/detail/",
        },
      };
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取详情
      async getRecord() {
        try {
          let res = await getInfoApiFun(this.detailId, this.apis.info);
          if (res.success) {
            this.ruleForm = res.data;
            this.bagData = [
              {
                date: res.data.date,
                bagPrice: res.data.bagPrice,
                bagRubbishTotal: res.data.bagRubbishTotal,
                bagTotalPrice: res.data.bagTotalPrice,
                factor: res.data.factor,
              },
            ];
          }
        } catch (error) {
          console.log(error);
        }
      },
      // 返回
      closeDetail() {
        this.$emit("closeDetail");
      },
      editRecord(row) {
        this.recordId = row.id;
        this.recordForm = row;
        this.showRecord = true;
      },
      closeRecord() {
        this.showRecord = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .detail-container {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-data {
    margin-bottom: 20px;
  }
</style>
