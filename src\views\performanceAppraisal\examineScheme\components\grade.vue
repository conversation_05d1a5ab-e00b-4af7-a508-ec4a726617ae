<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      :title="`${formItem ? '编辑' : '新增'}考核等级`"
      :visible.sync="dialogVisible"
      width="40%"
      destroy-on-close
      @open="initData"
    >
      <div v-loading="loading">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px">
          <el-form-item label="排名" prop="sort">
            <el-input-number v-model="ruleForm.sort" step-strictly :min="0" :max="9999"></el-input-number>
          </el-form-item>
          <el-form-item label="等级名称" prop="name">
            <el-input
              v-model="ruleForm.name"
              placeholder="请输入等级名称"
              clearable
              maxlength="32"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="等级分数区间" prop="min">
            <el-input-number v-model="ruleForm.min" step-strictly :min="0" :max="100"></el-input-number>
            &nbsp;&nbsp;———&nbsp;&nbsp;
            <el-input-number v-model="ruleForm.max" step-strictly :min="0" :max="100"></el-input-number>
          </el-form-item>
          <el-form-item label="考核系数" prop="coefficient">
            <el-input-number
              v-model="ruleForm.coefficient"
              step-strictly
              :precision="1"
              :step="0.1"
              :min="0"
              :max="9999"
            ></el-input-number>
          </el-form-item>
          <el-form-item label="人员占比（%）" prop="personnelProportion">
            <el-input-number v-model="ruleForm.personnelProportion" step-strictly :min="0" :max="100"></el-input-number>
          </el-form-item>
          <el-form-item label="年度绩效积分" prop="integrate" v-if="period === 1">
            <el-input-number
              v-model="ruleForm.integrate"
              step-strictly
              :precision="1"
              :step="0.1"
              :max="9999"
            ></el-input-number>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveThrottling">确定</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      formItem: {
        type: [String, Object],
        default: "",
      },
      formList: {
        type: Array,
        default: () => [],
      },
      period: {
        type: Number,
        default: 0,
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        originalForm: {
          sort: "", //排名
          name: "", //等级名称
          min: 0, //最小值
          max: 0, //最大值
          coefficient: "", //考核系数
          integrate: "", //年度绩效积分
          personnelProportion: undefined, //人员占比
        },
        ruleForm: {},
        rules: {
          sort: [{ required: true, message: "请输入排名", trigger: "blur" }],
          name: [{ required: true, message: "请输入等级名称", trigger: "blur" }],
          min: [{ required: true, trigger: "blur", validator: this.validateMinAndMax }],
          coefficient: [{ required: true, message: "请输入考核系数", trigger: "blur" }],
          integrate: [{ required: true, message: "请输入年度绩效积分", trigger: "blur" }],
        },
        saveThrottling: () => {},
        loading: false,
      };
    },
    created() {
      this.saveThrottling = this.$throttling(this.saveRecord, 500);
      this.ruleForm = this.originalForm;
    },
    methods: {
      validateMinAndMax(_, value, callback) {
        if ((!value && value !== 0) || (!this.ruleForm.max && this.ruleForm.max !== 0)) {
          callback(new Error("请填写等级分数区间"));
        } else {
          callback();
        }
      },
      async initData() {
        this.ruleForm = Object.assign({}, this.formItem ? this.formItem : this.originalForm);
        if (!this.formItem) {
          this.$nextTick(() => {
            this.$refs.ruleForm.clearValidate();
          });
        }
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            if (this.ruleForm.min >= this.ruleForm.max) {
              this.$message.warning("等级分数区间最小值不能大于等于最大值");
              return;
            }
            if (this.formItem) {
              let index = this.formList.findIndex((list) => list === this.formItem);
              let compareList = this.formList.filter((_, i) => i !== index);
              if (compareList.length > 0) {
                let judgmentFlag = this.judgmentLevelForm(compareList);
                if (!judgmentFlag) {
                  return;
                }
              }
              this.$emit("editList", {
                item: this.ruleForm,
                index,
                showfield: "showGrade",
                listField: "assessGrades",
              });
            } else {
              if (this.formList.length > 0) {
                let judgmentFlag = this.judgmentLevelForm(this.formList);
                if (!judgmentFlag) {
                  return;
                }
              }
              this.$emit("addList", {
                item: this.ruleForm,
                showfield: "showGrade",
                listField: "assessGrades",
              });
            }
          }
        });
      },
      // 等级列表字段重复判断
      judgmentLevelForm(formList) {
        let sortIndex = formList.findIndex((list) => list.sort === this.ruleForm.sort);
        if (sortIndex >= 0) {
          this.$message.warning("等级排名不能重复");
          return false;
        }
        let nameIndex = formList.findIndex((list) => list.name === this.ruleForm.name);
        if (nameIndex >= 0) {
          this.$message.warning("等级名称不能重复");
          return false;
        }
        let flag = this.isScoreOverlap(formList, this.ruleForm);
        if (flag) {
          this.$message.warning("分数区间不能重叠");
          return false;
        }
        return true;
      },
      isScoreOverlap(scoreList, newScore) {
        for (let i = 0; i < scoreList.length; i++) {
          // 检查新区间是否与已有区间重叠
          if (newScore.min < scoreList[i].max && scoreList[i].min < newScore.max) {
            return true; // 有重叠
          }
        }
        return false; // 没有重叠
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
