<template>
  <div class="charts-style">
    <el-button class="charts-btn" type="text" @click="initCharts" v-if="isShowBtn"> 返回上一级</el-button>
    <div id="sexCharts" class="charts-style"></div>
  </div>
</template>
<script>
  import * as echarts from "echarts";
  import gzJson from "./guangzhou.json";
  import czJson from "./chaozhou.json";
  import swJson from "./shanwei.json";
  export default {
    props: {
      additiveLabel: {
        type: Object,
        default: () => {},
      },
      externalEvent: {
        type: Boolean,
        default: false,
      },
      channelList: {
        type: Array,
        default: () => [],
      },
      channelId: {
        type: String,
        default: "",
      },
    },

    watch: {
      additiveLabel: {
        handler() {
          this.initCharts();
        },
        deep: true,
      },
    },

    data() {
      return {
        myChart: null,
        isShowBtn: false,
        gzData: [
          { name: "荔湾区", value: [113.227791, 23.088038, "荔湾区"], code: 440103 },
          { name: "海珠区", value: [113.326676, 23.082002, "海珠区"], code: 440105 },
          { name: "越秀区", value: [113.281396, 23.13291, "越秀区"], code: 440104 },
          { name: "白云区", value: [113.323943, 23.289328, "白云区"], code: 440111 },
          { name: "番禺区", value: [113.406928, 22.973597, "番禺区"], code: 440113 },
          { name: "南沙区", value: [113.544315, 22.736609, "南沙区"], code: 440115 },
          { name: "从化区", value: [113.680484, 23.677286, "从化区"], code: 440117 },
          { name: "花都区", value: [113.214795, 23.442737, "花都区"], code: 440114 },
        ],
        czData: [
          { name: "湘桥区", value: [116.711526, 23.670302, "湘桥区"], code: 445102 },
          { name: "潮安区", value: [116.67931, 23.461012, "潮安区"], code: 445103 },
          { name: "饶平县", value: [116.911862, 23.83412, "饶平县"], code: 445122 },
        ],
        swData: [
          { name: "城区", value: [115.426007, 22.76914, "城区"], code: 441502 },
          { name: "海丰县", value: [115.278327, 22.96101, "海丰县"], code: 441521 },
          { name: "陆河县", value: [115.622646, 23.277554, "陆河县"], code: 441523 },
          { name: "陆丰市", value: [115.784775, 22.95671, "陆丰市"], code: 441581 },
        ],
        gz: {
          lines: [
            {
              type: "lines",
              zlevel: 3, //设置这个才会有轨迹线的小尾巴
              polyline: true,
              lineStyle: {
                normal: {
                  color: "#44FFF5", //线条颜色
                  width: 1.5,
                  curveness: 0.2,
                  shadowColor: "#44FFF5",
                },
              },
              data: [
                {
                  coords: [
                    [113.227791, 23.088038],
                    [113.107791, 22.888038],
                    [112.987791, 22.888038],
                  ],
                },
                {
                  coords: [
                    [113.680484, 23.677286],
                    [113.780484, 23.757286],
                    [114.080484, 23.757286],
                  ],
                },
                {
                  coords: [
                    [113.281396, 23.13291],
                    [113.217791, 23.188038],
                    [113.067791, 23.188038],
                  ],
                },
                {
                  coords: [
                    [113.326676, 23.082002],
                    [113.726676, 23.082002],
                    [113.826676, 23.002002],
                  ],
                },
                {
                  coords: [
                    [113.214795, 23.442737],
                    [113.214795, 23.842737],
                    [113.004795, 23.842737],
                  ],
                },
                {
                  coords: [
                    [113.323943, 23.289328],
                    [113.623943, 23.469328],
                    [113.823943, 23.469328],
                  ],
                },
                {
                  coords: [
                    [113.406928, 22.973597],
                    [113.406928, 22.673597],
                    [113.306928, 22.573597],
                  ],
                },
                {
                  coords: [
                    [113.544315, 22.736609],
                    [113.844315, 22.736609],
                    [113.904315, 22.706609],
                  ],
                },
              ],
            },
          ],
          labelData: [
            {
              name: "荔湾区",
              coords: [
                [113.107791, 22.888038],
                [112.667791, 22.888038],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
            {
              name: "从化区",
              coords: [
                [113.780484, 23.757286],
                [114.080484, 23.757286],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
            {
              name: "越秀区",
              coords: [
                [113.217791, 23.188038],
                [112.747791, 23.188038],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
            {
              name: "海珠区",
              coords: [
                [113.726676, 23.082002],
                [113.826676, 23.002002],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
            {
              name: "花都区",
              coords: [
                [113.214795, 23.842737],
                [112.684795, 23.842737],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
            {
              name: "白云区",
              coords: [
                [113.623943, 23.469328],
                [113.823943, 23.469328],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
            {
              name: "番禺区",
              coords: [
                [113.406928, 22.673597],
                [112.986928, 22.573597],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
            {
              name: "南沙区",
              coords: [
                [113.844315, 22.736609],
                [113.904315, 22.706609],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
          ],
        },
        cz: {
          lines: [
            {
              type: "lines",
              zlevel: 3,
              polyline: true,
              lineStyle: {
                normal: {
                  color: "#44FFF5",
                  width: 1.5,
                  curveness: 0.2,
                  shadowColor: "#44FFF5",
                },
              },
              data: [
                {
                  coords: [
                    [116.711526, 23.670302],
                    [116.811526, 23.770302],
                    [116.911526, 23.770302],
                  ],
                },
                {
                  coords: [
                    [116.67931, 23.461012],
                    [116.57931, 23.361012],
                    [116.47931, 23.361012],
                  ],
                },
                {
                  coords: [
                    [116.911862, 23.83412],
                    [117.011862, 23.93412],
                    [117.111862, 23.93412],
                  ],
                },
              ],
            },
          ],
          labelData: [
            {
              name: "湘桥区",
              coords: [
                [116.811526, 23.770302],
                [116.911526, 23.770302],
              ],
              value: [56, 4, 4],
            },
            {
              name: "潮安区",
              coords: [
                [116.67931, 23.361012],
                [116.29931, 23.361012],
              ],
              value: [56, 4, 4],
            },
            {
              name: "饶平县",
              coords: [
                [117.011862, 23.93412],
                [117.111862, 23.93412],
              ],
              value: [56, 4, 4],
            },
          ],
        },
        sw: {
          lines: [
            {
              type: "lines",
              zlevel: 3,
              polyline: true,
              lineStyle: {
                normal: {
                  color: "#44FFF5",
                  width: 1.5,
                  curveness: 0.2,
                  shadowColor: "#44FFF5",
                },
              },
              data: [
                {
                  coords: [
                    [115.426007, 22.76914],
                    [115.326007, 22.66914],
                    [115.226007, 22.66914],
                  ],
                },
                {
                  coords: [
                    [115.278327, 22.96101],
                    [115.178327, 23.06101],
                    [115.078327, 23.06101],
                  ],
                },
                {
                  coords: [
                    [115.622646, 23.277554],
                    [115.722646, 23.377554],
                    [115.822646, 23.377554],
                  ],
                },
                {
                  coords: [
                    [115.784775, 22.95671],
                    [115.884775, 22.85671],
                    [115.984775, 22.85671],
                  ],
                },
              ],
            },
          ],
          labelData: [
            {
              name: "城区",
              coords: [
                [115.326007, 22.66914],
                [114.586007, 22.66914],
              ],
              value: [56, 4, 4],
            },
            {
              name: "海丰县",
              coords: [
                [115.178327, 23.06101],
                [114.438327, 23.06101],
              ],
              value: [56, 4, 4],
            },
            {
              name: "陆河县",
              coords: [
                [115.722646, 23.377554],
                [115.822646, 23.377554],
              ],
              value: [56, 4, 4],
            },
            {
              name: "陆丰市",
              coords: [
                [115.884775, 22.85671],
                [115.984775, 22.85671],
              ],
              value: [56, 4, 4],
            },
          ],
        },
      };
    },
    mounted() {
      setTimeout(() => {
        this.initCharts();
      }, 1000);
    },
    methods: {
      initCharts() {
        let chartDom = document.getElementById("sexCharts");
        this.myChart = echarts.init(chartDom);
        this.isShowBtn = false;
        let chartKey = "gz";
        let dataJson = gzJson;
        if (this.channelId) {
          let filterItem = this.channelList.filter((list) => list.id === this.channelId)[0];
          if (filterItem.name.includes("潮州")) {
            chartKey = "cz";
            dataJson = czJson;
          }
          if (filterItem.name.includes("汕尾")) {
            chartKey = "sw";
            dataJson = swJson;
          }
        }
        echarts.registerMap(chartKey.toUpperCase(), dataJson);
        this.$emit("refreshData", null);
        var lines = [];
        if (this.additiveLabel && Object.keys(this.additiveLabel).length != 0) {
          lines = this[chartKey].lines;
        }

        let LableData = this[chartKey].labelData;

        const option = {
          geo: {
            map: chartKey.toUpperCase(),
            roam: true,
            zoom: 0.8,
            top: "16%",
            layoutSize: "50%",
            aspectScale: 0.85,
            scaleLimit: {
              min: 0.85,
              max: 0.85,
            },
            label: {
              color: "#ffffff",
              fontWeight: 600,
              // show: true,
              fontSize: 0,
            },
            itemStyle: {
              normal: {
                areaColor: "#27A369",
                borderWidth: 2,
                borderColor: "#A3E789",
              },
              emphasis: {
                borderColor: "#A3E789",
                borderWidth: 2,
                areaColor: "#065244",
              },
            },
          },
          series: [
            {
              type: "scatter",
              coordinateSystem: "geo",
              symbolSize: 0,
              encode: { value: 2 },
              symbol: "pin",
              zlevel: 1,
            },
            {
              type: "effectScatter",
              coordinateSystem: "geo",
              zlevel: 2,
              symbolSize: 6,
              rippleEffect: {
                //坐标点动画
                period: 3,
                scale: 5,
                brushType: "fill",
              },
              label: {
                normal: {
                  show: true,
                  position: "right",
                  formatter: "{b}",
                  color: "#b3e2f2",
                  fontWeight: "bold",
                  fontSize: 12,
                },
              },

              data: this[chartKey + "Data"],
              itemStyle: {
                //坐标点颜色
                normal: {
                  show: true,
                  color: "#44FFF5",
                  shadowBlur: 20,
                  shadowColor: "#44FFF5",
                },
                emphasis: {
                  areaColor: "#f00",
                },
              },
            },
            ...lines,
            {
              type: "lines",
              zlevel: 4,
              color: "#ff8003",
              label: this.additiveLabel,
              lineStyle: {
                type: "dashed",
                color: "#fff",
                width: 0.1,
                opacity: 1,
              },
              data: LableData,
            },
          ],
        };

        this.myChart.setOption(option);

        if (this.externalEvent) {
          // 点击打开弹窗
          this.myChart.on("click", async (params) => {
            this.$emit("openRouteDialog", params);
          });
        } else {
          // 点击下钻
          this.myChart.on("click", async (params) => {
            this.initChartsToJump(params);
          });
        }
      },
      initChartsToJump(params) {
        if (params.name === "") {
          return;
        }
        this.isShowBtn = true;
        let chartKey = "gz";
        let dataJson = gzJson;
        if (this.channelId) {
          let filterItem = this.channelList.filter((list) => list.id === this.channelId)[0];
          if (filterItem.name.includes("潮州")) {
            chartKey = "cz";
            dataJson = czJson;
          }
          if (filterItem.name.includes("汕尾")) {
            chartKey = "sw";
            dataJson = swJson;
          }
        }
        const data = this[chartKey + "Data"].filter((i) => i.name == params.name);
        this.$emit("refreshData", data?.[0]?.code);
        const option2 = {
          geo: {
            map: params.name,
            roam: true,
            zoom: 0.8,
            top: "16%",
            layoutSize: "50%",
            aspectScale: 0.85,
            scaleLimit: {
              min: 0.85,
              max: 0.85,
            },
            label: {
              color: "#ffffff",
              fontWeight: 600,
              // show: true,
              fontSize: 0,
            },
            itemStyle: {
              normal: {
                areaColor: "#27A369",
                borderWidth: 2,
                borderColor: "#A3E789",
              },
              emphasis: {
                borderColor: "#A3E789",
                borderWidth: 2,
                areaColor: "#065244",
              },
            },
          },
          series: [
            {
              type: "scatter",
              coordinateSystem: "geo",
              symbolSize: 0,
              encode: { value: 2 },
              symbol: "pin",
              zlevel: 1,
            },
            {
              type: "effectScatter",
              coordinateSystem: "geo",
              zlevel: 2,
              symbolSize: 6,
              rippleEffect: {
                //坐标点动画
                period: 3,
                scale: 5,
                brushType: "fill",
              },
              label: {
                normal: {
                  show: true,
                  position: "right",
                  formatter: "{b}",
                  color: "#b3e2f2",
                  fontWeight: "bold",
                  fontSize: 12,
                },
              },

              data: data,
              itemStyle: {
                //坐标点颜色
                normal: {
                  show: true,
                  color: "#44FFF5",
                  shadowBlur: 20,
                  shadowColor: "#44FFF5",
                },
                emphasis: {
                  areaColor: "#f00",
                },
              },
            },
          ],
        };
        let arr = dataJson.features.filter((i) => i.properties.name == params.name);
        let obj = {
          type: "FeatureCollection",
          features: arr,
        };
        echarts.registerMap(params.name, obj);
        this.myChart.setOption(option2, true);
      },
    },
  };
</script>
<style lang="scss" scoped>
  .charts-style {
    position: relative;
    width: 100%;
    height: 100%;
  }
  .charts-btn {
    position: absolute;
    top: 200px;
    left: 560px;
    z-index: 999;
  }
</style>
