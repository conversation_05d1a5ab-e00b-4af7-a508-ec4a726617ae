<template>
  <div class="safeModule-wrapper">
    <div class="safeModule-header">
      <Title name="人车管理" :url="url"></Title>
      <div class="right-channel" v-if="!channel">
        <el-radio-group v-model="channelId" @change="toggleChannel">
          <el-radio-button :label="label.id" v-for="label in channelList" :key="label.id">{{
            label.name
          }}</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <ECharts2
      v-if="summaryData?.vehicleSituaion"
      eChartsId="carECharts"
      title="车辆情况"
      label1="收运车辆"
      label2="空闲车辆"
      label3="车辆总数"
      :unAttend="summaryData?.vehicleSituaion?.unAttendVehicle"
      :total="summaryData?.vehicleSituaion?.totalVehicle"
      :attend="summaryData?.vehicleSituaion?.attendVehicle"
      @click.native="$router.push('/sctmp_base/vehicles/')"
    ></ECharts2>
    <ECharts2
      v-if="summaryData?.personSituation"
      eChartsId="personECharts"
      title="员工情况"
      label1="出车员工"
      label2="空闲员工"
      label3="员工总数"
      :unAttend="summaryData?.personSituation?.unAttendNum"
      :total="summaryData?.personSituation?.total"
      :attend="summaryData?.personSituation?.attendNum"
      @click.native="$router.push('/sctmp_base/personManage')"
    ></ECharts2>
  </div>
</template>
<script>
  import Title from "./ModuleTitle.vue";
  import ECharts2 from "./ECharts2.vue";
  export default {
    components: { ECharts2, Title },
    props: {
      summaryData: {
        type: Object,
        default: () => {},
      },
      channelList: {
        type: Array,
        default: () => [],
      },
      channel: {
        type: String,
        default: "",
      },
      value: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        url: require("../images/ic_car.svg"),
      };
    },
    computed: {
      channelId: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    created() {},
    mounted() {},
    methods: {
      toggleChannel() {
        this.$emit("toggleChannel");
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .safeModule-wrapper {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    margin-left: 8px;
    // 设计比例左 1254px 右 512px
    flex: auto;
    width: 512px;
  }
  .safeModule-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .right-channel {
    padding-right: 8px;
  }
</style>
