<template>
  <v-scale-screen width="1920" height="1080" class="dp-first-content">
    <div class="bg-img" :style="{ 'background-image': 'url(' + iconData.bg + ')' }"></div>
    <Title></Title>
    <TodayNum></TodayNum>
    <ChartNum></ChartNum>
    <BarProgress></BarProgress>
    <Location title="停车场3" :positon="1"></Location>
    <Location title="停车场2" :positon="2"></Location>
    <Location title="停车场1" :positon="3"></Location>
    <pieProgress></pieProgress>
    <BarEcharts></BarEcharts>
    <Calendar></Calendar>
  </v-scale-screen>
</template>
<script>
  import VScaleScreen from "v-scale-screen";
  import Title from "./components/Title.vue";
  import TodayNum from "./components/TodayNum.vue";
  import ChartNum from "./components/ChartNum.vue";
  import BarProgress from "./components/BarProgress.vue";
  import Location from "./components/Location.vue";
  import pieProgress from "./components/pieProgress.vue";
  import BarEcharts from "./components/BarEcharts.vue";
  import Calendar from "./components/Calendar.vue";

  export default {
    components: {
      VScaleScreen,
      Title,
      TodayNum,
      ChartNum,
      BarProgress,
      Location,
      pieProgress,
      BarEcharts,
      Calendar,
    },
    props: {},
    data() {
      return {
        iconData: {
          bg: require("./images/RTMLargeScreenV3_bg2.jpg"),
        },
      };
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {},
    watch: {},
  };
</script>
<style scoped lang="scss">
  .bg-img {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    position: absolute;
    z-index: 0;
  }
</style>
