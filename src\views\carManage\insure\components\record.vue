<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="8">
            <el-form-item label="车牌号" prop="plateNumber">
              <el-select
                v-model="ruleForm.plateNumber"
                placeholder="请选择车牌号"
                clearable
                filterable
                :disabled="recordId ? true : false"
                @change="changePlateNumber"
              >
                <el-option v-for="item in carOptions" :key="item.id" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="8">
            <el-form-item label="所属年份" prop="year">
              <el-date-picker
                v-model="ruleForm.year"
                type="year"
                value-format="yyyy"
                placeholder="请选择所属年份"
                @change="changeYear"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="8">
            <el-form-item label="投保类型" prop="insuranceType">
              <el-select
                v-model="ruleForm.insuranceType"
                placeholder="请选择投保类型"
                filterable
                multiple
                :disabled="!ruleForm.plateNumber || !ruleForm.year ? true : false"
                class="w-300"
              >
                <el-option
                  v-for="(item, index) in operationalNature == 1 ? INSURANCE_TYPE.slice(0, 2) : INSURANCE_TYPE"
                  :key="index"
                  :label="item"
                  :value="index"
                  :disabled="selectedType.includes(index)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-card class="type-card" v-if="ruleForm.insuranceType.includes(0)">
            <div slot="header" class="card-header">交强险记录</div>
            <el-col :md="24" :lg="12">
              <el-form-item label="经办人" prop="operatorName">
                <el-input
                  v-model="ruleForm.operatorName"
                  placeholder="请输入经办人姓名"
                  clearable
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单号" prop="policyNo">
                <el-input
                  v-model="ruleForm.policyNo"
                  placeholder="请输入保单号"
                  clearable
                  maxlength="30"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单金额(元)" prop="money">
                <el-input-number
                  v-model="ruleForm.money"
                  :precision="2"
                  :min="0"
                  :max="99999999999999"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保险公司" prop="insuranceCompany">
                <el-input
                  v-model="ruleForm.insuranceCompany"
                  placeholder="请输入保险公司"
                  clearable
                  maxlength="100"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单开始日期" prop="policyEffectiveDate">
                <el-date-picker
                  v-model="ruleForm.policyEffectiveDate"
                  type="date"
                  placeholder="请选择保单开始日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单结束日期" prop="policyTerminationDate">
                <el-date-picker
                  v-model="ruleForm.policyTerminationDate"
                  type="date"
                  placeholder="请选择保单结束日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="凭证" prop="vehicleFileList">
                <FileUpload
                  @uploadChange="uploadChangeFileList($event, 'vehicleFileList')"
                  :imageList="imageList['vehicleFileList']"
                  :limit="5"
                >
                  <template #tips><el-tag type="warning">请上传保单、投保凭证</el-tag></template>
                </FileUpload>
              </el-form-item>
            </el-col>
          </el-card>
          <el-card class="type-card" v-if="ruleForm.insuranceType.includes(1)">
            <div slot="header" class="card-header">商业险记录</div>
            <el-col :md="24" :lg="12">
              <el-form-item label="经办人" prop="syOperatorName">
                <el-input
                  v-model="ruleForm.syOperatorName"
                  placeholder="请输入经办人姓名"
                  clearable
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单号" prop="syPolicyNo">
                <el-input
                  v-model="ruleForm.syPolicyNo"
                  placeholder="请输入保单号"
                  clearable
                  maxlength="30"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单金额(元)" prop="syMoney">
                <el-input-number
                  v-model="ruleForm.syMoney"
                  :precision="2"
                  :min="0"
                  :max="99999999999999"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保险公司" prop="syInsuranceCompany">
                <el-input
                  v-model="ruleForm.syInsuranceCompany"
                  placeholder="请输入保险公司"
                  clearable
                  maxlength="100"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单开始日期" prop="syPolicyEffectiveDate">
                <el-date-picker
                  v-model="ruleForm.syPolicyEffectiveDate"
                  type="date"
                  placeholder="请选择保单开始日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单结束日期" prop="syPolicyTerminationDate">
                <el-date-picker
                  v-model="ruleForm.syPolicyTerminationDate"
                  type="date"
                  placeholder="请选择保单结束日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="凭证" prop="syVehicleFileList">
                <FileUpload
                  @uploadChange="uploadChangeFileList($event, 'syVehicleFileList')"
                  :imageList="imageList['syVehicleFileList']"
                  :limit="5"
                >
                  <template #tips><el-tag type="warning">请上传保单、投保凭证</el-tag></template>
                </FileUpload>
              </el-form-item>
            </el-col>
          </el-card>
          <el-card class="type-card" v-if="ruleForm.insuranceType.includes(2)">
            <div slot="header" class="card-header">承运险记录</div>
            <el-col :md="24" :lg="12">
              <el-form-item label="经办人" prop="cyOperatorName">
                <el-input
                  v-model="ruleForm.cyOperatorName"
                  placeholder="请输入经办人姓名"
                  clearable
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单号" prop="cyPolicyNo">
                <el-input
                  v-model="ruleForm.cyPolicyNo"
                  placeholder="请输入保单号"
                  clearable
                  maxlength="30"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单金额(元)" prop="cyMoney">
                <el-input-number
                  v-model="ruleForm.cyMoney"
                  :precision="2"
                  :min="0"
                  :max="99999999999999"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保险公司" prop="cyInsuranceCompany">
                <el-input
                  v-model="ruleForm.cyInsuranceCompany"
                  placeholder="请输入保险公司"
                  clearable
                  maxlength="100"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单开始日期" prop="cyPolicyEffectiveDate">
                <el-date-picker
                  v-model="ruleForm.cyPolicyEffectiveDate"
                  type="date"
                  placeholder="请选择保单开始日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单结束日期" prop="cyPolicyTerminationDate">
                <el-date-picker
                  v-model="ruleForm.cyPolicyTerminationDate"
                  type="date"
                  placeholder="请选择保单结束日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="凭证" prop="cyVehicleFileList">
                <FileUpload
                  @uploadChange="uploadChangeFileList($event, 'cyVehicleFileList')"
                  :imageList="imageList['cyVehicleFileList']"
                  :limit="5"
                >
                  <template #tips><el-tag type="warning">请上传保单、投保凭证</el-tag></template>
                </FileUpload>
              </el-form-item>
            </el-col>
          </el-card>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import FileUpload from "@/components/FileUpload";
  import { INSURANCE_TYPE } from "@/enums";
  import { createApiFun, updateApiFun } from "@/api/base";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
      plateNumber: {
        type: String,
        default: "",
      },
      year: {
        type: String,
        default: "",
      },
      insuranceOptions: {
        type: Array,
        default: () => [],
      },
    },
    components: {
      FileUpload,
    },
    data() {
      return {
        ruleForm: {
          plateNumber: "", //车牌号
          year: "", //所属年份
          insuranceType: [], //投保类型
          operatorName: "", //交强险经办人
          policyNo: "", //交强险保单号
          money: "", //交强险保单金额
          insuranceCompany: "", //交强险保险公司
          policyEffectiveDate: "", //交强险保单开始日期
          policyTerminationDate: "", //交强险保单结束日期
          vehicleFileList: [], ///交强险投保凭证

          syOperatorName: "", //商业险经办人
          syPolicyNo: "", //商业险保单号
          syMoney: "", //商业险保单金额
          syInsuranceCompany: "", //商业险保险公司
          syPolicyEffectiveDate: "", //商业险保单开始日期
          syPolicyTerminationDate: "", //商业险保单结束日期
          syVehicleFileList: [], ///商业险投保凭证

          cyOperatorName: "", //承运险经办人
          cyPolicyNo: "", //承运险保单号
          cyMoney: "", //承运险保单金额
          cyInsuranceCompany: "", //承运险保险公司
          cyPolicyEffectiveDate: "", //承运险保单开始日期
          cyPolicyTerminationDate: "", //承运险保单结束日期
          cyVehicleFileList: [], ///承运险投保凭证
        },
        originalRuleForm: {
          plateNumber: "", //车牌号
          year: "", //所属年份
          insuranceType: [], //投保类型
          operatorName: "", //交强险经办人
          policyNo: "", //交强险保单号
          money: "", //交强险保单金额
          insuranceCompany: "", //交强险保险公司
          policyEffectiveDate: "", //交强险保单开始日期
          policyTerminationDate: "", //交强险保单结束日期
          vehicleFileList: [], ///交强险投保凭证

          syOperatorName: "", //商业险经办人
          syPolicyNo: "", //商业险保单号
          syMoney: "", //商业险保单金额
          syInsuranceCompany: "", //商业险保险公司
          syPolicyEffectiveDate: "", //商业险保单开始日期
          syPolicyTerminationDate: "", //商业险保单结束日期
          syVehicleFileList: [], ///商业险投保凭证

          cyOperatorName: "", //承运险经办人
          cyPolicyNo: "", //承运险保单号
          cyMoney: "", //承运险保单金额
          cyInsuranceCompany: "", //承运险保险公司
          cyPolicyEffectiveDate: "", //承运险保单开始日期
          cyPolicyTerminationDate: "", //承运险保单结束日期
          cyVehicleFileList: [], ///承运险投保凭证
        },
        rules: {
          // 交强险
          plateNumber: [{ required: true, message: "请选择车牌号", trigger: "change" }],
          insuranceType: [{ required: true, message: "请选择投保类型", trigger: "change" }],
          year: [{ required: true, message: "请选择所属年份", trigger: "change" }],
          operatorName: [{ required: true, message: "请输入经办人姓名", trigger: "blur" }],
          policyNo: [{ required: true, message: "请输入保单号", trigger: "blur" }],
          money: [{ required: true, message: "请输入保单金额", trigger: "blur" }],
          insuranceCompany: [{ required: true, message: "请输入保险公司", trigger: "blur" }],
          policyEffectiveDate: [{ required: true, message: "请选择保单开始日期", trigger: "change" }],
          policyTerminationDate: [{ required: true, message: "请选择保单结束日期", trigger: "change" }],
          vehicleFileList: [{ required: true, message: "请上传投保凭证", trigger: "change" }],
          // 商业险
          syOperatorName: [{ required: true, message: "请输入经办人姓名", trigger: "blur" }],
          syPolicyNo: [{ required: true, message: "请输入保单号", trigger: "blur" }],
          syMoney: [{ required: true, message: "请输入保单金额", trigger: "blur" }],
          syInsuranceCompany: [{ required: true, message: "请输入保险公司", trigger: "blur" }],
          syPolicyEffectiveDate: [{ required: true, message: "请选择保单开始日期", trigger: "change" }],
          syPolicyTerminationDate: [{ required: true, message: "请选择保单结束日期", trigger: "change" }],
          syVehicleFileList: [{ required: true, message: "请上传投保凭证", trigger: "change" }],
          // 承运险
          cyOperatorName: [{ required: true, message: "请输入经办人姓名", trigger: "blur" }],
          cyPolicyNo: [{ required: true, message: "请输入保单号", trigger: "blur" }],
          cyMoney: [{ required: true, message: "请输入保单金额", trigger: "blur" }],
          cyInsuranceCompany: [{ required: true, message: "请输入保险公司", trigger: "blur" }],
          cyPolicyEffectiveDate: [{ required: true, message: "请选择保单开始日期", trigger: "change" }],
          cyPolicyTerminationDate: [{ required: true, message: "请选择保单结束日期", trigger: "change" }],
          cyVehicleFileList: [{ required: true, message: "请上传投保凭证", trigger: "change" }],
        },
        apis: {
          create: "/api/vehicleInsure/create",
          update: "/api/vehicleInsure/update",
          info: "/api/vehicleInsure/get/",
          carList: "/api/vehicle/dossier/list",
          findByPlateNumberAndYear: "/api/vehicleInsure/findByPlateNumberAndYear",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: {
          vehicleFileList: [],
          cyVehicleFileList: [],
          syVehicleFileList: [],
        },
        INSURANCE_TYPE,
        carOptions: [],
        typeField: [
          [
            "operatorName",
            "policyNo",
            "money",
            "insuranceCompany",
            "policyEffectiveDate",
            "policyTerminationDate",
            "vehicleFileList",
          ],
          [
            "syOperatorName",
            "syPolicyNo",
            "syMoney",
            "syInsuranceCompany",
            "syPolicyEffectiveDate",
            "syPolicyTerminationDate",
            "syVehicleFileList",
          ],
          [
            "cyOperatorName",
            "cyPolicyNo",
            "cyMoney",
            "cyInsuranceCompany",
            "cyPolicyEffectiveDate",
            "cyPolicyTerminationDate",
            "cyVehicleFileList",
          ],
        ],
        operationalNature: 0,
        selectedType: [],
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptions();
      if (this.recordId) {
        this.ruleForm.plateNumber = this.plateNumber;
        this.ruleForm.year = this.year;
        this.getHasTypeByNumberAndYear();
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({ statusList: [0, 1] }, this.apis.carList)];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
      },
      handleBackImage() {
        for (const key in this.imageList) {
          if (Object.hasOwnProperty.call(this.imageList, key)) {
            const item = this.ruleForm[key];
            if (this.ruleForm[key]) {
              this.imageList[key] = item.map((i) => {
                return {
                  url: i.url,
                  file: i,
                };
              });
            } else {
              this.imageList[key] = [];
            }
          }
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            let params = {
              plateNumber: this.ruleForm.plateNumber,
              insuranceType: this.ruleForm.insuranceType.join(","),
              year: this.ruleForm.year,
            };
            let typeFieldList = this.typeField.filter((_, index) => this.ruleForm.insuranceType.includes(index));
            typeFieldList.forEach((list) => {
              list.forEach((field) => {
                params[field] = this.ruleForm[field];
              });
            });
            if (this.ruleForm.id) {
              params.id = this.ruleForm.id;
            }
            try {
              let res = this.ruleForm.id
                ? await updateApiFun(params, this.apis.update)
                : await createApiFun(params, this.apis.create);
              if (res.success) {
                this.$message.success(`操作成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      uploadChangeFileList(fileList, field) {
        if (fileList.length > 0) {
          this.ruleForm[field] = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
          this.$refs.ruleForm.clearValidate(field);
        } else {
          this.ruleForm[field] = [];
        }
      },
      // 修改车牌号时获取车辆营运性质
      changePlateNumber(value) {
        this.getHasTypeByNumberAndYear();
        if (!value) {
          this.ruleForm.insuranceType = "";
          this.operationalNature = 0;
          return;
        }
        let item = this.carOptions.filter((car) => car.name === value)[0];
        this.operationalNature = item.operationalNature;
      },
      // 修改年份
      changeYear() {
        this.getHasTypeByNumberAndYear();
      },
      // 根据车牌号、年份获取已存在的投保类型
      async getHasTypeByNumberAndYear() {
        if (!this.ruleForm.plateNumber || !this.ruleForm.year) {
          return;
        }
        let res = await createApiFun(
          { plateNumber: this.ruleForm.plateNumber, year: this.ruleForm.year },
          this.apis.findByPlateNumberAndYear,
        );
        if (res.data) {
          this.ruleForm = res.data;
          this.ruleForm.insuranceType = res.data.insuranceType.split(",").map((i) => Number(i));
          this.selectedType = this.ruleForm.insuranceType;
          this.handleBackImage();
        } else {
          let plateNumber = this.ruleForm.plateNumber;
          let year = this.ruleForm.year;
          this.ruleForm = JSON.parse(JSON.stringify(this.originalRuleForm));
          this.ruleForm.plateNumber = plateNumber;
          this.ruleForm.year = year;
          this.selectedType = [];
          this.handleBackImage();
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 16px;
  }
  .type-card {
    margin-bottom: 20px;
  }
  .w-300 {
    width: 300px;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
  }
  ::v-deep .el-transfer-panel__body {
    height: 400px;
  }
  ::v-deep .el-tag__close {
    display: none;
  }
</style>
