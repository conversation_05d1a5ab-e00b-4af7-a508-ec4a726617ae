<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      width="80%"
      top="0"
      destroy-on-close
      :show-close="false"
      @open="resetFilter"
    >
      <record ref="record" :recordId="recordId" :recordForm="recordForm" :showBack="false"></record>
    </el-dialog>
  </div>
</template>

<script>
  import record from "@/views/collectTransportManage/electronicWaybill/components/record.vue";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      recordId: {
        type: String,
        default: "",
      },
      recordForm: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      record,
    },
    data() {
      return {};
    },
    mounted() {},
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    methods: {
      initData() {},
      resetFilter() {
        this.$nextTick(() => {
          this.$refs.record.resetFilter();
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
