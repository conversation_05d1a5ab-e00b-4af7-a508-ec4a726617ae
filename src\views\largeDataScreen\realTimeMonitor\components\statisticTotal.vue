<template>
  <div class="statistics-container">
    <div class="left">
      <div class="tab-wrapper">
        <ul class="tab-list">
          <li
            class="tab-item"
            :class="{ active: activeTab == index }"
            v-for="(item, index) in tabList"
            :key="index"
            @click="toggleTab(index)"
            >{{ item }}</li
          >
        </ul>
      </div>
      <div v-show="activeTab == 0">
        <Title title="车辆/人员信息"></Title>
        <VehiclePerson></VehiclePerson>
        <Title title="收运点位信息"></Title>
        <Point></Point>
      </div>
      <div class="car-wrapper" v-show="activeTab == 1">
        <CarList :carList="carList" @itemClick="itemClick"></CarList>
      </div>
    </div>
    <div class="flex-1"></div>
    <div class="right">
      <template v-if="showCar">
        <Title title="车辆/人员基础信息"></Title>
        <VehicleInfo :carData="carData"></VehicleInfo>
        <Title title="车辆收运信息"></Title>
        <VehicleWaybill :carData="carData"></VehicleWaybill>
        <VehicleWaybillList :carData="carData" ref="vehicleWaybillList"></VehicleWaybillList>
      </template>
      <template v-else>
        <Title title="今日收运量信息"></Title>
        <Count></Count>
        <Title title="今日异常信息"></Title>
        <ExceptionMessage></ExceptionMessage>
        <Title title="今日路线收运单信息"></Title>
        <Waybill></Waybill>
        <WaybillList :channelId="channelId"></WaybillList>
      </template>
      <div class="ai-img">
        <div class="left-channel" v-if="!channel">
          <el-radio-group v-model="channelId" size="small" @change="toggleChannel">
            <el-radio-button :label="label.id" v-for="label in channelList" :key="label.id">{{
              label.name
            }}</el-radio-button>
          </el-radio-group>
        </div>
        <div class="currDate">{{ currDate }}</div>
        <img class="img-box" src="@/assets/images/new_ai.png" alt="" @click="openAIDrawer" />
      </div>
    </div>
  </div>
</template>

<script>
  import Title from "./Title";
  import Count from "./Count";
  import VehiclePerson from "./VehiclePerson";
  import Point from "./Point";
  import ExceptionMessage from "./ExceptionMessage";
  import Waybill from "./Waybill";
  import WaybillList from "./WaybillList";
  import VehicleInfo from "./VehicleInfo";
  import VehicleWaybill from "./VehicleWaybill";
  import VehicleWaybillList from "./VehicleWaybillList";
  import CarList from "./CarList.vue";
  import moment from "moment";
  export default {
    components: {
      Title,
      VehiclePerson,
      Point,
      Count,
      ExceptionMessage,
      Waybill,
      WaybillList,
      VehicleInfo,
      VehicleWaybill,
      VehicleWaybillList,
      CarList,
    },
    props: {
      carList: {
        type: Array,
        default: () => [],
      },
      channelList: {
        type: Array,
        default: () => [],
      },
      channel: {
        type: String,
        default: "",
      },
      value: {
        type: String,
        default: "",
      },
    },
    computed: {
      channelId: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        apis: {
          data: "/api/monitor/large-screen",
          vehicleInfo: "/api/monitor/vehicle/info",
        },
        formData: "",
        showCar: false,
        carData: "",
        tabList: ["信息页", "车辆列表"],
        activeTab: 0,
        currDate: "",
      };
    },
    mounted() {
      moment.locale();
      this.getDate();
    },
    methods: {
      getDate() {
        let timer = setTimeout(() => {
          clearTimeout(timer);
          this.currDate = moment().format("YYYY年MM月DD日 | ") + moment().format("dddd");
          this.getDate();
        }, 1000);
      },
      toggleTab(index) {
        if (this.activeTab == index) return;
        this.activeTab = index;
      },
      itemClick(item) {
        this.$router.push(
          `/sctmp_base/realTimeDriving?plateNumber=${item.plateNumber}&operationalNature=${item.operationalNature}`,
        );
      },
      openAIDrawer() {
        this.$emit("openAIDrawer");
      },
      toggleChannel() {
        this.$store.dispatch("getFormData", this.channelId);
        this.$emit("toggleChannel");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .statistics-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    justify-content: space-between;
  }
  .left,
  .right {
    width: 480px;
    padding: 0 20px;
    padding-top: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    position: relative;
  }
  .ai-img {
    position: fixed;
    top: 14px;
    right: 24px;
    pointer-events: auto;
    display: flex;
    align-items: center;
    .left-channel {
      margin-right: 10px;
    }
    .currDate {
      font-size: 18px;
      color: #2e6929;
      line-height: 18px;
      font-weight: bold;
      font-style: normal;
      padding-right: 20px;
    }
    .img-box {
      width: 60px;
      height: 60px;
      overflow: hidden;
      cursor: pointer;
    }
  }
  .flex-1 {
    flex: 8;
  }
  .tab-wrapper {
    display: inline-block;
  }
  .tab-list {
    pointer-events: auto;
    display: inline-flex;
    align-items: center;
    border-radius: 16px;
    margin-bottom: 12px;
    background-color: rgba(0, 0, 0, 0.3);
    color: #fff;
    .tab-item {
      width: 80px;
      text-align: center;
      padding: 8px 0;
      font-size: 14px;
      cursor: pointer;
      border-radius: 16px;
      &.active {
        background-color: var(--color-primary);
      }
    }
  }
  .car-wrapper {
    flex: 1;
    overflow: hidden;
  }
</style>
