<template>
  <div class="operate-info-container micro-app-sctmp_base" v-if="completed">
    <header class="header">
      <h2 class="header-title">运营信息报表</h2>
      <el-tabs class="header-tabs" v-model="activeTab">
        <el-tab-pane :label="item" v-for="(item, index) in tabList" :key="index"></el-tab-pane>
      </el-tabs>
      <div class="channel" v-if="!channel">
        <el-radio-group v-model="channelId">
          <el-radio-button :label="label.id" v-for="label in channelList" :key="label.id">{{
            label.name
          }}</el-radio-button>
        </el-radio-group>
      </div>
    </header>
    <main class="main">
      <Title title="垃圾收运量信息"></Title>
      <Rubbish :channelId="channelId"></Rubbish>
      <Title title="里程信息"></Title>
      <div class="mileage-box">
        <MileageRank :channelId="channelId"></MileageRank>
        <div class="mileage-right">
          <div class="mileage-right-top">
            <MileageAverage :channelId="channelId" ref="mileageAverageRef"></MileageAverage>
            <MileageComparison :channelId="channelId" ref="mileageComparisonRef"></MileageComparison>
          </div>
          <MileageAnomaly :channelId="channelId"></MileageAnomaly>
        </div>
      </div>
      <Title title="安全自检/培训信息"></Title>
      <SafetyCheck :channelId="channelId" ref="safetyCheckRef"></SafetyCheck>
      <SafetyTrain :channelId="channelId" ref="safetyTrainRef"></SafetyTrain>
      <Title title="加油数据信息"></Title>
      <Refuel :channelId="channelId" ref="refuelRef"></Refuel>
      <Title title="司机能力雷达图"></Title>
      <DriverAbility :channelId="channelId" ref="driverAbilityRef"></DriverAbility>
    </main>
  </div>
</template>

<script>
  import Title from "./components/Title.vue";
  import Rubbish from "./components/Rubbish.vue";
  import MileageRank from "./components/MileageRank.vue";
  import MileageAverage from "./components/MileageAverage.vue";
  import MileageComparison from "./components/MileageComparison.vue";
  import MileageAnomaly from "./components/MileageAnomaly.vue";
  import SafetyCheck from "./components/SafetyCheck.vue";
  import SafetyTrain from "./components/SafetyTrain.vue";
  import Refuel from "./components/Refuel.vue";
  import DriverAbility from "./components/DriverAbility.vue";
  import { getLocalStorage } from "@/utils/storage";
  import { INTERVIEW_CHANNEL } from "@/utils/commonValue"; //访问渠道
  import { getInfoApiFun } from "@/api/base";
  export default {
    components: {
      Title,
      Rubbish,
      MileageRank,
      MileageAverage,
      MileageComparison,
      MileageAnomaly,
      SafetyCheck,
      SafetyTrain,
      Refuel,
      DriverAbility,
    },
    data() {
      return {
        activeTab: 0,
        tabList: ["运输管理信息"],
        timer: null,
        refreshKey: 0,
        channelId: "",
        channelList: [],
        channel: "",
        apis: { channelList: "/api/base/dna/listByDna/" },
        refList: [
          "mileageAverageRef",
          "mileageComparisonRef",
          "safetyCheckRef",
          "safetyTrainRef",
          "refuelRef",
          "driverAbilityRef",
        ],
        completed: false,
      };
    },
    async created() {
      this.channel = getLocalStorage(INTERVIEW_CHANNEL);
      await this.getChannelList();
    },
    mounted() {
      this.$nextTick(() => {
        window.addEventListener("resize", this.handleResize);
      });
    },
    beforeDestroy() {
      // 组件销毁前清除监听器和定时器
      window.removeEventListener("resize", this.handleResize);
      // 清除定时器（在这个例子中可能不是必需的，但展示如何操作）
      if (this.timer !== null) {
        clearTimeout(this.timer);
        this.timer = null; // 重置为null是个好习惯
      }
    },
    methods: {
      // 获取渠道列表
      async getChannelList() {
        let res = await getInfoApiFun(this.$DNACONFIG.dna, this.apis.channelList);
        this.channelList = this.channelList.concat(res.data);
        if (this.channel) {
          this.channelId = this.channel;
        } else {
          this.channelId = this.channelList[0].id;
        }
        this.completed = true;
      },
      handleResize() {
        if (this.timer !== null) {
          clearTimeout(this.timer);
        }
        for (const item of this.refList) {
          this.$nextTick(() => {
            this.$refs[item].chartInstance.resize();
          });
        }
        this.timer = setTimeout(() => {
          for (const item of this.refList) {
            this.$nextTick(() => {
              this.$refs[item].chartInstance.resize();
            });
          }
        }, 1000);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .operate-info-container {
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;
    display: flex;
    flex-direction: column;
  }
  .header {
    background-color: #fff;
    padding: 20px 30px 0 30px;
    position: relative;
    .header-title {
      text-align: center;
      margin-top: 0;
    }
    ::v-deep .header-tabs .el-tabs__header {
      margin: 0;
    }
    .channel {
      position: absolute;
      top: 16px;
      right: 16px;
    }
  }
  .main {
    padding: 20px;
    padding-top: 0;
    flex: 1;
    overflow-y: auto;
  }
  .mileage-box {
    display: grid;
    grid-gap: 16px;
    grid-template-columns: 2fr 6fr;
    .mileage-right-top {
      display: grid;
      grid-gap: 16px;
      grid-template-columns: repeat(2, 1fr);
    }
  }
</style>
