<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">智能收集柜档案详情</div>
      <el-descriptions :labelStyle="labelStyle" :contentStyle="contentStyle" :column="2" border>
        <el-descriptions-item label="所属公司">
          <span class="desc-value">{{ ruleForm.companyName }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="设备编号">
          <span class="desc-value">{{ ruleForm.deviceCode }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="设备名称">
          <span class="desc-value">{{ ruleForm.deviceName }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="设备状态">
          <span class="desc-value">{{ DEVICE_STATUS[ruleForm.deviceStatus] }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="运行状态">
          <span class="desc-value">{{ CURRENT_STATUS[ruleForm.runStatus] }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="设备地址">
          <span class="desc-value">{{ ruleForm.deviceAddress }}</span>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="record-footer">
      <el-button @click="$emit('closeRecord')">返回</el-button>
    </div>
  </div>
</template>

<script>
  import { DEVICE_STATUS, CURRENT_STATUS } from "@/enums";
  import { getInfoApiFun } from "@/api/base";
  export default {
    props: {
      holdingTankId: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        ruleForm: {
          companyName: "", //所属公司
          deviceCode: "", //设备名称
          deviceName: "", //设备编号
          deviceStatus: "", //运行状态
          runStatus: "", //设备状态
          address: "", //详细地址
        },
        DEVICE_STATUS,
        CURRENT_STATUS,
        apis: {
          info: "/api/holding/holdingTank/get/",
        },
        loading: false,
        labelStyle: {
          width: "200px",
          height: "48px",
          fontSize: "14px",
          fontWeight: 400,
          color: "#122131",
          lineHeight: "20px",
        },
        contentStyle: {
          fontSize: "14px",
          fontWeight: 400,
          lineHeight: "20px",
        },
      };
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.holdingTankId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .w-400 {
    width: 400px;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
</style>
