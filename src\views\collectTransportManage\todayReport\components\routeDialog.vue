<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      width="80%"
      top="0"
      destroy-on-close
      :show-close="false"
      @open="initData"
    >
      <record
        :recordId="recordId"
        :recordForm="recordForm"
        v-if="showRecord"
        @closeRecord="showRecord = false"
      ></record>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input
              class="w400 mr-10"
              v-model="keyword"
              placeholder="请输入路线名称/收运车牌号/司机姓名/押运工姓名"
              clearable
            ></el-input>
          </div>
          <el-button @click="initData">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
        </header>
        <main class="main">
          <el-table
            ref="tableRef"
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            height="100%"
            border
          >
            <el-table-column prop="name" label="路线名称" align="center"></el-table-column>
            <el-table-column prop="date" label="日期" align="center"></el-table-column>
            <el-table-column prop="plateNumber" label="收运车辆" align="center"></el-table-column>
            <el-table-column prop="driverName" label="收运司机" align="center"></el-table-column>
            <el-table-column prop="cargoOneName" label="押运员1" align="center"></el-table-column>
            <el-table-column prop="cargoTwoName" label="押运员2" align="center"></el-table-column>
            <el-table-column prop="total" label="应收点位" align="center"></el-table-column>
            <el-table-column prop="collectNum" label="已收点位" align="center"></el-table-column>
            <el-table-column prop="unCollectNum" label="未收点位" align="center"></el-table-column>
            <el-table-column width="180" label="操作" align="center">
              <template #default="{ row }">
                <el-link type="primary" @click="checkRecord(row)">查看详情</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import { RECEIVING_CONDITION, POINT_TASK_TYPE, IS_NORMAL_OPERATION, BARRELS_BAGS, VERIFY_STATUS } from "@/enums";
  import moment from "moment";
  import record from "@/views/collectTransportManage/electronicWaybill/components/record.vue";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      completed: {
        type: Boolean,
        default: true,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    components: {
      record,
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/logisticsReport/waybill/listPage",
        },
        RECEIVING_CONDITION,
        POINT_TASK_TYPE,
        IS_NORMAL_OPERATION,
        BARRELS_BAGS,
        VERIFY_STATUS,
        loading: false,
        keyword: "",
        showRecord: false,
        recordId: "",
        recordForm: {},
      };
    },
    methods: {
      initData() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.page.pageSize = 10;
        this.getDataList();
      },
      async getDataList() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          beginStatisticsDate: moment().format("YYYY-MM-DD"),
          endStatisticsDate: moment().format("YYYY-MM-DD"),
          completed: this.completed,
          channelId: this.channelId,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.getDataList();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.getDataList();
      },
      // 查看详情
      checkRecord(row) {
        this.recordId = row.id;
        this.recordForm = row;
        this.showRecord = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
      position: relative;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .w400 {
    width: 400px;
  }
  .picture-img {
    width: 60px;
    height: 60px;
  }
  ::v-deep .el-badge__content.is-fixed {
    top: 8px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
