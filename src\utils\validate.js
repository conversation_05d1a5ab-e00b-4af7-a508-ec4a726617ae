/**
 * Created by PanJiaChen on 16/11/18.
 */

/**
 * @param {string} path
 * @returns {Boolean}
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUsername(str) {
  var reg = /^[A-Za-z0-9]{2,20}$/;
  return reg.test(str);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validLowerCase(str) {
  const reg = /^[a-z]+$/;
  return reg.test(str);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validUpperCase(str) {
  const reg = /^[A-Z]+$/;
  return reg.test(str);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function validAlphabets(str) {
  const reg = /^[A-Za-z]+$/;
  return reg.test(str);
}

/**
 * @param {string} str
 * @returns {Boolean}
 */
export function isString(str) {
  if (typeof str === "string" || str instanceof String) {
    return true;
  }
  return false;
}

/**
 * @param {Array} arg
 * @returns {Boolean}
 */
export function isArray(arg) {
  if (typeof Array.isArray === "undefined") {
    return Object.prototype.toString.call(arg) === "[object Array]";
  }
  return Array.isArray(arg);
}

/**
 * @param {string} value
 * @returns {Boolean}
 */
export function validPhone(value) {
  var reg = /^1[3456789]\d{9}$/;

  return reg.test(value);
}
/**
 * @param {string} value
 * @returns {Boolean}
 */
export function validPostcode(value) {
  var reg = /^[0-9]{6}$/;

  return reg.test(value);
}

/**
 * @param {string} value
 * @returns {Boolean}
 */
export function validVideo(value) {
  var reg = /(.mp4)|(.avi)|(.wav)$/i;

  return reg.test(value);
}
/**
 * @param {string} value
 * @returns {Boolean}
 */
export function validDoc(value) {
  var reg = /(.txt)|(.doc)|(.docx)|(.dot)|(.pdf)|(.xlsx)|(.xls)|(.ppt)|(.pptx)$/i;
  return reg.test(value);
}
/**
 * @param {string} value
 * @returns {Boolean}
 */
export function validPic(value) {
  var reg = /.png|.jpg|.bmp|.jpge|.svg|.gif$/i;
  return reg.test(value);
}
export function validAudio(value) {
  var reg = /.mp3$/i;
  return reg.test(value);
}
