<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage @closeRecord="closeRecord">
      <component
        v-if="showRecord"
        :is="componentName"
        :recordItem="recordItem"
        ref="backlogComponent"
        @closeRecord="closeRecord"
        @returnIndex="returnIndex"
        @refreshList="initData"
      />
      <div class="main-index" v-show="!showRecord">
        <header class="header">
          <div class="header-left">
            <el-form ref="filterForm" inline label-suffix=":" label-width="80px">
              <el-form-item label="待办模块">
                <el-select v-model="codeList" placeholder="请选择待办模块" clearable filterable>
                  <el-option
                    v-for="(item, index) in BACKLOG_MODULE"
                    :key="index"
                    :label="item.name"
                    :value="item.codeList"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="处理状态">
                <el-select v-model="status" placeholder="请选择处理状态" clearable filterable>
                  <el-option
                    v-for="(item, index) in BACKLOG_STATUS"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
            :colNum="12"
          ></InterviewChannel>
          <el-col :span="12">
            <el-form-item label="截止日期范围">
              <el-date-picker
                v-model="filterForm.deadline"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上报日期范围">
              <el-date-picker
                v-model="filterForm.createDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理日期范围">
              <el-date-picker
                v-model="filterForm.completionDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column prop="module" label="模块" align="center"></el-table-column>
            <el-table-column prop="todoTask" label="内容" align="center" min-width="300"></el-table-column>
            <el-table-column prop="createTime" label="上报时间" align="center"></el-table-column>
            <el-table-column prop="deadline" label="截止日期" align="center">
              <template #default="{ row }">{{ row.deadline || "-" }}</template>
            </el-table-column>
            <el-table-column prop="status" label="处理状态" align="center">
              <template #default="{ row }">{{ BACKLOG_STATUS[row.status] }}</template>
            </el-table-column>
            <el-table-column prop="handlePerson" label="处理人" align="center">
              <template #default="{ row }">{{ row.handlePerson || "-" }}</template>
            </el-table-column>
            <el-table-column prop="completionTime" label="处理时间" align="center">
              <template #default="{ row }">{{ row.completionTime || "-" }}</template>
            </el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column prop="code" label="code" align="center"></el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <el-link
                  class="mr-10"
                  type="primary"
                  @click="editRecord(row)"
                  v-if="[0, 2].includes(row.status) && !['22', '50', '51'].includes(row.code)"
                  >去处理</el-link
                >
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import { getListPageApiFun } from "@/api/base";
  import { BACKLOG_MODULE, BACKLOG_STATUS, BACKLOG_CODE_MAP } from "@/enums/backlog";
  import recordComponents from "./componentImport";
  export default {
    components: {
      defaultPage,
      ...recordComponents,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/task/listPage",
        },
        showRecord: false,
        recordId: "",
        loading: false,
        BACKLOG_MODULE,
        BACKLOG_STATUS,
        BACKLOG_CODE_MAP,
        componentName: "",
        showFilter: "",
        codeList: [],
        status: "",
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        this.loading = true;
        try {
          let params = {
            type: 0,
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            deadlineBeginDate: this.filterForm.deadline ? this.filterForm.deadline[0] : "",
            deadlineEndDate: this.filterForm.deadline ? this.filterForm.deadline[1] : "",
            createBeginDate: this.filterForm.createDate ? this.filterForm.createDate[0] : "",
            createEndDate: this.filterForm.createDate ? this.filterForm.createDate[1] : "",
            completionBeginDate: this.filterForm.completionDate ? this.filterForm.completionDate[0] : "",
            completionEndDate: this.filterForm.completionDate ? this.filterForm.completionDate[1] : "",
            codeList: this.codeList || [],
            status: this.status,
            channelId: this.filterForm.channelId || "",
          };
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.recordItem = row;
        if (row.code == 21) {
          this.recordItem.taskStatus = 0;
        }
        this.componentName = `record${row.code}`;
      },
      closeRecord() {
        this.$refs.backlogComponent.closeRecord();
      },
      returnIndex() {
        this.showRecord = false;
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.codeList = [];
        this.status = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .header-left .el-form-item,
  .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
