<template>
  <div class="table-container">
    <div class="color-list flex-center-start" v-if="isShowColorList">
      <div class="color-list-item flex-center-start" v-for="(item, index) in colorList" :key="index">
        <div class="icon" :style="{ background: colorList[index].color }"></div>
        <span>{{ item.text }}</span>
      </div>
    </div>
    <el-table
      :cell-style="{ background: '#F7F8FA', 'padding-left': '8px' }"
      :header-cell-style="{ background: '#EFF0F2', color: '#222222', 'padding-left': '8px' }"
      :data="formattedTableData"
    >
      <el-table-column
        :max-width="column.value == '序号' ? 60 : 220"
        :min-width="column.value == '序号' ? 60 : 190"
        v-for="(column, index) in headerData"
        :key="index"
        :label="column.value"
        :prop="String(column.index)"
      >
      </el-table-column>
    </el-table>
    <pre class="table-tip" v-if="rowData.length == 10">显示了前10条数据明细，更多数据请登录平台自行查询</pre>
  </div>
</template>
<script>
  export default {
    components: {},
    props: {
      tableList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        // isShowColorList: false,
        colorList: [
          { color: "#75C994", text: "优秀[90%,100%]" },
          { color: "#2688EB", text: "良好[80%,90%)" },
          { color: "#F09648", text: "一般[60%,80%)" },
          { color: "#DE6141", text: "较差[0%,60%)" },
        ],
      };
    },
    computed: {
      headerData() {
        // 仅返回数据，不产生副作用
        return this.tableList.find((item) => item.type === "header").data;
      },
      isShowColorList() {
        // 另一个计算属性，用于判断是否需要显示颜色列表
        const headerData = this.headerData; // 注意：这里假设 headerData 总是存在，否则需要添加额外的检查
        return headerData.some((item) => item.value === "掌握度" || item.value === "个人情况");
      },
      rowData() {
        let data = this.tableList.filter((item) => item.type === "row");
        return data;
      },
      formattedTableData() {
        let data = this.rowData.map((row) => ({
          ...row.data.reduce((acc, curr, i) => ({ ...acc, [this.headerData[i].index]: curr.value }), {}),
        }));
        return data;
      },
    },
    mounted() {
      // console.log("this.tableList", this.tableList);
    },
    methods: {
      /**
       * @description: 颜色判断
       * @Date: 2024-04-16 18:12:40
       */
      handleColor(text) {
        switch (text) {
          case "优秀":
            return "#75C994";
          case "良好":
            return "#2688EB";
          case "一般":
            return "#F09648";
          case "较差":
            return "#DE6141";
          default:
            break;
        }
      },
      //分数评级
      getScoreLevel(score) {
        score = Number(score.replace(/%/g, ""));
        if (score >= 90 && score <= 100) {
          return "#75C994";
        }
        if (score >= 80) {
          return "#2688EB";
        }
        if (score >= 60) {
          return "#F09648";
        }
        return "#DE6141";
      },
    },
  };
</script>
<style lang="scss" scoped>
  .table-container {
    border-top: 1px solid #ebedf0;
    padding-top: 16px;
    .color-list-item {
      padding: 16px 24px 16px 0;
      color: #646566;
      .icon {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 4px;
      }
    }
  }
  .table-tip {
    color: #171a1d;
    font-size: 14px;
    white-space: pre-wrap;
  }
</style>
