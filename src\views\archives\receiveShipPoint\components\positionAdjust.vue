<template>
  <div>
    <el-dialog title="" :visible.sync="dialogVisible" width="50%" destroy-on-close append-to-body @open="initData">
      <div class="micro-app-sctmp_base">
        <header class="header">
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="80px">
            <el-form-item prop="type" label="搜索方式">
              <el-radio-group v-model="ruleForm.type" @change="resetFilter">
                <el-radio :label="index" v-for="(item, index) in typeOptions" :key="index">{{ item }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <div class="search-box">
              <template v-if="ruleForm.type === 1">
                <el-form-item label="经度" prop="longitude">
                  <el-input-number v-model="ruleForm.longitude" :precision="6"></el-input-number>
                </el-form-item>
                <el-form-item label="纬度" prop="latitude">
                  <div class="location-box">
                    <el-input-number v-model="ruleForm.latitude" :precision="6"></el-input-number>
                  </div>
                </el-form-item>
              </template>
              <template v-else>
                <el-form-item prop="address" label="地址">
                  <el-input v-model="ruleForm.address" clearable placeholder="请输入地点"></el-input>
                </el-form-item>
              </template>
              <div class="header-right">
                <el-button @click="resetFilter">重置</el-button>
                <el-button type="primary" @click="searchFilter">查询</el-button>
              </div>
            </div>
          </el-form>
        </header>
        <div class="map-box">
          <mapContainer @initMap="initMap"></mapContainer>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">返回</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import mapContainer from "@/components/mapContainer";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      formItem: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      mapContainer,
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    beforeDestroy() {
      if (this.infoWindow && document.getElementById("position-adjust-button")) {
        document.getElementById("position-adjust-button").removeEventListener("click", this.setPosition);
      }
      if (this.map) {
        this.map.destroy();
        this.map = "";
      }
    },
    data() {
      return {
        ruleForm: {
          type: 0,
          address: "",
          longitude: "",
          latitude: "",
        },
        rules: {},
        map: "",
        typeOptions: ["按地址信息搜索", "按坐标搜索"],
        geocoder: "",
        markInfo: {
          address: "",
          longitude: "",
          latitude: "",
        },
      };
    },
    methods: {
      initData() {
        if (!this.map) {
          this.initMap();
        } else {
          this.resetFilter();
          this.initPosition();
        }
      },
      // 初始化地图
      initMap(map) {
        if (map) {
          this.map = map;
          window.AMap.plugin("AMap.Scale", () => {
            let scale = new window.AMap.Scale(); //创建工具条插件实例
            map.addControl(scale); //添加工具条插件到页面
          });
          window.AMap.plugin("AMap.Geocoder", () => {
            this.geocoder = new window.AMap.Geocoder({
              city: "全国", //城市设为广州，默认：“全国”
            }); //创建工具条插件实例
          });
          map.on("click", this.mapClick);
          this.resetFilter();
          this.initPosition();
        }
      },
      // 初始化地点
      async initPosition() {
        if (!this.formItem.address && (!this.formItem.longitude || !this.formItem.latitude)) return;
        if (this.formItem.longitude && this.formItem.latitude) {
          let address = await this.inverseGeocoding([this.formItem.longitude, this.formItem.latitude]);
          this.markInfo.address = address;
          this.markInfo.longitude = this.formItem.longitude;
          this.markInfo.latitude = this.formItem.latitude;
          this.generateMark([this.formItem.longitude, this.formItem.latitude]);
        } else {
          let obj = await this.positiveGeocoding(this.formItem.address);
          this.markInfo.address = obj.address;
          this.markInfo.longitude = obj.longitude;
          this.markInfo.latitude = obj.latitude;
          this.generateMark(obj.location);
        }
      },
      // 地图点击事件
      async mapClick(e) {
        this.map.clearInfoWindow();
        this.map.clearMap();
        let address = await this.inverseGeocoding(e.lnglat);
        this.markInfo.address = address;
        this.markInfo.longitude = e.lnglat.lng;
        this.markInfo.latitude = e.lnglat.lat;
        this.generateMark(e.lnglat);
      },
      // 生成点位
      generateMark(lnglat) {
        let marker = new window.AMap.Marker({
          map: this.map,
          position: lnglat,
        });
        this.generateInfoWindowContent(lnglat);
        marker.on("click", this.markerClick);
        // 绑定按钮点击事件
        document.getElementById("position-adjust-button").addEventListener("click", this.setPosition);
        this.map.setFitView(null, false, [150, 60, 100, 60], 14);
      },
      // 点位点击事件
      markerClick(e) {
        let lnglat = e.target.getPosition();
        this.generateInfoWindowContent(lnglat);
      },
      // 信息窗体结构生成
      generateInfoWindowContent(lnglat) {
        let infoWindowContent = `<div style="font-size: 16px;width: 400px">${this.markInfo.address}</div>
        <div style="margin: 10px 0"><span>经度：${this.markInfo.longitude}</span>&nbsp;&nbsp;<span>纬度：${this.markInfo.latitude}</span></div>
        <div id="position-adjust-button" style="display: inline-block;border-color: #4ca786;color: #fff;background-color: #4ca786;padding: 8px 16px;font-size: 14px;border-radius: 4px;cursor:pointer;">设为定位地点</div>`;
        let infoWindow = new window.AMap.InfoWindow({
          position: lnglat,
          offset: new window.AMap.Pixel(0, -40),
          autoMove: true, // 自动移动到标记位置
          content: infoWindowContent,
        });
        infoWindow.open(this.map);
      },
      // 重置
      resetFilter() {
        this.map.clearInfoWindow();
        this.map.clearMap();
        this.ruleForm.address = "";
        this.ruleForm.longitude = "";
        this.ruleForm.latitude = "";
        this.markInfo.address = "";
        this.markInfo.longitude = "";
        this.markInfo.latitude = "";
      },
      // 查询
      async searchFilter() {
        this.map.clearInfoWindow();
        this.map.clearMap();
        if (this.ruleForm.type === 0) {
          if (this.ruleForm.address) {
            let obj = await this.positiveGeocoding(this.ruleForm.address);
            this.markInfo.address = obj.address;
            this.markInfo.longitude = obj.longitude;
            this.markInfo.latitude = obj.latitude;
            this.generateMark(obj.location);
          }
        } else {
          if (this.ruleForm.longitude && this.ruleForm.latitude) {
            let address = await this.inverseGeocoding([this.ruleForm.longitude, this.ruleForm.latitude]);
            this.markInfo.address = address;
            this.markInfo.longitude = this.ruleForm.longitude;
            this.markInfo.latitude = this.ruleForm.latitude;
            this.generateMark([this.ruleForm.longitude, this.ruleForm.latitude]);
          }
        }
      },
      // 正地理编码
      positiveGeocoding(value) {
        return new Promise((resolve, reject) => {
          this.geocoder.getLocation(value, (status, result) => {
            if (status === "complete" && result.info === "OK") {
              resolve({
                address: result.geocodes[0].formattedAddress,
                longitude: result.geocodes[0].location.lng,
                latitude: result.geocodes[0].location.lat,
                location: result.geocodes[0].location,
              });
            } else {
              reject();
            }
          });
        });
      },
      // 逆地理编码
      inverseGeocoding(lnglat) {
        return new Promise((resolve, reject) => {
          this.geocoder.getAddress(lnglat, (status, result) => {
            if (status === "complete" && result.regeocode) {
              let address = result.regeocode.formattedAddress;
              resolve(address);
            } else {
              reject();
            }
          });
        });
      },
      // 设为定位地点
      setPosition() {
        this.$emit("setPosition", this.markInfo);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .search-box {
    display: flex;
  }
  .header-right {
    margin-left: 10px;
  }
  .map-box {
    height: 600px;
  }
</style>
