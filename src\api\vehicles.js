import { get } from "@/utils/request";
export const BASE_API = process.env.VUE_APP_BASE_API_URL + "/sctmpbase";

//车辆管理大屏 - 事件列表
export function vehicleEventStaticsApi(params) {
  return get(BASE_API + "/api/query/vehicle/eventStatics", params);
}

//车辆管理大屏 - 今日出车数相关
export function vehicleDayStaticsApi(params) {
  return get(BASE_API + "/api/query/vehicle/vehicleDayStatics", params);
}

//车辆管理大屏 - 今日异常上报一览
export function vehicleAbnormalDayApi(params) {
  return get(BASE_API + "/api/query/vehicle/vehicleAbnormalDay", params);
}

//车辆管理大屏 - 各区域分配车辆统计
export function vehicleAreaStaticsApi(params) {
  return get(BASE_API + "/api/query/vehicle/areaStatics", params);
}

//车辆管理大屏 - 总数统计
export function vehicleTotalStaticsApi(params) {
  return get(BASE_API + "/api/query/vehicle/totalStatics", params);
}

//车辆管理大屏 - 本年车辆管理情况
export function vehicleManageMonthStaticsApi(params) {
  return get(BASE_API + "/api/query/vehicle/vehicleManageMonthStatics", params);
}
