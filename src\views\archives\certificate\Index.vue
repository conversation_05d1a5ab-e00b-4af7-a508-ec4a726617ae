<template>
  <div class="micro-app-sctmp_base full-height">
    <defaultPage :topInfo="topInfo" @createRecord="createRecord" @closeRecord="closeRecord">
      <div class="main-index">
        <header class="header">
          <div class="header-input">
            <el-input
              class="w250"
              v-model="filterForm.keyword"
              placeholder="请输入所属人员/证件号"
              clearable
            ></el-input>
          </div>
          <InterviewChannel :value.sync="filterForm.channelId" :record.sync="channelRecord"></InterviewChannel>
          <el-form ref="filterForm" :model="filterForm" label-suffix=":" label-width="80px" class="mr-10">
            <el-form-item label="证件类型">
              <el-select v-model="filterForm.certType" placeholder="请选择证件类型" clearable filterable>
                <el-option v-for="(item, index) in CERT_TYPES" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" @click="handleExport">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button>
          </div>
        </header>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column prop="certType" label="证件类型" align="center">
              <template #default="{ row }">{{ CERT_TYPES[row.certType] }}</template>
            </el-table-column>
            <el-table-column prop="fullName" label="所属人员" align="center">
              <template #default="{ row }">{{ row.fullName || "-" }}</template>
            </el-table-column>
            <el-table-column prop="certNo" label="证件号" align="center"> </el-table-column>
            <el-table-column prop="photoFront" label="证件照片-正面" align="center">
              <template #default="{ row }">
                <el-image
                  class="photo-img"
                  v-if="row.photoFront?.url"
                  :src="row.photoFront.url"
                  :preview-src-list="[row.photoFront.url]"
                ></el-image>
                <div v-else>-</div>
              </template>
            </el-table-column>
            <el-table-column prop="photoBack" label="证件照片-反面" align="center">
              <template #default="{ row }">
                <el-image
                  v-if="row.photoBack?.url"
                  class="photo-img"
                  :src="row.photoBack.url"
                  :preview-src-list="[row.photoBack.url]"
                ></el-image>
                <div v-else>-</div>
              </template>
            </el-table-column>
            <el-table-column prop="validityBeginDate" label="有效期开始时间" align="center"></el-table-column>
            <el-table-column prop="validityEndDate" label="有效期截止时间" align="center"></el-table-column>
            <el-table-column prop="plateNumber" label="所属车辆车牌号" align="center">
              <template #default="{ row }">{{ row.plateNumber || "-" }}</template>
            </el-table-column>
            <el-table-column prop="registrationDate" label="登记日期" align="center"></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import { CERT_TYPES } from "@/enums";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, getInfoApiFun, deleteApiFun, BASE_API_URL } from "@/api/base";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  export default {
    components: {
      defaultPage,
      Pagination,
    },
    data() {
      return {
        topInfo: {
          buttonName: "新增",
          subTitle: "列表",
          buttonShow: true,
          buttonPermission: "create",
        },
        filterForm: {},
        CERT_TYPES,
        positionOptions: [],
        deptOptions: [],
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/certificate/listPage",
          positionList: "/api/dict/position/list",
          export: "/api/certificate/exportExcelCertificate",
          delete: "/api/certificate/delete/",
        },
        showRecord: false,
        recordId: "",
        showRoleEdit: false,
        roleOptions: [], //角色列表
        lgUnionId: "",
        channelRecord: {},
      };
    },
    mounted() {
      this.initData();
      this.getPositionList();
    },
    methods: {
      // 获取职位列表
      async getPositionList() {
        let res = await getInfoApiFun("", this.apis.positionList);
        if (res.success) {
          this.positionOptions = res.data;
        }
      },
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.tableData.forEach((item) => {
            try {
              item.photoFront = JSON.parse(item.photoFront);
              item.photoBack = JSON.parse(item.photoBack);
            } catch (error) {
              item.photoFront = "";
              item.photoBack = "";
            }
          });
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showRecord = true;
        this.topInfo.buttonShow = false;
        this.topInfo.subTitle = "新增";
        this.recordId = "";
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.topInfo.buttonShow = false;
        this.topInfo.subTitle = "编辑";
        this.recordId = row.id;
      },
      //删除
      async deleteRecord(row) {
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      closeRecord() {
        this.showRecord = false;
        this.topInfo.buttonShow = true;
        this.topInfo.subTitle = "列表";
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      //导出
      async handleExport() {
        this.$confirm(`确认是否以当前筛选条件导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, this.filterForm);
              if (res.success) {
                createDownloadEvent(`证件管理列表${Date.now()}.xlsx`, [res.data]);
                window.ELEMENT.Message.success("导出成功");
              }
            } catch (error) {
              console.warn(error);
            }
          })
          .catch(() => {});
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-input {
      margin-right: 10px;
    }
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w250 {
    width: 250px;
  }
  .photo-img {
    width: 60px;
  }
  ::v-deep .header .el-form-item {
    margin-bottom: 0;
  }
</style>
