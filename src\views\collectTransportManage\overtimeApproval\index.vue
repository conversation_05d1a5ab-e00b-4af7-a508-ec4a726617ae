<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <record v-if="showRecord" :recordId="recordId" @closeRecord="closeRecord" @refreshList="initData"></record>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="keyword" placeholder="请输入司机/押运工姓名/车牌号" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
            :colNum="12"
          ></InterviewChannel>
          <el-col :span="12">
            <el-form-item label="加班类型" prop="type">
              <el-select class="w300" v-model="filterForm.type" placeholder="请选择加班类型" clearable filterable>
                <el-option v-for="(item, index) in POINT_TYPE" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审批状态" prop="verifyStatus">
              <el-select
                class="w300"
                v-model="filterForm.verifyStatus"
                placeholder="请选择审批状态"
                clearable
                filterable
              >
                <el-option
                  v-for="(item, index) in OVERTIME_APPROVAL_STATUS"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="提交时间范围">
              <el-date-picker
                v-model="filterForm.submitTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column prop="applyNumber" label="申请编号" align="center"> </el-table-column>
            <el-table-column prop="applyDate" label="提交时间" align="center"> </el-table-column>
            <el-table-column prop="submitUserName" label="提交人" align="center"> </el-table-column>
            <el-table-column prop="defaultDriverDossierName" label="司机" align="center"></el-table-column>
            <el-table-column prop="supercargoDossierOneName" label="押运工1" align="center"></el-table-column>
            <el-table-column prop="supercargoDossierTwoName" label="押运工2" align="center"></el-table-column>
            <el-table-column prop="defaultVehiclePlateNumber" label="车辆信息" align="center"></el-table-column>
            <el-table-column prop="pointNumber" label="加班收运点位数量" align="center"></el-table-column>
            <el-table-column prop="type" label="加班类型" align="center">
              <template #default="{ row }">
                <span v-for="(item, index) in row.type.split(',')" :key="index">
                  <span>{{ POINT_TYPE[item] }}</span>
                  <span v-if="index != row.type.split(',').length - 1">,</span>
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="verifyStatus" label="审批状态" align="center">
              <template #default="{ row }">
                <el-tag :type="overTimeApprovalStatusType[row.verifyStatus]" effect="dark">
                  {{ OVERTIME_APPROVAL_STATUS[row.verifyStatus] }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="verifyUserName" label="审批人" align="center"></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <template>
                  <el-link type="primary" @click="editRecord(row)" v-if="row.verifyStatus != 0">查看详情</el-link>
                  <el-link type="primary" @click="editRecord(row)" v-else>去审批</el-link>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, createApiFun } from "@/api/base";
  import record from "./components/record.vue";
  import moment from "moment";
  import { POINT_TYPE, OVERTIME_APPROVAL_STATUS } from "@/enums";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
    },
    data() {
      return {
        moment,
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/overtime/listPage",
          carList: "/api/vehicle/dossier/list",
        },
        showRecord: false,
        recordId: "",
        loading: false,
        showFilter: false,
        keyword: "",
        carOptions: [],
        POINT_TYPE,
        OVERTIME_APPROVAL_STATUS,
        overTimeApprovalStatusType: ["warning", "success", "danger"],
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => (Array.isArray(arr) ? arr.length > 0 : arr) || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.getOptions();
      this.initData();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({ statusList: [0, 1] }, this.apis.carList)];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
      },
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          type: this.filterForm.type,
          verifyStatus: this.filterForm.verifyStatus,
          applyBeginTime: this.filterForm.submitTime ? this.filterForm.submitTime[0] : "",
          applyEndTime: this.filterForm.submitTime ? this.filterForm.submitTime[1] : "",
          channelId: this.filterForm.channelId || "",
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      closeRecord() {
        this.showRecord = false;
        this.showHandle = false;
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      editRecord(row) {
        this.recordId = row.id;
        this.showRecord = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w250 {
    width: 250px;
  }
  .w300 {
    width: 300px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
