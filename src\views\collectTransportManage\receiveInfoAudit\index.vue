<template>
  <div class="micro-app-sctmp_base full-height">
    <defaultPage>
      <div class="main-index" v-loading="loading">
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="productionUnit" placeholder="请输入产废单位名称" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
            :colNum="12"
          ></InterviewChannel>
          <el-col :span="12">
            <el-form-item label="收运状态">
              <el-select v-model="filterForm.waybillStatus" placeholder="请选择收运状态" clearable filterable>
                <el-option
                  v-for="(item, index) in RECEIVING_CONDITION"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收运日期范围">
              <el-date-picker
                v-model="filterForm.waybillTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收运有效日期范围">
              <el-date-picker
                v-model="filterForm.effectiveDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务类型">
              <el-select v-model="filterForm.detailType" placeholder="请选择任务类型" clearable filterable>
                <el-option
                  v-for="(item, index) in POINT_TASK_TYPE"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table
            ref="tableRef"
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            height="100%"
            border
          >
            <el-table-column width="50" type="index" label="顺序" align="center"></el-table-column>
            <el-table-column prop="code" label="点位编号" align="center"></el-table-column>
            <el-table-column prop="productionUnit" label="产废单位名称" align="center"></el-table-column>
            <el-table-column prop="pickupPathName" label="所属路线" align="center"></el-table-column>
            <el-table-column label="车辆" align="center" min-width="120px">
              <template #default="{ row }">
                <span
                  :class="{ 'rubbish-danger': row.defaultVehiclePlateNumber !== row.oldDefaultVehiclePlateNumber }"
                  >{{ row.defaultVehiclePlateNumber }}</span
                >
                <span v-if="row.defaultVehiclePlateNumber !== row.oldDefaultVehiclePlateNumber"
                  >（{{ row.oldDefaultVehiclePlateNumber }}）</span
                >
              </template>
            </el-table-column>
            <el-table-column label="司机" align="center" min-width="120px">
              <template #default="{ row }">
                <span :class="{ 'rubbish-danger': row.defaultDriverDossierName !== row.oldDefaultDriverDossierName }">{{
                  row.defaultDriverDossierName
                }}</span>
                <span v-if="row.defaultDriverDossierName !== row.oldDefaultDriverDossierName"
                  >（{{ row.oldDefaultDriverDossierName }}）</span
                >
              </template>
            </el-table-column>
            <el-table-column label="押运工1" align="center" min-width="120px">
              <template #default="{ row }">
                <span
                  :class="{ 'rubbish-danger': !row.supercargoDossierOneName !== !row.oldSupercargoDossierOneName }"
                  >{{ row.supercargoDossierOneName || "-" }}</span
                >
                <span v-if="!row.supercargoDossierOneName !== !row.oldSupercargoDossierOneName"
                  >（{{ row.oldSupercargoDossierOneName || "-" }}）</span
                >
              </template>
            </el-table-column>
            <el-table-column label="押运工2" align="center" min-width="120px">
              <template #default="{ row }">
                <span
                  :class="{ 'rubbish-danger': !row.supercargoDossierTwoName !== !row.oldSupercargoDossierTwoName }"
                  >{{ row.supercargoDossierTwoName || "-" }}</span
                >
                <span v-if="!row.supercargoDossierTwoName !== !row.oldSupercargoDossierTwoName"
                  >（{{ row.oldSupercargoDossierTwoName || "-" }}）</span
                >
              </template>
            </el-table-column>
            <el-table-column prop="operation" label="是否正常经营" align="center">
              <template #default="{ row }">{{ IS_NORMAL_OPERATION[row.operation] }}</template>
            </el-table-column>
            <el-table-column prop="detailType" label="任务类型" align="center">
              <template #default="{ row }">{{ POINT_TASK_TYPE[row.detailType] }}</template>
            </el-table-column>
            <el-table-column prop="overType" label="加班类型" align="center">
              <template #default="{ row }">{{
                row.overType || row.overType === 0 ? OVERTIME_TYPE[row.overType] : ""
              }}</template>
            </el-table-column>
            <el-table-column prop="rubbishTotal" label="废物总量（kg）" align="center" min-width="140">
              <template #default="{ row }">
                <template v-if="row.waybillStatus == 1">
                  <span :class="{ 'rubbish-danger': row.newRubbishTotal !== row.oldRubbishTotal }">{{
                    row.newRubbishTotal
                  }}</span>
                  <span v-if="row.newRubbishTotal !== row.oldRubbishTotal">（{{ row.oldRubbishTotal }}）</span>
                </template>
                <span v-else>{{ row.rubbishTotal }}</span>
              </template>
            </el-table-column>
            <el-table-column label="废物类型/重量（kg）" align="center">
              <el-table-column :label="item.label" align="center" v-for="item in ruleFormArr" :key="item.key">
                <template #default="{ row }">
                  <template v-if="row.waybillStatus == 1">
                    <span :class="{ 'rubbish-danger': row[item.newKey] !== row[item.key] }">{{
                      row[item.newKey]
                    }}</span>
                    <span v-if="row[item.newKey] !== row[item.key]">（{{ row[item.key] }}）</span>
                  </template>
                  <span v-else>{{ row[item.key] }}</span>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column prop="waybillStatus" label="收运状态" align="center" min-width="90">
              <template #default="{ row }">{{ RECEIVING_CONDITION[row.waybillStatus] }}</template>
            </el-table-column>
            <el-table-column prop="waybillTime" label="收运时间" align="center" min-width="90"></el-table-column>
            <el-table-column prop="effectiveDate" label="收运生效日期" align="center" min-width="120"></el-table-column>
            <el-table-column prop="endDate" label="收运截止日期" align="center" min-width="120"></el-table-column>
            <el-table-column prop="isClear" label="是否清空" align="center" min-width="90">
              <template #default="{ row }">{{ IS_NORMAL_OPERATION[row.isClear] }}</template>
            </el-table-column>
            <el-table-column prop="baggingMethod" label="桶装/袋装" align="center">
              <template #default="{ row }">{{ BARRELS_BAGS[row.baggingMethod] }}</template>
            </el-table-column>
            <el-table-column prop="verifyStatus" label="当前流程" align="center">
              <template #default="{ row }">{{ VERIFY_STATUS[row.verifyStatus] }}</template>
            </el-table-column>
            <el-table-column prop="verifyUserName" label="确认人" align="center"></el-table-column>
            <el-table-column prop="verifyTime" label="确认时间" align="center"></el-table-column>
            <el-table-column prop="longitude" label="经度" align="center"></el-table-column>
            <el-table-column prop="latitude" label="纬度" align="center"></el-table-column>
            <el-table-column prop="userLongitude" label="上报经度" align="center"></el-table-column>
            <el-table-column prop="userLatitude" label="上报纬度" align="center"></el-table-column>
            <el-table-column prop="distance" label="对比距离（m）" align="center" min-width="120"></el-table-column>
            <el-table-column prop="address" label="地址" align="center"></el-table-column>
            <el-table-column prop="image" label="图片" align="center" min-width="90">
              <template #default="{ row }">
                <el-badge
                  v-if="row.picture && row.picture.length > 0"
                  :value="row.picture.length > 1 ? row.picture.length : ''"
                  class="picture-badge"
                  type="success"
                >
                  <el-image
                    class="picture-img"
                    fit="cover"
                    :src="row.picture[0].url"
                    :preview-src-list="row.picture.map((i) => i.url)"
                    v-if="row.picture"
                  ></el-image>
                </el-badge>
              </template>
            </el-table-column>
            <el-table-column prop="productionUnitOperator" label="产废单位经办人" align="center"></el-table-column>
            <el-table-column prop="residueRubbish" label="剩余垃圾" align="center"></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column prop="auditStatus" label="审核状态" align="center" min-width="120" fixed="right">
              <template #default="{ row }">
                <el-tag type="warning" v-if="!isSameMonth(row.createTime) && row.auditStatus == 0">已超时</el-tag>
                <el-tag v-else :type="auditStatusType[row.auditStatus]" effect="dark">
                  {{ AUDIT_STATUS[row.auditStatus] }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column min-width="120" label="操作" align="center" fixed="right">
              <template #default="{ row }">
                <template v-if="isSameMonth(row.createTime) && row.auditStatus == 0">
                  <el-link class="mr-10" type="primary" @click="handleAudit(row, 1)">通过</el-link>
                  <el-link type="primary" @click="handleAudit(row, 2)">拒绝</el-link>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, createApiFun } from "@/api/base";
  import {
    RECEIVING_CONDITION,
    POINT_TASK_TYPE,
    IS_NORMAL_OPERATION,
    BARRELS_BAGS,
    VERIFY_STATUS,
    AUDIT_STATUS,
    OVERTIME_TYPE,
  } from "@/enums";
  import { isSameMonth } from "@/utils";
  export default {
    components: {
      defaultPage,
      Pagination,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/waybill/detail/updateRecord/listPage",
          audit: "/api/waybill/detail/updateRecord/audit",
        },
        RECEIVING_CONDITION,
        POINT_TASK_TYPE,
        IS_NORMAL_OPERATION,
        BARRELS_BAGS,
        VERIFY_STATUS,
        AUDIT_STATUS,
        OVERTIME_TYPE,
        loading: false,
        showFilter: false,
        productionUnit: "",
        isSameMonth,
        ruleFormArr: [
          { key: "oldInfectiousWaste", newKey: "newInfectiousWaste", label: "感染性废物" },
          { key: "oldDamagingWaste", newKey: "newDamagingWaste", label: "损伤性废物" },
          { key: "oldPharmaceuticalWaste", newKey: "newPharmaceuticalWaste", label: "药物性废物" },
          { key: "oldPathologicalWaste", newKey: "newPathologicalWaste", label: "病理性废物" },
          { key: "oldChemicalWaste", newKey: "newChemicalWaste", label: "化学性废物" },
          { key: "oldSludge", newKey: "newSludge", label: "感染性废物-污泥" },
        ],
        auditStatusType: ["warning", "success", "danger"],
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    created() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          productionUnit: this.productionUnit,
          startWaybillTime: this.filterForm.waybillTime ? this.filterForm.waybillTime[0] : "",
          endWaybillTime: this.filterForm.waybillTime ? this.filterForm.waybillTime[1] : "",
          effectiveEndBeginTime: this.filterForm.effectiveDate ? this.filterForm.effectiveDate[0] : "",
          effectiveEndEndTime: this.filterForm.effectiveDate ? this.filterForm.effectiveDate[1] : "",
          waybillStatus: this.filterForm.waybillStatus,
          detailType: this.filterForm.detailType,
          defaultDriverDossierId: this.filterForm.defaultDriverDossierId,
          supercargoDossierId: this.filterForm.supercargoDossierId,
          channelId: this.filterForm.channelId || "",
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.tableData.forEach((data) => {
            try {
              data.picture = JSON.parse(data.picture);
            } catch (error) {
              data.picture = "";
            }
          });
          this.page.total = res.data.total;
          await this.$nextTick();
          this.$refs.tableRef.doLayout();
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.productionUnit = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 审核
      async handleAudit(row, auditStatus) {
        this.loading = true;
        try {
          let res = await createApiFun({ id: row.id, auditStatus }, this.apis.audit);
          if (res.success) {
            this.$message.success("审核成功");
            this.initData();
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .w250 {
    width: 250px;
  }
  .picture-img {
    width: 60px;
    height: 60px;
  }
  .map-box {
    width: 1px;
    height: 1px;
    position: fixed;
    top: -9999px;
    left: -9999px;
    opacity: 0;
  }
  .rubbish-danger {
    color: #e83f3f;
  }
  ::v-deep .el-badge__content.is-fixed {
    top: 8px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
