<template>
  <div class="management">
    <main-title title="本年车辆管理情况"></main-title>
    <div class="chart-data">
      <Echart :chartData="chartData" chartType="bar" key="chart234232" style="width: 100%; height: 100%"></Echart>
    </div>
  </div>
</template>

<script>
  import Echart from "@/components/echart/index.vue";
  import MainTitle from "./MainTitle.vue";
  export default {
    components: {
      MainTitle,
      Echart,
    },
    props: {
      vehicleManageMonthStatics: {
        type: Array,
        default: () => [],
      },
    },
    watch: {
      vehicleManageMonthStatics: {
        handler(newV) {
          this.chartData.xAxis.data = newV.map((item) => item.month + "月");
          this.chartData.series[0].data = newV.map((item) => item?.maintenanceQuantity || 0);
          this.chartData.series[1].data = newV.map((item) => item?.repairQuantity || 0);
          this.chartData.series[2].data = newV.map((item) => item?.insureQuantity || 0);
          this.chartData.series[3].data = newV.map((item) => item?.annualReviewQuantity || 0);
        },
        immediate: true,
        deep: true,
      },
    },
    data() {
      return {
        iconData: {
          numbg: require("@/assets/images/numbg.png"),
        },
        chartData: {
          tooltip: {
            trigger: "axis",
          },
          legend: {
            show: true,
            itemHeight: 8,
            itemWidth: 8,
            left: "center",
            data: ["车辆保养数量", "车辆维修数量", "车辆投保数量", "车辆年审数量"],
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          xAxis: {
            type: "category",
            boundaryGap: false,
            data: [],
            axisLine: {
              show: false, // 去掉Y轴线
            },
          },
          yAxis: {
            name: "单位：件",
            nameTextStyle: {
              color: "#c9c9c9",
              fontSize: 12,
            },
            type: "value",
            axisLine: {
              show: false, // 去掉Y轴线
              lineStyle: {
                color: "transparent",
              },
            },
            axisTick: {
              show: false, // 去掉Y轴刻度线
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "rgba(255,255,255,0.2)",
              },
            },
          },
          series: [
            {
              name: "车辆保养数量",
              type: "line",
              showSymbol: false,
              data: [],
            },
            {
              name: "车辆维修数量",
              type: "line",
              showSymbol: false,
              data: [],
            },
            {
              name: "车辆投保数量",
              type: "line",
              showSymbol: false,
              data: [],
            },
            {
              name: "车辆年审数量",
              type: "line",
              showSymbol: false,
              data: [],
            },
          ],
          color: ["#009BFF", "#9AB4FF", "#74EEBF", "#AF86F8"],
        },
      };
    },
    mounted() {},
    methods: {},
  };
</script>

<style lang="scss" scoped>
  .management {
    width: 480px;
    position: absolute;
    top: 710px;
    left: 40px;
    z-index: 1;
  }
  .chart-data {
    width: 100%;
    height: 300px;
  }
</style>
