<template>
  <div class="sub-box">
    <div class="sub-box-nav">
      <el-row :gutter="10">
        <el-col :span="6">
          <div class="sub-box-title">{{ $route.meta?.title || "" }}</div>
        </el-col>
        <el-col :span="18" style="text-align: right">
          <el-button type="primary" size="small" @click="editRecord">新增{{ $route.meta?.title }}</el-button>
        </el-col>
      </el-row>
    </div>
    <div class="sub-box-main">
      <div class="sub-search">
        <el-row :gutter="10">
          <el-col :span="6">
            <el-input
              placeholder="请输入排放标准"
              prefix-icon="el-icon-search"
              v-model="searchValue"
              size="small"
              clearable
              @change="initData"
            >
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" size="small" plain icon="el-icon-search" @click="refresh">搜索</el-button>
          </el-col>
        </el-row>
      </div>
      <div class="sub-table">
        <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" style="width: 100%" border>
          <el-table-column prop="sort" label="序号" align="center"> </el-table-column>
          <el-table-column prop="name" label="排放标准" align="center"> </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                type="text"
                class="btn-color"
                @click="editRecord(scope.row)"
                size="mini"
                style="margin-right: 6px"
                >编辑</el-button
              >
              <el-popconfirm
                confirmButtonType="danger"
                confirmButtonText="删除"
                cancelButtonText="取消"
                icon="el-icon-info"
                iconColor="#c21d26"
                title="确认要删除吗？"
                @confirm="deleteRecord(scope.row)"
              >
                <el-button type="text" slot="reference" size="mini">删除</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <Pagination :page="page" @pageChange="pageChange"></Pagination>
    </div>
    <record :value.sync="isEdit" :apis="apis" :recordId="recordId" @refresh="refresh"></record>
  </div>
</template>
<script>
  import { getListPageApiFun, deleteApiFun } from "@/api/base.js";
  import Pagination from "@/components/pagination-v";
  import record from "./components/record.vue";
  export default {
    components: {
      Pagination,
      record,
    },
    data() {
      return {
        isEdit: false,
        tableData: [],
        recordId: "",
        searchValue: "",
        apis: {
          listPage: "/api/dict/emissionStandard/listPage",
          create: "/api/dict/emissionStandard/create",
          update: "/api/dict/emissionStandard/update",
          info: "/api/dict/emissionStandard/get/",
          delete: "/api/dict/emissionStandard/delete/",
        },
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
      };
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        try {
          let params = {
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            name: this.searchValue,
          };
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (!res) return;
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        } catch (error) {
          console.log("出现错误", error);
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      editRecord(e = null) {
        this.recordId = e?.id || null;
        this.editModeFun();
      },
      async deleteRecord(e) {
        try {
          let res = await deleteApiFun(e.id, this.apis.delete);
          if (!res) return;
          this.$message.success(`删除成功`);
          this.initData();
        } catch (error) {
          console.log(error);
        }
      },
      editModeFun() {
        this.isEdit = true;
      },
      refresh() {
        this.page.pageNo = 1;
        this.initData();
        this.isEdit = false;
      },
    },
  };
</script>
<style scoped></style>
