<template>
  <div class="micro-app-sctmp_base full-height">
    <defaultIndex v-show="showType == 0" @changeType="changeType"></defaultIndex>
    <dropRecord v-show="showType == 1" @changeType="changeType" ref="drop"></dropRecord>
    <transportRecord v-show="showType == 2" @changeType="changeType" ref="transport"></transportRecord>
  </div>
</template>

<script>
  import defaultIndex from "./components/defaultIndex.vue";
  import dropRecord from "./components/dropRecord.vue";
  import transportRecord from "./components/transportRecord.vue";
  export default {
    components: {
      defaultIndex,
      dropRecord,
      transportRecord,
    },
    data() {
      return {
        showType: 0,
      };
    },
    methods: {
      changeType(params) {
        let type = params.type;
        this.showType = type;
        switch (type) {
          case 1:
            this.$refs.drop.initData(params.id);
            break;
          case 2:
            this.$refs.transport.initData(params.id);
            break;
        }
      },
    },
  };
</script>

<style lang="scss" scoped></style>
