<template>
  <div class="micro-app-sctmp_base" v-loading="loading">
    <el-dialog
      title="当月油耗排名"
      :visible.sync="dialogVisible"
      width="1400px"
      top="0"
      destroy-on-close
      :modal-append-to-body="false"
      :before-close="closeDialog"
      @open="initData"
    >
      <div class="full-height" v-loading="loading">
        <div class="main-index">
          <header class="header">
            <div class="header-input">
              <el-input class="w250" v-model="filterForm.plateNumber" placeholder="请输入车牌号" clearable></el-input>
            </div>
            <el-form ref="filterForm" :model="filterForm" label-suffix=":" label-width="80px" class="mr-10">
              <el-form-item label="车辆性质">
                <el-select v-model="filterForm.operationalNature" placeholder="请选择车辆性质" clearable filterable>
                  <el-option
                    v-for="(item, index) in VEHICLE_NATURE"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-form>
            <el-button @click="initData">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
          </header>
          <main class="main">
            <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
              <el-table-column type="index" align="center"></el-table-column>
              <el-table-column prop="plateNumber" label="车牌号" align="center"> </el-table-column>
              <el-table-column prop="operationalNature" label="车辆性质" align="center">
                <template #default="{ row }">{{ VEHICLE_NATURE[row.operationalNature] }}</template>
              </el-table-column>
              <el-table-column prop="totalFuelQuantity" label="当月加油量（L）" align="center"></el-table-column>
              <el-table-column prop="amount" label="当月加油金额（元）" align="center"> </el-table-column>
              <el-table-column prop="fuelRate" label="当月油耗（L/100km）" align="center"></el-table-column>
            </el-table>
          </main>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getInfoApiFunByParams } from "@/api/base";
  import { VEHICLE_NATURE } from "@/enums";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        tableData: [],
        apis: {
          listPage: "/api/query/vehicle/getVehicleFuelConsumption",
        },
        loading: false,
        plateNumber: "",
        options: [], //用户列表
        filterForm: {},
        VEHICLE_NATURE,
      };
    },
    mounted() {},
    methods: {
      initData() {
        this.plateNumber = "";
        this.filterForm = {};
        this.getDataList();
      },
      async getDataList() {
        this.loading = true;
        try {
          let res = await getInfoApiFunByParams(
            { plateNumber: this.plateNumber, ...this.filterForm, channelId: this.channelId },
            this.apis.listPage,
          );
          if (res.success) {
            this.tableData = res.data;
          }
          this.loading = false;
        } catch (_) {
          this.loading = false;
        }
      },
      // 查询
      searchFilter() {
        this.getDataList();
      },
      closeDialog() {
        this.dialogVisible = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-input {
      margin-right: 10px;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w400 {
    width: 400px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .w250 {
    width: 250px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
  ::v-deep .header .el-form-item {
    margin-bottom: 0;
  }
</style>
