<template>
  <div class="micro-app-sctmp_base full-height">
    <defaultIndex v-show="showType == 0" @changeType="changeType"></defaultIndex>
    <routeRecord v-show="showType == 1" @changeType="changeType" ref="routeRecord"></routeRecord>
  </div>
</template>

<script>
  import defaultIndex from "./components/defaultIndex.vue";
  import routeRecord from "./components/routeRecord.vue";
  export default {
    components: {
      defaultIndex,
      routeRecord,
    },
    data() {
      return {
        showType: 0,
      };
    },
    methods: {
      changeType(params) {
        let type = params.type;
        this.showType = type;
        switch (type) {
          case 1:
            this.$refs.routeRecord.initData(params.id, params.date, params.channelId);
            break;
        }
      },
    },
  };
</script>

<style lang="scss" scoped></style>
