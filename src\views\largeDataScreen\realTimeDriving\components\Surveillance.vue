<template>
  <div class="micro-app-sctmp_base">
    <Title title="车辆视频监控信息"></Title>
    <div class="tab-list">
      <div
        class="tab-item"
        :class="{ active: activeTab === item.channel }"
        v-for="item in tabList"
        :key="item.channel"
        @click="toggleTab(item.channel)"
        >{{ item.name }}</div
      >
    </div>
    <div class="video-container">
      <el-tabs :value="String(activeTab)">
        <el-tab-pane label="" name="1">
          <li class="video-item" v-loading="loading1">
            <video class="video-box" ref="flvPlayer1" autoplay muted></video>
            <div class="videoSrc-bg" :class="{ fail: loadingEnd1 }">
              <div v-if="unSupported1">当前浏览器不支持，请更换其他浏览器</div>
              <div class="loading-box" v-else-if="loadingEnd1">
                <div class="loading-text" v-if="offline1">车辆静止&nbsp;&nbsp;设备离线</div>
                <span class="loading-button" @click="reloadVideo(1)">重新加载</span>
              </div>
            </div>
          </li>
        </el-tab-pane>
        <el-tab-pane label="" name="2">
          <li class="video-item" v-loading="loading2">
            <video class="video-box" ref="flvPlayer2" autoplay muted></video>
            <div class="videoSrc-bg" :class="{ fail: loadingEnd2 }">
              <div v-if="unSupported2">当前浏览器不支持，请更换其他浏览器</div>
              <div class="loading-box" v-else-if="loadingEnd2">
                <div class="loading-text" v-if="offline2">车辆静止&nbsp;&nbsp;设备离线</div>
                <span class="loading-button" @click="reloadVideo(2)">重新加载</span>
              </div>
            </div>
          </li>
        </el-tab-pane>
        <el-tab-pane label="" name="3">
          <li class="video-item" v-loading="loading3">
            <video class="video-box" ref="flvPlayer3" autoplay muted></video>
            <div class="videoSrc-bg" :class="{ fail: loadingEnd3 }">
              <div v-if="unSupported3">当前浏览器不支持，请更换其他浏览器</div>
              <div class="loading-box" v-else-if="loadingEnd3">
                <div class="loading-text" v-if="offline3">车辆静止&nbsp;&nbsp;设备离线</div>
                <span class="loading-button" @click="reloadVideo(3)">重新加载</span>
              </div>
            </div>
          </li>
        </el-tab-pane>
        <el-tab-pane label="" name="4">
          <li class="video-item" v-loading="loading4">
            <video class="video-box" ref="flvPlayer4" autoplay muted></video>
            <div class="videoSrc-bg" :class="{ fail: loadingEnd4 }">
              <div v-if="unSupported4">当前浏览器不支持，请更换其他浏览器</div>
              <div class="loading-box" v-else-if="loadingEnd4">
                <div class="loading-text" v-if="offline4">车辆静止&nbsp;&nbsp;设备离线</div>
                <span class="loading-button" @click="reloadVideo(4)">重新加载</span>
              </div>
            </div>
          </li>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
  import Title from "@/views/largeDataScreen/realTimeMonitor/components/Title";
  import { getListPageApiFun } from "@/api/base.js";
  import flvjs from "flv.js";
  export default {
    components: {
      Title,
    },
    data() {
      return {
        tabList: [
          {
            channel: 1,
            name: "司机正面",
          },
          {
            channel: 2,
            name: "车辆正面",
          },
          {
            channel: 3,
            name: "车辆侧面",
          },
          {
            channel: 4,
            name: "司机侧面",
          },
        ],
        activeTab: 1,
        apis: {
          postData: "/api/monitor/real-time",
        },
        maxTime: 60000,
        videoSrc1: "",
        videoSrc2: "",
        videoSrc3: "",
        videoSrc4: "",
        flvPlayer1: null,
        flvPlayer2: null,
        flvPlayer3: null,
        flvPlayer4: null,
        time1: null,
        time2: null,
        time3: null,
        time4: null,
        currentTime1: 0,
        currentTime2: 0,
        currentTime3: 0,
        currentTime4: 0,
        loading: false,
        loading1: true,
        loading2: false,
        loading3: false,
        loading4: false,
        loadingEnd1: false,
        loadingEnd2: false,
        loadingEnd3: false,
        loadingEnd4: false,
        unSupported1: false,
        unSupported2: false,
        unSupported3: false,
        unSupported4: false,
        plateNumber: "",
        overTimeCount: 600,
        overTimeArray: [
          {
            currentTime: 0,
            time: null,
          },
          {
            currentTime: 0,
            time: null,
          },
          {
            currentTime: 0,
            time: null,
          },
          {
            currentTime: 0,
            time: null,
          },
        ],
        offline1: false,
        offline2: false,
        offline3: false,
        offline4: false,
      };
    },
    created() {
      this.plateNumber = this.$route.query.plateNumber;
    },
    watch: {
      async $route(newV, oldVal) {
        this.plateNumber = newV.query.plateNumber;
        this.overTimeArray.forEach((item) => {
          item.currentTime = 0;
          clearInterval(item.time);
        });
        if (oldVal?.query?.plateNumber) {
          for (let index = 0; index < this.tabList.length; index++) {
            await this.endData(this.tabList[index].channel, oldVal.query.plateNumber);
          }
        }
        for (let i = 1; i < 5; i++) {
          clearInterval(this[`time${i}`]);
          this[`loading${i}`] = false;
          this[`loadingEnd${i}`] = false;
          this[`currentTime${i}`] = 0;
          this[`unSupported${i}`] = false;
          this[`offline${i}`] = false;
        }
        this.postData();
      },
    },
    mounted() {
      this.postData();
    },
    async beforeDestroy() {
      for (let i = 1; i < 5; i++) {
        clearInterval(this[`time${i}`]);
        this[`loading${i}`] = false;
        this[`loadingEnd${i}`] = false;
        this[`currentTime${i}`] = 0;
        this[`unSupported${i}`] = false;
        this[`offline${i}`] = false;
      }
      this.overTimeArray.forEach((item) => {
        item.currentTime = 0;
        clearInterval(item.time);
      });
      for (let index = 0; index < this.tabList.length; index++) {
        this.endData(this.tabList[index].channel, this.plateNumber);
      }
    },
    methods: {
      async toggleTab(channel) {
        if (this.activeTab === channel) return;
        this.activeTab = channel;
      },
      initPlayer(index) {
        if (flvjs.isSupported()) {
          if (!this[`videoSrc${index}`]) return;
          this[`loading${index}`] = true;
          this[`loadingEnd${index}`] = false;
          this[`currentTime${index}`] = 0;
          this[`offline${index}`] = false;
          this.loopFlvPlayer(index);
        } else {
          this[`loading${index}`] = false;
          this[`loadingEnd${index}`] = true;
          this[`unSupported${index}`] = true;
          this[`offline${index}`] = false;
          this.endData(index, this.plateNumber);
        }
      },
      loopFlvPlayer(index) {
        let videoElement = this.$refs[`flvPlayer${index}`];
        if (!videoElement) {
          console.error("Video element not found");
          this.endData(index, this.plateNumber);
          return;
        }
        this[`flvPlayer${index}`] = flvjs.createPlayer({
          type: "flv",
          url: this[`videoSrc${index}`],
        });
        this[`flvPlayer${index}`].attachMediaElement(videoElement);
        this[`flvPlayer${index}`].load();
        this[`time${index}`] = setInterval(() => {
          this[`currentTime${index}`] += 100;
          if (this[`flvPlayer${index}`].currentTime > 0) {
            // 假设如果currentTime大于0，视频已经开始播放
            this[`loading${index}`] = false; // 隐藏加载效果
            clearInterval(this[`time${index}`]);
          } else if (this[`currentTime${index}`] > this.maxTime) {
            this[`loading${index}`] = false; // 隐藏加载效果
            this[`loadingEnd${index}`] = true;
            this[`offline${index}`] = false;
            this[`flvPlayer${index}`].pause();
            this[`flvPlayer${index}`].unload();
            clearInterval(this[`time${index}`]);
            this.endData(index, this.plateNumber);
          }
        }, 100); // 你可以根据需要调整这个时间
        let playPromise = this[`flvPlayer${index}`].play();
        if (playPromise !== undefined) {
          playPromise
            .then((_) => {
              this[`loading${index}`] = false;
              this[`loadingEnd${index}`] = false;
              this[`unSupported${index}`] = false;
              this[`offline${index}`] = false;
              this.overTimeArray[index - 1].time = setInterval(() => {
                if (this.overTimeArray[index - 1].currentTime < this.overTimeCount) {
                  this.overTimeArray[index - 1].currentTime++;
                } else {
                  this.endData(index, this.plateNumber);
                }
              }, 1000);
            })
            .catch((error) => {});
        }
      },
      async postData() {
        if (!this.plateNumber) return;
        let promiseList = [
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 1,
              command: 1,
              streamType: 1,
            },
            this.apis.postData,
          ),
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 2,
              command: 1,
              streamType: 1,
            },
            this.apis.postData,
          ),
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 3,
              command: 1,
              streamType: 1,
            },
            this.apis.postData,
          ),
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 4,
              command: 1,
              streamType: 1,
            },
            this.apis.postData,
          ),
        ];
        this.loading1 = true;
        this.loading2 = true;
        this.loading3 = true;
        this.loading4 = true;
        try {
          let res = await Promise.allSettled(promiseList);
          if (res[0].status == "fulfilled") {
            if (res[0]?.value?.success && res[0]?.value?.data && res[0]?.value?.data !== "离线的客户端") {
              this.videoSrc1 = res[0]?.value?.data;
              this.initPlayer(1);
            } else {
              if (res[0]?.value?.data === "离线的客户端") {
                this.offline1 = true;
              }
              this.loading1 = false;
              this.loadingEnd1 = true;
              this.unSupported1 = false;
            }
          }
          if (res[1].status == "fulfilled") {
            if (res[1]?.value?.success && res[1]?.value?.data && res[1]?.value?.data !== "离线的客户端") {
              this.videoSrc2 = res[1]?.value?.data;
              this.initPlayer(2);
            } else {
              if (res[1]?.value?.data === "离线的客户端") {
                this.offline2 = true;
              }
              this.loading2 = false;
              this.loadingEnd2 = true;
              this.unSupported2 = false;
            }
          }
          if (res[2].status == "fulfilled") {
            if (res[2]?.value?.success && res[2]?.value?.data && res[2]?.value?.data !== "离线的客户端") {
              this.videoSrc3 = res[2]?.value?.data;
              this.initPlayer(3);
            } else {
              if (res[2]?.value?.data === "离线的客户端") {
                this.offline3 = true;
              }
              this.loading3 = false;
              this.loadingEnd3 = true;
              this.unSupported3 = false;
            }
          }
          if (res[3].status == "fulfilled") {
            if (res[3]?.value?.success && res[3]?.value?.data && res[3]?.value?.data !== "离线的客户端") {
              this.videoSrc4 = res[3]?.value?.data;
              this.initPlayer(4);
            } else {
              if (res[3]?.value?.data === "离线的客户端") {
                this.offline4 = true;
              }
              this.loading4 = false;
              this.loadingEnd4 = true;
              this.unSupported4 = false;
            }
          }
        } catch (error) {
          this.loading1 = false;
          this.loading2 = false;
          this.loading3 = false;
          this.loading4 = false;
          console.log(error);
        }
      },
      async endData(channel, plateNumber) {
        if (!plateNumber) return;
        clearInterval(this.overTimeArray[channel - 1].time);
        this.overTimeArray[channel - 1].currentTime = 0;
        clearInterval(this[`time${channel}`]);
        if (this[`flvPlayer${channel}`]) {
          this[`flvPlayer${channel}`].destroy();
          this[`flvPlayer${channel}`] = null;
        }
        try {
          this[`loading${channel}`] = true;
          await getListPageApiFun(
            {
              plateNumber,
              channel,
              command: 0,
              streamType: 1,
            },
            this.apis.postData,
          );
          this[`loading${channel}`] = false;
          this[`loadingEnd${channel}`] = true;
          this[`unSupported${channel}`] = false;
          this[`offline${channel}`] = false;
        } catch (error) {
          this[`loading${channel}`] = false;
          this[`loadingEnd${channel}`] = true;
          this[`unSupported${channel}`] = false;
          this[`offline${channel}`] = false;
          console.log(error);
        }
      },
      // 重新加载视频
      async reloadVideo(i) {
        clearInterval(this[`time${i}`]);
        this[`offline${i}`] = false;
        this[`loading${i}`] = false;
        this[`loadingEnd${i}`] = false;
        this[`unSupported${i}`] = false;
        if (this[`flvPlayer${i}`]) {
          this[`flvPlayer${i}`].destroy();
          this[`flvPlayer${i}`] = null;
        }
        try {
          this[`loading${i}`] = true;
          let res = await getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: i,
              command: 1,
              streamType: 1,
            },
            this.apis.postData,
          );
          if (res.data === "离线的客户端") {
            this[`loading${i}`] = false;
            this[`loadingEnd${i}`] = true;
            this[`unSupported${i}`] = false;
            this[`offline${i}`] = true;
            return;
          }
          this[`videoSrc${i}`] = res.data;
          if (this[`videoSrc${i}`]) {
            this[`loading${i}`] = true;
            this[`loadingEnd${i}`] = false;
            this[`unSupported${i}`] = false;
            this[`currentTime${i}`] = 0;
            this[`offline${i}`] = false;
            this.loopFlvPlayer(i);
          }
        } catch (error) {
          this[`loading${i}`] = false;
          this[`loadingEnd${i}`] = false;
          this[`unSupported${i}`] = false;
          this[`offline${i}`] = false;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .tab-list {
    margin: 20px 0;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    background-color: #adcad4;
    border-radius: 20px;
    pointer-events: auto;
    .tab-item {
      text-align: center;
      color: #fff;
      padding: 10px 0;
      font-size: 14px;
      border-radius: 20px;
      cursor: pointer;
      &.active {
        font-size: 16px;
        font-weight: bold;
        background-color: #64a1a7;
      }
    }
  }
  .video-container {
    width: 100%;
    height: 200px;
    margin-bottom: 20px;
    .video-item {
      width: 100%;
      height: 100%;
      overflow: hidden;
      position: relative;
      .video-box {
        width: 100%;
        height: 100%;
        position: relative;
        z-index: 2;
        overflow: hidden;
        transform: translateY(-18px);
      }
    }
  }
  .videoSrc-bg {
    display: flex;
    color: #fff;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    &.fail {
      z-index: 40;
    }
    .loading-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      .loading-button {
        margin-left: 4px;
        color: #f56c6c;
        cursor: pointer;
        pointer-events: auto;
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
  .loading-text {
    color: #f56c6c;
    margin-bottom: 10px;
  }
  ::v-deep .el-tabs__header {
    display: none;
  }
  ::v-deep .el-tabs,
  ::v-deep .el-tabs__content,
  ::v-deep .el-tab-pane {
    height: 100%;
  }
</style>
