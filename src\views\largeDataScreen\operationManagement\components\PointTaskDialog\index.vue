<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      :title="pointTaskData.name"
      :visible.sync="dialogVisible"
      width="80%"
      :show-close="true"
      :before-close="closeDialog"
      :modal-append-to-body="false"
      destroy-on-close
      @open="initData"
      :close-on-click-modal="false"
    >
      <Record @closeRecord="closeDialog" :recordItem="pointTaskData"></Record>
    </el-dialog>
  </div>
</template>

<script>
  import Record from "./record21.vue";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },

      pointTaskData: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      Record,
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        showRecord: false,
        keys: 1,
      };
    },
    methods: {
      initData() {
        this.keys++;
      },

      closeDialog() {
        this.dialogVisible = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
      position: relative;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .w400 {
    width: 400px;
  }
  .picture-img {
    width: 60px;
    height: 60px;
  }

  ::v-deep .el-dialog__wrapper {
    overflow: hidden;

    .el-dialog {
      margin-top: 3.5vh !important;
    }
    &::-webkit-scrollbar {
      width: 0 !important; /* 隐藏滚动条 */
      height: 0 !important; /* 隐藏滚动条 */
    }
    /* 隐藏滚动条（WebKit） */
    &::-webkit-scrollbar {
      display: none;
    }

    /* 隐藏滚动条（Firefox） */
    @-moz-document url-prefix() {
      scrollbar-width: thin;
    }
    /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
    -ms-overflow-style: none;
    scrollbar-width: none; /* Firefox 64+ */
    scrollbar-color: transparent transparent;
    .el-table__body-wrapper {
      scrollbar-width: initial; /* Firefox 64+ */
      scrollbar-color: initial;
    }
  }
</style>
