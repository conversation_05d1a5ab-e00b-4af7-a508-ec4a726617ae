<template>
  <div class="container">
    <div class="item except-box" @click="emitter.emit('open-dialog', 'showExceptReportDialog')">
      <div class="item-left">
        <span class="el-icon-warning-outline left-icon"></span>
      </div>
      <div class="item-right">
        <div class="right-title">异常上报数量</div>
        <div class="right-count">{{ abnormalInfo?.reportingNum }}</div>
      </div>
    </div>
    <div class="item">
      <div class="item-left">
        <span class="el-icon-warning-outline left-icon"></span>
      </div>
      <div class="item-right">
        <div class="right-title">驾驶状态提醒</div>
        <div class="right-count">{{ abnormalInfo?.abnormalStatusNum }}</div>
      </div>
    </div>
  </div>
</template>

<script>
  import emitter from "@/utils/mitt";
  export default {
    computed: {
      abnormalInfo() {
        return this.$store.state.formData.abnormalInfo;
      },
    },
    data() {
      return {
        emitter,
      };
    },
  };
</script>

<style lang="scss" scoped>
  .container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    margin: 30px 0;
  }
  .item {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    .left-icon {
      font-size: 32px;
    }
    .item-right {
      margin-left: 20px;
      .right-title {
        font-size: 14px;
      }
      .right-count {
        font-size: 40px;
        font-weight: bold;
        line-height: 46px;
      }
    }
  }
  .except-box {
    cursor: pointer;
    pointer-events: auto;
  }
</style>
