<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">换班申请处理</div>
      <baseTitle title="基础信息"></baseTitle>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="180px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="申请人名称" required>
              <el-input
                :value="ruleForm.applyFullName"
                placeholder="请输入申请人名称"
                clearable
                maxlength="30"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="申请人联系电话" required>
              <el-input
                :value="ruleForm.applyPhone"
                placeholder="请输入申请人联系电话"
                clearable
                maxlength="11"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="换班开始日期" required>
              <el-date-picker
                :value="ruleForm.changeStartDate"
                type="date"
                placeholder="请选择换班开始日期"
                clearable
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="换班结束日期" required>
              <el-date-picker
                :value="ruleForm.changeEndDate"
                type="date"
                placeholder="请选择换班结束日期"
                clearable
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="详情备注" required>
              <el-input
                :value="ruleForm.remark"
                placeholder="请输入详情备注"
                clearable
                type="textarea"
                rows="10"
                maxlength="800"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="业务调整信息"></baseTitle>
        <el-row>
          <el-col>
            <el-form-item label="是否批准换班申请" prop="applyStatus">
              <el-switch
                v-model="ruleForm.applyStatus"
                active-text="是"
                inactive-text="否"
                active-color="#4CA786"
                :active-value="1"
                :inactive-value="0"
                :disabled="ruleForm.auditStatus === 1 ? true : false"
                @change="changeApplyStatus"
              >
              </el-switch>
            </el-form-item>
          </el-col>
          <template v-if="ruleForm.applyStatus === 1">
            <el-col>
              <el-form-item label="是否需要安排顶班人员" prop="isChange">
                <el-switch
                  v-model="ruleForm.isChange"
                  active-text="是"
                  inactive-text="否"
                  active-color="#4CA786"
                  :active-value="1"
                  :inactive-value="0"
                  :disabled="ruleForm.auditStatus === 1 ? true : false"
                  @change="changeIsChange"
                >
                </el-switch>
              </el-form-item>
            </el-col>
            <el-col v-if="ruleForm.isChange === 1">
              <el-form-item label="选择顶班人员" prop="changeUserId">
                <el-select
                  v-model="ruleForm.changeUserId"
                  placeholder="请选择顶班人员"
                  clearable
                  filterable
                  :disabled="ruleForm.auditStatus === 1 ? true : false"
                >
                  <el-option
                    v-for="item in userOptions"
                    :key="item.lgUnionId"
                    :label="item.fullName"
                    :value="item.lgUnionId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">{{ ruleForm.auditStatus == 0 ? "取消" : "返回" }}</el-button>
      <el-button type="primary" @click="saveRecordThrottling" v-if="ruleForm.auditStatus == 0">保存</el-button>
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun, getListApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      baseTitle,
    },
    data() {
      return {
        ruleForm: {
          applyFullName: "", //申请人名称
          applyPhone: "", //申请人联系电话
          changeStartDate: "", //换班开始日期
          changeEndDate: "", //换班结束日期
          remark: "", //详情备注
          applyStatus: 0, //是否批准换班申请
          isChange: 0, //是否需要安排顶班人员
          changeUserId: "", //选择顶班人员
        },
        rules: {
          applyStatus: [{ required: true, message: "请选择是否批准换班申请", trigger: "change" }],
          isChange: [{ required: true, message: "请选择是否需要安排顶班人员", trigger: "change" }],
          changeUserId: [{ required: true, message: "请选择顶班人员", trigger: "change" }],
        },
        apis: {
          create: "/api/waybill/change/auditChange",
          info: "/api/waybill/change/get/",
          userInfo: "/api/baseuser/getInfoByLgUnionId/",
          userList: "/api/baseuser/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        userOptions: [],
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        let rsp = await getInfoApiFun("", this.apis.userInfo + res.data.applyUserId);
        this.ruleForm = res.data;
        let userIdentity = rsp.data.userIdentity.split(",").map(Number);
        if (userIdentity.includes(3)) {
          let { data } = await getListApiFun({ userIdentity: 3 }, this.apis.userList);
          this.userOptions = data.map((item) => {
            return {
              ...item,
              phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
              idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
            };
          });
        } else if (userIdentity.includes(4)) {
          let { data } = await getListApiFun({ userIdentity: 4 }, this.apis.userList);
          this.userOptions = data.map((item) => {
            return {
              ...item,
              phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
              idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
            };
          });
        }
      },
      changeApplyStatus(value) {
        if (!value) {
          this.ruleForm.isChange = 0;
          this.ruleForm.changeUserId = "";
        }
      },
      changeIsChange(value) {
        if (!value) {
          this.ruleForm.changeUserId = "";
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun(this.ruleForm, this.apis.create);
              if (res.success) {
                this.$message.success(`审批成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
</style>
