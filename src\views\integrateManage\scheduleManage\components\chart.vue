<template>
  <div class="chart-container">
    <div class="chart-title">{{ title }}</div>
    <div class="chart-box" :id="chartId" v-resize="chartInstance"></div>
  </div>
</template>

<script>
  import * as echarts from "echarts";
  export default {
    props: {
      chartId: {
        type: String,
        default: "chart",
      },
      title: {
        type: String,
        default: "",
      },
      centerTitle: {
        type: String,
        default: "",
      },
      dataList: {
        type: Array,
        default: () => [],
      },
      total: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        chartInstance: null,
      };
    },
    mounted() {
      this.init();
    },
    methods: {
      init() {
        this.chartInstance = echarts.init(document.getElementById(this.chartId));
        let option = {
          title: {
            left: "48%",
            top: "30%",
            text: this.centerTitle,
            textStyle: {
              fontSize: 16,
              color: "#333",
              fontWeight: "normal",
            },
            textAlign: "center",
          },
          graphic: [
            {
              type: "text",
              left: "center",
              top: "50%",
              style: {
                text: this.total,
                textAlign: "center",
                textVerticalAlign: "middle",
                fontSize: 40,
                color: "#000",
              },
            },
          ],
          tooltip: {
            trigger: "item",
          },
          series: [
            {
              type: "pie",
              radius: ["60%", "80%"],
              label: {
                show: false,
              },
              emphasis: {
                label: {
                  show: false,
                },
              },
              labelLine: {
                show: false,
              },
              data: [
                { value: this.dataList[0].count, name: this.dataList[0].name, itemStyle: { color: "#546FC6" } },
                { value: this.dataList[1].count, name: this.dataList[1].name, itemStyle: { color: "#74C1DF" } },
              ],
            },
          ],
        };
        this.chartInstance.setOption(option);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .chart-container {
    height: 100%;
  }
  .chart-title {
    font-size: 20px;
    font-weight: bold;
    text-align: center;
  }
  .chart-box {
    min-width: 200px;
    height: 100%;
  }
</style>
