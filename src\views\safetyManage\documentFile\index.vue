<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <div class="main-index">
        <header class="header">
          <div class="header-input">
            <el-input class="w250" v-model="filterForm.keywords" placeholder="请输入档案名称" clearable></el-input>
          </div>
          <InterviewChannel :value.sync="filterForm.channelId" :record.sync="channelRecord"></InterviewChannel>
          <el-form ref="filterForm" :model="filterForm" label-suffix=":" label-width="80px" class="mr-10">
            <el-form-item label="添加日期">
              <el-date-picker
                v-model="filterForm.renovateDate"
                type="date"
                placeholder="请选择添加日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button type="primary" class="mr-10" @click="addFolder" v-show="showAdd">新建文件夹</el-button>
            <el-upload
              action="custom"
              :http-request="httpRequest"
              :show-file-list="false"
              :before-upload="beforeUpload"
            >
              <el-button slot="trigger" type="primary">上传文件</el-button>
            </el-upload>
          </div>
        </header>
        <el-breadcrumb separator="/" class="bread-box">
          <el-breadcrumb-item v-for="(item, index) in breadList" :key="index">
            <template v-if="index < breadList.length - 1">
              <a @click="jumpAnotherFolder(item, index)">{{ item.name }}</a>
            </template>
            <template v-else>
              {{ item.name }}
            </template>
          </el-breadcrumb-item>
        </el-breadcrumb>
        <main class="main">
          <el-table
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            height="100%"
            @cell-click="handleCellClick"
          >
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column prop="name" label="档案名称" min-width="300">
              <template #default="{ row, $index }">
                <div v-if="row.isInput" class="input-box">
                  <img class="icon-img" :src="require('@/assets/icons/folder.png')" alt="" />
                  <el-input v-model="row.name" placeholder="请输入文件名称" clearable></el-input>
                  <el-button
                    type="primary"
                    class="ml-10"
                    icon="el-icon-check"
                    size="small"
                    @click.stop="confirmAddFolderThrottling(row)"
                  ></el-button>
                  <el-button
                    type="primary"
                    icon="el-icon-close"
                    size="small"
                    @click.stop="closeInput(row, $index)"
                  ></el-button>
                </div>
                <div v-else class="input-box">
                  <img
                    class="icon-img"
                    :src="fileIcon[row.fileSuffix] || require('@/assets/icons/otherIcon.png')"
                    alt=""
                    v-if="row.type == 1"
                  />
                  <img class="icon-img" :src="require('@/assets/icons/folder.png')" alt="" v-else />
                  <div>{{ row.name }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="createFullname" label="上传人"></el-table-column>
            <el-table-column prop="renovateDate" label="更新日期"></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="80" label="操作">
              <template #default="{ row }">
                <div v-if="!row.isInput">
                  <el-link class="mr-10" type="primary" @click.stop="updateFolder(row)" v-if="row.type != 1"
                    >重命名</el-link
                  >
                  <el-link class="mr-10" type="primary" @click.stop="downloadFile(row)" v-if="row.type == 1"
                    >下载</el-link
                  >
                  <el-popconfirm :title="`确认删除当前文件${row.type == 1 ? '' : '夹'}？`" @confirm="deleteRecord(row)">
                    <el-link class="mr-10" type="danger" slot="reference" @click.stop>删除</el-link>
                  </el-popconfirm>
                  <el-link type="primary" @click.stop="showMoveDialog(row)">移动</el-link>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
    <moveDialog :value.sync="moveDialogShow" :folderItem="folderItem" @refreshList="initData"></moveDialog>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, deleteApiFun, createApiFun, updateApiFun } from "@/api/base";
  import { OSSUpload } from "@/components/FileUpload/upload";
  import { downloadFile, createDownloadEvent } from "@/utils/download";
  import moveDialog from "./components/moveDialog.vue";
  import { fileIcon } from "@/utils";
  export default {
    components: {
      defaultPage,
      Pagination,
      moveDialog,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        breadList: [{ id: "", name: "全部文件" }],
        apis: {
          listPage: "/api/document/folder/listPage",
          delete: "/api/document/folder/delete/",
          create: "/api/document/folder/create",
          update: "/api/document/folder/update",
        },
        showAdd: true,
        fileList: [],
        maxSize: 100,
        moveDialogShow: false,
        parentId: "",
        loading: false,
        folderItem: {},
        confirmAddFolderThrottling: () => {},
        fileIcon,
        channelRecord: {},
      };
    },
    created() {
      this.confirmAddFolderThrottling = this.$throttling(this.confirmAddFolder, 500);
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        this.loading = true;
        let params = {
          parentId: this.parentId,
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          ...this.filterForm,
        };
        try {
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas.map((item) => {
              return {
                ...item,
                isInput: false,
              };
            });
            this.page.total = res.data.total;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 面包屑跳转回调事件
      jumpAnotherFolder(item, index) {
        this.parentId = item.id;
        this.page.pageNo = 1;
        this.breadList.splice(index + 1);
        this.initData();
      },
      // 新增文件夹
      addFolder() {
        this.showAdd = false;
        this.tableData.unshift({ name: "", isInput: true });
      },
      // 重命名文件夹
      updateFolder(row) {
        row.isInput = true;
      },
      // 确认新增文件夹
      async confirmAddFolder(row) {
        this.loading = true;
        try {
          let res = row.id
            ? await updateApiFun({ ...row, parentId: this.parentId }, this.apis.update)
            : await createApiFun({ ...row, parentId: this.parentId }, this.apis.create);
          if (res.success) {
            row.isInput = false;
            this.showAdd = true;
            this.page.pageNo = row.id ? this.page.pageNo : 1;
            this.$message.success(`${row.id ? "重命名" : "新建"}文件夹成功`);
            this.initData();
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 删除新建的文件夹
      closeInput(row, index) {
        this.showAdd = true;
        if (row.id) {
          this.initData();
          return;
        }
        this.tableData.splice(index, 1);
      },
      // 列表单元格点击事件回调
      handleCellClick(row) {
        if (row.isInput || row.type == 1) {
          return;
        }
        this.parentId = row.id;
        this.page.pageNo = 1;
        this.breadList.push({ id: row.id, name: row.name });
        this.initData();
      },
      //上传之前
      beforeUpload(file) {
        //文件大小判断
        const isLimit = file.size / 1024 / 1024 < this.maxSize;
        if (!isLimit) {
          this.$message.warning(`上传文件大小不能超过 ${this.maxSize}MB!`);
          return false;
        }
        return true;
      },
      async httpRequest(obj) {
        this.loading = true;
        //初始化OSSUpload
        await OSSUpload.getOssBase();
        let ossClient = new OSSUpload();
        ossClient.init();
        let fileSize = obj.file.size;
        let fileSuffix = obj.file.name.slice(obj.file.name.lastIndexOf(".") + 1);
        let name = obj.file.name;
        ossClient
          .upload(obj.file, () => {})
          .then(async (data) => {
            let filePath = data.url;
            try {
              let res = await createApiFun(
                { name, fileSize, fileSuffix, filePath, type: 1, parentId: this.parentId },
                this.apis.create,
              );
              if (res.success) {
                this.page.pageNo = 1;
                this.$message.success(`上传文件成功`);
                this.initData();
              }
            } catch (error) {
              this.loading = false;
            }
            this.loading = false;
          })
          .catch(() => {
            this.loading = false;
          });
      },
      // 打开移动文件弹框
      showMoveDialog(row) {
        this.moveDialogShow = true;
        this.folderItem = row;
      },
      // 删除
      async deleteRecord(row) {
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            this.page.pageNo = this.pageDropProcess(this.page.pageNo, this.page.pageSize, this.page.total);
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 下载
      async downloadFile(row) {
        let resBlob = await downloadFile(row.filePath, row.name);
        await createDownloadEvent(row.name, [resBlob]);
        this.$message.success("下载成功");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-input {
      margin-right: 10px;
    }
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .bread-box {
    margin-top: 20px;
  }
  .input-box {
    display: flex;
    align-items: center;
  }
  .icon-img {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    overflow: hidden;
  }
  .w250 {
    width: 250px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  ::v-deep .header .el-form-item {
    margin-bottom: 0;
  }
</style>
