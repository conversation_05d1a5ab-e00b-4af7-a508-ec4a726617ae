<template>
  <div class="manage-container">
    <div class="manage-content micro-app-sctmp_base" v-loading="loading">
      <div class="tree-menu">
        <el-tree
          :data="treeData"
          :props="defaultProps"
          :expand-on-click-node="false"
          :default-expand-all="true"
          @node-click="handleNodeClick"
        >
          <div class="custom-tree-node" slot-scope="{ node, data }">
            <div>{{ node.label }}</div>
            <div>
              <template v-if="!data.isRoot">
                <span class="el-icon-edit tree-icon" @click.stop="editNode(data)"></span>
                <span class="el-icon-delete tree-icon" @click.stop="deleteNode(data)"></span>
              </template>
              <span class="el-icon-plus tree-icon" @click.stop="addNode(data)"></span>
            </div>
          </div>
        </el-tree>
      </div>
      <div class="manage-main">
        <template v-if="currentNode?.id">
          <header class="main-header">
            <div class="header-left">
              <div class="manage-title">{{ currentNode.name }}（{{ page.total }}人）</div>
              <el-button type="primary" size="small" @click="addMember">新增人员</el-button>
            </div>
            <div class="header-right">
              <el-form label-width="160px" label-suffix="：">
                <el-form-item label="部门状态">
                  <el-select v-model="currentNode.state" placeholder="请选择部门状态" filterable @change="changeState">
                    <el-option
                      v-for="(item, index) in DEPT_STATUS"
                      :key="index"
                      :label="item"
                      :value="index"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="部门固定电话">
                  <div>{{ currentNode.contactNumber || "-" }}</div>
                </el-form-item>
              </el-form>
            </div>
          </header>
          <main class="main-content">
            <el-table
              :data="tableData"
              :header-cell-style="{ background: '#F5F7F9' }"
              style="width: 100%"
              height="100%"
              border
            >
              <el-table-column type="index" align="center"></el-table-column>
              <el-table-column prop="fullName" label="姓名" align="center">
                <template #default="{ row }">
                  <div class="name-box">
                    <div>{{ row.fullName }}</div>
                    <el-button class="name-button" size="small" type="primary" plain v-if="row.isComHead == 1"
                      >部门负责人</el-button
                    >
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="jobNo" label="工号" align="center"></el-table-column>
              <el-table-column prop="phone" label="手机" align="center"></el-table-column>
              <el-table-column min-width="140" label="操作" align="center">
                <template #default="{ row }">
                  <el-popconfirm
                    :title="`是否要${row.isComHead == 1 ? '取消' : '设为'}负责人？`"
                    @confirm="setHeader(row)"
                  >
                    <el-link class="mr-10" type="primary" slot="reference"
                      >{{ row.isComHead == 1 ? "取消" : "设为" }}负责人</el-link
                    >
                  </el-popconfirm>
                  <el-popconfirm title="是否要移出该成员？" @confirm="deleteMember(row)">
                    <el-link type="danger" slot="reference">移出</el-link>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </main>
          <Pagination :page="page" @pageChange="pageChange"></Pagination>
        </template>
      </div>
    </div>
    <div class="record-footer">
      <el-button @click="$emit('closeRecord')">返回</el-button>
    </div>
    <addDept
      :value.sync="showAddDept"
      :deptType="deptType"
      :enterpriseInfo="enterpriseInfo"
      :nodeInfo="nodeInfo"
      @refreshTree="getTreeMenuList"
    ></addDept>
    <addMember
      :value.sync="showAddMember"
      :enterpriseInfo="enterpriseInfo"
      :currentNode="currentNode"
      @refreshMember="initData"
      ref="memberComponent"
    ></addMember>
  </div>
</template>

<script>
  import { DEPT_STATUS } from "@/enums";
  import Pagination from "@/components/pagination-v";
  import addDept from "./addDept.vue";
  import addMember from "./addMember.vue";
  import { createApiFun, deleteApiFun, getListPageApiFun } from "@/api/base";
  export default {
    props: {
      enterpriseInfo: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      Pagination,
      addDept,
      addMember,
    },
    data() {
      return {
        treeData: [],
        defaultProps: {
          label: "name",
          children: "children",
        }, //树形配置
        DEPT_STATUS,
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        loading: false,
        showAddDept: false,
        deptType: 0, //0-新增 1-修改
        showAddMember: false,
        apis: {
          treeList: "/api/company/structure/find",
          delete: "/api/company/structure/delete",
          updateState: "/api/company/structure/updateState",
          userPage: "/api/company/structureUser/findPage",
          userDelete: "/api/company/structureUser/delete",
          userSet: "/api/company/structureUser/headSet",
        },
        tableData: [],
        nodeInfo: {},
        currentNode: {}, //当前节点
      };
    },
    mounted() {
      this.getTreeMenuList();
    },
    methods: {
      //获取树形下拉菜单
      async getTreeMenuList() {
        let res = await createApiFun({ companyId: this.enterpriseInfo.id }, this.apis.treeList);
        let treeList = {
          companyId: this.enterpriseInfo.id,
          name: this.enterpriseInfo.name,
          children: res.data,
          isRoot: true,
        };
        this.treeData = [treeList];
      },
      // 获取成员分页列表
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          structureId: this.currentNode.id,
        };
        let res = await getListPageApiFun(params, this.apis.userPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      // 树节点点击事件
      handleNodeClick(node) {
        if (node.isRoot || node.id == this.currentNode.id) {
          return;
        }
        this.currentNode = node;
        this.page.pageNo = 1;
        this.initData();
      },
      // 编辑树子节点
      editNode(data) {
        this.deptType = 1;
        this.showAddDept = true;
        this.nodeInfo = data;
      },
      // 删除树子节点
      deleteNode(data) {
        this.$confirm("确定是否删除该部门?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await deleteApiFun("", `${this.apis.delete}?id=${data.id}`);
              if (res.success) {
                this.$message.success("部门删除成功");
                this.getTreeMenuList();
                if (this.currentNode.id == data.id) {
                  this.currentNode = {};
                }
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          })
          .catch(() => {});
      },
      // 添加树子节点
      addNode(data) {
        this.deptType = 0;
        this.showAddDept = true;
        this.nodeInfo = data;
      },
      // 修改部门状态
      async changeState(value) {
        this.loading = true;
        try {
          let res = await createApiFun({ id: this.currentNode.id, state: value }, this.apis.updateState);
          if (res.success) {
            this.$message.success("状态修改成功");
            this.currentNode.state = value;
            this.getTreeMenuList();
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 添加人员
      addMember() {
        this.showAddMember = true;
        this.$refs.memberComponent.initData(this.enterpriseInfo.id, this.currentNode.id);
      },
      // 移出人员
      async deleteMember(row) {
        this.loading = true;
        try {
          let res = await deleteApiFun("", `${this.apis.userDelete}?id=${row.id}`);
          if (res.success) {
            this.$message.success("成员移出成功");
            this.initData();
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 设为负责人
      async setHeader(row) {
        this.loading = true;
        try {
          let res = await createApiFun({ id: row.id, operationType: row.isComHead == 1 ? 0 : 1 }, this.apis.userSet);
          if (res.success) {
            this.$message.success(`负责人${row.isComHead == 1 ? "取消" : "设置"}成功`);
            this.initData();
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .manage-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .manage-content {
    flex: 1;
    overflow-y: auto;
    display: flex;
    align-items: flex-start;
  }
  .manage-main {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-top: 20px;
    padding-left: 20px;
  }
  .tree-menu {
    width: 399px;
    height: 100%;
    border-right: 1px solid #ddd;
    overflow-y: auto;
  }
  .tree-icon {
    font-size: 16px;
    margin-right: 6px;
  }
  .manage-main {
    flex: 1;
    overflow: hidden;
  }
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
  }
  .main-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    .header-left {
      display: flex;
      align-items: center;
      .manage-title {
        font-size: 18px;
      }
    }
  }
  .mr-10 {
    margin-right: 10px;
  }
  .main-content {
    flex: 1;
    margin-top: 20px;
  }
  .name-box {
    display: flex;
    align-items: center;
    justify-content: center;
    .name-button {
      margin-left: 10px;
    }
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  ::v-deep .el-tree-node__expand-icon {
    font-size: 20px;
  }
  ::v-deep .el-tree-node__content {
    height: auto;
    padding: 6px 0;
  }
  ::v-deep .el-form-item {
    margin-bottom: 16px;
  }
  ::v-deep .el-form-item:last-child {
    margin-bottom: 0;
  }
</style>
