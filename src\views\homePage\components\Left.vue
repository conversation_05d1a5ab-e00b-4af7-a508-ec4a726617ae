<template>
  <div class="left-wrapper" style="display: flex; flex-direction: column">
    <Title name="指挥调度" :url="url">指挥调度</Title>
    <div class="second" @click="$router.push('/sctmp_base/receiveShipPoint')">
      <div class="text">点位总数</div>
      <div class="value">{{ summaryData?.pointSum }}</div>
    </div>
    <ECharts1
      v-if="summaryData?.situation24"
      eChartsId="situation24"
      title="今日24小时点位收运情况"
      :collectNum="summaryData?.situation24?.collectNum"
      :unCollectNum="summaryData?.situation24?.uncollectNum"
      :total="summaryData?.situation24?.total"
      @click.native="$router.push('/sctmp_base/pointTaskDetail')"
    >
    </ECharts1>
    <ECharts1
      v-if="summaryData?.situation48"
      eChartsId="situation48"
      title="今日48小时点位收运情况"
      :collectNum="summaryData?.situation48?.collectNum"
      :unCollectNum="summaryData?.situation48?.uncollectNum"
      :total="summaryData?.situation48?.total"
      @click.native="$router.push('/sctmp_base/pointTaskDetail')"
    >
    </ECharts1>
  </div>
</template>
<script>
  import Title from "./ModuleTitle.vue";
  import ECharts1 from "./ECharts1.vue";
  export default {
    components: { ECharts1, Title },
    props: ["summaryData"],
    data() {
      return {
        url: require("../images/ic_order.svg"),
      };
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {},
    watch: {},
  };
</script>
<style scoped lang="scss">
  .second {
    margin: 0 auto;
    width: 240px;
    height: 90px;
    background: linear-gradient(180deg, #ffffff 0%, #f7faf9 100%);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .text {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
    margin-bottom: 4px;
  }
  .value {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 24px;
    color: #2e3432;
    line-height: 33px;
    text-align: right;
    font-style: normal;
  }
  // 左右设计是1：1，按需求调整大致改为200：250

  .left-wrapper {
    flex: 200;
    background-color: #fff;
    margin-right: 8px;
  }
</style>
