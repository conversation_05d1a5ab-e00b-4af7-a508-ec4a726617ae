<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      width="1400px"
      top="0"
      destroy-on-close
      @open="initData"
      :show-close="false"
    >
      <template #title>
        <header class="header">今日出车人员表</header>
      </template>
      <div class="main-record" v-loading="loading">
        <div class="filter">
          <el-input
            class="input-box"
            v-model="keyword"
            placeholder="请输入姓名/联系方式/默认驾驶车辆"
            clearable
          ></el-input>
          <el-button type="primary" @click="getDataList">搜索</el-button>
          <el-button type="primary" @click="initData">重置</el-button>
          <el-button type="primary" @click="filterIdentity(3)">只看司机</el-button>
          <el-button type="primary" @click="filterIdentity(4)">只看押运工</el-button>
        </div>
        <div class="title-box">
          <Title title="出车人员" color="#000"></Title>
          <Title title="空闲人员" color="#000"></Title>
        </div>
        <div class="record-content">
          <el-table :data="departureList" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column prop="name" label="姓名" align="center"></el-table-column>
            <el-table-column prop="userIdentity" label="身份" align="center">
              <template #default="{ row }">{{ USER_IDENTITY[row.userIdentity] }}</template>
            </el-table-column>
            <el-table-column prop="phone" label="联系方式" align="center"></el-table-column>
            <el-table-column prop="plateNumber" label="默认驾驶车辆" align="center"></el-table-column>
          </el-table>
          <el-table :data="unDepartureList" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column prop="name" label="姓名" align="center"></el-table-column>
            <el-table-column prop="userIdentity" label="身份" align="center">
              <template #default="{ row }">{{ USER_IDENTITY[row.userIdentity] }}</template>
            </el-table-column>
            <el-table-column prop="phone" label="联系方式" align="center"></el-table-column>
            <el-table-column prop="plateNumber" label="默认驾驶车辆" align="center"></el-table-column>
          </el-table>
        </div>
        <div class="record-footer">
          <el-button @click="dialogVisible = false">返回</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import Title from "./Title.vue";
  import { createApiFun } from "@/api/base";
  import { USER_IDENTITY } from "@/enums";
  export default {
    components: {
      Title,
    },
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        loading: false,
        keyword: "",
        type: "",
        apis: {
          list: "/api/monitor/departure/driver",
        },
        departureList: [],
        unDepartureList: [],
        USER_IDENTITY,
      };
    },
    methods: {
      initData() {
        this.type = "";
        this.keyword = "";
        this.getDataList();
      },
      filterIdentity(value) {
        this.type = value;
        this.getDataList();
      },
      async getDataList() {
        try {
          let {
            data: { departureList, unDepartureList },
          } = await createApiFun({ type: this.type, keyword: this.keyword, channelId: this.channelId }, this.apis.list);
          this.departureList = departureList.map((item) => {
            return {
              ...item,
              phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            };
          });
          this.unDepartureList = unDepartureList.map((item) => {
            return {
              ...item,
              phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            };
          });
        } catch (error) {
          console.log(error);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .record-content {
    flex: 1;
    overflow: auto;
    display: grid;
    grid-gap: 80px;
    grid-template-columns: repeat(2, 1fr);
    padding-bottom: 20px;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .header {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
  }
  .filter {
    display: flex;
    align-items: center;
    .input-box {
      width: 40%;
      margin-right: 10px;
    }
  }
  .title-box {
    margin: 40px 0;
    display: grid;
    grid-gap: 80px;
    grid-template-columns: repeat(2, 1fr);
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
