<template>
  <div class="safeModule-wrapper">
    <Title name="安全模块" :url="url" @click.native="$router.push('/sctmp_base/selfTest')"></Title>
    <div style="flex: 1" id="safeModule"></div>
  </div>
</template>
<script>
  import * as echarts from "echarts";
  import Title from "./ModuleTitle.vue";
  export default {
    components: { Title },
    props: ["summaryData"],
    data() {
      return {
        url: require("../images/ic_safe.svg"),
        xAxisData: [],
        seriesData1: [],
        seriesData2: [],
      };
    },
    computed: {},
    created() {},
    mounted() {},
    watch: {
      summaryData: {
        handler(newV) {
          if (newV?.safetySituation && newV?.inspectSituation) {
            this.dataCb();
          }
        },
        deep: true,
      },
    },
    methods: {
      dataCb() {
        this.getXAxisData();
        this.getSeriesData1();
        this.getSeriesData2();
        this.init();
      },
      getSeriesData2() {
        let length = this.xAxisData.length;
        const mappedUndefinedArray = new Array(length).fill(0);
        this.summaryData.safetySituation.forEach((item) => {
          mappedUndefinedArray[item.month - 1] = item.safetyNum;
        });
        this.seriesData2 = mappedUndefinedArray;
      },
      getSeriesData1() {
        let length = this.xAxisData.length;
        const mappedUndefinedArray = new Array(length).fill(0);
        this.summaryData.inspectSituation.forEach((item) => {
          let value = (item.normalRate * 100).toFixed(2);
          value = parseFloat(value);
          mappedUndefinedArray[item.month - 1] = value;
        });
        this.seriesData1 = mappedUndefinedArray;
      },
      getXAxisData() {
        // 获取当前日期
        const now = new Date();

        // 获取当前年份的当前月份（注意：月份是从0开始的，所以8月是7）
        const currentMonth = now.getMonth() + 1;

        // 使用Array.from()方法生成从1到当前月份的数组
        // 第二个参数是一个函数，它接受两个参数：index（当前索引）和array（当前数组，但在这个场景下我们不需要它）
        // 我们通过index+1来确保数组从1开始
        const monthsArray = Array.from({ length: currentMonth }, (_, index) => index + 1);
        this.xAxisData = monthsArray.map((item) => {
          return `${item}月`;
        });
      },
      init() {
        var myChart = echarts.init(document.getElementById("safeModule"));
        myChart.setOption({
          color: ["#006BF2", "#FF9A2E"],
          barWidth: 8,
          title: {
            text: "本年各月日常安检正常率/安全活动次数",
            textStyle: {
              color: "#414D5A",
              fontStyle: "normal",
              fontWeight: "bold",
              fontFamily: "Microsoft YaHei",
              fontSize: "14",
              lineHeight: "20",
            },
            left: "center",
          },
          tooltip: {
            trigger: "axis", // 触发类型，默认为数据项触发，可选为：'item' | 'axis'
            axisPointer: {
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            formatter: function (params) {
              let res = params[0].name + "<br/>"; // x轴名称
              // 遍历params中的每一项数据
              params.forEach((item) => {
                let seriesName = item.seriesName; // 系列名称
                let value = item.value; // 数值
                let unit = ""; // 初始单位为空

                // 根据系列名称设置不同的单位
                if (seriesName === "日常安检正常率") {
                  unit = "%";
                } else if (seriesName === "安全活动次数") {
                  unit = "次";
                }

                res += `${seriesName}: ${value}${unit}<br/>`; // 拼接显示内容
              });

              return res;
            },
          },
          legend: {
            itemWidth: 8,
            itemHeight: 8,
            top: "30",
            data: ["日常安检正常率", "安全活动次数"],
          },
          xAxis: {
            type: "category",
            data: this.xAxisData,
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          yAxis: [
            {
              type: "value",
              name: "日常安检正常率",
              axisLabel: {
                formatter: "{value} %",
              },
            },
            {
              type: "value",
              name: "安全活动次数",
              axisLabel: {
                formatter: "{value} 次",
              },
            },
          ],
          series: [
            {
              name: "日常安检正常率",
              type: "bar",
              data: this.seriesData1,
              // itemStyle: {
              //   normal: {
              //     label: {
              //       show: true, // 显示标签
              //       position: "top", // 标签的位置，默认为'top'
              //       formatter: "{c}%", // 格式化标签文本，'{c}'表示数值本身
              //     },
              //   },
              // },
            },
            {
              yAxisIndex: 1,
              name: "安全活动次数",
              type: "bar",
              data: this.seriesData2,
              // itemStyle: {
              //   normal: {
              //     label: {
              //       show: true, // 显示标签
              //       position: "top", // 标签的位置，默认为'top'
              //       formatter: "{c}次", // 格式化标签文本，'{c}'表示数值本身
              //     },
              //   },
              // },
            },
          ],
        });
      },
    },
  };
</script>
<style scoped lang="scss">
  .safeModule-wrapper {
    flex-direction: column;
    width: 100%;
    display: flex;
    background-color: #fff;
    flex: 1;
    margin-right: 8px;
  }
</style>
