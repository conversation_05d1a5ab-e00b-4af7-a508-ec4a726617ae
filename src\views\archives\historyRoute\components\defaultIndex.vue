<template>
  <div class="micro-app-sctmp_base full-height">
    <defaultPage @closeRecord="closeRecord">
      <div class="main-index">
        <header class="header">
          <div class="header-left mr-10">
            <el-form ref="filterForm" :model="filterForm" label-suffix=":" label-width="80px">
              <el-form-item label="备份日期">
                <el-date-picker
                  v-model="filterForm.backUpDate"
                  type="date"
                  placeholder="请选择备份日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-form>
          </div>
          <InterviewChannel :value.sync="filterForm.channelId" :record.sync="channelRecord"></InterviewChannel>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
        </header>
        <main class="main">
          <el-table
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            height="100%"
            border
            @row-click="handleRowClick"
          >
            <el-table-column type="index" width="55" align="center"></el-table-column>
            <el-table-column prop="backUpDate" label="备份日期" align="center"></el-table-column>
            <el-table-column prop="num" label="路线总量" align="center"></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, BASE_API_URL } from "@/api/base";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  export default {
    components: {
      defaultPage,
      Pagination,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/pickup/pickupPath/backup/listPage",
          export: "/api/holding/holdingTank/exportExcelHoldingTank",
        },
        showRecord: false,
        recordId: "",
        loading: false,
        importDialogShow: false,
        importDialogType: "",
        channelRecord: {},
      };
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      handleRowClick(row) {
        this.$emit("changeType", {
          type: 1,
          id: row.id,
          date: row.backUpDate,
          channelId: this.filterForm.channelId || "",
        });
      },
      closeRecord() {
        this.showRecord = false;
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 导出
      handleExport() {
        this.$confirm(`确认是否以当前筛选条件导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, this.filterForm);
              if (res.success) {
                createDownloadEvent(`历史收运路线档案${Date.now()}.xlsx`, [res.data]);
                window.ELEMENT.Message.success("导出成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  ::v-deep .header-left .el-form-item {
    margin-bottom: 0;
  }
</style>
