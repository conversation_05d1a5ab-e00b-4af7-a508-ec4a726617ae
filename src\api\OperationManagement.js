import { get, post } from "@/utils/request";
// const BASE_API = process.env.VUE_APP_BASE_API_URL;
export const BASE_API = process.env.VUE_APP_BASE_API_URL + "/sctmpbase";

//今日各区收运量排名统计
export function areaCollectRankDay(params) {
  return get(BASE_API + "/api/operation/monitor/getTodayDistrictRubbishTotalInfo", params);
}

//今日路线收运量排名统计
export function waybillCollectRankDay(params) {
  return get(BASE_API + "/api/operation/monitor/getTodayPathRubbishTotalInfo", params);
}

//今日路线收运量排名统计2--子级
export function waybillCollectRankDayChild(params) {
  return get(BASE_API + "/api/operation/monitor/getTodayPathRubbishTotalInfoByDistrictId", params);
}

//各区点位数量统计
export function areaPointTotal(params) {
  return get(BASE_API + "/api/operation/monitor/getAreaPointTotalInfo", params);
}

//今日废物重量统计
export function rubbishWeightStatistics(params) {
  return get(BASE_API + "/api/operation/monitor/getTodayRubbishTotalInfo", params);
}

//基础数据
export function baseInfo(params) {
  return get(BASE_API + "/api/query/op/baseInfo", params);
}

//基础数据
export function areaTotal(params) {
  return get(BASE_API + "/api/operation/monitor/getAreaInfo", params);
}

// 根据区域id获取区域点位列表
export function getPointListByDistrictId(districtId, config) {
  return get(BASE_API + `/api/operation/monitor/getPointListByDistrictId/${districtId}`, {}, config);
}

// 根据点位id获取点位收运情况
export function getWaybillDetailByPointId(pickupPointId) {
  return get(BASE_API + `/api/operation/monitor/getWaybillDetailByPointId/${pickupPointId}`);
}

// 获取收运情况数据
export function getTodayWaybillApi(params, config) {
  return get(BASE_API + `/api/operation/monitor/getTodayWaybill`, params, config);
}

// 获取线路收运情况数据
export function getPathWaybillApi(params) {
  return get(BASE_API + `/api/operation/monitor/getPathWaybill`, params);
}

// 获取点位列表
export function pickupPointListApi(data, config) {
  return post(BASE_API + `/api/pickup/pickupPoint/list`, data, config);
}
