<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      width="1400px"
      top="0"
      destroy-on-close
      :before-close="closeRecord"
      @open="initData"
    >
      <template #title>
        <header class="header-title">{{ recordType ? "异常上报详情" : "今日异常上报" }}</header>
      </template>
      <div class="handle-wrapper" v-if="recordType == 2">
        <handleRecord :recordItem="recordItem" @returnIndex="closeRecord" @refreshList="getDataList"></handleRecord>
      </div>
      <record
        v-else-if="recordType == 1"
        :recordId="recordId"
        @closeRecord="closeRecord"
        @refreshList="getDataList"
      ></record>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-form ref="filterForm" inline :model="filterForm" label-suffix=":" label-width="140px">
              <el-form-item label="异常类型">
                <el-select v-model="filterForm.exceptionType" placeholder="请选择异常类型" clearable filterable>
                  <el-option
                    v-for="(item, index) in ERROR_STATUS"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否可以继续收运">
                <el-select
                  v-model="filterForm.continueCarrying"
                  placeholder="请选择是否可以继续收运"
                  clearable
                  filterable
                >
                  <el-option
                    v-for="item in CONTINUECARRYING_STATUS"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div class="header-right">
            <el-button @click="resetFilter">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
          </div>
        </header>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column prop="exceptionType" label="异常类型" align="center">
              <template #default="{ row }">{{ ERROR_STATUS[row.exceptionType] }}</template>
            </el-table-column>
            <el-table-column prop="reportingTime" label="上报日期" align="center"></el-table-column>
            <el-table-column width="200" prop="continueCarrying" label="是否可以继续收运" align="center">
              <template #default="{ row }">{{ CONTINUECARRYING_STATUS_MAP[row.continueCarrying] }}</template>
            </el-table-column>
            <el-table-column prop="status" label="处理状态" align="center">
              <template #default="{ row }">{{ BACKLOG_STATUS[row.status] }}</template>
            </el-table-column>
            <el-table-column width="180" label="操作" align="center">
              <template #default="{ row }">
                <el-link type="primary" @click="handleRecord(row)" v-if="!row.continueCarrying && row.status === 0"
                  >去处理</el-link
                >
                <el-link type="primary" @click="checkRecord(row)" v-else>查看详情</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import { ERROR_STATUS, CONTINUECARRYING_STATUS, CONTINUECARRYING_STATUS_MAP } from "@/enums";
  import record from "./ExceptReportDialogRecord.vue";
  import moment from "moment";
  import handleRecord from "@/views/eventWatch/backlog/components/record60.vue";
  import { BACKLOG_STATUS } from "@/enums/backlog";
  export default {
    components: {
      record,
      handleRecord,
    },
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        loading: false,
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/abnormalreporting/listPage",
        },
        recordType: 0,
        recordId: "",
        ERROR_STATUS,
        CONTINUECARRYING_STATUS,
        CONTINUECARRYING_STATUS_MAP,
        BACKLOG_STATUS,
        recordItem: {},
      };
    },
    methods: {
      initData() {
        this.recordId = "";
        this.recordType = 0;
        this.filterForm = {};
        this.page = { pageNo: 1, pageSize: 10, total: 0 };
        this.getDataList();
      },
      async getDataList() {
        try {
          let params = {
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            reportingTime: moment().format("YYYY-MM-DD"),
            channelId: this.channelId,
            ...this.filterForm,
          };
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
        } catch (error) {
          console.log(error, "error");
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.getDataList();
      },
      // 详情
      checkRecord(row) {
        this.recordType = 1;
        this.recordId = row.id;
      },
      closeRecord(done) {
        if (this.recordType) {
          this.recordType = 0;
          return;
        }
        done();
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.page.pageNo = 1;
        this.getDataList();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.getDataList();
      },
      // 去处理
      handleRecord(row) {
        this.recordType = 2;
        this.recordItem = row;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header-title {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
  }
  .header {
    display: flex;
    align-items: center;
    .header-left {
      flex: 1;
    }
    .header-right {
      margin-top: 20px;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w-300 {
    width: 300px;
  }
  .handle-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
