<template>
  <div>
    <Title title="收运点位信息"></Title>
    <div class="point-header">
      <div class="point-box">
        <div class="overtime-box">
          <div class="overtime-total">{{ collectInfo?.yesterdayExpiredNum }}</div>
          <div class="overtime-unit">（个）</div>
        </div>
        <div class="overtime-text">昨日超时未填报点位数量</div>
      </div>
      <div class="point-box">
        <div class="overtime-box">
          <div class="overtime-total">{{ collectInfo?.dayCollectNum }}</div>
        </div>
        <div class="overtime-text">今日收运点位总数</div>
      </div>
    </div>
    <div class="time-box">
      <el-progress
        :width="100"
        type="circle"
        :percentage="collectInfo?.collect24Rate"
        :stroke-width="10"
        define-back-color="#aaa"
        text-color="#fff"
        color="#CAF982"
      ></el-progress>
      <div class="right">
        <div class="right-title">今日24小时应收运点位总数</div>
        <div class="right-count">{{ collectInfo?.collect24Total }}</div>
        <div class="right-title mt-16">今日已收24小时收运点位数量</div>
        <div class="right-count">{{ collectInfo?.collect24Num }}</div>
      </div>
    </div>
    <div class="time-box">
      <el-progress
        :width="100"
        type="circle"
        :percentage="collectInfo?.collect48Rate"
        :stroke-width="10"
        define-back-color="#aaa"
        text-color="#fff"
        color="#CAF982"
      ></el-progress>
      <div class="right">
        <div class="right-title">今日48小时应收运点位总数</div>
        <div class="right-count">{{ collectInfo?.collect48Total }}</div>
        <div class="right-title mt-16">今日累计48小时点位已收运数量</div>
        <div class="right-count">{{ collectInfo?.collect48Num }}</div>
      </div>
    </div>
  </div>
</template>

<script>
  import Title from "@/views/largeDataScreen/realTimeMonitor/components/Title";
  export default {
    components: {
      Title,
    },
    props: ["collectInfo"],
  };
</script>

<style lang="scss" scoped>
  .point-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
  }
  .overtime-box {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    color: #fff;
    .overtime-total {
      font-size: 40px;
      font-weight: bold;
      line-height: 30px;
    }
    .overtime-unit {
      font-size: 12px;
    }
  }
  .overtime-text {
    margin-top: 16px;
    text-align: center;
    font-size: 16px;
    color: #fff;
  }
  .time-box {
    display: flex;
    align-items: center;
    color: #fff;
    margin-top: 40px;
  }
  .right {
    margin-left: 60px;
  }
  .right-title {
    font-size: 16px;
  }
  .right-count {
    font-size: 40px;
    font-weight: bold;
    margin-top: 4px;
  }
  .mt-16 {
    margin-top: 16px;
  }
</style>
