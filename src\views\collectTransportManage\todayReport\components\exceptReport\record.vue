<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="上报时间" prop="reportingTime">
              <el-date-picker
                disabled
                v-model="ruleForm.reportingTime"
                type="datetime"
                placeholder="请选择上报时间"
                format="yyyy-MM-dd HH:mm"
                value-format="yyyy-MM-dd HH:mm"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="基础信息"></baseTitle>
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="上报人" prop="reportPerson">
              <el-input
                disabled
                v-model="ruleForm.reportPerson"
                placeholder="请输入上报人"
                clearable
                maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="异常事件类型" prop="exceptionType">
              <el-select disabled v-model="ruleForm.exceptionType" placeholder="请选择处理情况" clearable filterable>
                <el-option v-for="(item, index) in ERROR_STATUS" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12">
            <el-form-item label="是否可以继续收运" prop="continueCarrying">
              <el-select disabled v-model="ruleForm.continueCarrying" placeholder="请选择处理情况" clearable filterable>
                <el-option
                  v-for="item in CONTINUECARRYING_STATUS"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="异常描述"></baseTitle>
        <el-row>
          <el-col>
            <el-form-item label="异常描述内容" prop="exceptionContent">
              <el-input
                disabled
                v-model="ruleForm.exceptionContent"
                placeholder="无"
                clearable
                type="textarea"
                rows="6"
                maxlength="500"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <template>
        <el-button @click="closeRecord">返回</el-button>
      </template>
    </div>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";
  import { getInfoApiFun } from "@/api/base";
  import { CONTINUECARRYING_STATUS, ERROR_STATUS } from "@/enums";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
      pageFlag: {
        type: String,
        default: "",
      },
    },
    components: {
      baseTitle,
    },
    data() {
      return {
        ERROR_STATUS,
        ruleForm: {
          id: "",
          exceptionType: "",
          continueCarrying: "",
          exceptionContent: "",
          reportingTime: "",
          reportPerson: "",
        },
        rules: {},
        apis: {
          info: "/api/abnormalreporting/get/",
        },
        CONTINUECARRYING_STATUS,
        loading: false,
      };
    },
    created() {},
    async mounted() {
      await this.getRecord();
    },
    methods: {
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    height: 100%;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding: 20px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .type-card {
    margin-bottom: 20px;
  }
  .w-300 {
    width: 300px;
  }
  ::v-deep .question-item .el-form-item__label {
    text-align: left;
  }
  // 测试反馈 字数限制提示挡住内容
  ::v-deep .el-textarea__inner {
    padding-right: 40px !important;
  }
</style>
