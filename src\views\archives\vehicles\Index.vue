<template>
  <div class="micro-app-sctmp_base full-height">
    <defaultPage :topInfo="topInfo" @createRecord="createRecord" @closeRecord="closeRecord">
      <record
        v-if="showRecord"
        :recordId="recordId"
        :deptOptions="deptOptions"
        :vehicleModelOptions="vehicleModelOptions"
        @closeRecord="closeRecord"
        @refreshList="initData"
      ></record>
      <div class="main-index" v-show="!showRecord">
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="keyword" placeholder="请输入车牌号/车架号/发动机号" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
            <el-dropdown
              size="small"
              trigger="click"
              @command="handleDataImport"
              class="dropdown-button"
              placement="bottom-start"
            >
              <el-button size="small" type="primary">
                <span class="sctmp-iconfont icon-ic_daoru"></span>
                <span class="ml-5">导入</span>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">
                  <div class="has-padding-sm has-border-bottom">导入基础信息</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button size="small" type="primary" @click="handleExport">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
          ></InterviewChannel>
          <el-col :span="8">
            <el-form-item label="工作状态">
              <el-select v-model="filterForm.workingStatus" placeholder="请选择工作状态" clearable filterable>
                <el-option
                  v-for="(item, index) in WORKING_STATUS"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="营运性质">
              <el-select v-model="filterForm.operationalNature" placeholder="请选择营运性质" clearable filterable>
                <el-option
                  v-for="(item, index) in OPERATIONAL_NATURE"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车辆状态">
              <el-select v-model="filterForm.status" placeholder="请选择车辆状态" clearable filterable>
                <el-option v-for="(item, index) in CAR_STATUS" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属部门">
              <el-cascader
                v-model="filterForm.departmentId"
                placeholder="请选择所属部门"
                filterable
                clearable
                :options="deptOptions"
                :props="deptProps"
                :show-all-levels="false"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="能源类型">
              <el-select v-model="filterForm.vehicleEnergyType" placeholder="请选择能源类型" clearable filterable>
                <el-option
                  v-for="(item, index) in VEHICLE_ENERGY_TYPE"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车辆型号">
              <el-select v-model="filterForm.vehicleModel" placeholder="请选择车辆型号" clearable filterable>
                <el-option
                  v-for="item in vehicleModelOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="购买日期">
              <el-date-picker
                v-model="filterForm.purchaseDate"
                type="date"
                placeholder="请选择购买时间"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总质量(kg)">
              <el-input
                v-model="filterForm.grossWeight"
                placeholder="请输入总质量"
                :maxlength="50"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="装载重量(kg)">
              <el-input
                v-model="filterForm.loadCapacity"
                placeholder="请输入装载重量"
                :maxlength="50"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" width="55" align="center"></el-table-column>
            <el-table-column prop="plateNumber" label="车牌号" align="center"> </el-table-column>
            <el-table-column prop="departmentName" label="所属部门" align="center"> </el-table-column>
            <el-table-column prop="purchaseDate" label="购买日期" align="center"> </el-table-column>
            <el-table-column prop="vehicleModelName" label="车辆型号" align="center"> </el-table-column>
            <el-table-column prop="grossWeight" label="总质量(kg)" align="center"> </el-table-column>
            <el-table-column prop="vehicleEnergyType" label="能源类型" align="center">
              <template #default="{ row }">{{ VEHICLE_ENERGY_TYPE[row.vehicleEnergyType] }}</template>
            </el-table-column>
            <el-table-column prop="operationalNature" label="营运性质" align="center">
              <template #default="{ row }">{{ OPERATIONAL_NATURE[row.operationalNature] }}</template>
            </el-table-column>
            <el-table-column prop="loadCapacity" label="装载重量(kg)" align="center"></el-table-column>
            <el-table-column prop="status" label="车辆状态" align="center">
              <template #default="{ row }">{{ CAR_STATUS[row.status] }}</template>
            </el-table-column>
            <el-table-column prop="workingStatus" label="工作状态" align="center">
              <template #default="{ row }">{{ WORKING_STATUS[row.workingStatus] }}</template>
            </el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row)" v-if="row.status != 2">编辑</el-link>
                <el-popconfirm title="确认删除车辆档案？" class="mlr-12" @confirm="deleteRecord(row)">
                  <el-link type="danger" slot="reference">删除</el-link>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
    <importDialog
      :value.sync="importDialogShow"
      title="车辆档案"
      :importApi="apis.import"
      :templateApi="apis.template"
      :importDialogType="importDialogType"
      :isTemplateApi="true"
      :hasDept="true"
      :deptOptions="deptOptions"
      deptField="department"
      @importSuccess="searchFilter"
    ></importDialog>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import { OPERATIONAL_NATURE, WORKING_STATUS, CAR_STATUS, VEHICLE_ENERGY_TYPE } from "@/enums";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, getInfoApiFun, deleteApiFun, BASE_API_URL } from "@/api/base";
  import record from "./components/record.vue";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import importDialog from "@/components/importDialog";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
      importDialog,
    },
    data() {
      return {
        OPERATIONAL_NATURE,
        WORKING_STATUS,
        CAR_STATUS,
        VEHICLE_ENERGY_TYPE,
        topInfo: {
          buttonName: "新增",
          subTitle: "列表",
          buttonShow: true,
          buttonPermission: "",
        },
        filterForm: {},
        deptOptions: [],
        vehicleModelOptions: [],
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/vehicle/dossier/listPage",
          positionList: "/api/dict/position/list",
          export: "/api/vehicle/dossier/export",
          delete: "/api/vehicle/dossier/delete/",
          deptList: "/api/company/structure/findIn",
          vehicleModel: "/api/dict/vehicleModel/list", //车辆型号
          import: "/api/vehicle/dossier/import",
          template: `/api/vehicle/dossier/template`,
        },
        showRecord: false,
        recordId: "",
        showRoleEdit: false,
        importDialogShow: false,
        importDialogType: "",
        showFilter: false,
        keyword: "",
        deptProps: {
          checkStrictly: true,
          value: "id",
          label: "name",
          emitPath: false,
        },
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
      this.getOptionsList();
    },
    methods: {
      // 获取数据列表
      async getOptionsList() {
        let promiseList = [getInfoApiFun("", this.apis.deptList), getInfoApiFun("", this.apis.vehicleModel)];
        try {
          let res = await Promise.all(promiseList);
          this.deptOptions = res[0].data;
          this.vehicleModelOptions = res[1].data;
        } catch (error) {
          console.warn(error);
        }
      },
      filterChange(params) {
        this.filterForm = JSON.parse(JSON.stringify(params));
        this.initData();
      },
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showRecord = true;
        this.topInfo.buttonShow = false;
        this.topInfo.subTitle = "新增";
        this.recordId = "";
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.topInfo.buttonShow = false;
        this.topInfo.subTitle = "编辑";
        this.recordId = row.id;
      },
      //删除
      async deleteRecord(row) {
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      closeRecord() {
        this.showRecord = false;
        this.topInfo.buttonShow = true;
        this.topInfo.subTitle = "列表";
      },
      //导出
      async handleExport() {
        try {
          let res = await exportFile(BASE_API_URL + this.apis.export, { keyword: this.keyword, ...this.filterForm });
          if (res.success) {
            createDownloadEvent(`车辆档案${Date.now()}.xlsx`, [res.data]);
            window.ELEMENT.Message.success("导出成功");
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 导入
      handleDataImport(command) {
        this.importDialogType = command;
        this.importDialogShow = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
</style>
