<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage @createRecord="createRecord" @closeRecord="closeRecord">
      <record
        v-if="showRecord"
        :recordId="recordId"
        :districtOptions="districtOptions"
        :cascaderProps="cascaderProps"
        @closeRecord="closeRecord"
        @refreshList="initData"
        @openPathAdjust="openPathAdjust"
      ></record>
      <pathAdjust
        v-else-if="showAdjust"
        :recordId="recordId"
        @closeRecord="closeRecord"
        @refreshList="initData"
      ></pathAdjust>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="keyword" placeholder="请输入路线名称/编号" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
            <el-dropdown
              size="small"
              trigger="click"
              @command="handleDataImport"
              class="dropdown-button"
              placement="bottom-start"
            >
              <el-button size="small" type="primary">
                <span class="sctmp-iconfont icon-ic_daoru"></span>
                <span class="ml-5">导入</span>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">
                  <div class="has-padding-sm has-border-bottom">导入基础信息</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button size="small" type="primary" @click="handleExport">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button>
            <el-button class="mr-10" type="primary" size="small" icon="el-icon-folder-opened" @click="handleBackup"
              >备份</el-button
            >
            <el-popover placement="bottom" width="240" trigger="click" :append-to-body="false">
              <div class="reveal-box">
                <div class="reveal-header">
                  <div>展示全部</div>
                  <el-switch
                    :value="allItemChecked"
                    active-text="是"
                    inactive-text="否"
                    @change="changeAllReveal"
                  ></el-switch>
                </div>
                <ul class="reveal-list">
                  <li class="reveal-item" v-for="(item, index) in itemList" :key="index">
                    <el-checkbox :value="item.value" :disabled="index === 0" @change="changeReveal($event, item)">{{
                      item.label
                    }}</el-checkbox>
                  </li>
                </ul>
              </div>
              <el-button slot="reference">
                <div class="btn-inner">
                  <span class="el-icon-s-grid"></span>
                  <span>显示列</span>
                  <span class="el-icon-caret-bottom"></span>
                </div>
              </el-button>
            </el-popover>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
          ></InterviewChannel>
          <el-col :span="8">
            <el-form-item label="驾驶司机">
              <el-select v-model="filterForm.defaultDriverDossierId" placeholder="请选择驾驶司机" clearable filterable>
                <el-option
                  v-for="item in driverOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车辆车牌号">
              <el-select
                v-model="filterForm.defaultVehiclePlateNumber"
                placeholder="请选择车辆车牌号"
                clearable
                filterable
              >
                <el-option v-for="item in carOptions" :key="item.id" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="路线属性">
              <el-select v-model="filterForm.type" placeholder="请选择路线属性" clearable filterable>
                <el-option
                  v-for="(item, index) in ROUTE_PROPERTY"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="路线状态">
              <el-select v-model="filterForm.status" placeholder="请选择路线状态" clearable filterable>
                <el-option v-for="(item, index) in ROUTE_STATUS" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属区域">
              <el-cascader
                v-model="filterForm.districtId"
                :options="districtOptions"
                clearable
                filterable
                :props="cascaderProps"
              ></el-cascader>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            height="100%"
            border
            ref="tableRef"
          >
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column
              prop="name"
              label="路线名称"
              align="center"
              min-width="120"
              v-if="itemList[0].value"
            ></el-table-column>
            <el-table-column
              prop="code"
              label="路线编号"
              align="center"
              min-width="120"
              v-if="itemList[1].value"
            ></el-table-column>
            <el-table-column
              prop="districtName"
              label="所属区域"
              align="center"
              v-if="itemList[2].value"
            ></el-table-column>
            <el-table-column prop="type" label="路线属性" align="center" min-width="120" v-if="itemList[3].value">
              <template #default="{ row }">{{ ROUTE_PROPERTY[row.type] }}</template>
            </el-table-column>
            <el-table-column
              prop="defaultVehiclePlateNumber"
              label="收运车辆"
              align="center"
              v-if="itemList[4].value"
            ></el-table-column>
            <el-table-column
              prop="defaultDriverDossierName"
              label="司机"
              align="center"
              v-if="itemList[5].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierOneName"
              label="押运工"
              align="center"
              v-if="itemList[6].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierTwoName"
              label="押运工2"
              align="center"
              v-if="itemList[7].value"
            ></el-table-column>
            <el-table-column prop="status" label="路线状态" align="center" v-if="itemList[8].value">
              <template #default="{ row }">{{ ROUTE_STATUS[row.status] }}</template>
            </el-table-column>
            <el-table-column prop="waybillType" label="收运方式" align="center" v-if="itemList[9].value">
              <template #default="{ row }">{{ WAYBILL_TYPE[row.waybillType] }}</template>
            </el-table-column>
            <el-table-column
              prop="pointNumber"
              label="点位数量"
              align="center"
              v-if="itemList[10].value"
            ></el-table-column>
            <el-table-column
              prop="defaultDriverDossierPhone"
              label="司机联系方式"
              align="center"
              min-width="120"
              v-if="itemList[11].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierOnePhone"
              label="押运工联系方式"
              align="center"
              min-width="120"
              v-if="itemList[12].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierTwoPhone"
              label="押运工2联系方式"
              align="center"
              min-width="120"
              v-if="itemList[13].value"
            ></el-table-column>
            <el-table-column prop="versionNumber" label="路线版本号" align="center" v-if="itemList[14].value">
              <template #default="{ row }">v{{ row.versionNumber.toFixed(1) }}</template>
            </el-table-column>
            <el-table-column prop="strategy" label="路线优化策略" align="center" v-if="itemList[15].value">
              <template #default="{ row }">{{ ROUTE_STRATEGY[row.strategy] }}</template>
            </el-table-column>
            <el-table-column
              prop="updateFullname"
              label="最近修改人"
              align="center"
              min-width="140"
              v-if="itemList[16].value"
            ></el-table-column>
            <el-table-column
              prop="updateTime"
              label="最近修改时间"
              align="center"
              min-width="160"
              v-if="itemList[17].value"
            ></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center" v-if="itemList[18].value">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column label="操作" align="center" fixed="right" min-width="240">
              <template #default="{ row }">
                <el-link type="primary" @click="editRecord(row)" v-if="row.status === 2">详情</el-link>
                <template v-else>
                  <el-link class="mr-10" type="primary" @click="editRecord(row)">编辑</el-link>
                  <el-link class="mr-10" type="primary" @click="handleAdjust(row)">路径调整</el-link>
                  <el-popconfirm title="确认删除当前收运路线档案？" @confirm="deleteRecord(row)">
                    <el-link class="mr-10" type="danger" slot="reference">删除</el-link>
                  </el-popconfirm>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
    <dateDialog :value.sync="showDate" :routeId="routeId"></dateDialog>
    <importDialog
      :value.sync="importDialogShow"
      title="收运路线档案"
      :importApi="apis.import"
      :templateApi="apis.template"
      :importDialogType="importDialogType"
      :isTemplateApi="true"
      @importSuccess="searchFilter"
    ></importDialog>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import {
    getListPageApiFun,
    deleteApiFun,
    BASE_API_URL,
    getInfoApiFunByParams,
    createApiFun,
    getListApiFun,
  } from "@/api/base";
  import record from "./components/record.vue";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import { ROUTE_PROPERTY, ROUTE_STATUS, ROUTE_STRATEGY, WAYBILL_TYPE } from "@/enums";
  import dateDialog from "./components/dateDialog.vue";
  import pathAdjust from "./components/pathAdjust.vue";
  import importDialog from "@/components/importDialog";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
      dateDialog,
      pathAdjust,
      importDialog,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        ROUTE_PROPERTY,
        ROUTE_STATUS,
        ROUTE_STRATEGY,
        WAYBILL_TYPE,
        tableData: [],
        apis: {
          listPage: "/api/pickup/pickupPath/listPage",
          delete: "/api/pickup/pickupPath/delete/",
          export: "/api/pickup/pickupPath/exportPickupPath",
          regionList: "/api/region/regionList",
          backup: "/api/pickup/pickupPath/backup/backUp",
          carList: "/api/vehicle/dossier/list",
          userList: "/api/baseuser/list",
          import: "/api/pickup/pickupPath/importPickupPath",
          template: "/api/pickup/pickupPath/excleModel",
        },
        showRecord: false,
        recordId: "",
        loading: false,
        showDate: false,
        routeId: "",
        showAdjust: false,
        carOptions: [],
        driverOptions: [],
        importDialogShow: false,
        importDialogType: "",
        showFilter: false,
        keyword: "",
        itemList: [
          { label: "路线名称", value: true },
          { label: "路线编号", value: true },
          { label: "所属区域", value: true },
          { label: "路线属性", value: true },
          { label: "默认车辆", value: true },
          { label: "默认司机", value: true },
          { label: "押运工", value: true },
          { label: "押运工2", value: true },
          { label: "路线状态", value: true },
          { label: "收运方式", value: true },
          { label: "点位数量", value: true },
          { label: "司机联系方式", value: false },
          { label: "押运工联系方式", value: false },
          { label: "押运工2联系方式", value: false },
          { label: "路线版本号", value: false },
          { label: "路线优化策略", value: false },
          { label: "最近修改人", value: false },
          { label: "最近修改时间", value: false },
          { label: "渠道名称", value: true },
        ],
        allItemChecked: false,
        districtOptions: [
          { code: "440100000000", name: "广州市", children: [] },
          { code: "441500000000", name: "汕尾市", children: [] },
          { code: "445100000000", name: "潮州市", children: [] },
        ], //区域列表
        cascaderProps: {
          emitPath: false,
          value: "code",
          label: "name",
        },
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.getOptions();
      this.initData();
      if (this.$route.params.pickupPathId) {
        this.showRecord = true;
        this.recordId = this.$route.params.pickupPathId;
      }
    },
    activated() {
      if (this.$route.params.pickupPathId) {
        this.showRecord = true;
        this.recordId = this.$route.params.pickupPathId;
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getInfoApiFunByParams({ pid: this.districtOptions[0].code }, this.apis.regionList),
          getInfoApiFunByParams({ pid: this.districtOptions[1].code }, this.apis.regionList),
          getInfoApiFunByParams({ pid: this.districtOptions[2].code }, this.apis.regionList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.districtOptions[0].children = res[2].data;
        this.districtOptions[1].children = res[3].data;
        this.districtOptions[2].children = res[4].data;
      },
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas.map((item) => {
            return {
              ...item,
              defaultDriverDossierPhone: item.defaultDriverDossierPhone
                ? this.$sm2Decrypt(item.defaultDriverDossierPhone)
                : "",
              supercargoDossierOnePhone: item.supercargoDossierOnePhone
                ? this.$sm2Decrypt(item.supercargoDossierOnePhone)
                : "",
              supercargoDossierTwoPhone: item.supercargoDossierTwoPhone
                ? this.$sm2Decrypt(item.supercargoDossierTwoPhone)
                : "",
            };
          });
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showRecord = true;
        this.recordId = "";
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.recordId = row.id;
      },
      // 路径调整
      handleAdjust(row) {
        this.showAdjust = true;
        this.recordId = row.id;
      },
      closeRecord() {
        this.showRecord = false;
        this.showAdjust = false;
      },
      // 删除
      async deleteRecord(row) {
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            this.page.pageNo = this.pageDropProcess(this.page.pageNo, this.page.pageSize, this.page.total);
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 导出
      handleExport() {
        this.$confirm(`确认是否以当前筛选条件导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, {
                keyword: this.keyword,
                ...this.filterForm,
              });
              if (res.success) {
                createDownloadEvent(`收运路线档案${Date.now()}.xlsx`, [res.data]);
                window.ELEMENT.Message.success("导出成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
      // 备份
      handleBackup() {
        this.$confirm(`确认是否备份当前收运路线档案`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await createApiFun({}, this.apis.backup);
              if (res.success) {
                this.$message.success("备份成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
      // 设置生效日期
      handleSetDate(row) {
        this.routeId = row.id;
        this.showDate = true;
      },
      // 导入
      handleDataImport(command) {
        this.importDialogType = command;
        this.importDialogShow = true;
      },
      // 修改所有显示列
      changeAllReveal(value) {
        this.allItemChecked = value;
        this.itemList.slice(1, this.itemList.length).forEach((item) => (item.value = value));
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 列点击事件
      changeReveal(value, item) {
        item.value = value;
        let uncheckList = this.itemList.slice(1, this.itemList.length).filter((list) => !list.value);
        if (uncheckList.length === 0) {
          this.allItemChecked = true;
        } else {
          this.allItemChecked = false;
        }
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 打开路线优化
      openPathAdjust(id) {
        this.recordId = id;
        this.showAdjust = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .w250 {
    width: 250px;
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
</style>
