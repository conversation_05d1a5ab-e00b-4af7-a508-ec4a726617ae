<template>
  <div class="container micro-app-sctmp_base">
    <div class="title">收运车辆信息</div>
    <div class="search">
      <el-input placeholder="输入路线名称/司机名称/车牌号" v-model="keyword" clearable class="search-input"></el-input>
      <el-link type="primary" class="search-button" @click="getDataList">搜索</el-link>
    </div>
    <ul class="header-list">
      <li v-for="(item, index) in headerList" :key="index">{{ item }}</li>
    </ul>
    <div class="line"></div>
    <vue-seamless-scroll
      v-if="listData && listData.length > 0"
      class="seaml-scroll"
      :data="listData"
      :class-option="seamlessScrollOption"
    >
      <ul>
        <div class="data-item" v-for="(item, index) in listData" :key="index">
          <div class="text-ellipsis">{{ item.name }}</div>
          <div class="text-ellipsis">{{ item.defaultDriverDossierName }}</div>
          <div class="text-ellipsis">{{ item.defaultVehiclePlateNumber }}</div>
          <span class="item-link" @click="viewDetail(item)">查看详情</span>
        </div>
      </ul>
    </vue-seamless-scroll>
    <el-empty description="暂无数据" v-else class="seaml-scroll"></el-empty>
  </div>
</template>

<script>
  export default {
    props: {
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      listData() {
        return this.$store.state.waybillList;
      },
    },
    watch: {
      listData(newV) {
        if (newV.length < 6) {
          this.seamlessScrollOption.step = 0;
        } else {
          this.seamlessScrollOption.step = 2;
        }
      },
      channelId() {
        this.getDataList();
      },
    },
    data() {
      return {
        keyword: "",
        seamlessScrollOption: {
          step: 2, // 数值越大速度滚动越快
          limitMoveNum: 6, // 开始无缝滚动的数据量 this.dataList.length
          hoverStop: true, // 是否开启鼠标悬停stop
          direction: 1, // 0向下 1向上 2向左 3向右
          openWatch: true, // 开启数据实时监控刷新dom
          singleHeight: 50, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
          singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
          waitTime: 2000, // 单步运动停止的时间(默认值1000ms)
        },
        headerList: ["路线名称", "驾驶司机", "车牌号", "操作"],
      };
    },
    mounted() {
      this.getDataList();
    },
    methods: {
      async getDataList() {
        this.$store.dispatch("getWaybillList", { keyword: this.keyword, channelId: this.channelId });
      },
      viewDetail(item) {
        this.$router.push(`/sctmp_base/realTimeDriving?plateNumber=${item.defaultVehiclePlateNumber}`);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 20px;
    pointer-events: auto;
  }
  .title {
    color: #fff;
    font-size: 16px;
    font-weight: bold;
  }
  .search {
    display: flex;
    align-items: center;
    margin: 20px 0;
    .search-input {
      flex: 1;
      overflow: hidden;
    }
    .search-button {
      flex-shrink: 0;
      margin-left: 20px;
    }
  }
  .line {
    width: 100%;
    height: 1px;
    background-color: #fff;
    margin-top: 10px;
  }
  .header-list,
  .data-item {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    color: #fff;
    font-size: 12px;
    text-align: center;
    line-height: 16px;
    margin-bottom: 14px;
  }
  .seaml-scroll {
    height: 190px;
    overflow: hidden;
    width: 100%;
    margin-top: 10px;
  }
  .item-link {
    cursor: pointer;
    color: var(--color-primary);
    &:hover {
      text-decoration: underline;
    }
  }
  ::v-deep .search-input input {
    border: none;
    outline: none;
    background-color: rgba(153, 153, 153, 0.4);
    color: #fff;
  }
</style>
