<template>
  <div class="micro-app-sctmp_base driving-record-container full-height" v-loading="loading">
    <main class="driving-record-main">
      <div class="left" v-show="isExpand">
        <div class="left-channel" v-if="!channel">
          <el-radio-group v-model="channelId" size="medium" @change="toggleChannel">
            <el-radio-button :label="label.id" v-for="label in channelList" :key="label.id">{{
              label.name
            }}</el-radio-button>
          </el-radio-group>
        </div>
        <el-input class="w-400" v-model="plateNumber" placeholder="查询车辆" clearable size="small"></el-input>
        <div class="car-list">
          <el-tree
            :data="carOptions"
            @node-click="handleNodeClick"
            empty-text=""
            node-key="id"
            :props="{ label: 'name' }"
          >
            <div class="tree-item" slot-scope="{ data }">
              <span class="el-icon-location" :class="{ 'icon-active': filterForm.plateNumber === data.name }"></span>
              <span class="node-label" :class="{ active: filterForm.plateNumber === data.name }">{{ data.name }}</span>
            </div>
          </el-tree>
        </div>
        <div class="time-box">
          <el-form ref="filterForm" :model="filterForm">
            <el-form-item label="开始时间">
              <el-date-picker
                v-model="filterForm.startTime"
                type="datetime"
                placeholder="开始时间"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                size="small"
                :picker-options="startTimeOptions"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间">
              <el-date-picker
                v-model="filterForm.endTime"
                type="datetime"
                placeholder="结束时间"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                size="small"
                :picker-options="endTimeOptions"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
          <div class="button-box">
            <el-button
              type="primary"
              size="small"
              :disabled="!filterForm.plateNumber || !filterForm.startTime || !filterForm.endTime"
              @click="queryTrack"
              >查询轨迹</el-button
            >
            <el-button type="success" size="small" :disabled="tableData.length == 0" @click="startAnimation"
              >开始回放</el-button
            >
          </div>
          <div class="button-box">
            <el-button type="warning" size="small" :disabled="tableData.length == 0" @click="pauseAnimation"
              >暂停回放</el-button
            >
            <el-button type="danger" size="small" :disabled="tableData.length == 0" @click="resumeAnimation"
              >继续回放</el-button
            >
          </div>
          <div class="export-box">
            <el-button type="primary" size="small" :disabled="tableData.length == 0" @click="exportDrvingRecord"
              >导出明细</el-button
            >
            <div class="export-right"></div>
          </div>
        </div>
      </div>
      <div class="middle">
        <el-button type="text" size="small" class="toggle-button" @click="toggleLeft">
          <span :class="[isExpand ? 'el-icon-arrow-left' : 'el-icon-arrow-right']"></span>
        </el-button>
      </div>
      <div class="right">
        <div class="tooltip-box" v-show="showMap">
          <div>红点&nbsp;&nbsp;&nbsp;上报经纬度</div>
          <div>路线&nbsp;&nbsp;&nbsp;行驶路线</div>
        </div>
        <div class="right-map" v-show="showMap">
          <mapContainer @initMap="initMap"></mapContainer>
        </div>
        <div class="right-expand">
          <el-button type="text" size="small" class="expand-button" @click="showMap = !showMap">
            <span :class="[showMap ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"></span>
          </el-button>
        </div>
        <div class="right-table">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column prop="plateNumber" label="车牌号" align="center"></el-table-column>
            <el-table-column prop="positionTime" label="定位时间" align="center" min-width="140"></el-table-column>
            <el-table-column prop="speed" label="速度(km/h)" align="center"></el-table-column>
            <el-table-column prop="mileage" label="里程数" align="center"></el-table-column>
            <el-table-column prop="status" label="车辆状态" align="center">
              <template #default="{ row }">{{ DRIVING_CAR_STATUS[row.status] }}</template>
            </el-table-column>
            <el-table-column prop="address" label="详细地址" align="center" min-width="600"></el-table-column>
            <el-table-column prop="longitude" label="经度" align="center"></el-table-column>
            <el-table-column prop="latitude" label="纬度" align="center"></el-table-column>
          </el-table>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
  import mapContainer from "@/components/mapContainer";
  import { createApiFun, BASE_API_URL, getInfoApiFun } from "@/api/base";
  import { DRIVING_CAR_STATUS } from "@/enums";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import moment from "moment";
  import { wgs84togcj02 } from "@/utils/coordinateTransform";
  import { getLocalStorage } from "@/utils/storage";
  import { INTERVIEW_CHANNEL } from "@/utils/commonValue"; //访问渠道
  export default {
    components: {
      mapContainer,
    },
    data() {
      return {
        plateNumber: "",
        filterForm: {
          plateNumber: "",
          startTime: "",
          endTime: "",
        },
        loading: false,
        carList: [],
        isExpand: true,
        tableData: [],
        apis: {
          carList: "/api/vehicle/dossier/list",
          list: "/api/vehicle/drivingRecord/list",
          export: "/api/vehicle/drivingRecord/export",
          channelList: "/api/base/dna/listByDna/",
        },
        map: "",
        DRIVING_CAR_STATUS,
        carMarker: "",
        routePath: [],
        showMap: true,
        startTimeOptions: {
          disabledDate: (time) => {
            if (this.filterForm.endTime) {
              let endDate = new Date(`${moment(this.filterForm.endTime).format("YYYY/MM/DD")} 00:00:00`).getTime();
              return time.getTime() > endDate || time.getTime() < endDate - 2 * 24 * 60 * 60 * 1000;
            }
            return false;
          },
        },
        endTimeOptions: {
          disabledDate: (time) => {
            if (this.filterForm.startTime) {
              let startDate = new Date(`${moment(this.filterForm.startTime).format("YYYY/MM/DD")} 00:00:00`).getTime();
              return time.getTime() < startDate || time.getTime() > startDate + 2 * 24 * 60 * 60 * 1000;
            }
            return false;
          },
        },
        polygon: null,
        districtSearch: "",
        channelId: null,
        channelList: [{ id: "", name: "本部" }],
        channel: "",
      };
    },
    computed: {
      carOptions() {
        if (!this.plateNumber) {
          return this.carList;
        }
        return this.carList.filter((list) => list.name.includes(this.plateNumber));
      },
    },
    created() {
      this.channel = getLocalStorage(INTERVIEW_CHANNEL);
      if (this.channel) {
        this.getOptions();
      } else {
        this.getChannelList();
      }
    },
    beforeDestroy() {
      if (this.carMarker) {
        this.pauseAnimation();
      }
      this.map.destroy();
      this.map = "";
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        this.carList = [];
        if (!this.channel && this.channelId === null) return;
        let res = await createApiFun(
          { statusList: [0, 1], channelId: this.channelId || this.channel },
          this.apis.carList,
        );
        this.carList = res.data;
      },
      // 获取渠道列表
      async getChannelList() {
        let res = await getInfoApiFun(this.$DNACONFIG.dna, this.apis.channelList);
        this.channelList = this.channelList.concat(res.data);
      },
      // 切换渠道
      toggleChannel() {
        this.plateNumber = "";
        this.getOptions();
      },
      // 初始化地图
      initMap(map) {
        if (map) {
          this.map = map;
          window.AMap.plugin("AMap.Scale", () => {
            let scale = new window.AMap.Scale(); //创建工具条插件实例
            map.addControl(scale); //添加工具条插件到页面
          });
          window.AMap.plugin("AMap.ToolBar", () => {
            let toolbar = new window.AMap.ToolBar(); //创建工具条插件实例
            map.addControl(toolbar); //添加工具条插件到页面
          });
        }
      },
      handleNodeClick(data) {
        this.filterForm.plateNumber = data.name;
        this.$message.success(`当前选择车牌号为：${data.name}`);
      },
      // 展开/隐藏左侧盒子
      toggleLeft() {
        this.isExpand = !this.isExpand;
      },
      // 查询轨迹
      async queryTrack() {
        if (this.carMarker) {
          this.pauseAnimation();
        }
        this.loading = true;
        try {
          let res = await createApiFun(this.filterForm, this.apis.list);
          if (res.success) {
            this.tableData = res.data;
            this.map.clearMap();
            this.map.clearInfoWindow();
            if (res.data.length > 0) {
              const seen = new Set();
              let resList = res.data
                .filter((item) => {
                  const str = JSON.stringify([item.longitude, item.latitude]);
                  if (!seen.has(str)) {
                    seen.add(str);
                    return true;
                  }
                  return false;
                })
                .reverse();
              let pathList = resList.map((list) => {
                return wgs84togcj02(list.longitude, list.latitude);
              });
              this.drawMarker(resList, pathList);
              this.drawRoute(pathList);
            }
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 绘制点位
      drawMarker(dataList, pathList) {
        // 设置汽车点位图标
        const icon = new window.AMap.Icon({
          size: new window.AMap.Size(26, 52), //图标尺寸
          image: "https://a.amap.com/jsapi_demos/static/demo-center-v2/car.png", //Icon 的图像
          imageOffset: new window.AMap.Pixel(2, 0), //图像相对展示区域的偏移量，适于雪碧图等
        });
        this.carMarker = new window.AMap.Marker({
          map: this.map,
          position: pathList[0],
          icon: icon,
          offset: new window.AMap.Pixel(-13, -26),
        });
        // 设置起始/终点坐标点位图标
        const startIcon = new window.AMap.Icon({
          // 图标尺寸
          size: new window.AMap.Size(25, 34),
          // 图标的取图地址
          image: "https://a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png",
          // 图标所用图片大小
          imageSize: new window.AMap.Size(135, 40),
          // 图标取图偏移量
          imageOffset: new window.AMap.Pixel(-9, -3),
        });
        let startMarker = new window.AMap.Marker({
          position: pathList[0],
          icon: startIcon,
          offset: new window.AMap.Pixel(-13, -30),
        });

        const endIcon = new window.AMap.Icon({
          size: new window.AMap.Size(25, 34),
          image: "https://a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png",
          imageSize: new window.AMap.Size(135, 40),
          imageOffset: new window.AMap.Pixel(-95, -3),
        });

        // 将 icon 传入 marker
        let endMarker = new window.AMap.Marker({
          position: pathList[pathList.length - 1],
          icon: endIcon,
          offset: new window.AMap.Pixel(-13, -30),
        });
        // 将 markers 添加到地图
        this.map.add([startMarker, endMarker]);
        // 添加点位标记
        let content = `<div class="marker-icon" style="width:8px;height:8px;overflow:hidden;background-color:#D9001B;color:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:12px"></div>`;
        dataList.forEach((data, index) => {
          let marker = new window.AMap.Marker({
            content,
            map: this.map,
            position: pathList[index],
          });
          marker.content = `<div>定位时间：${data.positionTime}</div><div>速度：${data.speed}km/h</div>`;
          marker.on("click", this.markerClick);
        });
      },
      // 点标记点击事件
      markerClick(e) {
        let infoWindow = new window.AMap.InfoWindow({ offset: new window.AMap.Pixel(4, -4) });
        infoWindow.setContent(e.target.content);
        infoWindow.open(this.map, e.target.getPosition());
      },
      // 开始动画
      startAnimation() {
        window.AMap.plugin("AMap.MoveAnimation", () => {
          //在回调函数中实例化插件，并使用插件功能
          this.carMarker.moveAlong(this.routePath, {
            // 每一段的时长
            duration: 100, //可根据实际采集时间间隔设置
            // JSAPI2.0 是否延道路自动设置角度在 moveAlong 里设置
            autoRotation: true,
          });
        });
      },
      // 暂停动画
      pauseAnimation() {
        this.carMarker.pauseMove();
      },
      // 继续动画
      resumeAnimation() {
        this.carMarker.resumeMove();
      },
      // 调用高德地图jsApi获取驾驶路线
      handleDrive(start, end, points) {
        window.AMap.plugin("AMap.Driving", () => {
          let driving = new window.AMap.Driving({
            // map: this.map,
            policy: 0, //驾车路线规划策略，0是速度优先的策略
            // panel: "container",
          });
          driving.search(start, end, { waypoints: points }, (status, result) => {
            //status：complete 表示查询成功，no_data 为查询无结果，error 代表查询错误
            //查询成功时，result 即为对应的驾车导航信息
            if (status === "complete") {
              this.drawRoute(result.routes[0]);
            }
          });
        });
      },
      drawRoute(path) {
        // let path = this.parseRouteToPath(route);
        this.routePath = this.routePath.concat(path);
        // 绘制轨迹
        new window.AMap.Polyline({
          map: this.map,
          path,
          showDir: true,
          strokeColor: "#28F", //线颜色
          strokeWeight: 6, //线宽
        });
        let passedPolyline = new window.AMap.Polyline({
          map: this.map,
          strokeColor: "#AF5", //线颜色
          strokeWeight: 6, //线宽
        });
        this.carMarker.on("moving", (e) => {
          passedPolyline.setPath(e.passedPath);
        });
        this.map.setFitView();
      },
      // 解析DrivingRoute对象，构造成AMap.Polyline的path参数需要的格式
      parseRouteToPath(route) {
        let path = [];
        for (let i = 0, l = route.steps.length; i < l; i++) {
          let step = route.steps[i];
          for (let j = 0, n = step.path.length; j < n; j++) {
            path.push(step.path[j]);
          }
        }
        return path;
      },
      // 按照数组长度为16切割，多余的数据存进最后一个数组
      cutArray(array) {
        let finalArray = [];
        // 商
        let quotient = Math.floor(array.length / 16);
        // 余数
        let remainder = array.length % 16;
        // 是否为16的倍数
        if (quotient > 0) {
          for (let i = 0; i < quotient; i++) {
            finalArray.push(array.slice(i * 16, (i + 1) * 16));
          }
          if (remainder > 0) {
            finalArray.push(array.slice(16 * quotient, array.length));
          }
          // 需要插入数组的索引数组
          let indexArr = [];
          // 需要插入数组的数据数组
          let valueArr = [];
          finalArray.forEach((arr, index) => {
            if (index >= 0 && index < finalArray.length - 1) {
              indexArr.push(index);
              valueArr.push([arr[15], finalArray[index + 1][0]]);
            }
          });
          // 将每个数组的终点坐标和起点坐标连接成数组并插入对应的位置
          indexArr.forEach((item, number) => {
            if (number >= 0 && number < indexArr.length) {
              finalArray.splice(item + number + 1, 0, valueArr[number]);
            }
          });
          finalArray = finalArray.filter((list) => list.length > 1);
        } else {
          finalArray = [array];
        }
        return finalArray;
      },
      // 导出明细
      exportDrvingRecord() {
        this.$confirm(`确认是否以当前车辆与时间导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, this.filterForm);
              if (res.success) {
                createDownloadEvent(
                  `${this.filterForm.plateNumber} ${this.filterForm.startTime}-${
                    this.filterForm.endTime
                  }行驶记录${Date.now()}.xlsx`,
                  [res.data],
                );
                window.ELEMENT.Message.success("导出成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
    },
  };
</script>

<style lang="scss" scoped>
  .driving-record-container {
    width: 100%;
    display: flex;
    flex-direction: column;
  }
  .driving-record-main {
    flex: 1;
    overflow: auto;
    display: flex;
    margin: 20px;
    background-color: #fff;
  }
  .w-400 {
    width: 400px;
  }
  .left {
    border: 1px solid #ebeef5;
    display: flex;
    flex-direction: column;
    .left-channel {
      padding: 4px 10px;
    }
    .car-list {
      flex: 1;
      overflow-y: auto;
      .tree-item {
        .node-label {
          margin-left: 5px;
          color: #606266;
          &.active {
            color: #67c23a;
          }
        }
        .icon-active {
          color: #67c23a;
        }
      }
    }
    .time-box {
      border-top: 1px solid #c1c5cd;
      background-color: #ced9e7;
      padding: 10px;
      padding-bottom: 40px;
      .button-box {
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        margin-bottom: 10px;
      }
    }
  }
  .middle {
    display: flex;
    align-items: center;
    .toggle-button {
      background-color: #67c23a;
      color: #fff;
    }
  }
  .right {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    .right-map {
      height: 900px;
    }
    .right-expand {
      display: flex;
      align-items: center;
      justify-content: center;
      .expand-button {
        background-color: #67c23a;
        color: #fff;
        padding: 2px 8px;
      }
    }
    .right-table {
      flex: 1;
      overflow: hidden;
    }
    .tooltip-box {
      padding: 16px;
      background-color: #fff;
      position: absolute;
      top: 0;
      right: 0;
      z-index: 600;
    }
  }
  .export-box {
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    .export-right {
      width: 90px;
    }
  }
  ::v-deep .is-current .el-tree-node__content {
    background-color: #ecf9f8;
  }
  ::v-deep .time-box .el-form-item {
    margin-bottom: 10px;
  }
</style>
