<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">{{ recordId ? "监督抽查记录编辑" : "新增监督抽查记录" }}</div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
        <baseTitle title="基础信息"></baseTitle>
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="检查人" prop="inspectorName">
              <el-input
                v-model="ruleForm.inspectorName"
                placeholder="请输入检查人姓名"
                clearable
                maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="被检查人" prop="checkedPersonName">
              <el-input
                v-model="ruleForm.checkedPersonName"
                placeholder="请输入被检查人姓名"
                clearable
                maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="检查车辆" prop="checkedVehiclePlateNumber">
              <el-select v-model="ruleForm.checkedVehiclePlateNumber" placeholder="请选择检查车辆" clearable filterable>
                <el-option v-for="item in carOptions" :key="item.id" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="抽查日期" prop="spotCheckDate">
              <el-date-picker
                class="w-300"
                v-model="ruleForm.spotCheckDate"
                type="date"
                placeholder="请选择抽查日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="是否危运车辆" prop="isError">
              <el-switch
                v-model="ruleForm.isError"
                active-text="是"
                inactive-text="否"
                :active-value="1"
                :inactive-value="0"
              ></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="抽查信息"></baseTitle>
        <el-row>
          <el-col>
            <el-form-item label="问题" prop="problemDescription">
              <el-input
                v-model="ruleForm.problemDescription"
                placeholder="请输入问题"
                clearable
                type="textarea"
                rows="8"
                maxlength="250"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="是否完成整改" prop="rectifiedStatus">
              <el-select v-model="ruleForm.rectifiedStatus" placeholder="请选择是否完成整改" clearable filterable>
                <el-option
                  v-for="(item, index) in SUPERVISORY_TEST_COMPLETED"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="是否进行酒精测试" prop="isAlcohol">
              <el-switch
                v-model="ruleForm.isAlcohol"
                active-text="是"
                inactive-text="否"
                :active-value="1"
                :inactive-value="0"
              ></el-switch>
            </el-form-item>
          </el-col>
          <template v-if="ruleForm.isAlcohol">
            <el-col>
              <el-form-item label="酒精测试/人" prop="alcoholTestCount">
                <el-input-number v-model="ruleForm.alcoholTestCount" :min="0" step-strictly></el-input-number>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="酒精测试异常人员" prop="alcoholAbnormalPerson">
                <el-input
                  v-model="ruleForm.alcoholAbnormalPerson"
                  placeholder="请输入酒精测试异常人员"
                  clearable
                  type="textarea"
                  rows="5"
                ></el-input>
              </el-form-item>
            </el-col>
          </template>
          <el-col :md="24" :lg="12">
            <el-form-item label="整改前照片" prop="beforeRectificationPhotoUrl">
              <FileUpload
                @uploadChange="handleUploadChange($event, 'beforeRectificationPhotoUrl')"
                :limit="5"
                :imageList="photoImageList['beforeRectificationPhotoUrl']"
              >
                <template #tips><el-tag type="warning">请上传整改前的车辆照片</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="整改后照片" prop="afterRectificationPhotoUrl">
              <FileUpload
                @uploadChange="handleUploadChange($event, 'afterRectificationPhotoUrl')"
                :limit="5"
                :imageList="photoImageList['afterRectificationPhotoUrl']"
              >
                <template #tips><el-tag type="warning">请上传整改后的车辆照片</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import { SUPERVISORY_TEST_COMPLETED } from "@/enums";
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  import FileUpload from "@/components/FileUpload";
  import baseTitle from "@/components/baseTitle";
  import moment from "moment";
  import { deepClone } from "logan-common/utils";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      FileUpload,
      baseTitle,
    },
    data() {
      return {
        ruleForm: {
          inspectorName: "", //检查人
          checkedPersonId: "", //被检查人
          checkedVehiclePlateNumber: "", //车牌号
          spotCheckDate: moment(new Date()).format("YYYY-MM-DD"), //抽查日期
          problemDescription: "", //问题
          alcoholTestCount: 0, //酒精测试/人
          rectifiedStatus: "", //是否完成整改
          beforeRectificationPhotoUrl: [], //整改前照片
          afterRectificationPhotoUrl: [], //整改后照片
          isError: 0, // 是否危运车辆
          isAlcohol: 0, //是否进行酒精测试
          alcoholAbnormalPerson: "", //酒精测试异常人员
        },
        rules: {
          inspectorName: [{ required: true, message: "请输入检查人姓名", trigger: "blur" }],
          checkedPersonName: [{ required: true, message: "请输入被检查人", trigger: "blur" }],
          checkedVehiclePlateNumber: [{ required: true, message: "请选择检查车辆", trigger: "change" }],
          spotCheckDate: [{ required: true, message: "请选择抽查日期", trigger: "change" }],
          problemDescription: [{ required: true, message: "请输入问题", trigger: "blur" }],
          rectifiedStatus: [{ required: true, message: "请选择是否完成整改", trigger: "change" }],
          isError: [{ required: true, message: "请选择是否危运车辆", trigger: "change" }],
          isAlcohol: [{ required: true, message: "请选择是否进行酒精测试", trigger: "change" }],
        },
        apis: {
          create: "/api/safety/spotCheckRecord/create",
          update: "/api/safety/spotCheckRecord/update",
          info: "/api/safety/spotCheckRecord/get/",
          carList: "/api/vehicle/dossier/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        SUPERVISORY_TEST_COMPLETED,
        carOptions: [],
        photoImageList: {
          beforeRectificationPhotoUrl: [],
          afterRectificationPhotoUrl: [],
        },
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    async mounted() {
      if (this.recordId) {
        await this.getRecord();
      }
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({ statusList: [0, 1] }, this.apis.carList)];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
      },
      handleBackImage() {
        for (const key in this.photoImageList) {
          if (Object.hasOwnProperty.call(this.photoImageList, key)) {
            if (this.ruleForm[key] && this.ruleForm[key].length > 0) {
              this.photoImageList[key] = this.ruleForm[key].map((list) => {
                return {
                  url: list.url,
                  file: list,
                };
              });
            }
          }
        }
      },
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          try {
            this.ruleForm.beforeRectificationPhotoUrl = JSON.parse(this.ruleForm.beforeRectificationPhotoUrl);
            this.ruleForm.afterRectificationPhotoUrl = JSON.parse(this.ruleForm.afterRectificationPhotoUrl);
          } catch (error) {
            this.ruleForm.beforeRectificationPhotoUrl = [];
            this.ruleForm.afterRectificationPhotoUrl = [];
          }
          this.handleBackImage();
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            let params = deepClone(this.ruleForm);
            params.beforeRectificationPhotoUrl = JSON.stringify(params.beforeRectificationPhotoUrl);
            params.afterRectificationPhotoUrl = JSON.stringify(params.afterRectificationPhotoUrl);
            try {
              let res = this.recordId
                ? await updateApiFun(params, this.apis.update)
                : await createApiFun(params, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      handleUploadChange(fileList, field) {
        if (fileList.length) {
          this.ruleForm[field] = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
        } else {
          this.ruleForm[field] = [];
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 16px;
  }
  .type-card {
    margin-bottom: 20px;
  }
  .w-300 {
    width: 300px;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
</style>
