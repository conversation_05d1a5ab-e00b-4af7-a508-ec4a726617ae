<template>
  <div class="filter-content">
    <el-form label-suffix=":" :label-width="labelWidth">
      <el-row>
        <slot></slot>
      </el-row>
    </el-form>
  </div>
</template>

<script>
  export default {
    props: {
      labelWidth: {
        type: String,
        default: "80px",
      },
    },
  };
</script>

<style lang="scss" scoped>
  .filter-content {
    background-color: #f7f8fa;
    margin-top: 16px;
    padding: 0 10px 10px 10px;
  }
</style>
