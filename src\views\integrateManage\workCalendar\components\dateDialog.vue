<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="调整" :visible.sync="dialogVisible" width="30%" destroy-on-close @open="initData">
      <div v-loading="loading">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px">
          <el-form-item prop="isHoliday" label="当天内容">
            <el-radio-group v-model="ruleForm.isHoliday">
              <el-radio :label="index" v-for="(item, index) in WORK_CALENDAR_STATUS" :key="index">{{ item }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="isSalaryHoliday" label="是否三倍加班工资" v-if="ruleForm.isHoliday === 1">
            <el-radio-group v-model="ruleForm.isSalaryHoliday">
              <el-radio :label="index" v-for="(item, index) in salaryHolidayList" :key="index">{{ item }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="调整备注" prop="remark">
            <el-input type="textarea" v-model="ruleForm.remark" :rows="8"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { WORK_CALENDAR_STATUS } from "@/enums";
  import { filterObjectByFieldArr } from "@/utils";
  import { updateApiFun } from "@/api/base";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      dialogForm: {
        type: Object,
        default: () => {},
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        WORK_CALENDAR_STATUS,
        ruleForm: {
          isHoliday: "",
          remark: "",
          isSalaryHoliday: "",
        },
        rules: {
          isHoliday: [{ required: true, message: "请选择当天内容", trigger: "change" }],
          isSalaryHoliday: [{ required: true, message: "请选择是否节假日", trigger: "change" }],
        },
        apis: {
          update: "/api/holiday/update",
        },
        saveRecordThrottling: () => {},
        loading: false,
        salaryHolidayList: ["否", "是"],
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    methods: {
      initData() {
        this.ruleForm = Object.assign(this.ruleForm, this.dialogForm);
      },
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            let params = filterObjectByFieldArr(this.ruleForm, ["id", "isHoliday", "isSalaryHoliday", "remark"]);
            if (params.isHoliday === 0) {
              params.isSalaryHoliday = 0;
            }
            this.loading = true;
            try {
              let res = await updateApiFun(params, this.apis.update);
              if (res.success) {
                this.$message.success("调整成功");
                this.dialogVisible = false;
                this.$emit("refreshCalendar");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
