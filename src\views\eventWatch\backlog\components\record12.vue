<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">驾驶证过期待处理</div>
      <el-form label-width="210px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="上报时间" required>
              <el-date-picker
                :value="recordItem.createTime"
                type="datetime"
                placeholder="请选择上报时间"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="截止日期" required>
              <el-date-picker
                :value="recordItem.deadline"
                type="date"
                placeholder="请选择截止日期"
                clearable
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="基础信息"></baseTitle>
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="用户名" required>
              <el-input
                :value="baseForm.userName ? baseForm.userName : baseForm.phone"
                placeholder="请输入用户名"
                clearable
                :maxlength="13"
                show-word-limit
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="真实姓名" required>
              <el-input
                :value="baseForm.fullName"
                placeholder="请输入真实姓名"
                clearable
                :maxlength="14"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="邮箱">
              <el-input :value="baseForm.email" placeholder="请输入邮箱" clearable readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="性别" required>
              <el-select :value="baseForm.sex" placeholder="请选择性别" clearable filterable disabled>
                <el-option v-for="(item, index) in SEX_OPTIONS" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="联系电话" required>
              <el-input
                :value="baseForm.phone"
                placeholder="请输入联系电话"
                clearable
                :maxlength="11"
                show-word-limit
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="工号" required>
              <el-input
                :value="baseForm.jobNo"
                placeholder="请输入工号"
                clearable
                maxlength="20"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="身份证号码">
              <el-input
                :value="baseForm.idCard"
                placeholder="身份证号码"
                clearable
                maxlength="18"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="所属部门">
              <el-cascader
                ref="cascaderRef"
                :value="baseForm.deptId"
                placeholder="请选择所属部门"
                filterable
                clearable
                :options="deptOptions"
                :props="deptProps"
                :show-all-levels="false"
                disabled
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="部门负责人">
              <el-input :value="baseForm.deptHeadName" placeholder="请输入部门负责人" clearable readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="直属上级">
              <el-select :value="baseForm.directSuperior" placeholder="请选择直属上级" clearable filterable disabled>
                <el-option
                  v-for="item in userOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="在职状态" required>
              <el-select :value="baseForm.jobStatus" placeholder="请选择在职状态" clearable filterable disabled>
                <el-option v-for="(item, index) in JOB_STATUS" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="职位">
              <el-select :value="baseForm.position" placeholder="请选择职位" clearable filterable disabled>
                <el-option
                  v-for="item in positionOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="默认驾驶车辆" required>
              <el-select :value="baseForm.plateNumber" placeholder="请选择车牌号" clearable filterable disabled>
                <el-option v-for="item in carOptions" :key="item.id" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="准驾车型" required>
              <el-select :value="baseForm.quasiDrivingType" placeholder="请选择准驾车型" clearable filterable disabled>
                <el-option
                  v-for="(item, index) in QUASI_DRIVING_TYPE"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="用户头像">
              <el-upload
                action="custom"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :file-list="imageList"
                disabled
                class="base-upload"
                v-if="imageList && imageList.length > 0"
              ></el-upload>
              <span v-else>-</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="180px" label-suffix="：">
        <el-row>
          <el-col>
            <el-form-item label="驾驶证编号" prop="drivingCertificateNumber">
              <el-input
                class="w-400"
                v-model="ruleForm.drivingCertificateNumber"
                placeholder="请输入驾驶证编号"
                clearable
                maxlength="100"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="驾驶证有效期开始日期" prop="drivingCertificateBeginDate">
              <el-date-picker
                v-model="ruleForm.drivingCertificateBeginDate"
                type="date"
                placeholder="选择有效期开始日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="驾驶证有效期结束日期" prop="drivingCertificateEndDate">
              <el-date-picker
                v-model="ruleForm.drivingCertificateEndDate"
                type="date"
                placeholder="选择有效期结束日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="驾驶证登记日期" prop="drivingCertificateRegistrationDate">
              <el-date-picker
                v-model="ruleForm.drivingCertificateRegistrationDate"
                type="date"
                placeholder="选择登记日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="驾驶证主页照片" prop="drivingCertificatePhotoFront">
              <FileUpload
                @uploadChange="handleUploadChange($event, 'drivingCertificatePhotoFront')"
                :imageList="photoImageList['drivingCertificatePhotoFront']"
              ></FileUpload>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="驾驶证副页照片" prop="drivingCertificatePhotoBack">
              <FileUpload
                @uploadChange="handleUploadChange($event, 'drivingCertificatePhotoBack')"
                :imageList="photoImageList['drivingCertificatePhotoBack']"
              ></FileUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form label-width="210px" label-suffix="：">
        <el-row>
          <el-col>
            <el-form-item label="从业资格证编号" required>
              <el-input
                class="w-400"
                :value="baseForm.qualificationCertificateNumber"
                placeholder="请输入从业资格证编号"
                clearable
                maxlength="100"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="从业资格证有效期开始日期" required>
              <el-date-picker
                :value="baseForm.qualificationCertificateBeginDate"
                type="date"
                placeholder="选择有效期开始日期"
                clearable
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="从业资格证有效期结束日期" required>
              <el-date-picker
                :value="baseForm.qualificationCertificateEndDate"
                type="date"
                placeholder="选择有效期结束日期"
                clearable
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="24">
            <el-form-item label="从业资格证登记日期" required>
              <el-date-picker
                :value="baseForm.qualificationCertificateRegistrationDate"
                type="date"
                placeholder="选择登记日期"
                clearable
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="从业资格证主页照片">
              <el-upload
                action="custom"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :file-list="photoImageList['qualificationCertificatePhotoFront']"
                disabled
                class="base-upload"
                v-if="
                  photoImageList['qualificationCertificatePhotoFront'] &&
                  photoImageList['qualificationCertificatePhotoFront'].length > 0
                "
              ></el-upload>
              <span v-else>-</span>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="从业资格证副页照片">
              <el-upload
                action="custom"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :file-list="photoImageList['qualificationCertificatePhotoBack']"
                disabled
                class="base-upload"
                v-if="
                  photoImageList['qualificationCertificatePhotoBack'] &&
                  photoImageList['qualificationCertificatePhotoBack'].length > 0
                "
              ></el-upload>
              <span v-else>-</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
      <el-button type="primary" @click="saveRecordThrottling">完成</el-button>
    </div>
    <el-image
      v-show="false"
      :src="dialogImageUrl"
      fit="cover"
      :preview-src-list="dialogImageList"
      ref="image__preview"
    ></el-image>
  </div>
</template>

<script>
  import { getInfoApiFun, updateApiFun, getListApiFun, createApiFun } from "@/api/base";
  import { SEX_OPTIONS, USER_IDENTITY, USER_TYPE, JOB_STATUS, QUASI_DRIVING_TYPE } from "@/enums";
  import baseTitle from "@/components/baseTitle";
  import FileUpload from "@/components/FileUpload";
  import { filterObjectByFieldArr } from "@/utils";
  export default {
    props: {
      recordItem: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      baseTitle,
      FileUpload,
    },
    data() {
      return {
        baseForm: {}, //业务基础信息
        ruleForm: {
          drivingCertificateNumber: "", //驾驶证编号
          drivingCertificateBeginDate: "", //驾驶证有效期开始时间
          drivingCertificateEndDate: "", //驾驶证有效期结束时间
          drivingCertificateRegistrationDate: "", //驾驶证登记日期
          drivingCertificatePhotoFront: null, //驾驶证主页照片
          drivingCertificatePhotoBack: null, //驾驶证副页照片
        },
        rules: {
          drivingCertificateNumber: [{ required: true, message: "请输入驾驶证编号", trigger: "blur" }],
          drivingCertificateBeginDate: [{ required: true, message: "请选择驾驶证有效期开始日期", trigger: "blur" }],
          drivingCertificateEndDate: [{ required: true, message: "请选择驾驶证有效期结束日期", trigger: "blur" }],
          drivingCertificateRegistrationDate: [{ required: true, message: "请选择驾驶证登记日期", trigger: "blur" }],
        },
        photoImageList: {
          drivingCertificatePhotoFront: [],
          drivingCertificatePhotoBack: [],
          qualificationCertificatePhotoFront: [],
          qualificationCertificatePhotoBack: [],
        },
        imageList: [],
        apis: {
          info: "/api/baseuser/get/",
          update: "/api/baseuser/update",
          userList: "/api/baseuser/list",
          companyList: "/api/company/list",
          carList: "/api/vehicle/dossier/list",
          positionList: "/api/dict/position/list",
          deptList: "/api/company/structure/findIn",
        },
        saveRecordThrottling: () => {},
        loading: false,
        SEX_OPTIONS,
        USER_IDENTITY,
        USER_TYPE,
        JOB_STATUS,
        QUASI_DRIVING_TYPE,
        userOptions: [], //用户列表
        companyOptions: [],
        carOptions: [],
        deptProps: {
          checkStrictly: true,
          value: "id",
          label: "name",
          emitPath: false,
        },
        dialogImageUrl: "",
        dialogImageList: [],
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptions();
      if (this.recordItem && this.recordItem.businessId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          getListApiFun({}, this.apis.userList),
          getListApiFun({}, this.apis.companyList),
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getInfoApiFun("", this.apis.positionList),
          getInfoApiFun("", this.apis.deptList),
        ];
        let res = await Promise.all(promiseList);
        this.userOptions = res[0].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.companyOptions = res[1].data;
        this.carOptions = res[2].data;
        this.positionOptions = res[3].data;
        this.deptOptions = res[4].data;
      },
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordItem.businessId, this.apis.info);
        if (res.success) {
          this.baseForm = res.data;
          this.handleBackImage();
          let imageObj = "";
          try {
            imageObj = JSON.parse(this.baseForm.avatar);
          } catch (error) {
            imageObj = "";
          }
          if (imageObj) {
            this.imageList = [{ url: imageObj.url, file: imageObj }];
          }
          this.ruleForm = filterObjectByFieldArr(res.data, [
            "drivingCertificateNumber",
            "drivingCertificateBeginDate",
            "drivingCertificateEndDate",
            "drivingCertificateRegistrationDate",
            "drivingCertificatePhotoFront",
            "drivingCertificatePhotoBack",
          ]);
          this.baseForm.phone = this.$sm2Decrypt(this.baseForm.phone);
          if (this.baseForm.idCard) {
            this.baseForm.idCard = this.$sm2Decrypt(this.baseForm.idCard);
          }
        }
      },
      handleBackImage() {
        for (const key in this.photoImageList) {
          if (Object.hasOwnProperty.call(this.photoImageList, key)) {
            const item = this.baseForm[key];
            if (this.baseForm[key]) {
              this.photoImageList[key] = [
                {
                  url: item.url,
                  file: item,
                },
              ];
            }
          }
        }
      },
      //图片预览
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogImageList = [file.url];
        this.$nextTick(() => {
          this.$refs.image__preview.clickHandler();
        });
      },
      handleUploadChange(fileList, field) {
        if (fileList.length && fileList[0].file) {
          this.ruleForm[field] = { name: fileList[0].name, size: fileList[0].size, ...fileList[0].file };
        } else {
          this.ruleForm[field] = null;
        }
      },
      uploadChangeFileList(fileList) {
        if (fileList.length > 0) {
          this.ruleForm.avatar = JSON.stringify({
            name: fileList[0].name,
            size: fileList[0].size,
            ...fileList[0].file,
          });
        } else {
          this.ruleForm.avatar = "";
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("returnIndex");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            let params = Object.assign(this.baseForm, this.ruleForm);
            try {
              let res = await updateApiFun(params, this.apis.update);
              if (res.success) {
                this.$message.success(`操作成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-box {
    margin-bottom: 20px;
  }
  .w-400 {
    width: 400px;
  }
  ::v-deep .base-upload .el-upload--picture-card {
    display: none;
  }
</style>
