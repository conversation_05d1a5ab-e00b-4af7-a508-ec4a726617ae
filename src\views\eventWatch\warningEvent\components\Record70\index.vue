<template>
  <div class="micro-app-sctmp_base" v-loading="loading">
    <record v-show="showRecord" @closeRecord="closeRecord" ref="record"></record>
    <div class="main-index" v-show="!showRecord">
      <header class="header">
        <div class="header-left">
          <el-form ref="filterForm" inline :model="filterForm" label-suffix=":" label-width="110px">
            <el-form-item label="合同编号">
              <el-input v-model="filterForm.code" placeholder="请输入合同编号" clearable></el-input>
            </el-form-item>
            <el-form-item label="客商名称">
              <el-input v-model="filterForm.merchantFileName" placeholder="请输入客商名称" clearable></el-input>
            </el-form-item>
            <el-form-item label="合同类型">
              <el-select v-model="filterForm.type" placeholder="请选择合同类型" clearable filterable>
                <el-option v-for="item in typeOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="合同状态">
              <el-select v-model="filterForm.status" placeholder="请选择合同状态" clearable filterable>
                <el-option v-for="item in statusOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="生效日期">
              <el-date-picker
                v-model="filterForm.effectiveDate"
                type="date"
                placeholder="请选择生效日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="失效日期">
              <el-date-picker
                v-model="filterForm.expiryDate"
                disabled
                type="date"
                placeholder="请选择失效日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-form>
        </div>
        <div class="header-right">
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
        </div>
      </header>
      <main class="main">
        <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" style="width: 100%" border>
          <el-table-column type="index" width="55" align="center"></el-table-column>
          <el-table-column prop="code" label="合同编号" align="center"> </el-table-column>
          <el-table-column prop="typeName" label="合同类型" align="center"> </el-table-column>
          <el-table-column prop="statusName" label="合同状态" align="center"></el-table-column>
          <el-table-column prop="effectiveDate" label="生效日期" align="center"></el-table-column>
          <el-table-column prop="expiryDate" label="失效日期" align="center"></el-table-column>
          <el-table-column prop="payDate" label="已缴费期间" align="center"> </el-table-column>
          <el-table-column prop="merchantFileName" label="客商名称" align="center"> </el-table-column>
          <el-table-column prop="dailyEmissions" label="日计量（KG）" align="center"> </el-table-column>
          <el-table-column prop="monthEmissions" label="月计量（KG）" align="center"> </el-table-column>
          <el-table-column prop="districtName" label="地区" align="center"> </el-table-column>
          <el-table-column min-width="140" label="操作" align="center">
            <template #default="{ row }">
              <el-link class="mr-10" type="primary" @click="editRecord(row)">详情</el-link>
            </template>
          </el-table-column>
        </el-table>
      </main>
      <Pagination :page="page" @pageChange="pageChange"></Pagination>
    </div>
    <div style="width: 100; align-items: center; text-align: center; margin-top: 10px">
      <el-button :key="buttonKey" @click="closeRecord">返回</el-button>
    </div>
  </div>
</template>

<script>
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, getInfoApiFun } from "@/api/base";
  import record from "./components/record.vue";
  export default {
    components: {
      Pagination,
      record,
    },
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        buttonKey: 0,
        ruleForm: {},
        topInfo: {
          buttonName: "新增",
          subTitle: "列表",
          buttonShow: true,
          buttonPermission: "-1",
        },
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/contract/contractinfo/listPage",
          contractStatusList: "/api/dict/contractStatus/list",
          contractTypeList: "/api/dict/contractType/list",
          recordInfo: "/api/task/get/",
        },
        showRecord: false,
        loading: false,
        statusOptions: [],
        typeOptions: [],
      };
    },
    async mounted() {
      this.getOptions();
      await this.getRecord();
      this.initData();
    },
    methods: {
      // 获取告警详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.recordInfo);
        if (res.success) {
          this.ruleForm = {
            ...res.data,
          };
          this.filterForm.expiryDate = this.ruleForm.deadline;
        }
      },
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          getInfoApiFun("", this.apis.contractStatusList),
          getInfoApiFun("", this.apis.contractTypeList),
        ];
        let res = await Promise.all(promiseList);
        this.statusOptions = res[0].data;
        this.typeOptions = res[1].data;
      },
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.topInfo.buttonShow = false;
        this.topInfo.subTitle = "详情";
        this.$refs.record.getRecord(row.id);
      },
      closeRecord() {
        this.buttonKey++;
        if (this.showRecord == true) {
          this.showRecord = false;
          this.topInfo.buttonShow = false;
          this.topInfo.subTitle = "列表";
        } else {
          this.$emit("closeRecord70");
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    .header-left {
      flex: 1;
    }
    .header-right {
      margin-top: 20px;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  ::v-deep .header-left .el-form-item {
    margin-bottom: 0;
    margin-top: 20px;
  }
</style>
