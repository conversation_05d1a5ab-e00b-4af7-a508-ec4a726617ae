<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <backButton @closeRecord="closeRecord" v-if="showBack"></backButton>
    <div class="detail-box">
      <header class="header">
        <div class="header-left">
          <el-input class="w250" v-model="productionUnit" placeholder="请输入产废单位名称" clearable></el-input>
        </div>
        <div class="filter-box">
          <el-badge :value="filterNumber" class="filter-badge">
            <el-button type="primary" @click="showFilter = !showFilter">
              <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
              筛选
              <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
            </el-button>
          </el-badge>
        </div>
        <el-button @click="resetFilter">重置</el-button>
        <el-button type="primary" @click="searchFilter">查询</el-button>
        <div class="header-right">
          <span class="el-icon-info info-icon" v-if="recordForm?.memo" @click="dialogVisible = true"></span>
          <el-popover class="ml-10" placement="bottom" width="240" trigger="click">
            <div class="reveal-box">
              <div class="reveal-header">
                <div>展示全部</div>
                <el-switch
                  :value="allItemChecked"
                  active-text="是"
                  inactive-text="否"
                  @change="changeAllReveal"
                ></el-switch>
              </div>
              <ul class="reveal-list">
                <li class="reveal-item" v-for="(item, index) in itemList" :key="index">
                  <el-checkbox :value="item.value" :disabled="index === 0" @change="changeReveal($event, item)">{{
                    item.label
                  }}</el-checkbox>
                </li>
              </ul>
            </div>
            <el-button slot="reference">
              <div class="btn-inner">
                <span class="el-icon-s-grid"></span>
                <span>显示列</span>
                <span class="el-icon-caret-bottom"></span>
              </div>
            </el-button>
          </el-popover>
        </div>
      </header>
      <FilterContent label-width="160px" v-show="showFilter">
        <el-col :span="12">
          <el-form-item label="收运状态">
            <el-select v-model="filterForm.waybillStatus" placeholder="请选择收运状态" clearable filterable>
              <el-option
                v-for="(item, index) in RECEIVING_CONDITION"
                :key="index"
                :label="item"
                :value="index"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收运日期范围">
            <el-date-picker
              v-model="filterForm.waybillTime"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收运截止日期范围">
            <el-date-picker
              v-model="filterForm.endDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="流程状态">
            <el-select v-model="filterForm.verifyStatus" placeholder="请选择流程状态" clearable filterable>
              <el-option v-for="(item, index) in VERIFY_STATUS" :key="index" :label="item" :value="index"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务类型">
            <el-select v-model="filterForm.detailType" placeholder="请选择任务类型" clearable filterable>
              <el-option v-for="(item, index) in POINT_TASK_TYPE" :key="index" :label="item" :value="index"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </FilterContent>
      <div class="record-content">
        <el-table
          :data="tableData"
          :header-cell-style="{ background: '#F5F7F9' }"
          border
          height="100%"
          ref="tableRef"
          @sort-change="handleSortChange"
        >
          <el-table-column type="index" align="center" label="顺序" width="60"></el-table-column>
          <el-table-column prop="code" label="点位编号" align="center" v-if="itemList[0].value"></el-table-column>
          <el-table-column
            prop="productionUnit"
            label="产废单位名称"
            align="center"
            width="120"
            v-if="itemList[1].value"
          ></el-table-column>
          <el-table-column
            prop="productionUnitOperator"
            label="产废单位经办人"
            align="center"
            width="120"
            v-if="itemList[2].value"
          ></el-table-column>
          <el-table-column prop="detailType" label="任务类型" align="center" v-if="itemList[3].value">
            <template #default="{ row }">{{ POINT_TASK_TYPE[row.detailType] }}</template>
          </el-table-column>
          <el-table-column prop="overType" label="加班类型" align="center" v-if="itemList[4].value">
            <template #default="{ row }">{{
              row.overType || row.overType === 0 ? OVERTIME_TYPE[row.overType] : ""
            }}</template>
          </el-table-column>
          <el-table-column
            prop="rubbishTotal"
            label="废物总量（kg）"
            align="center"
            min-width="140"
            v-if="itemList[5].value"
          >
            <template #default="{ row }">
              <div class="flex-center">
                <div>{{ row.rubbishTotal }}（kg）</div>
                <span
                  class="el-icon-edit-outline rubbish-edit-icon"
                  v-if="row.waybillStatus === 1 && isSameMonth(row.waybillTime)"
                  @click="openRubbishDialog(row)"
                ></span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="waybillStatus"
            label="收运状态"
            align="center"
            v-if="itemList[6].value"
            sortable="custom"
          >
            <template #default="{ row }">{{ RECEIVING_CONDITION[row.waybillStatus] }}</template>
          </el-table-column>
          <el-table-column
            prop="waybillTime"
            label="收运时间"
            align="center"
            v-if="itemList[7].value"
            sortable="custom"
          ></el-table-column>
          <el-table-column
            prop="endDate"
            label="收运截止日期"
            align="center"
            width="130"
            v-if="itemList[8].value"
            sortable="custom"
          ></el-table-column>
          <el-table-column prop="isClear" label="是否清空" align="center" v-if="itemList[9].value" sortable="custom">
            <template #default="{ row }">{{ IS_NORMAL_OPERATION[row.isClear] }}</template>
          </el-table-column>
          <el-table-column prop="baggingMethod" label="桶装/袋装" align="center" v-if="itemList[10].value">
            <template #default="{ row }">{{ BARRELS_BAGS[row.baggingMethod] }}</template>
          </el-table-column>
          <el-table-column prop="verifyStatus" label="当前流程" align="center" v-if="itemList[11].value">
            <template #default="{ row }">{{ VERIFY_STATUS[row.verifyStatus] }}</template>
          </el-table-column>
          <el-table-column
            prop="verifyUserName"
            label="确认人"
            align="center"
            v-if="itemList[12].value"
          ></el-table-column>
          <el-table-column
            prop="verifyTime"
            label="确认时间"
            align="center"
            v-if="itemList[13].value"
          ></el-table-column>
          <el-table-column prop="longitude" label="经度" align="center" v-if="itemList[14].value"></el-table-column>
          <el-table-column prop="latitude" label="纬度" align="center" v-if="itemList[15].value"></el-table-column>
          <el-table-column
            prop="userLongitude"
            label="上报经度"
            align="center"
            v-if="itemList[16].value"
          ></el-table-column>
          <el-table-column
            prop="userLatitude"
            label="上报纬度"
            align="center"
            v-if="itemList[17].value"
          ></el-table-column>
          <el-table-column
            prop="distance"
            label="对比距离（m）"
            align="center"
            v-if="itemList[18].value"
            min-width="100"
          ></el-table-column>
          <el-table-column prop="address" label="地址" align="center" v-if="itemList[19].value"></el-table-column>
          <el-table-column prop="image" label="图片" align="center">
            <template #default="{ row }">
              <el-badge
                v-if="row.picture && row.picture.length > 0"
                :value="row.picture.length > 1 ? row.picture.length : ''"
                class="picture-badge"
                type="success"
              >
                <el-image
                  class="picture-img"
                  fit="cover"
                  :src="row.picture[0].url"
                  :preview-src-list="row.picture.map((i) => i.url)"
                  v-if="row.picture"
                ></el-image>
              </el-badge>
            </template>
          </el-table-column>
          <el-table-column
            prop="weighingWeight"
            min-width="140px"
            label="核对重量（kg）"
            align="center"
            v-if="itemList[25].value"
          ></el-table-column>
          <el-table-column
            prop="pictureTime"
            label="拍照时间"
            align="center"
            v-if="itemList[24].value"
          ></el-table-column>
          <el-table-column label="废物类型/重量（kg）" align="center" v-if="itemList[20].value">
            <el-table-column prop="infectiousWaste" label="感染性废物" align="center"></el-table-column>
            <el-table-column prop="damagingWaste" label="损伤性废物" align="center"></el-table-column>
            <el-table-column prop="pharmaceuticalWaste" label="药物性废物" align="center"></el-table-column>
            <el-table-column prop="pathologicalWaste" label="病理性废物" align="center"></el-table-column>
            <el-table-column prop="chemicalWaste" label="化学性废物" align="center"></el-table-column>
            <el-table-column prop="sludge" label="感染性废物一污泥" align="center" width="140"></el-table-column>
          </el-table-column>
          <el-table-column
            prop="firstCarrier"
            label="第一承运人"
            align="center"
            v-if="itemList[21].value"
          ></el-table-column>
          <el-table-column
            prop="residueRubbish"
            label="剩余垃圾"
            align="center"
            v-if="itemList[22].value"
          ></el-table-column>
          <el-table-column prop="operation" label="正常经营" align="center" v-if="itemList[23].value">
            <template #default="{ row }">{{ IS_NORMAL_OPERATION[row.operation] }}</template>
          </el-table-column>
        </el-table>
      </div>
      <Pagination :page="page" @pageChange="pageChange"></Pagination>
    </div>
    <el-dialog title="备注" :visible.sync="dialogVisible" width="30%" destroy-on-close>
      <el-form :model="ruleForm" label-width="0" label-suffix="：">
        <el-form-item label="">
          <el-input
            :value="recordForm.memo"
            placeholder=""
            type="textarea"
            rows="10"
            maxlength="500"
            show-word-limit
            readonly
          ></el-input>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- <div class="map-box">
      <mapContainer @initMap="initMap"></mapContainer>
    </div> -->
    <rubbishDialog :value.sync="showRubbishDialog" :rubbishRecord="rubbishRecord"></rubbishDialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import {
    TRASH_TYPE,
    IS_NORMAL_OPERATION,
    RECEIVING_CONDITION,
    BARRELS_BAGS,
    VERIFY_STATUS,
    POINT_TASK_TYPE,
    OVERTIME_TYPE,
  } from "@/enums";
  import backButton from "@/components/backButton";
  import moment from "moment";
  import { isSameMonth } from "@/utils";
  import rubbishDialog from "./rubbishDialog";
  // import mapContainer from "@/components/mapContainer";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
      recordForm: {
        type: Object,
        default: () => {},
      },
      showBack: {
        type: Boolean,
        default: true,
      },
    },
    components: { backButton, rubbishDialog },
    data() {
      return {
        filterForm: {},
        TRASH_TYPE,
        IS_NORMAL_OPERATION,
        RECEIVING_CONDITION,
        BARRELS_BAGS,
        VERIFY_STATUS,
        POINT_TASK_TYPE,
        OVERTIME_TYPE,
        ruleForm: {
          remark: "", //备注
        },
        apis: {
          listPage: "/api/waybill/waybillDetail/listPage",
        },
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        loading: false,
        tableData: [],
        showFilter: false,
        productionUnit: "",
        itemList: [
          { label: "点位编号", value: true },
          { label: "产废单位名称", value: true },
          { label: "产废单位经办人", value: true },
          { label: "任务类型", value: true },
          { label: "加班类型", value: true },
          { label: "废物总量", value: true },
          { label: "收运状态", value: true },
          { label: "收运时间", value: true },
          { label: "收运截止日期", value: true },
          { label: "是否清空", value: true },
          { label: "桶装/袋装", value: true },
          { label: "当前流程", value: true },
          { label: "确认人", value: true },
          { label: "确认时间", value: true },
          { label: "经度", value: true },
          { label: "纬度", value: true },
          { label: "上报经度", value: true },
          { label: "上报纬度", value: true },
          { label: "对比距离（m）", value: true },
          { label: "地址", value: true },
          // { label: "拍照地点", value: true },
          { label: "废物类型/重量", value: false },
          { label: "第一承运人", value: false },
          { label: "剩余垃圾", value: false },
          { label: "正常经营", value: false },
          { label: "拍照时间", value: true },
          { label: "核对重量（kg）", value: true },
        ],
        allItemChecked: false,
        dialogVisible: false,
        geocoder: "",
        showRubbishDialog: false,
        rubbishRecord: {},
        isSameMonth,
      };
    },
    mounted() {
      this.initData();
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    methods: {
      initMap(map) {
        if (map) {
          window.AMap.plugin("AMap.Geocoder", () => {
            this.geocoder = new window.AMap.Geocoder({
              city: "全国", //城市设为广州，默认："全国"
            }); //创建工具条插件实例
            if (this.recordId) {
              this.initData();
            }
          });
        }
      },
      async initData() {
        let params = {
          waybillId: this.recordId,
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          productionUnit: this.productionUnit,
          waybillStatus: this.filterForm.waybillStatus,
          startWaybillTime: this.filterForm.waybillTime ? this.filterForm.waybillTime[0] : "",
          endWaybillTime: this.filterForm.waybillTime ? this.filterForm.waybillTime[1] : "",
          startEndDate: this.filterForm.endDate ? this.filterForm.endDate[0] : "",
          endEndDate: this.filterForm.endDate ? this.filterForm.endDate[1] : "",
          verifyStatus: this.filterForm.verifyStatus,
          detailType: this.filterForm.detailType,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.tableData.forEach((data) => {
            try {
              data.picture = JSON.parse(data.picture);
            } catch (error) {
              data.picture = "";
            }
          });
          this.page.total = res.data.total;
          // this.tableData.forEach(async (data) => {
          //   if (data.pictureLongitude && data.pictureLatitude) {
          //     if (Number(data.pictureLongitude) && Number(data.pictureLatitude)) {
          //       let address = await this.inverseGeocoding(wgs84togcj02(data.pictureLongitude, data.pictureLatitude));
          //       this.$set(data, "pictureAddress", address || "");
          //     }
          //   }
          // });
        }
      },
      // 逆地理编码
      inverseGeocoding(lnglat) {
        return new Promise((resolve, reject) => {
          this.geocoder.getAddress(lnglat, (status, result) => {
            if (status === "complete" && result.regeocode) {
              let address = result.regeocode.formattedAddress;
              resolve(address);
            } else {
              reject();
            }
          });
        });
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.productionUnit = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 修改所有显示列
      changeAllReveal(value) {
        this.allItemChecked = value;
        this.itemList.slice(1, this.itemList.length).forEach((item) => (item.value = value));
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 列点击事件
      changeReveal(value, item) {
        item.value = value;
        let uncheckList = this.itemList.slice(1, this.itemList.length).filter((list) => !list.value);
        if (uncheckList.length === 0) {
          this.allItemChecked = true;
        } else {
          this.allItemChecked = false;
        }
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 当前排序状态
      handleSortChange({ prop, order }) {
        if (["waybillTime", "endDate"].includes(prop)) {
          this.customSort(prop, order);
        } else {
          this.tableData.sort((a, b) => {
            if (order === "ascending") {
              return a[prop] - b[prop];
            } else if (order === "descending") {
              return b[prop] - a[prop];
            }
          });
        }
      },
      customSort(prop, order) {
        let nullList = this.tableData.filter((list) => list[prop] === null);
        let valueList = this.tableData.filter((list) => list[prop] !== null);
        if (order === "ascending") {
          valueList.sort((a, b) => moment(a[prop]).valueOf() - moment(b[prop]).valueOf());
          this.tableData = nullList.concat(valueList);
        } else if (order === "descending") {
          valueList.sort((a, b) => moment(b[prop]).valueOf() - moment(a[prop]).valueOf());
          this.tableData = valueList.concat(nullList);
        }
      },
      // 打开重量弹窗
      openRubbishDialog(row) {
        this.showRubbishDialog = true;
        this.rubbishRecord = row;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-top: 20px;
  }
  .detail-back {
    margin-bottom: 20px;
  }
  .detail-box {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    padding-top: 20px;
  }
  .header {
    display: flex;
    align-items: center;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
  .info-icon {
    font-size: 30px;
    cursor: pointer;
  }
  .record-content {
    margin-top: 20px;
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .picture-img {
    width: 60px;
    height: 60px;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .map-box {
    width: 1px;
    height: 1px;
    position: fixed;
    top: -9999px;
    left: -9999px;
    opacity: 0;
  }
  .rubbish-edit-icon {
    font-size: 18px;
    cursor: pointer;
    color: var(--color-primary);
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
