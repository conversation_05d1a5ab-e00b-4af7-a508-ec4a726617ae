import moment from "moment";
//节流
export function throttling(fn, delay) {
  let last = 0;
  return function () {
    let now = Date.now();
    if (last && now < last + delay) {
      last = now;
    } else {
      fn.apply(this, arguments);
      last = now;
    }
  };
}
/**
 * 深度合并代码，思路来自 zepto.js 源代码
 * 切记不要对象递归引用，否则会陷入递归跳不出来，导致堆栈溢出
 * 作用是会合并 target 和 other 对应位置的值，冲突的会保留 target 的值
 */
export function deepMerge(target, other) {
  const targetToString = Object.prototype.toString.call(target);
  const otherToString = Object.prototype.toString.call(other);
  if (targetToString === "[object Object]" && otherToString === "[object Object]") {
    for (let [key, val] of Object.entries(other)) {
      if (!target[key]) {
        target[key] = val;
      } else {
        target[key] = deepMerge(target[key], val);
      }
    }
  } else if (targetToString === "[object Array]" && otherToString === "[object Array]") {
    for (let [key, val] of Object.entries(other)) {
      if (target[key]) {
        target[key] = deepMerge(target[key], val);
      } else {
        target.push(val);
      }
    }
  }
  return target;
}

// 导出的流Blob,filename 导出的文件名
export function downloadFileBlob(data, filename) {
  if (!data) {
    return;
  }
  let url = window.URL.createObjectURL(new Blob([data]));
  let link = document.createElement("a");
  link.style.display = "none";
  link.href = url;
  link.setAttribute("download", filename);

  document.body.appendChild(link);
  link.click();
}

export const fileIcon = {
  docx: require("@/assets/icons/word.png"),
  doc: require("@/assets/icons/word.png"),
  pptx: require("@/assets/icons/ppt.png"),
  ppt: require("@/assets/icons/ppt.png"),
  pptm: require("@/assets/icons/ppt.png"),
  xls: require("@/assets/icons/excel.png"),
  xlsx: require("@/assets/icons/excel.png"),
  pdf: require("@/assets/icons/pdf.png"),
  wps: require("@/assets/icons/wps.png"),
  txt: require("@/assets/icons/txt.png"),
  png: require("@/assets/icons/jpgorpng.png"),
  jpg: require("@/assets/icons/jpgorpng.png"),
  jpeg: require("@/assets/icons/jpgorpng.png"),
  gif: require("@/assets/icons/jpgorpng.png"),
  zip: require("@/assets/icons/yswj.png"),
  rar: require("@/assets/icons/yswj.png"),
  mp3: require("@/assets/icons/mp3.png"),
  mp4: require("@/assets/icons/mp4.png"),
};

// 前端数字1-100转中文
export function numToChinese(num) {
  const chineseNumbers = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
  const units = ["", "十", "百", "千", "万"];

  let str = "";
  if (num === 0) {
    return chineseNumbers[0];
  }

  for (let i = 0; num > 0; i++) {
    const digit = num % 10;
    if (digit !== 0) {
      str = chineseNumbers[digit] + units[i] + str;
    } else if (!str.startsWith(chineseNumbers[0])) {
      str = chineseNumbers[0] + str;
    }
    num = Math.floor(num / 10);
  }

  return str.replace(/^一十/, "十");
}

// 字段过滤，传入对象以及需要返回的字段数组，过滤出一个只包含字段数组所含字段的对象
export function filterObjectByFieldArr(obj, arr) {
  return Object.fromEntries(Object.entries(obj).filter(([key]) => arr.includes(key)));
}

// 小数乘法
export function floatMul(arg1, arg2) {
  let m = 0,
    s1 = arg1.toString(),
    s2 = arg2.toString();
  try {
    m += s1.split(".")[1].length;
  } catch (e) {
    let u;
  }
  try {
    m += s2.split(".")[1].length;
  } catch (e) {
    let u;
  }
  return (Number(s1.replace(".", "")) * Number(s2.replace(".", ""))) / Math.pow(10, m);
}

//小数除法
export function floatDiv(arg1, arg2) {
  let t1 = 0,
    t2 = 0,
    r1,
    r2;
  try {
    t1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    let u;
  }
  try {
    t2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    let u;
  }
  r1 = Number(arg1.toString().replace(".", ""));
  r2 = Number(arg2.toString().replace(".", ""));
  return (r1 / r2) * Math.pow(10, t2 - t1);
}

//小数加法
export function floatAdd(arg1, arg2) {
  let r1, r2, m;
  try {
    r1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  return (arg1 * m + arg2 * m) / m;
}

//小数减法
export function floatSub(arg1, arg2) {
  let r1, r2, m, n;
  try {
    r1 = arg1.toString().split(".")[1].length;
  } catch (e) {
    r1 = 0;
  }
  try {
    r2 = arg2.toString().split(".")[1].length;
  } catch (e) {
    r2 = 0;
  }
  m = Math.pow(10, Math.max(r1, r2));
  //动态控制精度长度
  n = r1 >= r2 ? r1 : r2;
  return ((arg1 * m - arg2 * m) / m).toFixed(n);
}

export function base64ToFile(base64, fileName) {
  // 将base64按照 , 进行分割 将前缀  与后续内容分隔开
  let data = base64.split(",");
  // 利用正则表达式 从前缀中获取图片的类型信息（image/png、image/jpeg、image/webp等）
  let type = data[0].match(/:(.*?);/)[1];
  // 从图片的类型信息中 获取具体的文件格式后缀（png、jpeg、webp）
  let suffix = type.split("/")[1];
  // 使用atob()对base64数据进行解码  结果是一个文件数据流 以字符串的格式输出
  const bstr = window.atob(data[1]);
  // 获取解码结果字符串的长度
  let n = bstr.length;
  // 根据解码结果字符串的长度创建一个等长的整形数字数组
  // 但在创建时 所有元素初始值都为 0
  const u8arr = new Uint8Array(n);
  // 将整形数组的每个元素填充为解码结果字符串对应位置字符的UTF-16 编码单元
  while (n--) {
    // charCodeAt()：获取给定索引处字符对应的 UTF-16 代码单元
    u8arr[n] = bstr.charCodeAt(n);
  }
  // 利用构造函数创建File文件对象
  // new File(bits, name, options)
  const file = new File([u8arr], `${fileName}.${suffix}`, {
    type: type,
  });
  // 将File文件对象返回给方法的调用者
  return file;
}
/**
 * 四舍五入保留n位小数
 * @param {number} number
 * @param {number} digit
 * @returns {number}
 */
export function roundReserveDecimals(number = 0, digit = 2) {
  return floatDiv(Math.round(floatMul(number, Math.pow(10, digit))), Math.pow(10, digit));
}

/**
 * 绘制路线围栏多边形
 * @param {array} pathList 点位坐标集合
 * @param {object} map 地图实例对象
 */
export async function routeFencePolygon(pathList, map) {
  let lineString = {
    type: "LineString",
    coordinates: pathList,
  };
  let turf = await import("@turf/turf");
  let {
    geometry: { coordinates: path },
  } = turf.buffer(lineString, 100, { units: "meters" });
  const polygon = new window.AMap.Polygon({
    path, //多边形路径
    fillColor: "#ccebc5", //多边形填充颜色
    strokeOpacity: 1, //线条透明度
    fillOpacity: 0.5, //填充透明度
    strokeColor: "#2b8cbe", //线条颜色
    strokeWeight: 2, //线条宽度
    strokeStyle: "dashed", //线样式
    strokeDasharray: [5, 5], //轮廓的虚线和间隙的样式
  });
  //鼠标移入更改样式
  polygon.on("mouseover", () => {
    polygon.setOptions({
      fillOpacity: 0.7, //多边形填充透明度
      fillColor: "#7bccc4",
    });
  });
  //鼠标移出恢复样式
  polygon.on("mouseout", () => {
    polygon.setOptions({
      fillOpacity: 0.5,
      fillColor: "#ccebc5",
    });
  });
  map.add(polygon);
}

/**
 * 将指定组件设置自定义名称
 *
 * @param {String} name 组件自定义名称
 * @param {Component | Promise<Component>} component
 * @return {Component}
 */
export function createCustomComponent(name, component) {
  return {
    name,
    data() {
      return { component: null };
    },
    async created() {
      if (component instanceof Promise) {
        try {
          const module = await component;
          this.component = module?.default;
        } catch (error) {
          console.error(`can not resolve component ${name}, error:`, error);
        }

        return;
      }
      this.component = component;
    },
    render(h) {
      return this.component ? h(this.component) : null;
    },
  };
}

/**
 * 日期格式化
 * @param {*} time
 * @param {*} format
 * @param {*} separator
 * @returns
 */
export function formatTime(time, format = "ymd", separator = "-") {
  let res = "";
  if (!time) {
    return res;
  }
  if (typeof time === "string" && time.includes("-")) {
    time = time.replace(/-/g, "/");
  }
  let date = new Date(time);
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();

  const ymd = [year, month, day].map(formatNumber).join(separator);
  const hms = [hour, minute, second].map(formatNumber).join(":");
  const hm = [hour, minute].map(formatNumber).join(":");

  switch (format) {
    case "ymd":
      res = ymd;
      break;
    case "ymdhms":
      res = ymd + " " + hms;
      break;
    case "ymdhm":
      res = ymd + " " + hm;
      break;
    default:
      res = hms;
  }

  return res;
}
const formatNumber = (n) => {
  n = n.toString();
  return n[1] ? n : "0" + n;
};

// 返回包括今天在内的前七天日期，只有月日
export function getLastSevenDaysMonthDay() {
  // 创建一个数组来存储前七天的月份和日期
  const lastSevenDays = [];
  // 获取今天的日期
  const today = new Date();
  // 使用循环生成前七天的日期
  for (let i = 0; i < 7; i++) {
    // 创建一个新的Date对象，代表当前循环的日期
    const date = new Date(today);
    // 减去相应的天数
    date.setDate(today.getDate() - i);
    // 提取月份（注意月份是从0开始的，所以需要加1）
    const month = String(date.getMonth() + 1).padStart(2, "0");
    // 提取日期
    const day = String(date.getDate()).padStart(2, "0");
    // 将月份和日期拼接成字符串，并添加到数组中
    lastSevenDays.unshift(`${month}-${day}`);
  }
  return lastSevenDays;
}

/**
 * 判断指定时间是否在N天前到当前时间的范围内
 * @param {Date|string|number} date 要判断的时间
 * @param {number} days N天前,默认7天
 * @returns {boolean} 是否在N天前到当前时间的范围内
 */
export function isSevenDaysAgo(date, days = 7) {
  // 将输入的时间转换为moment对象,支持Date对象、时间戳、标准日期字符串等格式
  const targetDate = moment(date);

  // 获取当前时间的moment对象
  const now = moment();

  // 获取7天前的开始时间(0点0分0秒)
  const sevenDaysAgo = now.clone().subtract(days, "days").startOf("day");

  // 获取当前时间的结束时间(23点59分59秒)
  const todayEnd = now.clone().endOf("day");

  // 判断目标时间是否在7天前到当前时间的范围内
  return targetDate.isBetween(sevenDaysAgo, todayEnd, null, "[]");
}

export function isSameMonth(waybillTime) {
  if (!waybillTime) return false;
  return moment().format("YYYY-MM") === moment(waybillTime, "YYYY-MM-DD HH:mm:ss").format("YYYY-MM");
}

const sm2 = require("sm-crypto").sm2;
const publicKey = process.env.VUE_APP_PUBLICKEY;
const privateKey = process.env.VUE_APP_PRIVATEKEY;

/**
 * sm2加密
 * @param {string} plainText - 要加密的明文
 * @param {string} publicKey - SM2公钥
 * @param {number} mode - 加密模式，1表示C1C3C2模式（默认），0表示C1C2C3模式
 * @returns {string} - 加密后的密文
 */
export function sm2Encrypt(plainText, mode = 1) {
  return "04" + sm2.doEncrypt(plainText, publicKey, mode);
}

/**
 * sm2解密（通常在服务器端使用，前端一般不会有私钥）
 * @param {string} cipherText - 要解密的密文
 * @param {string} privateKey - SM2私钥
 * @param {number} mode - 解密模式，1表示C1C3C2模式（默认），0表示C1C2C3模式
 * @returns {string} - 解密后的明文
 */
export function sm2Decrypt(cipherText, mode = 1) {
  if (cipherText.startsWith("04")) {
    cipherText = cipherText.substring(2); // 去 04
  }
  return sm2.doDecrypt(cipherText, privateKey, mode);
}
