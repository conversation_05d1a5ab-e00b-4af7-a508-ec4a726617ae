<template>
  <div class="total-box">
    <div class="total-title">{{ title }}</div>
    <div class="total-num">{{ num }}</div>
  </div>
</template>

<script>
  export default {
    props: {
      title: {
        type: String,
        default: "",
      },
      num: {
        type: [String, Number],
        default: 0,
      },
    },
  };
</script>

<style lang="scss" scoped>
  .total-box {
    padding: 6px 60px 12px 60px;
    text-align: center;
    background-color: #f2f2f2;
    margin: 10px 0 30px 0;
    display: inline-block;
    border-radius: 10px;
    .total-title {
      font-size: 16px;
      color: #606266;
    }
    .total-num {
      font-size: 40px;
      font-weight: bold;
      margin-top: 10px;
    }
  }
</style>
