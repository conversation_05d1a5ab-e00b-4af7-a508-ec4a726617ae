<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="200px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="车牌号" prop="plateNumber">
              <el-select v-model="ruleForm.plateNumber" placeholder="请选择车牌号" clearable filterable>
                <el-option v-for="item in carOptions" :key="item.id" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="驾驶司机" prop="driverDossierId">
              <el-select v-model="ruleForm.driverDossierId" placeholder="请选择驾驶司机" clearable filterable>
                <el-option
                  v-for="item in userOptions"
                  :key="item.id"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="行驶总里程(KM)" prop="totalMileage">
              <el-input-number
                v-model="ruleForm.totalMileage"
                :precision="2"
                :min="0"
                :max="99999999999999"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="加油类型" prop="refuelType">
              <el-select v-model="ruleForm.refuelType" placeholder="请选择加油类型" clearable filterable>
                <el-option v-for="(item, index) in options" :key="index" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="加油(L)/充电量(Kwh)" prop="fuelQuantity">
              <el-input-number
                v-model="ruleForm.fuelQuantity"
                :precision="2"
                :min="0"
                :max="99999999999999"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="加油/充电日期" prop="fuelTime">
              <el-date-picker
                v-model="ruleForm.fuelTime"
                type="date"
                placeholder="选择日期"
                clearable
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="加油/充电金额(元)" prop="amount">
              <el-input-number
                v-model="ruleForm.amount"
                :precision="2"
                :min="0"
                :max="99999999999999"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="加油地点" prop="fuelPlace">
              <el-input
                v-model="ruleForm.fuelPlace"
                placeholder="请输入加油地点"
                clearable
                :maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="24">
            <el-form-item label="备注" prop="memo">
              <el-input
                type="textarea"
                placeholder="请输入备注"
                v-model="ruleForm.memo"
                :maxlength="50"
                show-word-limit
                :autosize="{ minRows: 4 }"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="凭证" prop="vehicleFileList">
              <FileUpload
                @uploadChange="uploadChangeFileList($event, 'vehicleFileList')"
                :imageList="photoImageList['vehicleFileList']"
                :limit="5"
              >
                <template #tips><el-tag type="warning">请上传加油凭证</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="仪表里程照片" prop="dashBoardFileList">
              <FileUpload
                @uploadChange="uploadChangeFileList($event, 'dashBoardFileList')"
                :imageList="photoImageList['dashBoardFileList']"
                :limit="5"
              >
                <template #tips><el-tag type="warning">请上传车辆仪表里程照片</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import FileUpload from "@/components/FileUpload";
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  export default {
    components: { FileUpload },
    props: {
      recordId: {
        type: String,
        default: "",
      },
      positionOptions: {
        type: Array,
        default: () => [],
      },
      roleOptions: {
        type: Array,
        default: () => [],
      },
      userOptions: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        ruleForm: {
          plateNumber: null,
          driverDossierId: null,
          totalMileage: null,
          refuelType: null,
          fuelQuantity: null,
          fuelTime: null,
          amount: null,
          fuelPlace: null,
          memo: null,
          vehicleFileList: null,
          dashBoardFileList: [],
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号", trigger: "change" }],
          driverDossierId: [{ required: true, message: "请选择驾驶司机", trigger: "blur" }],
          totalMileage: [{ required: true, message: "请输入行驶里程", trigger: "change" }],
          refuelType: [{ required: true, message: "请选择加油类型", trigger: "change" }],
          fuelQuantity: [{ required: true, message: "请输入加油量/充电量", trigger: "change" }],
          fuelTime: [{ required: true, message: "请输入加油/充电日期", trigger: "change" }],
          amount: [{ required: true, message: "请输入加油/充电金额", trigger: "change" }],
          vehicleFileList: [{ required: true, message: "请上传加油凭证", trigger: "change" }],
          dashBoardFileList: [{ required: true, message: "请上传车辆仪表里程照片", trigger: "change" }],
        },
        options: [], //加油类型
        apis: {
          vehicleFuelList: "/api/dict/refuelType/list",
          create: "/api/vehicleFuel/create",
          update: "/api/vehicleFuel/update",
          info: "/api/vehicleFuel/get/",
          carList: "/api/vehicle/dossier/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        photoImageList: {
          vehicleFileList: [],
          dashBoardFileList: [],
        },
        carOptions: [],
        pickerOptions: {
          disabledDate: (time) => time.getTime() > new Date(new Date().setHours(23, 59, 59)),
        },
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptions();
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      uploadChangeFileList(fileList, field) {
        if (fileList.length > 0) {
          this.ruleForm[field] = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
          this.$refs.ruleForm.clearValidate(field);
        } else {
          this.ruleForm[field] = [];
        }
      },
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          getInfoApiFun("", this.apis.vehicleFuelList),
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
        ];
        let res = await Promise.all(promiseList);
        this.options = res[0].data;
        this.carOptions = res[1].data;
      },
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.handleBackImage();
        }
      },
      handleBackImage() {
        for (const key in this.photoImageList) {
          if (Object.hasOwnProperty.call(this.photoImageList, key)) {
            const item = this.ruleForm[key];
            if (this.ruleForm[key]) {
              this.photoImageList[key] = item.map((i) => {
                return {
                  url: i.url,
                  file: i,
                };
              });
            }
          }
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = this.recordId
                ? await updateApiFun(this.ruleForm, this.apis.update)
                : await createApiFun(this.ruleForm, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}加油记录成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
  }
  ::v-deep .el-transfer-panel__body {
    height: 400px;
  }
  .w400 {
    width: 400px;
  }

  .w200 {
    width: 200px;
  }
</style>
