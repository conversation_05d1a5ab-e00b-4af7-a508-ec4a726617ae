<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage @closeRecord="closeRecord">
      <record v-if="showRecord" :recordId="recordId" @closeRecord="closeRecord" @refreshList="initData"></record>
      <createRecord v-else-if="showCreateRecord" @closeRecord="closeRecord" @refreshList="initData"></createRecord>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="keyword" placeholder="请输入申请人姓名" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增换班</el-button>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
            :colNum="12"
          ></InterviewChannel>
          <el-col :span="12">
            <el-form-item label="申请日期">
              <el-date-picker
                v-model="filterForm.applyDate"
                type="date"
                placeholder="选择申请日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column prop="applyFullName" label="申请人" align="center"></el-table-column>
            <el-table-column prop="applyDate" label="申请日期" align="center"></el-table-column>
            <el-table-column prop="changeStartDate" label="换班开始日期" align="center"></el-table-column>
            <el-table-column prop="changeEndDate" label="换班结束日期" align="center"></el-table-column>
            <el-table-column prop="applyStatus" label="是否批准" align="center">
              <template #default="{ row }">
                {{ row.auditStatus === 1 ? IS_NORMAL_OPERATION[row.applyStatus] : "-" }}
              </template>
            </el-table-column>
            <el-table-column prop="auditFullName" label="处理人" align="center">
              <template #default="{ row }">{{
                row.auditFullName || row.auditFullName === 0 ? row.auditFullName : "-"
              }}</template>
            </el-table-column>
            <el-table-column prop="auditDate" label="处理日期" align="center">
              <template #default="{ row }">{{ row.auditDate || row.auditDate === 0 ? row.auditDate : "-" }}</template>
            </el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row)" v-if="row.auditStatus === 1"
                  >详情</el-link
                >
                <el-link class="mr-10" type="primary" @click="editRecord(row)" v-else>审批</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import { getListPageApiFun } from "@/api/base";
  import record from "./components/record.vue";
  import createRecord from "./components/create.vue";
  import { IS_NORMAL_OPERATION } from "@/enums";
  export default {
    components: {
      defaultPage,
      record,
      createRecord,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/waybill/change/listPage",
        },
        showRecord: false,
        recordId: "",
        loading: false,
        IS_NORMAL_OPERATION,
        showFilter: false,
        keyword: "",
        showCreateRecord: false,
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          applyFullName: this.keyword,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.recordId = row.id;
      },
      closeRecord() {
        this.showRecord = false;
        this.showCreateRecord = false;
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 新增换班
      createRecord() {
        this.showCreateRecord = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
</style>
