<template>
  <div class="record-container" v-loading="loading">
    <el-radio-group v-model="showData" class="record-header">
      <el-radio-button :label="true">
        <div class="record-header-box">
          <span class="el-icon-s-data"></span>
          <div>数据视图</div>
        </div>
      </el-radio-button>
      <el-radio-button :label="false">
        <div class="record-header-box">
          <span class="el-icon-s-custom"></span>
          <div>人员视图</div>
        </div>
      </el-radio-button>
    </el-radio-group>
    <div class="record-wrapper record-data" v-show="showData">
      <div class="record-title">{{ moment(recordItem.dutyDate).format("YYYY年MM月DD日") }}收运任务一览</div>
      <baseTitle title="当日概览"></baseTitle>
      <div class="record-content">
        <template v-if="form">
          <div class="top-top">
            <div class="top-top-left">
              <div class="report-title">当日收运路线信息</div>
              <div class="top-flex">
                <div class="top-chart">
                  <chart
                    centerTitle="路线总数"
                    chartId="chart1"
                    :total="form.pickupPathCount"
                    :dataList="[
                      { value: form.completePickupCount, name: '已收路线', itemStyle: { color: '#546FC6' } },
                      { value: form.noCompletePickupCount, name: '待收路线', itemStyle: { color: '#74C1DF' } },
                    ]"
                  ></chart>
                </div>
                <div class="top-card">
                  <card
                    :cardList="[
                      {
                        title: '已完成收运路线数量',
                        count: form.completePickupCount,
                      },
                      {
                        title: '未完成收运路线数量',
                        count: form.noCompletePickupCount,
                      },
                      {
                        title: '当日路线收运率',
                        count: form.completePickupRate,
                        unit: '%',
                      },
                    ]"
                  ></card>
                </div>
              </div>
            </div>
            <div class="top-top-right">
              <div class="report-title">当日收运点位信息</div>
              <div class="top-flex">
                <div class="top-chart">
                  <chart
                    centerTitle="点位总数"
                    chartId="chart2"
                    :total="form.pickupPointCount"
                    :dataList="[
                      { value: form.completePickupPointCount, name: '已收点位', itemStyle: { color: '#546FC6' } },
                      { value: form.noCompletePickupPointCount, name: '待收点位', itemStyle: { color: '#74C1DF' } },
                    ]"
                  ></chart>
                </div>
                <div class="top-card">
                  <card
                    :cardList="[
                      {
                        title: '已完成收运点位数量',
                        count: form.completePickupPointCount,
                      },
                      {
                        title: '待收运点位数量',
                        count: form.noCompletePickupPointCount,
                      },
                      {
                        title: '当日点位总收运率',
                        count: form.completePickupPointRate,
                        unit: '%',
                      },
                    ]"
                  ></card>
                </div>
              </div>
            </div>
          </div>
          <div class="top-bottom">
            <div class="bottom-left">
              <ul class="left-grid">
                <li class="left-grid-item">
                  <div class="grid-item-title">当日收运车辆信息</div>
                  <div class="grid-item-chart">
                    <chart2
                      centerTitle="车辆总数"
                      chartId="chart3"
                      :total="form.vehicleCount"
                      :dataList="[
                        {
                          value: form.workVehicleCount,
                          name: '当日收运车辆',
                          itemStyle: { color: '#546FC6' },
                        },
                        {
                          value: form.freeVehicleCount,
                          name: '当日空闲车辆',
                          itemStyle: { color: '#74C1DF' },
                        },
                      ]"
                    ></chart2>
                  </div>
                </li>
                <li class="left-grid-item">
                  <div class="grid-item-title">当日司机信息</div>
                  <div class="grid-item-chart">
                    <chart2
                      centerTitle="司机总数"
                      chartId="chart4"
                      :total="form.driverCount"
                      :dataList="[
                        { value: form.workDriverCount, name: '当日收运司机', itemStyle: { color: '#546FC6' } },
                        {
                          value: form.freeDriverCount,
                          name: '当日空闲司机',
                          itemStyle: { color: '#74C1DF' },
                        },
                      ]"
                    ></chart2>
                  </div>
                </li>
                <li class="left-grid-item">
                  <div class="grid-item-title">当日押运工信息</div>
                  <div class="grid-item-chart">
                    <chart2
                      centerTitle="押运工总数"
                      chartId="chart5"
                      :total="form.supercargoCount"
                      :dataList="[
                        {
                          value: form.workSupercargoCount,
                          name: '当日收运押运工',
                          itemStyle: { color: '#546FC6' },
                        },
                        {
                          value: form.freeSupercargoCount,
                          name: '当日空闲押运工',
                          itemStyle: { color: '#74C1DF' },
                        },
                      ]"
                    ></chart2>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="record-wrapper record-person" v-show="!showData">
      <div class="person-left">
        <div class="status-box">
          <div class="status-title">状态筛选</div>
          <el-checkbox :value="allAttendance" @change="handleChangeAll($event, 'checkAttendance')">全部</el-checkbox>
          <el-checkbox-group class="status-checkbox" v-model="checkAttendance">
            <el-checkbox :label="index" v-for="(item, index) in attendanceOptions" :key="index">{{ item }}</el-checkbox>
          </el-checkbox-group>
        </div>
        <div class="status-box mt-40">
          <div class="status-title">人员类型</div>
          <el-checkbox :value="allIdentity" @change="handleChangeAll($event, 'checkIdentity')">全部人员</el-checkbox>
          <el-checkbox-group class="status-checkbox" v-model="checkIdentity">
            <el-checkbox :label="item.id" v-for="item in identityOptions" :key="item.id">{{ item.name }}</el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <div class="person-right">
        <div class="right-header">
          <div class="right-header-left">排班信息总览</div>
          <div class="right-header-right">
            <el-input placeholder="搜索" prefix-icon="el-icon-search" v-model.trim="inputValue" clearable></el-input>
          </div>
        </div>
        <div class="right-table">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column prop="fullName" label="人员信息" align="center"> </el-table-column>
            <el-table-column label="任务信息" align="center">
              <template #default="{ row }">
                <div v-for="item in row.waybillInfoList" :key="item.id">{{ item.waybillName }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
            <el-table-column prop="userIdentity" label="身份" align="center">
              <template #default="{ row }">
                <span v-for="(item, index) in row.userIdentity.split(',')" :key="item">
                  <span>{{ USER_IDENTITY[item] }}</span>
                  <span v-if="index < row.userIdentity.split(',').length - 1">，</span>
                </span>
              </template>
            </el-table-column>
            <el-table-column label="当前状态" align="center">
              <template #default="{ row }">
                <el-tag :type="attendanceType[row.attendanceStatus]" effect="dark">
                  {{ attendanceOptions[row.attendanceStatus] }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="record-footer record-data">
      <el-button @click="$emit('closeRecord')">返回</el-button>
    </div>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle/index.vue";
  import chart from "@/views/collectTransportManage/todayReport/components/chart.vue";
  import chart2 from "@/views/collectTransportManage/todayReport/components/chart2.vue";
  import card from "@/views/collectTransportManage/todayReport/components/card.vue";
  import { getInfoApiFun, createApiFun } from "@/api/base";
  import moment from "moment";
  import { USER_IDENTITY } from "@/enums";
  export default {
    components: {
      baseTitle,
      chart,
      chart2,
      card,
    },
    props: {
      recordItem: {
        type: Object,
        default: () => {},
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        apis: {
          info: "/api/dutyplan/get/",
          attendanceList: "/api/waybill/attendance/list",
        },
        loading: false,
        form: "",
        moment,
        showData: true,
        attendanceOptions: ["空闲", "在岗", "旷工", "换班"],
        attendanceType: ["warning", "success", "danger", ""],
        checkAttendance: [],
        identityOptions: [
          { id: 3, name: "司机" },
          { id: 4, name: "押运工" },
        ],
        checkIdentity: [],
        inputValue: "",
        USER_IDENTITY,
        attendanceList: [],
      };
    },
    computed: {
      allAttendance() {
        return this.checkAttendance.length === 4;
      },
      allIdentity() {
        return this.checkIdentity.length === 2;
      },
      tableData() {
        let attendanceList = this.attendanceList;
        if (this.inputValue) {
          attendanceList = attendanceList.filter((list) => list.fullName.includes(this.inputValue));
        }
        if (this.checkAttendance.length) {
          attendanceList = attendanceList.filter((list) => this.checkAttendance.includes(list.attendanceStatus));
        }
        if (this.checkIdentity.length) {
          attendanceList = attendanceList.filter((list) => {
            // 检查this.checkIdentity中的任何一个元素是否在list.userIdentity字符串中
            return this.checkIdentity.some((id) => list.userIdentity.split(",").includes(String(id)));
          });
        }
        return attendanceList;
      },
    },
    mounted() {
      this.getRecord();
      this.getAttendanceList();
    },
    methods: {
      // 获取详情
      async getRecord() {
        this.loading = true;
        try {
          let res = await getInfoApiFun(this.recordItem.id, this.apis.info);
          if (res.success) {
            this.form = res.data;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 获取人员信息
      async getAttendanceList() {
        try {
          let res = await createApiFun(
            { queryDate: this.recordItem.dutyDate, channelId: this.channelId },
            this.apis.attendanceList,
          );
          this.attendanceList = res.data.map((item) => {
            return {
              ...item,
              phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            };
          });
        } catch (error) {
          console.log(error);
        }
      },
      // 切换全选
      handleChangeAll(value, key) {
        switch (key) {
          case "checkAttendance":
            this[key] = value ? [0, 1, 2, 3] : [];
            break;
          case "checkIdentity":
            this[key] = value ? [3, 4] : [];
            break;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .record-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .record-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
  }
  .record-data {
    background-color: #fff;
    flex-direction: column;
    padding: 0 10px;
  }
  .record-header {
    padding: 10px;
    background-color: #fff;
    .record-header-box {
      display: flex;
      align-items: center;
      div {
        margin-left: 4px;
      }
    }
  }
  .record-title {
    padding: 10px 0;
    font-size: 20px;
    font-weight: bold;
    text-align: center;
  }
  .record-content {
    flex: 1;
    overflow: auto;
    &::-webkit-scrollbar {
      width: 5px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      -webkit-box-shadow: inset 0 0 10px rgba(85, 122, 191, 0.1);
      background: #cccccc;
    }
  }
  .record-footer {
    padding: 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .container-top {
    background-color: #fff;
    padding: 16px;
    border-radius: 6px;
  }
  .container-bottom {
    margin-top: 16px;
    background-color: #fff;
    padding: 16px;
    border-radius: 6px;
  }
  .report-title {
    font-weight: 400;
    font-size: 16px;
    color: #000000;
    line-height: 20px;
  }
  .top-top {
    display: flex;
    .top-top-left,
    .top-top-right {
      flex: 782;
      min-height: 310px;
      display: flex;
      flex-direction: column;
    }
    .top-top-right {
      margin-left: 16px;
    }
  }
  .top-chart {
    flex: 211;
    height: 100%;
  }
  .top-card {
    margin-left: 28px;
    flex: 447;
  }
  .top-bottom {
    display: flex;
    margin-top: 24px;
    .bottom-left {
      flex: 782;
      min-height: 290px;
      padding: 30px 16px 16px 16px;
      display: flex;
      background-color: #f7faf9;
      border-radius: 3px;
    }
  }
  .top-bottom-bottom {
    margin-top: 24px;
    display: flex;
    .bottom-middle {
      flex: 406;
      padding: 16px;
      background-color: #f7faf9;
      border-radius: 3px;
      display: flex;
      flex-direction: column;
      .middle-box {
        margin-top: 24px;
        min-height: 240px;
        flex: 1;
        overflow: hidden;
        display: flex;
        align-items: center;
        .box-left {
          height: 100%;
          flex: 1;
          overflow: hidden;
        }
      }
    }
  }
  .top-flex {
    margin-top: 24px;
    flex: 1;
    display: flex;
    align-items: center;
  }
  .left-grid {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    .left-grid-item {
      display: flex;
      flex-direction: column;
      cursor: pointer;
      .grid-item-title {
        font-weight: 400;
        font-size: 14px;
        color: #5c6663;
        line-height: 16px;
        text-align: center;
      }
      .grid-item-chart {
        flex: 1;
        overflow: hidden;
      }
    }
  }
  .right-item {
    cursor: pointer;
    &:first-child {
      margin-bottom: 24px;
    }
    .right-title {
      font-weight: 400;
      font-size: 14px;
      color: #5c6663;
      line-height: 16px;
    }
    .right-count {
      font-weight: 500;
      font-size: 20px;
      color: #1d2925;
      line-height: 23px;
      margin-top: 4px;
    }
  }
  .table-title {
    font-weight: 400;
    font-size: 18px;
    color: #000000;
    line-height: 20px;
    padding-left: 12px;
    margin-bottom: 12px;
  }
  .table-list {
    margin-bottom: 16px;
  }
  .mr-16 {
    margin-right: 16px;
  }
  .record-person {
    padding: 16px 0;
  }
  .person-left {
    width: 300px;
    height: 100%;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    margin-right: 20px;
    padding: 20px 10px;
  }
  .person-right {
    flex: 1;
    overflow: hidden;
    background-color: #fff;
    height: 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    border-radius: 10px;
    padding: 10px;
    display: flex;
    flex-direction: column;
  }
  .status-box {
    .status-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 16px;
    }
    .status-checkbox {
      label {
        display: block;
      }
    }
  }
  .mt-40 {
    margin-top: 40px;
  }
  .right-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .right-table {
    flex: 1;
    overflow: auto;
    margin: 10px 0;
  }
</style>
