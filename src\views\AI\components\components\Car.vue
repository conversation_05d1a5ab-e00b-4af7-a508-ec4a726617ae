<template>
  <el-popover
    v-model="visible"
    placement="top"
    width="400"
    trigger="click"
    :append-to-body="false"
    v-clickoutside="() => (visible = false)"
  >
    <el-select v-model="value" placeholder="请选择" class="full-width" @change="changeOption" clearable filterable>
      <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.name"></el-option>
    </el-select>
    <span class="content-text" slot="reference">{{ content }}</span>
  </el-popover>
</template>

<script>
  import Clickoutside from "element-ui/src/utils/clickoutside";
  export default {
    directives: {
      Clickoutside,
    },
    props: {
      content: {
        type: String,
        default: "",
      },
      options: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        value: "",
        visible: true,
      };
    },
    methods: {
      changeOption(value) {
        this.$emit("changeOption", value ? value : "某车辆");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .full-width {
    width: 100%;
  }
  .content-text {
    color: #4ca786;
    &:hover {
      text-decoration: underline;
    }
  }
</style>
