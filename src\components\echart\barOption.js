/**
 * @Date: 2022-06-09 11:11:12
 * @return {*}
 * @description:柱形图-竖向
 */

// 引入 echarts 核心模块，核心模块提供了 echarts 使用必须要的接口。
// import * as echarts from "echarts/core";
let barOption = {
  // tooltip: {
  //   //提示
  //   trigger: "axis",
  //   textStyle: {
  //     fontWeight: 400,
  //   },
  // },

  legend: {
    show: false,
    //图例
    icon: "rect",
    itemHeight: 2,
    itemWidth: 8,
    right: "1%",
    top: "4%",
    textStyle: {
      color: "#666",
      fontWeight: 400,
      fontSize: "12",
    },
  },

  //悬浮显示图例
  tooltip: {
    trigger: "axis",
  },

  grid: {
    //网格
    top: "20%",
    bottom: "4%",
    left: "2%",
    right: "2%",
    containLabel: true,
  },

  xAxis: {
    type: "category",

    axisLabel: {
      fontSize: "12", //x轴字体大小
      color: "#909399", //x轴字体颜色

      formatter: function (params) {
        //x轴超过8个字换行
        if (params.length > 10) {
          var newParamsName = "";
          var paramsNameNumber = params.length;
          var provideNumber = 10;
          var rowNumber = Math.ceil(paramsNameNumber / provideNumber);
          for (let row = 0; row < rowNumber; row++) {
            newParamsName += params.substring(row * provideNumber, (row + 1) * provideNumber) + "\n";
          }
          return newParamsName;
        }
        return params;
      },
    },

    //显示x轴线
    axisLine: {
      lineStyle: {
        color: "#F3F6F6",
        width: 1,
        // type: 'dashed', //虚线
      },
    },
    axisTick: {
      show: false, //关闭x轴刻度
    },
    data: [],
  },

  yAxis: {
    type: "value",
    minInterval: 1, //最小刻度1，显示整数
    axisLabel: {
      fontSize: "12", //y轴字体大小
      fontWeight: 400,
      color: "#999",
      margin: 20, //y轴数据到图之间的距离
      interval: 0, // 设置字体倾斜
      rotate: "0", //字体旋转度数
    },

    splitLine: {
      // 网格线
      lineStyle: {
        color: ["#F3F6F6"], // 颜色
      },
    },
    axisLine: {
      show: true, //显示y轴线
      lineStyle: {
        color: "#F3F6F6",
        width: 1,
      },
    },
    axisTick: {
      show: false, //关闭y轴刻度
    },
  },

  series: [
    // {
    //   itemStyle: {
    //     normal: {
    //       //柱形图圆角，初始化效果
    //       barBorderRadius: [0, 20, 20, 0],
    //       color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
    //         { offset: 0, color: "#8FCBFF" },
    //         { offset: 0.3, color: "#78BFFB" },
    //         { offset: 1, color: "#5CB0F6" },
    //       ]),
    //     },
    //   },
    // },
  ],
  color: [
    "#FABB28",
    "#60B7F7",
    "#F5825F",
    "#5470c6",
    "#91cc75",
    "#fac858",
    "#ee6666",
    "#73c0de",
    "#3ba272",
    "#fc8452",
    "#9a60b4",
    "#ea7ccc",
  ],
};

export default barOption;
