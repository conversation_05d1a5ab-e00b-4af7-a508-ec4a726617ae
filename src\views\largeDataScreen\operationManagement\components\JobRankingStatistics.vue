<template>
  <div class="ranking">
    <main-title title="各区收运量排名统计(日)"></main-title>
    <div class="data-title">
      <div class="title flex-h">排名</div>
      <div class="title">区域名称</div>
      <div class="title">收运点位数量</div>
      <!-- <div class="title">收运点位占比</div> -->
      <div class="title">收运量 (KG)</div>
    </div>
    <!-- 全部的，需要滚动 -->
    <vue-seamless-scroll
      class="seamless-scroll"
      :class="{ 'can-scroll': jobRankingData.length < 100 }"
      :data="jobRankingData"
      :class-option="seamlessScrollOption"
    >
      <ul class="data-list">
        <li
          class="data-item"
          @click="handleItem(item)"
          :class="['data-item' + index, item.districtId === areaId ? 'data-item-active' : '']"
          v-for="(item, index) in jobRankingData"
          :key="index"
        >
          <div class="title flex-h data-no">
            <img :src="require(`@/assets/images/rankingNo${index + 1 > 3 ? 'Other' : index + 1}.png`)" />
            <div class="index">
              {{ index + 1 < 10 ? "0" + (index + 1) : index + 1 }}
            </div>
          </div>

          <div class="title">{{ item.areaName }}</div>
          <div class="title">{{ item.quantity }}</div>
          <!-- <div class="title">{{ item.percentage }}%</div> -->
          <div class="title">{{ item.rubbishTotal }}</div>
        </li>
      </ul>
    </vue-seamless-scroll>
  </div>
</template>

<script>
  import MainTitle from "@/components/mainTitle/index.vue";

  export default {
    components: {
      MainTitle,
    },
    props: {
      jobRankingData: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        listData: [],
        seamlessScrollOption: {
          step: 2, // 数值越大速度滚动越快
          limitMoveNum: 100, // 开始无缝滚动的数据量 this.dataList.length
          hoverStop: true, // 是否开启鼠标悬停stop
          direction: 1, // 0向下 1向上 2向左 3向右
          openWatch: true, // 开启数据实时监控刷新dom
          singleHeight: 50, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
          singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
          waitTime: 2000, // 单步运动停止的时间(默认值1000ms)
          openTouch: false, // 关闭移动端滑动,不然移动端点击自己动了
        },
        areaId: "",
      };
    },
    created() {
      let list = [];
      for (let i = 0; i < 8; i++) {
        list.push({
          id: i + 199239239 + "",
          areaName: "越秀1线",
          quantity: "粤A.FD557",
          rubbishTotal: 900,
          percentage: 900,
        });
      }
      this.listData = list;
    },

    mounted() {},

    beforeDestroy() {},

    methods: {
      handleItem(item) {
        if (this.areaId === item.districtId) return;
        this.areaId = item.areaId;
        // 通知 mapCharts 组件切换地图
        this.$emit("changeAreaByFromJobRanking", item);
      },

      setActiveId(id) {
        this.areaId = id;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .ranking {
    width: 480px;
    position: absolute;
    top: 130px;
    left: 40px;
    z-index: 1;
  }

  .data-title {
    display: flex;
    padding: 8px 12px;
    margin-bottom: 6px;
    color: #ffffff;
    background: linear-gradient(270deg, rgba(93, 255, 203, 0) 0%, rgba(93, 255, 203, 0.1) 100%);
    font-size: 16px;
    line-height: 22px;
    font-weight: 400;
    width: 100%;
    .title {
      flex: 1;
      position: relative;
      text-align: center;
    }
  }

  .seamless-scroll {
    height: 420px;
    overflow: hidden;
    width: 100%;
    font-size: 16px;
    line-height: 22px;
    font-weight: 400;
    color: #ffffff;
    margin-top: 10px;

    .data-list {
      .data-item {
        cursor: pointer;
        position: relative;
        display: flex;
        padding: 6px 12px;
        margin-bottom: 6px;
        align-items: center;
        background: linear-gradient(270deg, rgba(93, 255, 203, 0) 0%, rgba(93, 255, 203, 0.1) 100%);
        .title {
          flex: 1;
          margin: 0 6px;
          text-align: center;
        }
        .data-no {
          margin-left: 0;
          .index {
            position: absolute;
            font-size: 12px;
            color: #449578;
            font-weight: 700;
            top: 9px;
            left: 27px;
            margin: 0;
          }
        }
        img {
          width: 44px;
        }

        &-active {
          color: #00fffc;
          border: 1px solid #ffa600;
        }
      }
    }
  }

  .can-scroll {
    overflow: auto;
    &::-webkit-scrollbar {
      width: 0 !important; /* 隐藏滚动条 */
      height: 0 !important; /* 隐藏滚动条 */
    }
    /* 隐藏滚动条（WebKit） */
    &::-webkit-scrollbar {
      display: none;
    }

    /* 隐藏滚动条（Firefox） */
    @-moz-document url-prefix() {
      scrollbar-width: thin;
    }
    /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
    -ms-overflow-style: none;
    scrollbar-width: none; /* Firefox 64+ */
    scrollbar-color: transparent transparent;
  }

  .flex-h {
    flex: 0.5 !important;
  }
</style>
