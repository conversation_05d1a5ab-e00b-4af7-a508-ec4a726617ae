<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="活动名称" prop="activityName">
              <el-input
                v-model="ruleForm.activityName"
                placeholder="请输入活动名称"
                clearable
                maxlength="30"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="活动类型" prop="type">
              <el-select v-model="ruleForm.type" placeholder="请选择活动类型" clearable filterable>
                <el-option
                  v-for="(item, index) in SAFEPRODUCTION_TYPE"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="参与人数" prop="number">
              <el-input-number v-model="ruleForm.number" step-strictly :min="0" :max="300"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="负责人" prop="personInCharge">
              <el-input
                v-model="ruleForm.personInCharge"
                placeholder="请输入负责人"
                clearable
                maxlength="10"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="活动日期" prop="activeDate">
              <el-date-picker
                v-model="ruleForm.activeDate"
                type="date"
                placeholder="选择日期"
                clearable
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="活动地点" prop="address">
              <el-input
                v-model="ruleForm.address"
                placeholder="请输入活动地点"
                clearable
                maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24">
            <el-form-item label="活动内容" prop="description">
              <el-input
                v-model="ruleForm.description"
                placeholder="请输入活动内容"
                clearable
                type="textarea"
                rows="10"
                maxlength="500"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24">
            <el-form-item label="附件">
              <FileUpload
                listType="text"
                @uploadChange="uploadChangeFileList"
                :imageList="imageList"
                :limit="1"
                :suffix="suffix"
                :accept="suffix"
              >
                <template #tips><el-tag type="warning">请上传压缩文件（支持格式zip、rar）</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import FileUpload from "@/components/FileUpload";
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  import { SAFEPRODUCTION_TYPE } from "@/enums";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      FileUpload,
    },
    data() {
      return {
        ruleForm: {
          activityName: "", //活动名称
          type: "", //活动类型
          number: "", //参与人数
          personInCharge: "", //负责人
          activeDate: "", //活动日期
          address: "", //活动地点
          description: "", //活动内容
        },
        rules: {
          activityName: [{ required: true, message: "请输入活动名称", trigger: "blur" }],
          type: [{ required: true, message: "请选择活动类型", trigger: "change" }],
          number: [{ required: true, message: "请输入参与人数", trigger: "change" }],
          personInCharge: [{ required: true, message: "请输入负责人", trigger: "blur" }],
          activeDate: [{ required: true, message: "请选择活动日期", trigger: "change" }],
          address: [{ required: true, message: "请输入活动地点", trigger: "change" }],
        },
        apis: {
          create: "/api/safety/safetyproduction/create",
          update: "/api/safety/safetyproduction/update",
          info: "/api/safety/safetyproduction/get/",
        },
        suffix: ".zip,.rar",
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [],
        carOptions: [],
        SAFEPRODUCTION_TYPE,
        pickerOptions: {
          disabledDate: (time) => time.getTime() > new Date(new Date().setHours(23, 59, 59)),
        },
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          let imageObj = "";
          try {
            imageObj = JSON.parse(this.ruleForm.fileUrl);
          } catch (error) {
            imageObj = "";
          }
          if (imageObj) {
            this.imageList = [{ url: imageObj.url, file: imageObj, name: imageObj.name }];
          }
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = this.recordId
                ? await updateApiFun(this.ruleForm, this.apis.update)
                : await createApiFun(this.ruleForm, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}安全生产记录成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      uploadChangeFileList(fileList) {
        if (fileList.length > 0) {
          this.ruleForm.fileUrl = JSON.stringify({
            name: fileList[0].name,
            size: fileList[0].size,
            ...fileList[0].file,
          });
        } else {
          this.ruleForm.fileUrl = "";
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
  }
  ::v-deep .el-transfer-panel__body {
    height: 400px;
  }
</style>
