<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="" :visible.sync="dialogVisible" width="1400px" top="0" destroy-on-close @open="getRecord">
      <template #title>
        <header class="header-title">临时调整</header>
      </template>
      <temporary v-if="recordForm?.id" :recordForm="recordForm" @closeRecord="dialogVisible = false"></temporary>
    </el-dialog>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  import temporary from "@/views/collectTransportManage/electronicWaybill/components/temporary";
  export default {
    components: {
      temporary,
    },
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      temporaryId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        apis: {
          info: "/api/waybill/get/",
        },
        loading: false,
        recordForm: {},
      };
    },
    methods: {
      async getRecord() {
        let res = await getInfoApiFun(this.temporaryId, this.apis.info);
        if (res.success) {
          this.recordForm = res.data;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
