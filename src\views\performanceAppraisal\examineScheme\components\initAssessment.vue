<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      :title="`发起${period ? '年度' : '月度'}考核`"
      :visible.sync="dialogVisible"
      width="420px"
      top="0"
      destroy-on-close
      @open="initData"
    >
      <div v-loading="loading">
        <el-form class="form-box" :model="ruleForm" ref="ruleForm" label-width="80px">
          <el-form-item
            label="考核年度"
            prop="date"
            v-if="period === 1"
            :rules="[{ required: true, message: '请选择考核年度', trigger: 'change' }]"
          >
            <el-date-picker
              v-model="ruleForm.date"
              type="year"
              placeholder="请选择考核年度"
              :picker-options="yearOptions"
            ></el-date-picker>
          </el-form-item>
          <el-form-item
            label="考核月度"
            v-else
            prop="date"
            :rules="[{ required: true, message: '请选择考核月度', trigger: 'change' }]"
          >
            <el-date-picker
              v-model="ruleForm.date"
              type="month"
              placeholder="请选择考核月度"
              :picker-options="monthOptions"
            ></el-date-picker>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveThrottling">确定</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import moment from "moment";
  import { createApiFun } from "@/api/base";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      period: {
        type: Number,
        default: 0,
      },
      initId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        originalForm: {
          date: "",
        },
        ruleForm: {},
        saveThrottling: () => {},
        loading: false,
        monthOptions: {
          disabledDate: (time) => {
            let nowYY = new Date().getFullYear();
            let nowMM = new Date().getMonth() + 1;
            let nowDD = new Date(nowYY, nowMM, 0).getDate();
            nowMM = nowMM >= 10 ? nowMM : "0" + nowMM;
            nowDD = nowDD >= 10 ? nowDD : "0" + nowDD;
            let nowTime = new Date(`${nowYY}/${nowMM}/${nowDD}`).getTime();
            let lastDate = new Date();
            lastDate.setDate(0);
            let lastYY = lastDate.getFullYear();
            let lastMM = lastDate.getMonth() + 1;
            lastMM = lastMM >= 10 ? lastMM : "0" + lastMM;
            let lastTime = new Date(`${lastYY}/${lastMM}/01`).getTime();
            return time.getTime() <= lastTime || time.getTime() >= nowTime;
          },
        },
        yearOptions: {
          disabledDate: (time) => {
            let nowYear = new Date().getFullYear();
            let lastYear = new Date(new Date().setFullYear(nowYear - 1)).getFullYear();
            return (
              time.getTime() <= new Date(`${lastYear}/01/01`).getTime() ||
              time.getTime() >= new Date(`${nowYear}/12/31`).getTime()
            );
          },
        },
        apis: {
          initAssess: "/api/assess/scheme/initiateAssess",
        },
      };
    },
    created() {
      this.saveThrottling = this.$throttling(this.saveRecord, 500);
    },
    methods: {
      async initData() {
        this.ruleForm = Object.assign({}, this.originalForm);
        this.$nextTick(() => {
          this.$refs.ruleForm.clearValidate();
        });
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun(
                {
                  id: this.initId,
                  year: moment(this.ruleForm.date).format("YYYY"),
                  month: this.period === 1 ? "" : moment(this.ruleForm.date).format("M"),
                },
                this.apis.initAssess,
              );
              if (res.success) {
                this.$message.success("发起考核成功");
                this.dialogVisible = false;
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .form-box {
    padding: 20px 0 10px 0;
  }
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  ::v-deep .el-dialog {
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
  }
  ::v-deep .el-dialog__body {
    padding: 0px 20px 20px 20px;
  }
</style>
