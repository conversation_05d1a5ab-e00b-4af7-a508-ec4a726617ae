<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="电子签名" :visible.sync="dialogVisible" width="40%" destroy-on-close @open="initData">
      <div v-loading="loading">
        <div style="border: 1px solid #eee" ref="canvasBox">
          <canvas id="canvasId" />
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="clearSignature">重签</el-button>
          <el-button type="primary" @click="saveThrottling">确定</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import SignaturePad from "signature_pad";
  export default {
    name: "Signature",
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      baseUrl: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        saveThrottling: () => {},
        loading: false,
        SignaturePad: null,
        config: {
          penColor: "#000000", //笔刷颜色
          minWidth: 3, //最小宽度
        },
      };
    },
    created() {
      this.saveThrottling = this.$throttling(this.saveRecord, 500);
    },
    methods: {
      async initData() {
        this.$nextTick(() => {
          this.getCanvas();
        });
      },
      getCanvas() {
        let canvas = document.getElementById("canvasId");
        this.signaturePad = new SignaturePad(canvas, this.config);
        canvas.height = 400;
        canvas.width = this.$refs.canvasBox.clientWidth;
        if (this.baseUrl) {
          this.signaturePad.fromDataURL(this.baseUrl, {
            width: this.$refs.canvasBox.clientWidth,
            height: 400,
          });
        }
      },
      // 确定签名
      saveRecord() {
        if (this.signaturePad.isEmpty()) {
          this.$message.warning("电子签名不能为空");
        } else {
          this.$emit("success", this.signaturePad.toDataURL());
        }
      },
      // 重签
      clearSignature() {
        this.signaturePad.clear();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
