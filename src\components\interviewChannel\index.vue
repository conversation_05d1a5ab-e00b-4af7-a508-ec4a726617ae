<template>
  <div class="micro-app-sctmp_base" v-if="!channel">
    <template v-if="needCol">
      <el-col class="mt-10" :span="colNum">
        <el-form-item label="渠道名称">
          <el-select v-model="channelId" placeholder="请选择渠道名称" clearable filterable>
            <el-option v-for="item in channelList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </template>
    <el-form class="interview-form" label-suffix=":" label-width="80px" v-else>
      <el-form-item label="渠道名称">
        <el-select v-model="channelId" placeholder="请选择渠道名称" clearable filterable>
          <el-option v-for="item in channelList" :key="item.id" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  import { getLocalStorage } from "@/utils/storage";
  import { INTERVIEW_CHANNEL } from "@/utils/commonValue"; //访问渠道
  export default {
    props: {
      value: {
        type: [String, Number],
        default: "",
      },
      record: {
        type: Object,
        default: () => {},
      },
      needCol: {
        type: Boolean,
        default: false,
      },
      colNum: {
        type: Number,
        default: 8,
      },
    },
    data() {
      return {
        channel: "",
        channelList: [],
        apis: {
          list: "/api/base/dna/listByDna/",
        },
      };
    },
    computed: {
      channelId: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
      channelRecord: {
        get() {
          return this.record;
        },
        set(e) {
          this.$emit("update:record", e);
        },
      },
    },
    created() {
      this.channel = getLocalStorage(INTERVIEW_CHANNEL);
      this.getChannelList();
    },
    methods: {
      async getChannelList() {
        try {
          let res = await getInfoApiFun(this.$DNACONFIG.dna, this.apis.list);
          if (res.success) {
            this.channelList = res.data;
            let channelRecord = {};
            if (res.data.length > 0) {
              res.data.forEach((item) => {
                channelRecord[item.id] = item.name;
              });
            }
            this.channelRecord = channelRecord;
          }
        } catch (error) {
          console.log(error);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .interview-form {
    margin-right: 10px;
  }
  ::v-deep .el-form-item {
    margin-bottom: 0;
  }
</style>
