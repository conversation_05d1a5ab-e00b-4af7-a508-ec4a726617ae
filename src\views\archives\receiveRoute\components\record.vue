<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-left">
      <div class="record-content">
        <div class="card-header">
          <div>{{ recordId ? (ruleForm.status === 2 ? "详情" : "编辑收运路线档案") : "新增收运路线档案" }}</div>
          <div class="version-number">版本号：v{{ ruleForm.versionNumber.toFixed(1) }}</div>
        </div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" label-suffix="：">
          <baseTitle title="路线基础信息："></baseTitle>
          <el-row>
            <el-col :md="24" :lg="12">
              <el-form-item label="路线名称" prop="name">
                <el-input
                  v-model="ruleForm.name"
                  placeholder="请输入路线名称"
                  clearable
                  maxlength="32"
                  show-word-limit
                  :disabled="ruleForm.status === 2"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="路线编号" prop="code">
                <el-input
                  v-model="ruleForm.code"
                  placeholder="请输入路线编号"
                  clearable
                  maxlength="6"
                  show-word-limit
                  :disabled="ruleForm.status === 2"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="路线状态" prop="status">
                <el-select
                  v-model="ruleForm.status"
                  placeholder="请选择路线状态"
                  clearable
                  filterable
                  :disabled="ruleForm.status === 2"
                >
                  <el-option
                    v-for="(item, index) in ruleForm.status === 2 ? ROUTE_STATUS : ROUTE_STATUS.slice(0, 2)"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="所属区域" prop="districtId">
                <el-cascader
                  v-model="ruleForm.districtId"
                  :options="districtOptions"
                  clearable
                  filterable
                  :props="cascaderProps"
                  :disabled="ruleForm.status === 2"
                ></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="路线属性" prop="type">
                <el-select
                  v-model="ruleForm.type"
                  placeholder="请选择路线属性"
                  clearable
                  filterable
                  :disabled="ruleForm.status === 2"
                  @change="changeRouteType"
                >
                  <el-option
                    v-for="(item, index) in ROUTE_PROPERTY"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="收运方式" prop="waybillType">
                <el-select
                  v-model="ruleForm.waybillType"
                  placeholder="请选择收运方式"
                  clearable
                  filterable
                  :disabled="ruleForm.status === 2"
                >
                  <el-option
                    v-for="(item, index) in WAYBILL_TYPE"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <baseTitle title="收运车辆、人员基础信息："></baseTitle>
          <el-row>
            <el-col>
              <el-form-item label="收运车辆" prop="defaultVehiclePlateNumber">
                <el-select
                  v-model="ruleForm.defaultVehiclePlateNumber"
                  placeholder="请选择收运车辆"
                  clearable
                  filterable
                  :disabled="ruleForm.status === 2"
                  @change="changePlateNumber"
                >
                  <el-option
                    v-for="item in carOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="驾驶司机" prop="defaultDriverDossierId">
                <el-select
                  v-model="ruleForm.defaultDriverDossierId"
                  placeholder="请选择驾驶司机"
                  clearable
                  filterable
                  :disabled="ruleForm.status === 2"
                  @change="driverAndWorkerChange($event, 'defaultDriverDossierPhone', driverOptions)"
                >
                  <el-option
                    v-for="item in driverOptions"
                    :key="item.lgUnionId"
                    :label="item.fullName"
                    :value="item.lgUnionId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="司机联系方式" prop="defaultDriverDossierPhone">
                <el-input
                  v-model="ruleForm.defaultDriverDossierPhone"
                  placeholder="请输入司机联系方式"
                  clearable
                  :maxlength="11"
                  show-word-limit
                  :disabled="ruleForm.status === 2"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="押运工1" prop="supercargoDossierOneId">
                <el-select
                  v-model="ruleForm.supercargoDossierOneId"
                  placeholder="请选择押运工1"
                  clearable
                  filterable
                  :disabled="ruleForm.status === 2"
                  @change="driverAndWorkerChange($event, 'supercargoDossierOnePhone', shipWorkerOptions)"
                >
                  <el-option
                    v-for="item in shipWorkerOptions"
                    :key="item.lgUnionId"
                    :label="item.fullName"
                    :value="item.lgUnionId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="押运工联系方式" prop="supercargoDossierOnePhone">
                <el-input
                  v-model="ruleForm.supercargoDossierOnePhone"
                  placeholder="请输入押运工联系方式"
                  clearable
                  :maxlength="11"
                  show-word-limit
                  :disabled="ruleForm.status === 2"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="押运工2" prop="supercargoDossierTwoId">
                <el-select
                  v-model="ruleForm.supercargoDossierTwoId"
                  placeholder="请选择押运工2"
                  clearable
                  filterable
                  :disabled="ruleForm.status === 2"
                  @change="driverAndWorkerChange($event, 'supercargoDossierTwoPhone', shipWorkerOptions)"
                >
                  <el-option
                    v-for="item in shipWorkerOptions"
                    :key="item.lgUnionId"
                    :label="item.fullName"
                    :value="item.lgUnionId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="押运工联系方式" prop="supercargoDossierTwoPhone">
                <el-input
                  v-model="ruleForm.supercargoDossierTwoPhone"
                  placeholder="请输入押运工联系方式"
                  clearable
                  :maxlength="11"
                  show-word-limit
                  :disabled="ruleForm.status === 2"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="收运人员轮值" prop="isChange">
                <el-switch
                  v-model="ruleForm.isChange"
                  active-text="开启"
                  inactive-text="关闭"
                  :active-value="1"
                  :inactive-value="0"
                  :disabled="ruleForm.status === 2"
                ></el-switch>
              </el-form-item>
            </el-col>
          </el-row>
          <template v-if="ruleForm.isChange == 1">
            <baseTitle title="收运人员轮值信息："></baseTitle>
            <el-form-item label="轮值司机"></el-form-item>
            <el-form-item label-width="0">
              <el-table
                :data="ruleForm.driverChangeList"
                :header-cell-style="{ background: '#F5F7F9' }"
                style="width: 100%"
                border
              >
                <el-table-column prop="changeUserId" label="司机名称" align="center">
                  <template #default="{ row }">
                    <el-select
                      v-model="row.changeUserId"
                      placeholder="请选择司机"
                      clearable
                      filterable
                      :disabled="ruleForm.status === 2"
                      @change="turnDriverAndWorker($event, row, driverOptions)"
                    >
                      <el-option
                        v-for="item in driverOptions"
                        :key="item.lgUnionId"
                        :label="item.fullName"
                        :value="item.lgUnionId"
                      ></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="changeUserPhone" label="司机联系方式" align="center">
                  <template #default="{ row }">{{ row.changeUserPhone || "-" }}</template>
                </el-table-column>
                <el-table-column prop="changeWeek" label="轮值日" align="center">
                  <template #default="{ row }">
                    <el-select
                      v-model="row.changeWeek"
                      placeholder="请选择轮值日"
                      clearable
                      filterable
                      :disabled="ruleForm.status === 2"
                    >
                      <el-option
                        v-for="(item, index) in weekList"
                        :key="index + 1"
                        :label="item"
                        :value="index + 1"
                      ></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" v-if="ruleForm.status !== 2">
                  <template #default="{ $index }">
                    <el-link type="danger" @click="deleteTurn($index, 'driverChangeList')">删除</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
            <el-button
              type="primary"
              icon="el-icon-plus"
              class="mb-10"
              size="small"
              @click="addTurn(0, 'driverChangeList')"
              v-if="ruleForm.status !== 2"
              >增加轮值司机</el-button
            >
            <el-form-item label="轮值押运工1"></el-form-item>
            <el-form-item label-width="0">
              <el-table
                :data="ruleForm.shipWorkerOneChangeList"
                :header-cell-style="{ background: '#F5F7F9' }"
                style="width: 100%"
                border
              >
                <el-table-column prop="changeUserId" label="押运工名称" align="center">
                  <template #default="{ row }">
                    <el-select
                      v-model="row.changeUserId"
                      placeholder="请选择押运工"
                      clearable
                      filterable
                      :disabled="ruleForm.status === 2"
                      @change="turnDriverAndWorker($event, row, shipWorkerOptions)"
                    >
                      <el-option
                        v-for="item in shipWorkerOptions"
                        :key="item.lgUnionId"
                        :label="item.fullName"
                        :value="item.lgUnionId"
                      ></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="changeUserPhone" label="押运工联系方式" align="center">
                  <template #default="{ row }">{{ row.changeUserPhone || "-" }}</template>
                </el-table-column>
                <el-table-column prop="changeWeek" label="轮值日" align="center">
                  <template #default="{ row }">
                    <el-select
                      v-model="row.changeWeek"
                      placeholder="请选择轮值日"
                      clearable
                      filterable
                      :disabled="ruleForm.status === 2"
                    >
                      <el-option
                        v-for="(item, index) in weekList"
                        :key="index + 1"
                        :label="item"
                        :value="index + 1"
                      ></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" v-if="ruleForm.status !== 2">
                  <template #default="{ $index }">
                    <el-link type="danger" @click="deleteTurn($index, 'shipWorkerOneChangeList')">删除</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
            <el-button
              type="primary"
              class="mb-10"
              icon="el-icon-plus"
              size="small"
              @click="addTurn(1, 'shipWorkerOneChangeList')"
              v-if="ruleForm.status !== 2"
              >增加轮值押运工</el-button
            >
            <el-form-item label="轮值押运工2"></el-form-item>
            <el-form-item label-width="0">
              <el-table
                :data="ruleForm.shipWorkerTwoChangeList"
                :header-cell-style="{ background: '#F5F7F9' }"
                style="width: 100%"
                border
              >
                <el-table-column prop="changeUserId" label="押运工名称" align="center">
                  <template #default="{ row }">
                    <el-select
                      v-model="row.changeUserId"
                      placeholder="请选择押运工"
                      clearable
                      filterable
                      :disabled="ruleForm.status === 2"
                      @change="turnDriverAndWorker($event, row, shipWorkerOptions)"
                    >
                      <el-option
                        v-for="item in shipWorkerOptions"
                        :key="item.lgUnionId"
                        :label="item.fullName"
                        :value="item.lgUnionId"
                      ></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column prop="changeUserPhone" label="押运工联系方式" align="center">
                  <template #default="{ row }">{{ row.changeUserPhone || "-" }}</template>
                </el-table-column>
                <el-table-column prop="changeWeek" label="轮值日" align="center">
                  <template #default="{ row }">
                    <el-select
                      v-model="row.changeWeek"
                      placeholder="请选择轮值日"
                      clearable
                      filterable
                      :disabled="ruleForm.status === 2"
                    >
                      <el-option
                        v-for="(item, index) in weekList"
                        :key="index + 1"
                        :label="item"
                        :value="index + 1"
                      ></el-option>
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" v-if="ruleForm.status !== 2">
                  <template #default="{ $index }">
                    <el-link type="danger" @click="deleteTurn($index, 'shipWorkerTwoChangeList')">删除</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
            <el-button
              type="primary"
              class="mb-20"
              icon="el-icon-plus"
              size="small"
              @click="addTurn(2, 'shipWorkerTwoChangeList')"
              v-if="ruleForm.status !== 2"
              >增加轮值押运工</el-button
            >
          </template>
          <baseTitle title="收运点位基础信息："></baseTitle>
          <el-form-item prop="tableData" label-width="0">
            <el-table
              class="point-table"
              :data="ruleForm.tableData"
              :header-cell-style="{ background: '#F5F7F9' }"
              style="width: 100%"
              border
              row-key="id"
              row-class-name="table-row"
            >
              <el-table-column type="index" label="收运顺序" align="center" width="80"></el-table-column>
              <el-table-column prop="name" label="点位名称" align="center" min-width="200">
                <template #default="{ row }">
                  <el-tooltip effect="dark" :content="row.name" placement="top" :open-delay="100">
                    <div class="line-clamp line-clamp-2">{{ row.name }}</div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="code" label="点位编号" align="center"></el-table-column>
              <el-table-column prop="address" label="点位地址" align="center" min-width="240">
                <template #default="{ row }">
                  <el-tooltip effect="dark" :content="row.address" placement="top" :open-delay="100">
                    <div class="line-clamp line-clamp-2">{{ row.address }}</div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="contact" label="点位联系人" align="center"></el-table-column>
              <el-table-column prop="contactPhone" label="点位联系方式" align="center"></el-table-column>
              <el-table-column prop="type" label="点位类型" align="center">
                <template #default="{ row }">{{ POINT_TYPE[row.type] }}</template>
              </el-table-column>
              <el-table-column prop="baggingMethod" label="点位收运方式" align="center">
                <template #default="{ row }">{{ POINT_RECEIVING_METHOD[row.baggingMethod] }}</template>
              </el-table-column>
              <el-table-column prop="period" label="收运周期" align="center">
                <template #default="{ row }">{{ COLLECTION_CYCLE[row.period] }}</template>
              </el-table-column>
              <el-table-column prop="frequency" label="收运频次" align="center"></el-table-column>
              <el-table-column prop="isUndock" label="经营状态" align="center">
                <template #default="{ row }">{{ REMOVE_STATUS[row.isUndock] }}</template>
              </el-table-column>
              <el-table-column label="操作" align="center" v-if="ruleForm.status !== 2">
                <template #default="{ row }">
                  <el-link class="mr-10" type="primary" @click="openPointDialog(row)">编辑</el-link>
                  <el-link type="danger" @click="deletePoint(row)">删除</el-link>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-button
            type="primary"
            class="mt-10"
            icon="el-icon-plus"
            size="small"
            @click="showPointList = true"
            v-if="ruleForm.status !== 2"
            >增加收运点位</el-button
          >
        </el-form>
      </div>
      <div class="record-footer">
        <el-button @click="closeRecord">{{ ruleForm.status === 2 ? "返回" : "取消" }}</el-button>
        <el-button type="primary" @click="saveRecordThrottling" v-if="ruleForm.status !== 2">保存</el-button>
      </div>
    </div>
    <template v-if="recordId">
      <div class="vertical-line"></div>
      <div class="record-right">
        <ul class="history-list">
          <li class="history-item" v-for="item in historyList" :key="item.id">
            <div class="item-header">
              <div class="header-left"
                >{{ item.createFullname }}&nbsp;&nbsp;版本号v{{ item.versionNumber.toFixed(1) }}</div
              >
              <div class="header-right">{{ item.createTime }}</div>
            </div>
            <div class="item-content" v-html="item.content"></div>
            <div class="item-remark">备注：{{ item.remark }}</div>
            <el-link type="primary" @click="viewHistoryList(item.pickupPathHistoryId, item.columns)"
              >历史路线信息</el-link
            >
          </li>
        </ul>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-suffix="：" v-if="ruleForm.status !== 2">
          <el-form-item label="备注" prop="historyRemark">
            <el-input
              v-model="ruleForm.historyRemark"
              placeholder="请输入备注"
              clearable
              type="textarea"
              rows="10"
              maxlength="500"
              show-word-limit
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </template>
    <pointList
      :value.sync="showPointList"
      :pointArray="ruleForm.tableData"
      :routeType="ruleForm.type"
      :selectedPointIds="selectedPointIds"
      @selectPoint="selectPoint"
      @deletePoint="deletePoint"
    ></pointList>
    <historyRecord
      :value.sync="showHistoryRecord"
      :historyId="historyId"
      :historyColumns="historyColumns"
      :districtOptions="districtOptions"
      :cascaderProps="cascaderProps"
      :carOptions="carOptions"
      :driverOptions="driverOptions"
      :shipWorkerOptions="shipWorkerOptions"
    ></historyRecord>
    <pointDialog
      :value.sync="showPointDialog"
      :recordId="pointId"
      :routeId="recordId"
      @setPoint="setPoint"
    ></pointDialog>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun, updateApiFun, getListApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import {
    ROUTE_PROPERTY,
    ROUTE_STATUS,
    POINT_TYPE,
    REMOVE_STATUS,
    WAYBILL_TYPE,
    COLLECTION_CYCLE,
    POINT_RECEIVING_METHOD,
  } from "@/enums";
  import pointList from "./pointList.vue";
  import historyRecord from "./historyRecord.vue";
  import pointDialog from "./pointDialog.vue";
  import Sortable from "sortablejs";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
      districtOptions: {
        type: Array,
        default: () => [],
      },
      cascaderProps: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      baseTitle,
      pointList,
      historyRecord,
      pointDialog,
    },
    data() {
      return {
        ruleForm: {
          name: "", //路线名称
          code: "", //路线编号
          status: "", //路线状态
          districtId: "", //所属区域id
          type: "", //路线属性
          waybillType: "", //收运方式
          strategy: 0, //路线策略（默认为0-速度优先）
          defaultVehiclePlateNumber: "", //默认收运车辆
          defaultDriverDossierId: "", //默认驾驶司机
          defaultDriverDossierPhone: "", //司机联系方式
          supercargoDossierOneId: "", //默认押运工1
          supercargoDossierOnePhone: "", //押运工联系方式
          supercargoDossierTwoId: "", //默认押运工2
          supercargoDossierTwoPhone: "", //押运工联系方式
          tableData: [], //点位列表
          historyRemark: "", //备注
          versionNumber: 1,
          isChange: 0, // 是否开启收运人员轮值
          driverChangeList: [], //轮值司机列表
          shipWorkerOneChangeList: [], //轮值押运工1列表
          shipWorkerTwoChangeList: [], //轮值押运工2列表
        },
        rules: {
          name: [{ required: true, message: "请输入路线名称", trigger: "blur" }],
          code: [{ required: true, message: "请输入路线编号", trigger: "blur" }],
          status: [{ required: true, message: "请选择路线状态", trigger: "change" }],
          districtId: [{ required: true, message: "请选择所属区域", trigger: "change" }],
          type: [{ required: true, message: "请选择路线属性", trigger: "change" }],
          waybillType: [{ required: true, message: "请选择收运方式", trigger: "change" }],
          defaultVehiclePlateNumber: [{ required: true, message: "请选择收运车辆", trigger: "change" }],
          defaultDriverDossierId: [{ required: true, message: "请选择驾驶司机", trigger: "change" }],
          defaultDriverDossierPhone: [{ required: true, message: "请输入司机联系方式", trigger: "blur" }],
          tableData: [{ type: "array", required: true, message: "请添加收运点位信息", trigger: "change" }],
          historyRemark: [{ required: true, message: "请输入备注", trigger: "blur" }],
        },
        ROUTE_PROPERTY,
        ROUTE_STATUS,
        POINT_TYPE,
        REMOVE_STATUS,
        WAYBILL_TYPE,
        COLLECTION_CYCLE,
        POINT_RECEIVING_METHOD,
        carOptions: [], //车辆列表
        driverOptions: [], //司机列表
        shipWorkerOptions: [], //押运工列表
        apis: {
          create: "/api/pickup/pickupPath/create",
          update: "/api/pickup/pickupPath/update",
          info: "/api/pickup/pickupPath/get/",
          carList: "/api/vehicle/dossier/list",
          userList: "/api/baseuser/list",
          driverAndWorkerInfo: "/api/baseuser/getInfoByPlateNumberAndUserIdentity",
          historyList: "/api/pickup/pickupPath/history/record/pickupPathHistoryRecordList",
          compare: "/api/pickup/pickupPath/compareCurrentPathWaybill/",
        },
        saveRecordThrottling: () => {},
        loading: false,
        showPointList: false, //点位列表弹框
        showHistoryRecord: false,
        historyList: [],
        historyId: "",
        historyColumns: "",
        showPointDialog: false,
        pointId: "",
        selectedPointIds: [],
        weekList: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
        needReplace: false,
        isReplace: "",
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    async mounted() {
      await this.getOptions();
      if (this.recordId) {
        this.getRecord();
        this.getHistoryList();
      } else {
        this.initSortable();
      }
    },
    methods: {
      initSortable() {
        this.$nextTick(() => {
          const el = this.$el.querySelector(".point-table .el-table__body-wrapper tbody");
          Sortable.create(el, {
            onEnd: (event) => {
              const { oldIndex, newIndex } = event;
              this.updateRowOrder(oldIndex, newIndex);
            },
          });
        });
      },
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getListApiFun({ userIdentity: 4 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipWorkerOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },
      // 获取收运路线档案详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.ruleForm.tableData = res.data.pickupPointList.map((item) => {
            return {
              ...item,
              contactPhone: item.contactPhone ? this.$sm2Decrypt(item.contactPhone) : "",
            };
          });
          this.selectedPointIds = res.data.pickupPointList.map((list) => list.id);
          if (this.ruleForm.status !== 2) {
            this.initSortable();
          }
          res.data.pickupPathChanges.forEach((list) => {
            if (list.type == 0) {
              let filterItem = this.driverOptions.filter((o) => o.lgUnionId === list.changeUserId)[0] || {};
              list.changeUserPhone = filterItem?.phone || "";
            } else {
              let filterItem = this.shipWorkerOptions.filter((o) => o.lgUnionId === list.changeUserId)[0] || {};
              list.changeUserPhone = filterItem?.phone || "";
            }
          });
          this.ruleForm.driverChangeList = res.data.pickupPathChanges.filter((list) => list.type == 0);
          this.ruleForm.shipWorkerOneChangeList = res.data.pickupPathChanges.filter((list) => list.type == 1);
          this.ruleForm.shipWorkerTwoChangeList = res.data.pickupPathChanges.filter((list) => list.type == 2);
          this.ruleForm.defaultDriverDossierPhone = this.$sm2Decrypt(this.ruleForm.defaultDriverDossierPhone);
          this.ruleForm.supercargoDossierOnePhone = this.ruleForm.supercargoDossierOnePhone
            ? this.$sm2Decrypt(this.ruleForm.supercargoDossierOnePhone)
            : "";
          this.ruleForm.supercargoDossierTwoPhone = this.ruleForm.supercargoDossierTwoPhone
            ? this.$sm2Decrypt(this.ruleForm.supercargoDossierTwoPhone)
            : "";
        }
      },
      // 获取历史记录列表
      async getHistoryList() {
        let res = await createApiFun({ pickupPathId: this.recordId }, this.apis.historyList);
        if (res.success) {
          this.historyList = res.data;
        }
      },
      // 选择默认车牌号事件回调
      async changePlateNumber(value) {
        if (value) {
          let promiseList = [
            createApiFun({ userIdentity: "3", plateNumber: value }, this.apis.driverAndWorkerInfo),
            createApiFun({ userIdentity: "4", plateNumber: value }, this.apis.driverAndWorkerInfo),
          ];
          let res = await Promise.all(promiseList);
          let driverInfo = res[0].data;
          let workerInfo = res[1].data;
          if (driverInfo) {
            this.ruleForm.defaultDriverDossierId = driverInfo.lgUnionId;
            this.ruleForm.defaultDriverDossierPhone = this.$sm2Decrypt(driverInfo.phone);
          } else {
            this.ruleForm.defaultDriverDossierId = "";
            this.ruleForm.defaultDriverDossierPhone = "";
          }
          if (workerInfo) {
            this.ruleForm.supercargoDossierOneId = workerInfo.lgUnionId;
            this.ruleForm.supercargoDossierOnePhone = workerInfo.phone ? this.$sm2Decrypt(workerInfo.phone) : "";
          } else {
            this.ruleForm.supercargoDossierOneId = "";
            this.ruleForm.supercargoDossierOnePhone = "";
          }
        }
      },
      // 修改路线属性
      changeRouteType() {
        this.ruleForm.tableData = [];
      },
      // 驾驶司机选择事件回调
      driverAndWorkerChange(value, field, options) {
        if (!value) {
          this.ruleForm[field] = "";
          return;
        }
        let filterItem = options.filter((o) => o.lgUnionId === value)[0];
        this.ruleForm[field] = filterItem.phone;
      },
      // 列表行拖拽顺序调整
      updateRowOrder(oldIndex, newIndex) {
        let arr = this.ruleForm.tableData;
        arr.splice(newIndex, 0, arr.splice(oldIndex, 1)[0]);
        this.$nextTick(() => {
          this.ruleForm.tableData = arr;
        });
      },
      // 选择点位
      selectPoint(row) {
        this.ruleForm.tableData.push(row);
      },
      // 打开编辑点位弹窗
      openPointDialog(row) {
        this.pointId = row.id;
        this.showPointDialog = true;
      },
      // 删除点位
      deletePoint(row) {
        let index = this.ruleForm.tableData.findIndex((list) => list.id === row.id);
        if (index >= 0) {
          this.ruleForm.tableData.splice(index, 1);
        }
      },
      // 轮值司机/押运工change事件
      turnDriverAndWorker(value, row, options) {
        if (!value) {
          row.changeUserPhone = "";
          return;
        }
        let filterItem = options.filter((o) => o.lgUnionId === value)[0];
        row.changeUserPhone = filterItem.phone || "";
      },
      // 添加轮值司机/押运工
      addTurn(type, listField) {
        this.ruleForm[listField].push({
          changeUserId: "",
          changeUserPhone: "",
          changeWeek: "",
          type,
        });
      },
      // 删除轮值司机/押运工
      deleteTurn(index, listField) {
        this.ruleForm[listField].splice(index, 1);
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            if (!this.recordId) {
              this.saveSecond();
              return;
            }
            let res = await getInfoApiFun(this.recordId, this.apis.compare);
            if (res.data) {
              this.needReplace = true;
              this.$confirm(
                `当前路线实际收运人员与路线档案中收运人员不一致，是否要以路线档案中人员为准更新路线收运单。是（以路线档案人员为准更新最新收运单）否（保持当前路线收运单人员不变）`,
                "提示",
                {
                  distinguishCancelAndClose: true,
                  confirmButtonText: "是",
                  cancelButtonText: "否",
                  type: "warning",
                },
              )
                .then(async () => {
                  this.isReplace = 1;
                  this.saveSecond();
                })
                .catch((action) => {
                  if (action === "cancel") {
                    this.isReplace = 0;
                    this.saveSecond();
                  }
                });
            } else {
              this.needReplace = false;
              this.saveSecond();
            }
          }
        });
      },
      // 保存第二个弹窗方法
      saveSecond() {
        this.$confirm(
          `是否要对${this.recordId ? "编辑" : "新增"}的路线进行优化？（按照时间最短策略对收运点位顺序进行调整）`,
          "提示",
          {
            distinguishCancelAndClose: true,
            confirmButtonText: "去优化",
            cancelButtonText: "保存",
            type: "warning",
          },
        )
          .then(() => {
            this.saveFunc(true);
          })
          .catch((action) => {
            if (action === "cancel") {
              this.saveFunc();
            }
          });
      },
      async saveFunc(flag = false) {
        this.loading = true;
        this.ruleForm.pickupPointIds = this.ruleForm.tableData.map((data) => data.id);
        let params = this.ruleForm;
        params.pickupPathChanges = [
          ...params.driverChangeList,
          ...params.shipWorkerOneChangeList,
          ...params.shipWorkerTwoChangeList,
        ];
        if (this.needReplace) {
          params.isReplace = this.isReplace;
        }
        try {
          let res = this.recordId
            ? await updateApiFun(this.$sm2Encrypt(JSON.stringify(params)), this.apis.update)
            : await createApiFun(this.$sm2Encrypt(JSON.stringify(params)), this.apis.create);
          if (res.success) {
            this.$message.success(`${this.recordId ? "编辑" : "新增"}收运路线档案成功`);
            this.closeRecord();
            this.$emit("refreshList");
            if (flag) {
              this.$emit("openPathAdjust", res.data);
            }
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 查看历史记录
      viewHistoryList(id, columns) {
        this.historyId = id;
        this.historyColumns = columns;
        this.showHistoryRecord = true;
      },
      // 编辑点位后设置点位列表中对应点位信息
      setPoint(obj) {
        if (obj.needDelete) {
          this.deletePoint({ id: obj.point.id });
        } else {
          let index = this.ruleForm.tableData.findIndex((list) => list.id === obj.point.id);
          if (index >= 0) {
            Object.assign(this.ruleForm.tableData[index], obj.point);
          }
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    margin-top: 20px;
  }
  .record-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
  .vertical-line {
    width: 0;
    height: 100%;
    border-right: 1px dashed #606266;
    margin: 0 20px;
  }
  .record-right {
    width: 400px;
    height: 100%;
    display: flex;
    flex-direction: column;
    .history-list {
      flex: 1;
      overflow: auto;
    }
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
    position: relative;
    .version-number {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      font-size: 14px;
      color: #909399;
    }
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .mt-10 {
    margin-top: 10px;
  }
  .mb-10 {
    margin-bottom: 10px;
  }
  .mb-20 {
    margin-bottom: 20px;
  }
  .history-item {
    font-size: 14px;
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
    .item-header {
      color: #909399;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
    }
  }
  ::v-deep .table-row {
    cursor: move;
  }
</style>
