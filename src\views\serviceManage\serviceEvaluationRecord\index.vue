<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage @createRecord="createRecord" @closeRecord="closeRecord">
      <record v-if="showRecord" :recordId="recordId" @closeRecord="closeRecord" @refreshList="initData"></record>
      <dialogue v-else-if="showDialogue" :recordId="recordId" @closeRecord="showDialogue = false"></dialogue>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="customerName" placeholder="请输入客商名称" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
            <el-dropdown
              size="small"
              trigger="click"
              @command="handleDataImport"
              class="dropdown-button"
              placement="bottom-start"
            >
              <el-button size="small" type="primary">
                <span class="sctmp-iconfont icon-ic_daoru"></span>
                <span class="ml-5">导入</span>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">
                  <div class="has-padding-sm has-border-bottom">导入基础信息</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button size="small" type="primary" @click="exportFileThrottling">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
          ></InterviewChannel>
          <el-col :span="8">
            <el-form-item label="评价日期">
              <el-date-picker
                v-model="filterForm.evaluationDate"
                type="date"
                placeholder="请选择评价日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="回单据编号">
              <el-input
                v-model="filterForm.returnDocumentNumber"
                placeholder="请输入回单据编号名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column prop="customerName" label="客商名称" align="center"></el-table-column>
            <el-table-column prop="evaluationDate" label="评价日期" align="center"></el-table-column>
            <el-table-column prop="returnDocumentNumber" label="回单据编号" align="center"></el-table-column>
            <el-table-column prop="content" label="评价情况" align="center" min-width="200">
              <template #default="{ row }">
                <el-tooltip effect="dark" :content="row.content" placement="top" :open-delay="100">
                  <div class="line-clamp line-clamp-1">{{ row.content }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="附件" align="center">
              <template #default="{ row }">
                <template v-if="row?.fileList?.length">
                  <div v-for="(item, index) in row.fileList" :key="index">
                    <el-link type="primary" @click="previewFile(item)">{{ item.name }}</el-link>
                  </div>
                </template>
                <div v-else>-</div>
              </template>
            </el-table-column>
            <el-table-column prop="evaluationDate" label="对话状态" align="center"></el-table-column>
            <el-table-column prop="evaluationDate" label="最后回复时间" align="center"></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="80" label="操作" align="center">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row)">编辑</el-link>
                <el-popconfirm class="mr-10" title="确认删除当前服务评价记录？" @confirm="deleteRecord(row)">
                  <el-link type="danger" slot="reference">删除</el-link>
                </el-popconfirm>
                <el-link class="mr-10" type="primary" @click="viewDialogue(row)">查看对话</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
    <importDialog
      :value.sync="importDialogShow"
      title="服务评价记录"
      :importApi="apis.import"
      :templateApi="apis.template"
      :importDialogType="importDialogType"
      :isTemplateApi="true"
      @importSuccess="searchFilter"
    ></importDialog>
    <el-image
      v-show="false"
      :src="dialogImageUrl"
      fit="contain"
      :preview-src-list="[dialogImageUrl]"
      ref="image__preview"
    ></el-image>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, deleteApiFun, BASE_API_URL } from "@/api/base";
  import record from "./components/record.vue";
  import dialogue from "./components/dialogue.vue";
  import { VISIT_RECORD_TYPE, VISIT_HANDLE_STATUS } from "@/enums";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import importDialog from "@/components/importDialog";
  import { downloadFileBlob } from "@/utils";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
      importDialog,
      dialogue,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/serviceEvaluationRecord/listPage",
          delete: "/api/serviceEvaluationRecord/delete/",
          export: "/api/serviceEvaluationRecord/export",
          template: "/api/serviceEvaluationRecord/excelModel",
          import: "/api/serviceEvaluationRecord/import",
        },
        showRecord: false,
        recordId: "",
        VISIT_RECORD_TYPE,
        VISIT_HANDLE_STATUS,
        importDialogShow: false,
        importDialogType: "",
        dialogVisible: false,
        exportForm: {
          registrationId: "",
        },
        exportRules: {
          registrationId: [{ required: true, message: "请选择回访登记表", trigger: "change" }],
        },
        formOptions: [],
        loading: false,
        exportFileThrottling: null,
        showFilter: false,
        customerName: "",
        dialogImageUrl: "", //图片预览路径
        showDialogue: false,
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    created() {
      this.exportFileThrottling = this.$throttling(this.handleExport, 500);
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        this.loading = true;
        try {
          let params = {
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            customerName: this.customerName,
            ...this.filterForm,
          };
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showRecord = true;
        this.recordId = "";
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.recordId = row.id;
      },
      closeRecord() {
        this.showRecord = false;
      },
      // 删除
      async deleteRecord(row) {
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            this.page.pageNo = this.pageDropProcess(this.page.pageNo, this.page.pageSize, this.page.total);
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.customerName = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 导出
      handleExport() {
        this.$confirm(`确认是否以当前筛选条件导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, {
                customerName: this.customerName,
                ...this.filterForm,
              });
              if (res.success) {
                createDownloadEvent(`服务评价记录${Date.now()}.xlsx`, [res.data]);
                window.ELEMENT.Message.success("导出成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
      // 导入
      handleDataImport(command) {
        this.importDialogType = command;
        this.importDialogShow = true;
      },
      // 预览附件
      previewFile(item) {
        let suffix = item.url.slice(item.url.lastIndexOf("."));
        if ([".png", ".jpg", ".jpeg", ".gif"].includes(suffix)) {
          this.dialogImageUrl = item.url;
          this.$refs.image__preview.clickHandler();
        } else {
          this.downloadFile(item);
        }
      },
      // 下载文件
      downloadFile(item) {
        fetch(item.url)
          .then((res) => res.blob())
          .then((blob) => {
            // 将链接地址字符内容转变成blob地址
            downloadFileBlob(blob, item.name);
          });
      },
      // 查看对话
      viewDialogue(row) {
        this.showDialogue = true;
        this.recordId = row.id;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
</style>
