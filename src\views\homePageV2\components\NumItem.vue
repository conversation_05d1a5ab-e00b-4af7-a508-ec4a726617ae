<template>
  <div class="NumItem-wrapper">
    <div class="flex-wrapper">
      <img class="img" :src="config.icon" alt="" />
      <div>
        <div class="title">{{ config.title }}</div>
        <div>
          <span class="num">{{ config.num }}</span>
          <span class="subtext">{{ config.subtext }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    components: {},
    props: ["config"],
    data() {
      return {};
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {},
    watch: {},
  };
</script>
<style scoped lang="scss">
  .img {
    width: 56px;
    margin-right: 16px;
  }
  .title {
    font-size: 14px;
    line-height: 14px;
    color: #fff;
    margin-bottom: 10px;
  }
  .num {
    line-height: 26px;
    font-size: 20px;
    color: #fff;
    font-weight: bold;
  }
  .subtext {
    line-height: 19px;
    font-size: 14px;
    color: #fff;
  }
  .flex-wrapper {
    align-items: center;
    display: flex;
  }
  .NumItem-wrapper {
    width: 230px;
  }
</style>
