<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="证件名称" prop="name">
              <el-input
                v-model="ruleForm.name"
                placeholder="请输入证件名称"
                clearable
                :maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="所属人员" prop="userId">
              <el-select v-model="ruleForm.userId" placeholder="请选择所属人员" clearable filterable>
                <el-option
                  v-for="item in userOptions"
                  :key="item.id"
                  :label="item.fullName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="证件类型" prop="certType">
              <el-select v-model="ruleForm.certType" placeholder="请选择证件类型" clearable filterable>
                <el-option v-for="(item, index) in CERT_TYPES" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="证件号" prop="certNo">
              <el-input
                class="w400"
                v-model="ruleForm.certNo"
                placeholder="请输入证件号"
                clearable
                :maxlength="30"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="证件照片-主页" prop="photoFront">
              <FileUpload @uploadChange="uploadChangeFront" :imageList="photoFrontImageList"></FileUpload>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="证件照片-副页" prop="photoBack">
              <FileUpload @uploadChange="uploadChangeBack" :imageList="photoBackImageList"></FileUpload>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="有效期开始时间" prop="validityBeginDate">
              <el-date-picker
                v-model="ruleForm.validityBeginDate"
                type="date"
                placeholder="请选择有效期开始时间"
                align="right"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="有效期截止时间" prop="validityEndDate">
              <el-date-picker
                v-model="ruleForm.validityEndDate"
                type="date"
                placeholder="请选择有效期截止时间"
                align="right"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆档案ID" prop="vehicleDossierId">
              <el-input
                class="w400"
                v-model="ruleForm.vehicleDossierId"
                placeholder="请输入工号"
                :maxlength="50"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="登记日期" prop="registrationDate">
              <el-date-picker
                v-model="ruleForm.registrationDate"
                type="datetime"
                placeholder="请选择登记日期"
                align="right"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import FileUpload from "@/components/FileUpload";
  import { CERT_TYPES } from "@/enums";
  import { getInfoApiFun, createApiFun, updateApiFun, getListApiFun } from "@/api/base";
  export default {
    components: { FileUpload },
    props: {
      recordId: {
        type: String,
        default: "",
      },
      positionOptions: {
        type: Array,
        default: () => [],
      },
      roleOptions: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        ruleForm: {
          userId: null, //所属人员ID
          name: null, //证件名称
          certType: null, //证件类型
          certNo: null, //证件号
          photoFront: null, //证件照片-正面
          photoBack: null, //证件照片-反面
          validityBeginDate: null, //有效期开始时间
          validityEndDate: null, //有效期截止时间
          vehicleDossierId: null, //车辆档案ID
          registrationDate: null, //登记日期
        },
        rules: {
          name: [{ required: true, message: "请输入证件名称", trigger: "blur" }],
          certNo: [{ required: true, message: "请输入证件号", trigger: "blur" }],
          certType: [{ required: true, message: "请选择证件类型", trigger: "change" }],
          photoFront: [{ required: true, message: "请选择证件主页照片", trigger: "change" }],
          photoBack: [{ required: true, message: "请选择证件副页照片", trigger: "change" }],
          validityBeginDate: [{ required: true, message: "请选择有效期开始时间", trigger: "change" }],
          validityEndDate: [{ required: true, message: "请选择有效期截止时间", trigger: "change" }],
        },
        photoFrontImageList: [],
        photoBackImageList: [],
        CERT_TYPES,
        userOptions: [], //用户列表
        apis: {
          create: "/api/certificate/create",
          update: "/api/certificate/update",
          inofo: "/api/certificate/get/",
        },
        saveRecordThrottling: () => {},
        loading: false,
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getUserList();
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      uploadChangeFront(fileList) {
        if (fileList.length && fileList[0].file) {
          this.ruleForm.photoFront = fileList[0].file.url;
        } else {
          this.ruleForm.photoFront = null;
        }
      },
      uploadChangeBack(fileList) {
        if (fileList.length && fileList[0].file) {
          this.ruleForm.photoBack = fileList[0].file.url;
        } else {
          this.ruleForm.photoBack = null;
        }
      },
      // 获取用户列表
      async getUserList() {
        let res = await getListApiFun("", this.apis.userList);
        if (res.success) {
          this.userOptions = res.data;
        }
      },
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.inofo);
        if (res.success) {
          if (res.data.photoFront) {
            this.photoFrontImageList = [
              {
                url: res.data.photoFront,
                file: {
                  url: res.data.photoFront,
                },
              },
            ];
          }
          if (res.data.photoBack) {
            this.photoBackImageList = [
              {
                url: res.data.photoBack,
                file: {
                  url: res.data.photoBack,
                },
              },
            ];
          }
          this.ruleForm = res.data;
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = this.recordId
                ? await updateApiFun(this.ruleForm, this.apis.update)
                : await createApiFun(this.ruleForm, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}证件成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
  }
  ::v-deep .el-transfer-panel__body {
    height: 400px;
  }
  .w400 {
    width: 400px;
  }
</style>
