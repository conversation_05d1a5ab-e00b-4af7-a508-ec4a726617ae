<template>
  <div class="backlog">
    <main-title title="车辆实时数据"></main-title>
    <div class="num-flex-wrapper mr-top-36">
      <NumItem :config="numList[0]"></NumItem>
      <NumItem :config="numList[1]"></NumItem>
    </div>
    <div class="num-flex-wrapper mr-top-56">
      <NumItem :config="numList[2]"></NumItem>
      <NumItem :config="numList[3]"></NumItem>
    </div>
  </div>
</template>

<script>
  import NumItem from "./NumItem.vue";

  import MainTitle from "./MainTitle.vue";
  export default {
    components: {
      NumItem,
      MainTitle,
    },
    props: ["numList"],
    data() {
      return {};
    },
    async created() {},
    methods: {},
  };
</script>

<style lang="scss" scoped>
  .backlog {
    width: 480px;
    position: absolute;
    top: 130px;
    left: 40px;
    z-index: 1;
  }
  .mr-top-36 {
    margin-top: 36px;
  }
  .mr-top-56 {
    margin-top: 56px;
  }
  .num-flex-wrapper {
    width: 480px;

    display: flex;
    flex-wrap: wrap;
    /* 其他样式 */
  }
</style>
