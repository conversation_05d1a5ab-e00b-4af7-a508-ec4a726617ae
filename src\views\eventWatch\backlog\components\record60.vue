<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">异常事件上报详情</div>
      <el-form label-width="180px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="上报时间" required>
              <el-date-picker
                :value="recordItem.createTime"
                type="datetime"
                placeholder="请选择上报时间"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="截止日期" required>
              <el-date-picker
                :value="recordItem.deadline"
                type="date"
                placeholder="请选择截止日期"
                clearable
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="基础信息"></baseTitle>
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="上报人" required>
              <el-input
                :value="baseForm.reportPerson"
                placeholder="请输入上报人名称"
                clearable
                maxlength="30"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="上报时间" required>
              <el-date-picker
                :value="baseForm.reportingTime"
                type="date"
                placeholder="请选择上报时间"
                clearable
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="异常事件类型" required>
              <el-select :value="baseForm.exceptionType" placeholder="请选择异常事件类型" clearable filterable disabled>
                <el-option v-for="(item, index) in ERROR_STATUS" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="是否可以继续收运" required>
              <el-select
                :value="baseForm.continueCarrying"
                placeholder="请选择是否可以继续收运"
                clearable
                filterable
                disabled
              >
                <el-option
                  v-for="item in CONTINUECARRYING_STATUS"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="异常描述">
              <el-input
                :value="baseForm.exceptionContent"
                placeholder="请输入异常描述"
                clearable
                type="textarea"
                rows="10"
                maxlength="800"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <baseTitle title="业务调整信息"></baseTitle>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
        <el-row>
          <el-col>
            <el-form-item label="收运车辆" prop="newPlateNumber">
              <el-select
                v-model="ruleForm.newPlateNumber"
                placeholder="请选择收运车辆"
                clearable
                filterable
                @change="changePlateNumber"
              >
                <el-option
                  v-for="item in carOptions"
                  :key="item.id"
                  :label="item.plateNumber + (item.isFree === 1 ? '' : '（空闲）')"
                  :value="item.plateNumber"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="8" :lg="8">
            <el-form-item label="司机" prop="newDriverDossierId">
              <el-select
                v-model="ruleForm.newDriverDossierId"
                placeholder="请选择司机"
                clearable
                filterable
                @change="driverAndWorkerChange($event, 'newDriverDossierPhone', driverOptions)"
              >
                <el-option
                  v-for="item in driverOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8">
            <el-form-item label="" prop="newDriverDossierPhone">
              <el-input
                v-model="ruleForm.newDriverDossierPhone"
                placeholder="请输入司机联系方式"
                clearable
                :maxlength="11"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="8" :lg="8">
            <el-form-item label="押运工1" prop="supercargoDossierOneId">
              <el-select
                v-model="ruleForm.supercargoDossierOneId"
                placeholder="请选择押运工1"
                clearable
                filterable
                @change="driverAndWorkerChange($event, 'supercargoDossierOnePhone', shipWorkerOptions)"
              >
                <el-option
                  v-for="item in shipWorkerOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8">
            <el-form-item label="" prop="supercargoDossierOnePhone">
              <el-input
                v-model="ruleForm.supercargoDossierOnePhone"
                placeholder="请输入押运工联系方式"
                clearable
                :maxlength="11"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="8" :lg="8">
            <el-form-item label="押运工2" prop="supercargoDossierTwoId">
              <el-select
                v-model="ruleForm.supercargoDossierTwoId"
                placeholder="请选择押运工2"
                clearable
                filterable
                @change="driverAndWorkerChange($event, 'supercargoDossierTwoPhone', shipWorkerOptions)"
              >
                <el-option
                  v-for="item in shipWorkerOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8">
            <el-form-item label="" prop="supercargoDossierTwoPhone">
              <el-input
                v-model="ruleForm.supercargoDossierTwoPhone"
                placeholder="请输入押运工联系方式"
                clearable
                :maxlength="11"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <baseTitle title="收运任务"></baseTitle>
      <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" border class="table-box">
        <el-table-column label="收运任务" align="center">
          <el-table-column prop="code" label="点位编号" align="center"></el-table-column>
          <el-table-column prop="productionUnit" label="产废单位名称" align="center"></el-table-column>
          <el-table-column prop="productionUnitOperator" label="产废单位经办人" align="center"></el-table-column>
          <el-table-column prop="waybillStatus" label="收运状态" align="center">
            <template #default="{ row }">{{ RECEIVING_CONDITION[row.waybillStatus] }}</template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <baseTitle title="备注信息"></baseTitle>
      <el-input
        v-model="ruleForm.memo"
        placeholder="请输入备注信息"
        clearable
        type="textarea"
        rows="10"
        maxlength="500"
        show-word-limit
      ></el-input>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
      <el-button type="primary" @click="saveRecordThrottling">完成</el-button>
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun, getListApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import { ERROR_STATUS, CONTINUECARRYING_STATUS, RECEIVING_CONDITION } from "@/enums";
  export default {
    props: {
      recordItem: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      baseTitle,
    },
    data() {
      return {
        baseForm: {}, //业务基础信息
        ruleForm: {
          newPlateNumber: "", //收运车辆
          newDriverDossierId: "", //司机
          newDriverDossierPhone: "", //司机联系方式
          supercargoDossierOneId: "", //押运工1
          supercargoDossierOnePhone: "", //押运工联系方式
          supercargoDossierTwoId: "", //押运工2
          supercargoDossierTwoPhone: "", //押运工联系方式
          memo: "",
        },
        rules: {
          newPlateNumber: [{ required: true, message: "请选择收运车辆", trigger: "change" }],
          newDriverDossierId: [{ required: true, message: "请选择司机", trigger: "change" }],
          newDriverDossierPhone: [{ required: true, message: "请输入司机联系方式", trigger: "blur" }],
        },
        apis: {
          info: "/api/abnormalreporting/get/",
          carList: "/api/vehicle/dossier/getFreeList",
          userList: "/api/baseuser/list",
          driverAndWorkerInfo: "/api/baseuser/getInfoByPlateNumberAndUserIdentity",
          waybillList: "/api/waybill/waybillDetail/getListByPlateNumber",
          create: "/api/waybill/todo/temporary",
        },
        saveRecordThrottling: () => {},
        loading: false,
        carOptions: [],
        driverOptions: [],
        shipWorkerOptions: [],
        tableData: [],
        ERROR_STATUS,
        CONTINUECARRYING_STATUS,
        RECEIVING_CONDITION,
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      if (this.recordItem && (this.recordItem.businessId || this.recordItem.id)) {
        this.getOptions();
        this.getRecord();
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getListApiFun({ userIdentity: 4 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipWorkerOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },
      // 获取详情
      async getRecord() {
        let res = await getInfoApiFun(
          this.recordItem.businessId ? this.recordItem.businessId : this.recordItem.id,
          this.apis.info,
        );
        this.baseForm = res.data;
        this.ruleForm.newPlateNumber = this.baseForm.plateNumber || "";
        this.ruleForm.newDriverDossierId = this.baseForm.lgUnionId || "";
        if (this.baseForm.plateNumber) {
          let rsp = await createApiFun(
            { waybillStatus: 0, defaultVehiclePlateNumber: this.baseForm.plateNumber },
            this.apis.waybillList,
          );
          this.tableData = rsp.data;
        }
      },
      // 选择默认车牌号事件回调
      async changePlateNumber(value) {
        if (value) {
          let promiseList = [
            createApiFun({ userIdentity: "3", plateNumber: value }, this.apis.driverAndWorkerInfo),
            createApiFun({ userIdentity: "4", plateNumber: value }, this.apis.driverAndWorkerInfo),
          ];
          let res = await Promise.all(promiseList);
          let driverInfo = res[0].data;
          let workerInfo = res[1].data;
          if (driverInfo) {
            this.ruleForm.newDriverDossierId = driverInfo.lgUnionId;
            this.ruleForm.defaultDriverDossierPhone = this.$sm2Decrypt(driverInfo.phone);
          } else {
            this.ruleForm.newDriverDossierId = "";
            this.ruleForm.newDriverDossierPhone = "";
          }
          if (workerInfo) {
            this.ruleForm.supercargoDossierOneId = workerInfo.lgUnionId;
            this.ruleForm.supercargoDossierOnePhone = workerInfo.phone ? this.$sm2Decrypt(workerInfo.phone) : "";
          } else {
            this.ruleForm.supercargoDossierOneId = "";
            this.ruleForm.supercargoDossierOnePhone = "";
          }
        }
      },
      // 驾驶司机选择事件回调
      driverAndWorkerChange(value, field, options) {
        if (!value) {
          this.ruleForm[field] = "";
          return;
        }
        let filterItem = options.filter((o) => o.lgUnionId === value)[0];
        this.ruleForm[field] = filterItem.phone;
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("returnIndex");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun(
                {
                  businessId: this.recordItem.businessId || this.recordItem.id,
                  originalPlateNumber: this.baseForm.plateNumber,
                  code: 60,
                  ...this.ruleForm,
                },
                this.apis.create,
              );
              if (res.success) {
                this.$message.success(`操作成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-box {
    margin-bottom: 20px;
  }
</style>
