<template>
  <el-popover
    v-model="visible"
    placement="top"
    width="600"
    trigger="click"
    :append-to-body="false"
    v-clickoutside="() => (visible = false)"
  >
    <el-select-v2
      v-model="value"
      :options="options"
      clearable
      filterable
      value-key="id"
      label-key="name"
      class="full-width"
      @change="changeOption"
    />
    <span class="content-text" slot="reference">{{ content }}</span>
  </el-popover>
</template>

<script>
  import ElSelectV2 from "el-select-v2";
  import Clickoutside from "element-ui/src/utils/clickoutside";
  export default {
    directives: {
      Clickoutside,
    },
    props: {
      content: {
        type: String,
        default: "",
      },
      options: {
        type: Array,
        default: () => [],
      },
    },
    components: {
      ElSelectV2,
    },
    data() {
      return {
        value: "",
        visible: true,
      };
    },
    methods: {
      changeOption(value) {
        let pointName = "某点位";
        if (value) {
          pointName = this.options.filter((o) => o.id === value)[0].name;
        }
        this.$emit("changeOption", pointName);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .full-width {
    width: 100%;
  }
  .content-text {
    color: #4ca786;
    &:hover {
      text-decoration: underline;
    }
  }
</style>
