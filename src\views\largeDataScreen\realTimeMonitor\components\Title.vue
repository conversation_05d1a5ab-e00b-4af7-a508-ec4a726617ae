<template>
  <div class="title-container">
    <div class="parallelogram-box" :style="{ backgroundColor: color }">
      <div class="parallelogram-item"></div>
    </div>
    <div class="parallelogram-box" :style="{ backgroundColor: color }">
      <div class="parallelogram-item"></div>
    </div>
    <div class="parallelogram-box" :style="{ backgroundColor: color }">
      <div class="parallelogram-item"></div>
    </div>
    <div class="title-text" :style="{ color }">{{ title }}</div>
    <div class="title-line" :style="{ backgroundColor: color }"></div>
  </div>
</template>

<script>
  export default {
    props: {
      title: {
        type: String,
        default: "",
      },
      color: {
        type: String,
        default: "#fff",
      },
    },
  };
</script>

<style lang="scss" scoped>
  .title-container {
    display: flex;
    align-items: center;
  }
  .parallelogram-box {
    width: 14px;
    height: 10px;
    transform: skewX(45deg);
    margin-right: 8px;
    flex-shrink: 0;
    .parallelogram-item {
      display: inline-block;
      transform: skewX(-45deg);
    }
  }
  .title-text {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    margin: 0 8px;
    flex-shrink: 0;
  }
  .title-line {
    width: 100%;
    height: 2px;
    background-color: #fff;
  }
</style>
