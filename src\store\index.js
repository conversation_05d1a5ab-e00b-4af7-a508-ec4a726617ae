import Vue from "vue";
import Vuex from "vuex";
import { LoginInfoKey, TOKEN_EXPIRED_TIME_KEY, TOKEN_KEY } from "@/utils/commonValue";
import { getLocalStorage, setLocalStorage } from "@/utils/storage";
import axios from "axios";
import { getInfoApiFunByParams, createApiFun } from "@/api/base";
import { nanoid } from "nanoid";

Vue.use(Vuex);

export default new Vuex.Store({
  state: {
    axiosCancelArr: [],
    tokenExpiration: getLocalStorage(TOKEN_EXPIRED_TIME_KEY) || "",
    isRefreshingToken: false,
    pendingRequests: [],
    formData: {}, //表单数据
    plateNumber: "", //车牌号
    integerList: [], //整数列表
    decimalsList: [], //小数列表
    waybillList: [], //收运单列表
  },
  getters: {
    isTokenExpiredOrNearExpiry: (state) => {
      if (!state.tokenExpiration) return false;
      const nowTime = new Date().getTime();
      return nowTime >= state.tokenExpiration - 5 * 60 * 1000 && nowTime <= state.tokenExpiration;
    },
  },
  mutations: {
    PUSH_CANCEL(state, cancel) {
      state.axiosCancelArr.push(cancel.cancelToken);
    },
    CLEAR_CANCEL(state) {
      state.axiosCancelArr.forEach((e) => {
        if (e) {
          e();
        }
      });
      state.axiosCancelArr = [];
    },
    setIsRefreshingToken(state, isRefreshing) {
      state.isRefreshingToken = isRefreshing;
    },
    clearPendingRequests(state) {
      state.pendingRequests = [];
    },
    // 处理表单信息
    setFormData(state, formData) {
      // 司机
      formData.statistic.departureDriverInfo.total =
        formData.statistic.departureDriverInfo.departureDriverNum +
        formData.statistic.departureDriverInfo.unDepartureDriverNum;
      formData.statistic.departureDriverInfo.totalList = String(formData.statistic.departureDriverInfo.total).split("");
      // 押运工
      formData.statistic.departureEscortInfo.total =
        formData.statistic.departureEscortInfo.departureEscortNum +
        formData.statistic.departureEscortInfo.unDepartureScortNum;
      formData.statistic.departureEscortInfo.totalList = String(formData.statistic.departureEscortInfo.total).split("");
      // 出车数
      formData.statistic.departureVehicleInfo.totalList = String(
        formData.statistic.departureVehicleInfo.departureVehicleNum,
      ).split("");
      // 空闲车数
      formData.statistic.unDepartureVehicleInfo.totalList = String(
        formData.statistic.unDepartureVehicleInfo.unDepartureVehicleNum,
      ).split("");
      state.formData = formData;
    },
    // 处理formData/statistic数据
    setStatisticData(state, data) {
      // 司机
      data.departureDriverInfo.total =
        data.departureDriverInfo.departureDriverNum + data.departureDriverInfo.unDepartureDriverNum;
      data.departureDriverInfo.totalList = String(data.departureDriverInfo.total).split("");
      // 押运工
      data.departureEscortInfo.total =
        data.departureEscortInfo.departureEscortNum + data.departureEscortInfo.unDepartureScortNum;
      data.departureEscortInfo.totalList = String(data.departureEscortInfo.total).split("");
      // 出车数
      data.departureVehicleInfo.totalList = String(data.departureVehicleInfo.departureVehicleNum).split("");
      // 空闲车数
      data.unDepartureVehicleInfo.totalList = String(data.unDepartureVehicleInfo.unDepartureVehicleNum).split("");
      state.formData.statistic = data;
    },
    // 处理formData其他数据
    setFormOtherData(state, obj) {
      state.formData[obj.key] = obj.data;
    },
    // 处理车辆信息
    setPlateNumber(state, plateNumber) {
      state.plateNumber = plateNumber;
    },
    // 处理收运量信息
    setRubbishTotal(state, value) {
      let numList = value.split(".");
      state.integerList = numList[0].split("").map((item) => {
        return {
          id: nanoid(),
          num: item,
        };
      });
      state.decimalsList = numList[1].split("").map((item) => {
        return {
          id: nanoid(),
          num: item,
        };
      });
      if (state.integerList.length < 7) {
        let zeroNum = 7 - state.integerList.length;
        for (let i = 0; i < zeroNum; i++) {
          state.integerList.unshift({
            id: nanoid(),
            num: "0",
          });
        }
      }
    },
    // 处理收运单列表
    setWaybillList(state, list) {
      state.waybillList = list;
    },
  },
  actions: {
    pushCancel({ commit }, cancel) {
      commit("PUSH_CANCEL", cancel);
    },
    clearCancel({ commit }) {
      commit("CLEAR_CANCEL");
    },
    async refreshAuthToken({ commit, state, dispatch }) {
      if (state.isRefreshingToken) {
        // 如果已经在刷新，则等待刷新完成
        return new Promise((resolve) => {
          state.pendingRequests.push(resolve);
        });
      }
      commit("setIsRefreshingToken", true);

      // 获取refreshToken，如果不存在则直接登出
      let refreshToken = getLocalStorage(LoginInfoKey)?.refreshToken;
      if (!refreshToken) {
        dispatch("clearCancel");
        window.LOGAN.logout();
        return false;
      }

      try {
        const { data: res } = await axios.post(`${process.env.VUE_APP_BASE_API_URL}/sctmpbase/api/oauth/token`, {
          client_id: "logansoft_hpis_id",
          client_secret: "logansoft_hpis_secret",
          grant_type: "refresh_token",
          scope: "scope",
          refresh_token: refreshToken,
          dna: window.LOGAN.$DNACONFIG.dna,
        });
        if (res.status == 200) {
          // 刷新token成功，更新存储
          setLocalStorage(LoginInfoKey, res.data);
          window.localStorage.setItem(TOKEN_KEY, "Bearer " + res.data.accessToken);
          let expiredTime = new Date().getTime() + res.data.surplusTime * 1000;
          setLocalStorage(TOKEN_EXPIRED_TIME_KEY, expiredTime);

          // 执行所有等待的请求
          state.pendingRequests.forEach((resolve) => resolve(res.data.accessToken));
          commit("clearPendingRequests");

          return res.data.accessToken;
        }

        // 刷新token失败，清除所有请求并登出
        dispatch("clearCancel");
        window.ELEMENT.Message({
          message: res.errmsg || res.message,
          type: "warning",
        });
        window.LOGAN.logout();
        return false;
      } catch (error) {
        // 取消所有等待的请求
        state.pendingRequests.forEach((resolve) => resolve(false));
        commit("clearPendingRequests");

        // 刷新token出错，清除所有请求并登出
        dispatch("clearCancel");
        window.ELEMENT.Message({
          message: "刷新令牌失败",
          type: "warning",
        });
        window.LOGAN.logout();
        return false;
      } finally {
        commit("setIsRefreshingToken", false);
      }
    },
    // 实时监控大屏
    async getFormData({ commit }, payload) {
      try {
        let res = await Promise.allSettled([
          getInfoApiFunByParams({ channelId: payload }, "/api/monitor/abnormalInfo"),
          getInfoApiFunByParams({ channelId: payload }, "/api/monitor/waybillInfo"),
          getInfoApiFunByParams({ channelId: payload }, "/api/monitor/collectInfo"),
          getInfoApiFunByParams({ channelId: payload }, "/api/monitor/statistic"),
          getInfoApiFunByParams({ channelId: payload }, "/api/monitor/collect/summary"),
        ]);
        let formData = {};
        if (res[0].status === "fulfilled") {
          formData.abnormalInfo = res[0].value.data;
        }
        if (res[1].status === "fulfilled") {
          formData.waybillInfo = res[1].value.data;
        }
        if (res[2].status === "fulfilled") {
          formData.collectInfo = res[2].value.data;
        }
        if (res[3].status === "fulfilled") {
          formData.statistic = res[3].value.data;
        }
        if (res[4].status === "fulfilled") {
          formData.rubbishTotal = res[4].value.data;
        }
        commit("setFormData", formData);
        commit("setRubbishTotal", formData.rubbishTotal);
      } catch (error) {
        console.log(error);
      }
    },
    async getWaybillList({ commit }, payload) {
      try {
        let res = await createApiFun(
          { keyword: payload.keyword, channelId: payload.channelId },
          "/api/monitor/waybill/list",
        );
        if (res.success) {
          commit("setWaybillList", res.data);
        }
      } catch (error) {
        console.log(error);
      }
    },
  },
});
