<template>
  <div class="micro-app-sctmp_base report-container" v-loading="loading" :key="refreshKey">
    <div class="left-channel" v-if="!channel">
      <el-radio-group v-model="channelId" size="medium" @change="toggleChannel">
        <el-radio-button :label="label.id" v-for="label in channelList" :key="label.id">{{
          label.name
        }}</el-radio-button>
      </el-radio-group>
    </div>
    <template v-if="form">
      <div class="container-top">
        <div class="flex-center mb-10">
          <h2>时间：{{ moment().format("YYYY年MM月DD日") }}</h2>
        </div>
        <div class="top-top">
          <div class="top-top-left">
            <div class="report-title">当日收运路线信息</div>
            <div class="top-flex">
              <div class="top-chart">
                <chart
                  centerTitle="路线总数"
                  chartId="chart1"
                  :total="form.routeInfo[0].total"
                  :dataList="[
                    { value: form.routeInfo[0].completeNum || 0, name: '已收路线', itemStyle: { color: '#546FC6' } },
                    { value: form.routeInfo[0].unCompleteNum || 0, name: '待收路线', itemStyle: { color: '#74C1DF' } },
                  ]"
                ></chart>
              </div>
              <div class="top-card">
                <card
                  :cardList="[
                    {
                      title: '已完成收运路线数量',
                      count: form.routeInfo[0].completeNum || 0,
                    },
                    {
                      title: '未完成收运路线数量',
                      count: form.routeInfo[0].unCompleteNum || 0,
                    },
                    {
                      title: '当日路线收运率',
                      count: form.routeInfo[0].completeRate || 0,
                      unit: '%',
                    },
                  ]"
                  @itemClick="routeClick"
                ></card>
              </div>
            </div>
          </div>
          <div class="top-top-right">
            <div class="report-title">当日收运点位信息</div>
            <div class="top-flex">
              <div class="top-chart">
                <chart
                  centerTitle="点位总数"
                  chartId="chart2"
                  :total="form.pointInfo[0].total"
                  :dataList="[
                    { value: form.pointInfo[0].collectNum || 0, name: '已收点位', itemStyle: { color: '#546FC6' } },
                    { value: form.pointInfo[0].unCollectNum || 0, name: '待收点位', itemStyle: { color: '#74C1DF' } },
                  ]"
                ></chart>
              </div>
              <div class="top-card">
                <card
                  :cardList="[
                    {
                      title: '已完成收运点位数量',
                      count: form.pointInfo[0].collectNum || 0,
                    },
                    {
                      title: '待收运点位数量',
                      count: form.pointInfo[0].unCollectNum || 0,
                    },
                    {
                      title: '当日点位总收运率',
                      count: form.pointInfo[0].completeRate || 0,
                      unit: '%',
                    },
                  ]"
                  @itemClick="pointClick"
                ></card>
              </div>
            </div>
          </div>
        </div>
        <div class="top-bottom" v-show="showMore">
          <div class="top-bottom-left">
            <div class="report-title">收运重量信息</div>
            <div class="weight-box">
              <div class="weight-chart">
                <weightChart chartId="chart10" :total="wasteTotal" :dataList="wasteDataList"></weightChart>
              </div>
              <div class="weight-card">
                <ul class="weight-list">
                  <li
                    class="weight-item"
                    :style="{ backgroundColor: item.bgColor }"
                    v-for="(item, index) in weightCardList"
                    :key="index"
                  >
                    <div class="weight-item-top">
                      <div class="weight-item-square" :style="{ backgroundColor: item.squareColor }"></div>
                      <div class="weight-item-title">{{ item.title }}</div>
                    </div>
                    <div class="weight-item-bottom">{{ item.waste }}&nbsp;kg</div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="top-bottom-right">
            <div class="report-title">当日人员车辆信息</div>
            <ul class="left-grid">
              <li class="left-grid-item" @click="openVehicleDialog">
                <div class="grid-item-chart">
                  <chart2
                    centerTitle="车辆总数"
                    chartId="chart3"
                    :total="form.vehicleInfo[0].total"
                    :dataList="[
                      {
                        value: form.vehicleInfo[0].departureNum || 0,
                        name: '当日收运车辆',
                        itemStyle: { color: '#546FC6' },
                      },
                      {
                        value: form.vehicleInfo[0].unDepartureNum || 0,
                        name: '当日空闲车辆',
                        itemStyle: { color: '#74C1DF' },
                      },
                    ]"
                  ></chart2>
                </div>
              </li>
              <li class="left-grid-item" @click="openDriverShipDialog(3)">
                <div class="grid-item-chart">
                  <chart2
                    centerTitle="司机总数"
                    chartId="chart4"
                    :total="form.driverInfo[0].total"
                    :dataList="[
                      {
                        value: form.driverInfo[0].departureNum || 0,
                        name: '当日收运司机',
                        itemStyle: { color: '#546FC6' },
                      },
                      {
                        value: form.driverInfo[0].unDepartureNum || 0,
                        name: '当日空闲司机',
                        itemStyle: { color: '#74C1DF' },
                      },
                    ]"
                  ></chart2>
                </div>
              </li>
              <li class="left-grid-item" @click="openDriverShipDialog(4)">
                <div class="grid-item-chart">
                  <chart2
                    centerTitle="押运工总数"
                    chartId="chart5"
                    :total="form.scortInfo[0].total"
                    :dataList="[
                      {
                        value: form.scortInfo[0].departureNum || 0,
                        name: '当日收运押运工',
                        itemStyle: { color: '#546FC6' },
                      },
                      {
                        value: form.scortInfo[0].unDepartureNum || 0,
                        name: '当日空闲押运工',
                        itemStyle: { color: '#74C1DF' },
                      },
                    ]"
                  ></chart2>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div class="top-top mt-16" v-show="showMore">
          <div class="top-top-left">
            <div class="report-title">异常信息</div>
            <div class="top-flex">
              <div class="top-chart">
                <chart2
                  centerTitle="异常总数"
                  chartId="chart6"
                  :radius="[60, 80]"
                  :total="form.abnormalInfo[0].platformNum + form.abnormalInfo[0].thirdPartyNum"
                  :dataList="[
                    {
                      value: form.abnormalInfo[0].platformNum || 0,
                      name: '收运平台异常上报数量',
                      itemStyle: { color: '#7372E9' },
                    },
                    {
                      value: form.abnormalInfo[0].thirdPartyNum || 0,
                      name: '两客一危一重异常上报数量',
                      itemStyle: { color: '#A7A6FE' },
                    },
                  ]"
                ></chart2>
              </div>
              <div class="top-card">
                <card
                  :cardList="[
                    {
                      title: '收运平台异常上报数量',
                      count: form.abnormalInfo[0].platformNum || 0,
                    },
                    {
                      title: '两客一危一重异常上报数量',
                      count: form.abnormalInfo[0].thirdPartyNum || 0,
                    },
                  ]"
                  @itemClick="abnormalClick"
                ></card>
              </div>
            </div>
          </div>
          <div class="top-top-right">
            <div class="report-title">调度情况</div>
            <div class="top-flex">
              <div class="top-chart">
                <chart2
                  centerTitle="调度总数"
                  chartId="chart7"
                  :radius="[60, 80]"
                  :total="form.dispatchInfo.total"
                  :dataList="[
                    {
                      value: form.dispatchInfo.tempNum || 0,
                      name: '临时调整收运单数量',
                      itemStyle: { color: '#546FC6' },
                    },
                    {
                      value: form.dispatchInfo.newCollectNum || 0,
                      name: '新发起收运任务数量',
                      itemStyle: { color: '#74C1DF' },
                    },
                  ]"
                ></chart2>
              </div>
              <div class="top-card">
                <card
                  :cardList="[
                    {
                      title: '临时调整收运单数量',
                      count: form.dispatchInfo.tempNum || 0,
                    },
                    {
                      title: '新发起收运任务数量',
                      count: form.dispatchInfo.newCollectNum || 0,
                    },
                  ]"
                  @itemClick="openWaybillDialog"
                ></card>
              </div>
            </div>
          </div>
        </div>
        <div class="more-box" @click="showMore = !showMore">
          <div class="more-title">{{ showMore ? "折叠图表" : "展开图表" }}</div>
          <span class="more-icon" :class="[showMore ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
        </div>
      </div>
      <div class="container-bottom">
        <div class="table-title">收运任务明细</div>
        <el-table class="table-list" :data="form.waybillInfo" :header-cell-style="{ background: '#F5F7F9' }" border>
          <el-table-column prop="name" label="路线名称"></el-table-column>
          <el-table-column prop="effectiveDate" label="日期"></el-table-column>
          <el-table-column prop="plateNumber" label="收运车辆"></el-table-column>
          <el-table-column prop="driverName" label="收运司机"></el-table-column>
          <el-table-column prop="cargoOneName" label="押运员1"></el-table-column>
          <el-table-column prop="cargoTwoName" label="押运员2"></el-table-column>
          <el-table-column prop="total" label="应收点位"></el-table-column>
          <el-table-column prop="collectNum" label="已收点位"></el-table-column>
          <el-table-column prop="unCollectNum" label="未收点位"></el-table-column>
          <el-table-column label="操作" align="right">
            <template #default="{ row }">
              <el-link type="primary" @click="openPointRecordDialog(row)">详情</el-link>
            </template>
          </el-table-column>
        </el-table>
        <div class="table-title">异常情况明细</div>
        <el-table :data="form.abnormalDetailInfo" :header-cell-style="{ background: '#F5F7F9' }" border>
          <el-table-column prop="date" label="日期"></el-table-column>
          <el-table-column prop="plateNumber" label="车辆"></el-table-column>
          <el-table-column prop="driverName" label="收运司机"></el-table-column>
          <el-table-column prop="cargoOneName" label="收运押运"></el-table-column>
          <el-table-column prop="catgoTwoName" label="收运押运2"></el-table-column>
          <el-table-column prop="abnormalTotal" label="异常总数"></el-table-column>
          <el-table-column prop="personAbnormalTotal" label="人员异常总数"></el-table-column>
          <el-table-column prop="activeReportAbnormalTotal" label="主动上报异常数量"></el-table-column>
          <el-table-column label="操作" align="right">
            <template #default="{ row }">
              <el-link type="primary" @click="checkRecord(row)">详情</el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </template>
    <driverShipDialog
      :value.sync="showDriverShipDialog"
      :dialogType="driverShipDialogType"
      :channelId="channelId"
    ></driverShipDialog>
    <vehicleDialog :value.sync="showVehicleDialog" :channelId="channelId"></vehicleDialog>
    <pointDialog :value.sync="showPointDialog" :waybillStatus="pointType" :channelId="channelId"></pointDialog>
    <exceptReportDialog :value.sync="showExceptReportDialog" :channelId="channelId"></exceptReportDialog>
    <pointRecordDialog
      :value.sync="showPointRecordDialog"
      :recordId="pointRecordId"
      :recordForm="pointRecordForm"
    ></pointRecordDialog>
    <waybillDialog :value.sync="showWaybillDialog" :waybillType="waybillType" :channelId="channelId"></waybillDialog>
    <routeDialog :value.sync="showRouteDialog" :completed="completed" :channelId="channelId"></routeDialog>
    <dangerDialog :value.sync="showDangerDialog" :channelId="channelId"></dangerDialog>
    <exceptDetail
      :value.sync="showExceptDetailDialog"
      :plateNumber="exceptDetailPlateNumber"
      :reportId="reportId"
    ></exceptDetail>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFun } from "@/api/base";
  import driverShipDialog from "./components/driverShipDialog";
  import vehicleDialog from "./components/vehicleDialog";
  import pointDialog from "./components/pointDialog";
  import exceptReportDialog from "./components/exceptReport/dialog.vue";
  import pointRecordDialog from "./components/pointRecordDialog";
  import moment from "moment";
  import card from "./components/card.vue";
  import chart from "./components/chart.vue";
  import chart2 from "./components/chart2.vue";
  import waybillDialog from "./components/waybillDialog";
  import routeDialog from "./components/routeDialog";
  import dangerDialog from "./components/dangerDialog";
  import exceptDetail from "./components/exceptDetail";
  import weightChart from "./components/weightChart.vue";
  import { getLocalStorage } from "@/utils/storage";
  import { INTERVIEW_CHANNEL } from "@/utils/commonValue"; //访问渠道
  export default {
    components: {
      driverShipDialog,
      vehicleDialog,
      pointDialog,
      exceptReportDialog,
      pointRecordDialog,
      card,
      chart,
      chart2,
      waybillDialog,
      routeDialog,
      dangerDialog,
      exceptDetail,
      weightChart,
    },
    data() {
      return {
        refreshKey: 0,
        apis: {
          info: "/api/logisticsReport/statistics/info",
          channelList: "/api/base/dna/listByDna/",
        },
        loading: false,
        form: "",
        tableData: [],
        driverShipDialogType: 3,
        showDriverShipDialog: false,
        showVehicleDialog: false,
        pointType: 0,
        showPointDialog: false,
        showExceptReportDialog: false,
        pointRecordId: "",
        pointRecordForm: {},
        showPointRecordDialog: false,
        showMore: true,
        showWaybillDialog: false,
        waybillType: 1,
        showRouteDialog: false,
        completed: true,
        showDangerDialog: false,
        showExceptDetailDialog: false,
        exceptDetailPlateNumber: "",
        reportId: "",
        weightCardList: [
          {
            title: "感染性废物",
            waste: 0,
            squareColor: "#1A9CFF",
            bgColor: "#E8F5FF",
          },
          {
            title: "感染性-污泥重量",
            waste: 0,
            squareColor: "#FFA440",
            bgColor: "#FFF4E7",
          },
          {
            title: "药物性废物",
            waste: 0,
            squareColor: "#33D1C9",
            bgColor: "#ECFFFE",
          },
          {
            title: "病理性废物",
            waste: 0,
            squareColor: "#5CC78A",
            bgColor: "#ECFFF4",
          },
          {
            title: "化学性废物",
            waste: 0,
            squareColor: "#7372E9",
            bgColor: "#F2F1FF",
          },
          {
            title: "损伤性废物",
            waste: 0,
            squareColor: "#FABB28",
            bgColor: "#FFF4D9",
          },
        ],
        wasteDataList: [],
        wasteTotal: 0,
        timer: null,
        moment,
        channelId: "",
        channelList: [],
        channel: "",
      };
    },
    async created() {
      this.channel = getLocalStorage(INTERVIEW_CHANNEL);
      if (!this.channel) {
        await this.getChannelList();
      } else {
        this.channelId = this.channel;
      }
      await this.getRecord();
    },
    mounted() {
      this.$nextTick(() => {
        window.addEventListener("resize", this.handleResize);
      });
    },
    methods: {
      // 获取渠道列表
      async getChannelList() {
        let res = await getInfoApiFun(this.$DNACONFIG.dna, this.apis.channelList);
        this.channelList = this.channelList.concat(res.data);
        this.channelId = this.channelList[0].id;
      },
      // 监听窗口变化
      handleResize() {
        if (this.timer !== null) {
          clearTimeout(this.timer);
        }
        this.timer = setTimeout(() => {
          this.refreshKey++;
        }, 500);
      },
      // 切换渠道
      toggleChannel() {
        this.form = null;
        this.getRecord();
      },
      // 获取详情
      async getRecord() {
        this.loading = true;
        try {
          let res = await createApiFun(
            {
              beginStatisticsDate: moment().format("YYYY-MM-DD"),
              endStatisticsDate: moment().format("YYYY-MM-DD"),
              channelId: this.channelId || this.channel,
            },
            this.apis.info,
          );
          if (res.success) {
            this.form = res.data;
            this.weightCardList[0].waste = res.data.rublishSituation.infectiousWaste;
            this.weightCardList[1].waste = res.data.rublishSituation.sludge;
            this.weightCardList[2].waste = res.data.rublishSituation.pharmaceuticalWaste;
            this.weightCardList[3].waste = res.data.rublishSituation.pathologicalWaste;
            this.weightCardList[4].waste = res.data.rublishSituation.chemicalWaste;
            this.weightCardList[5].waste = res.data.rublishSituation.damaginWaste;
            this.wasteTotal = res.data.rublishSituation.total;
            this.wasteDataList = [
              { value: res.data.rublishSituation.infectiousWaste, name: "感染性废物", itemStyle: { color: "#1A9CFF" } },
              { value: res.data.rublishSituation.sludge, name: "感染性-污泥重量", itemStyle: { color: "#FFA440" } },
              {
                value: res.data.rublishSituation.pharmaceuticalWaste,
                name: "药物性废物",
                itemStyle: { color: "#33D1C9" },
              },
              {
                value: res.data.rublishSituation.pathologicalWaste,
                name: "病理性废物",
                itemStyle: { color: "#5CC78A" },
              },
              { value: res.data.rublishSituation.chemicalWaste, name: "化学性废物", itemStyle: { color: "#7372E9" } },
              { value: res.data.rublishSituation.damaginWaste, name: "损伤性废物", itemStyle: { color: "#FABB28" } },
            ];
          }
          this.loading = false;
        } catch (error) {
          console.log(error);
          this.loading = false;
        }
      },
      // 查看详情
      checkRecord(row) {
        this.exceptDetailPlateNumber = row.plateNumber;
        this.reportId = row.reportId;
        this.showExceptDetailDialog = true;
      },
      // 路线点击事件回调
      routeClick(index) {
        this.completed = index === 1 ? false : true;
        this.showRouteDialog = true;
      },
      // 点位点击事件回调
      pointClick(index) {
        switch (index) {
          case 0:
            this.pointType = 1;
            break;
          case 1:
            this.pointType = 0;
            break;
        }
        this.showPointDialog = true;
      },
      // 异常信息点击事件回调
      abnormalClick(index) {
        switch (index) {
          case 0:
            this.showExceptReportDialog = true;
            break;
          case 1:
            this.showDangerDialog = true;
            break;
        }
      },
      // 打开司机押运工弹窗
      openDriverShipDialog(type) {
        this.driverShipDialogType = type;
        this.showDriverShipDialog = true;
      },
      // 打开车辆弹窗
      openVehicleDialog() {
        this.showVehicleDialog = true;
      },
      // 打开点位任务详情弹窗
      openPointRecordDialog(row) {
        this.pointRecordId = row.id;
        this.pointRecordForm = row;
        this.showPointRecordDialog = true;
      },
      // 打开收运单弹窗
      openWaybillDialog(type) {
        this.waybillType = type;
        this.showWaybillDialog = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .report-container {
    height: 100%;
    padding: 18px 16px;
    background-color: #f4f5f4;
    overflow: auto;
    &::-webkit-scrollbar {
      width: 5px;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      -webkit-box-shadow: inset 0 0 10px rgba(85, 122, 191, 0.1);
      background: #cccccc;
    }
  }
  .container-top {
    background-color: #fff;
    padding: 16px;
    border-radius: 6px;
  }
  .container-bottom {
    margin-top: 16px;
    background-color: #fff;
    padding: 16px;
    border-radius: 6px;
  }
  .report-title {
    font-weight: 400;
    font-size: 16px;
    color: #000000;
    line-height: 20px;
  }
  .top-top {
    display: flex;
    .top-top-left,
    .top-top-right {
      flex: 1;
      overflow: hidden;
      min-height: 310px;
      display: flex;
      flex-direction: column;
    }
    .top-top-right {
      margin-left: 16px;
    }
  }
  .top-flex {
    margin-top: 24px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    overflow: hidden;
  }
  .top-chart {
    width: 44%;
    height: 100%;
  }
  .top-card {
    margin-left: 28px;
  }
  .top-bottom {
    display: flex;
    margin-top: 24px;
    .top-bottom-left,
    .top-bottom-right {
      flex: 1;
      overflow: hidden;
      padding: 16px;
    }
    .weight-box {
      display: flex;
      align-items: center;
      .weight-chart {
        width: 46%;
      }
    }
  }
  .top-bottom-bottom {
    margin-top: 24px;
    display: flex;
    .bottom-middle {
      flex: 406;
      padding: 16px;
      background-color: #f7faf9;
      border-radius: 3px;
      display: flex;
      flex-direction: column;
      .middle-box {
        margin-top: 24px;
        min-height: 240px;
        flex: 1;
        overflow: hidden;
        display: flex;
        align-items: center;
        .box-left {
          height: 100%;
          flex: 1;
          overflow: hidden;
        }
      }
    }
  }
  .left-grid {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    .left-grid-item {
      cursor: pointer;
      min-height: 290px;
      .grid-item-chart {
        height: 100%;
      }
    }
  }
  .right-item {
    cursor: pointer;
    &:first-child {
      margin-bottom: 24px;
    }
    .right-title {
      font-weight: 400;
      font-size: 14px;
      color: #5c6663;
      line-height: 16px;
    }
    .right-count {
      font-weight: 500;
      font-size: 20px;
      color: #1d2925;
      line-height: 23px;
      margin-top: 4px;
    }
  }
  .table-title {
    font-weight: 400;
    font-size: 18px;
    color: #000000;
    line-height: 20px;
    padding-left: 12px;
    margin-bottom: 12px;
  }
  .table-list {
    margin-bottom: 16px;
  }
  .mr-16 {
    margin-right: 16px;
  }
  .more-box {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 12px;
    cursor: pointer;
    .more-title {
      font-weight: 400;
      font-size: 12px;
      color: #4ca786;
      line-height: 14px;
      margin-right: 4px;
      margin-top: 2px;
    }
    .more-icon {
      color: #4ca786;
      font-size: 16px;
    }
  }
  .weight-list {
    display: grid;
    grid-gap: 12px;
    grid-template-columns: repeat(3, 1fr);
    .weight-item {
      padding: 12px 12px 15px 6px;
      .weight-item-top {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        .weight-item-square {
          width: 6px;
          height: 6px;
        }
        .weight-item-title {
          font-weight: 400;
          font-size: 12px;
          color: #414d5a;
          line-height: 14px;
          padding-left: 6px;
        }
      }
      .weight-item-bottom {
        padding-left: 12px;
        font-weight: 400;
        font-size: 14px;
        color: #414d5a;
        line-height: 12px;
      }
    }
  }
  @media screen and (max-width: 1794px) {
    .weight-list {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  .left-channel {
    background-color: #fff;
    padding: 10px;
  }
</style>
