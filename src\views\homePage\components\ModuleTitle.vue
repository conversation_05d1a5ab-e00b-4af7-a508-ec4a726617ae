<template>
  <div class="ModuleTitle">
    <img class="svgUrl" :src="url" alt="" />
    <span>{{ name }}</span>
  </div>
</template>
<script>
  export default {
    components: {},
    props: ["name", "url"],
    data() {
      return {};
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {},
    watch: {},
  };
</script>
<style scoped lang="scss">
  .ModuleTitle {
    line-height: 24px;
    font-size: 18px;
    align-items: center;
    display: flex;
    align-items: center;
    margin-top: 16px;
    margin-left: 16px;
    font-weight: bold;
    color: #1d2925;
  }
  .svgUrl {
    margin-right: 7px;
    width: 24px;
  }
</style>
