<template>
  <div class="main-record micro-app-sctmp_base">
    <div class="record-content">
      <div class="card-header">合同信息</div>
      <baseTitle title="合同信息"></baseTitle>
      <el-descriptions
        class="card-description"
        :labelStyle="labelStyle"
        :contentStyle="contentStyle"
        :column="2"
        border
      >
        <el-descriptions-item label="合同流水号">
          <span class="desc-value">{{ ruleForm.serialNumber }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="合同编号">
          <span class="desc-value">{{ ruleForm.code }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="合同名称">
          <span class="desc-value">{{ ruleForm.name }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="合同类型">
          <span class="desc-value">{{ ruleForm.typeName }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="生效日期">
          <span class="desc-value">{{ ruleForm.effectiveDate }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="失效日期">
          <span class="desc-value">{{ ruleForm.expiryDate }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="计费期数">
          <span class="desc-value">{{ ruleForm.billingPeriod }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="计费周期">
          <span class="desc-value">{{ ruleForm.chargingCycle }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="计费方式">
          <span class="desc-value">{{ ruleForm.chargeMode }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="价格方案">
          <span class="desc-value">{{ ruleForm.pricingScheme }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="每日排放量（Kg）">
          <span class="desc-value">{{ ruleForm.dailyEmissions }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="单价">
          <span class="desc-value">{{ ruleForm.price }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="计数数量（Kg）">
          <span class="desc-value">{{ ruleForm.countQuantity }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="每月排放量（kg）">
          <span class="desc-value">{{ ruleForm.monthEmissions }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="每月金额（元）" :span="2">
          <span class="desc-value">{{ ruleForm.monthPrice }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          <span class="desc-value">{{ ruleForm.remark }}</span>
        </el-descriptions-item>
      </el-descriptions>
      <baseTitle title="客商信息"></baseTitle>
      <el-descriptions
        class="card-description"
        :labelStyle="labelStyle"
        :contentStyle="contentStyle"
        :column="2"
        border
      >
        <el-descriptions-item label="客商名称">
          <span class="desc-value">{{ merchantInfo.name }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="客商地址">
          <span class="desc-value">{{ merchantInfo.address }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="法人代表">
          <span class="desc-value">{{ merchantInfo.corporateName }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="联系人">
          <span class="desc-value">{{ merchantInfo.contact }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="联系方式">
          <span class="desc-value">{{ merchantInfo.contactPhone }}</span>
        </el-descriptions-item>
      </el-descriptions>
      <baseTitle title="收费清单"></baseTitle>
      <el-table
        :data="tableData"
        :header-cell-style="{ background: '#F5F7F9' }"
        style="width: 100%"
        border
        show-summary
        :summary-method="getSummaries"
      >
        <el-table-column prop="chargeType" label="收费类型" align="center"></el-table-column>
        <el-table-column prop="year" label="年度" align="center"></el-table-column>
        <el-table-column prop="month" label="月份" align="center"></el-table-column>
        <el-table-column prop="countQuantity" label="计数数量" align="center"></el-table-column>
        <el-table-column prop="monthEmissions" label="每月排放量（KG）" align="center"></el-table-column>
        <el-table-column prop="monthPrice" label="每月金额（元）" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" align="center"></el-table-column>
        <el-table-column prop="isStop" label="是否停运" align="center">
          <template #default="{ row }">{{ row.isStop == 1 ? "是" : "否" }}</template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun, getListPageApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      baseTitle,
    },
    data() {
      return {
        ruleForm: {},
        apis: {
          info: "/api/contract/contractinfo/get/",
          inventoryList: "/api/contract/contractinventory/list",
          merchantInfo: "/api/merchant/merchantFile/get/",
        },
        labelStyle: {
          width: "200px",
          height: "48px",
          fontSize: "14px",
          fontWeight: 400,
          color: "#122131",
          lineHeight: "20px",
        },
        contentStyle: {
          fontSize: "14px",
          fontWeight: 400,
          lineHeight: "20px",
        },
        tableData: [],
        merchantInfo: {},
      };
    },
    mounted() {},
    methods: {
      // 获取用户详情
      async getRecord(id) {
        let res = await getInfoApiFun(id, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          for (let key in this.ruleForm) {
            this.ruleForm[key] = this.ruleForm[key] || this.ruleForm[key] === 0 ? this.ruleForm[key] : "-";
          }
          this.getMerchantRecord(res.data.merchantFileId);
          this.getInventoryList(id);
        }
      },
      // 获取客商档案详情
      async getMerchantRecord(id) {
        let res = await getInfoApiFun(id, this.apis.merchantInfo);
        if (res.success) {
          this.merchantInfo = res.data;
          this.merchantInfo.contactPhone = this.merchantInfo.contactPhone
            ? this.$sm2Decrypt(this.merchantInfo.contactPhone)
            : this.merchantInfo.contactPhone;
          for (let key in this.merchantInfo) {
            this.merchantInfo[key] =
              this.merchantInfo[key] || this.merchantInfo[key] === 0 ? this.merchantInfo[key] : "-";
          }
        }
      },
      // 获取收费清单列表
      async getInventoryList(id) {
        let params = {
          contractInfoId: id,
        };
        let res = await getListPageApiFun(params, this.apis.inventoryList);
        if (res.success) {
          this.tableData = res.data;
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      getSummaries(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = "合计";
            return;
          }
          if (index === 1 || index === 2 || index === 7) {
            sums[index] = "";
            return;
          }
          const values = data.map((item) => Number(item[column.property]));
          if (!values.every((value) => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              }
              return prev;
            }, 0);
          } else {
            sums[index] = "";
          }
        });

        return sums;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .desc-value {
    color: #46535e;
  }
  .card-description {
    margin-bottom: 24px;
  }
</style>
