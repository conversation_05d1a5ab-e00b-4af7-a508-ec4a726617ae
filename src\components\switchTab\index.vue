<template>
  <div class="tab-list">
    <div
      class="tab-item"
      :class="{ active: activeId === item.id }"
      v-for="item in tabList"
      :key="item.id"
      @click="toggleTab(item)"
      >{{ item.name }}</div
    >
  </div>
</template>

<script>
  export default {
    props: {
      // tab列表
      tabList: {
        type: Array,
        default: () => [
          //   {
          //     id: 1,
          //     name: "tab1",
          //   },
          //   {
          //     id: 2,
          //     name: "tab2",
          //   },
          //   {
          //     id: 3,
          //     name: "tab3",
          //   },
        ],
      },

      // 默认激活的tab
      activeTab: {
        type: [String, Number],
        default: 1,
      },
    },

    watch: {
      activeTab: {
        handler(val) {
          this.activeId = val;
        },
        immediate: true,
      },
    },

    data() {
      return {
        activeId: "",
      };
    },

    created() {
      this.activeId = this.activeTab;
    },

    methods: {
      async toggleTab(item) {
        if (this.activeId === item.id) return;
        this.activeId = item.id;
        this.$emit("toggleTab", item);
      },
    },
  };
</script>

<style scoped lang="scss">
  .tab-list {
    margin: 20px 0;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    background-color: rgba(76, 167, 134, 0.3);
    border-radius: 20px;
    pointer-events: auto;
    .tab-item {
      text-align: center;
      color: #fff;
      padding: 10px 0;
      font-size: 14px;
      border-radius: 20px;
      cursor: pointer;
      &.active {
        font-size: 16px;
        font-weight: bold;
        background-color: var(--color-primary);
      }
    }
  }
</style>
