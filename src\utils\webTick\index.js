import workerTick from "./main.js";

function setupCounter(element) {
  let counter = 0;
  const setCounter = (count) => {
    counter = count;
    element.innerHTML = `count is ${counter}`;
  };
  element.addEventListener("click", () => setCounter(counter + 1));
  setCounter(0);

  // 👇 -------------- 👇
  window.setInterval(() => setCounter(counter + 1), 2000);
}

function setupCounter2(element) {
  let counter = 0;
  const setCounter = (count) => {
    counter = count;
    element.innerHTML = `count is ${counter}`;
  };
  element.addEventListener("click", () => setCounter(counter + 1));
  setCounter(0);

  // 👇 -------------- 👇
  workerTick.setInterval(() => setCounter(counter + 1), 2000);
}

const counter1 = document.querySelector("#counter");
const counter2 = document.querySelector("#counter2");

setupCounter(counter1);
setupCounter2(counter2);
