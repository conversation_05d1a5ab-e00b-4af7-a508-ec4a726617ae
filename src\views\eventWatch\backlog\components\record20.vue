<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">客商经营状态变更</div>
      <el-form label-width="180px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="上报时间" required>
              <el-date-picker
                :value="recordItem.createTime"
                type="datetime"
                placeholder="请选择上报时间"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="截止日期" required>
              <el-date-picker
                :value="recordItem.deadline"
                type="date"
                placeholder="请选择截止日期"
                clearable
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="基础信息"></baseTitle>
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="点位名称" required>
              <el-input
                :value="baseForm.name"
                placeholder="请输入点位名称"
                clearable
                maxlength="32"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="点位联系人电话" required>
              <el-input
                :value="baseForm.phone"
                placeholder="请输入点位联系人电话"
                clearable
                maxlength="11"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="联系人电子邮件" required>
              <el-input
                :value="baseForm.email"
                placeholder="请输入联系人电子邮件"
                clearable
                maxlength="11"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="省市区" required>
              <el-input
                :value="`${baseForm.province}/${baseForm.cityName}/${baseForm.districtName}`"
                placeholder="请输入省市区"
                clearable
                maxlength="32"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="详细地址" required>
              <el-input
                :value="baseForm.address"
                placeholder="请输入详细地址"
                clearable
                maxlength="32"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="开始日期" required>
              <el-date-picker
                :value="baseForm.startDate"
                type="date"
                placeholder="请选择开始日期"
                clearable
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="结束日期" required>
              <el-date-picker
                :value="baseForm.endDate"
                type="date"
                placeholder="请选择结束日期"
                clearable
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <baseTitle title="业务调整信息"></baseTitle>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="180px" label-suffix="：">
        <el-row>
          <el-col>
            <el-form-item label="经营状态" prop="status">
              <el-switch
                v-model="ruleForm.status"
                active-text="正常"
                inactive-text="暂停营业"
                active-color="#4CA786"
                :active-value="0"
                :inactive-value="1"
              >
              </el-switch>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
      <el-button type="primary" @click="saveRecordThrottling">完成</el-button>
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  export default {
    props: {
      recordItem: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      baseTitle,
    },
    data() {
      return {
        baseForm: {}, //业务基础信息
        ruleForm: {
          status: 0, //移除状态 0-否 1-移除
        },
        rules: {
          status: [{ required: true, message: "请选择移除状态", trigger: "change" }],
        },
        apis: {
          create: "/api/supplierAlter/handle",
          info: "/api/supplierAlter/get/",
        },
        saveRecordThrottling: () => {},
        loading: false,
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      if (this.recordItem && this.recordItem.businessId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordItem.businessId, this.apis.info);
        if (res.success) {
          this.baseForm = res.data;
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("returnIndex");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun({ ...this.ruleForm, id: this.recordItem.businessId }, this.apis.create);
              if (res.success) {
                this.$message.success(`操作成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-box {
    margin-bottom: 20px;
  }
</style>
