<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <record
        v-if="showRecord"
        :pageFlag="pageFlag"
        :recordId="recordId"
        @closeRecord="closeRecord"
        @refreshList="initData"
      ></record>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-form label-suffix=":" label-width="80px" class="mr-10" inline>
              <el-form-item label="异常类型">
                <el-select v-model="exceptionType" placeholder="请选择异常类型" clearable filterable>
                  <el-option
                    v-for="(item, index) in ERROR_STATUS"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="上报日期">
                <el-date-picker
                  v-model="reportingTime"
                  type="date"
                  placeholder="请选择上报日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-form>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
          ></InterviewChannel>
          <el-col :span="8">
            <el-form-item label="是否可以继续收运">
              <el-select
                v-model="filterForm.continueCarrying"
                placeholder="请选择是否可以继续收运"
                clearable
                filterable
              >
                <el-option
                  v-for="item in CONTINUECARRYING_STATUS"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column prop="exceptionType" label="异常类型" align="center">
              <template #default="{ row }">{{ ERROR_STATUS[row.exceptionType] }}</template>
            </el-table-column>
            <el-table-column prop="reportingTime" label="上报时间" align="center"></el-table-column>
            <el-table-column prop="reportPerson" label="上报人" align="center"></el-table-column>
            <el-table-column prop="continueCarrying" label="是否可以继续收运" align="center">
              <template #default="{ row }">{{ CONTINUECARRYING_STATUS_MAP[row.continueCarrying] }}</template>
            </el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="{ row }">
                <el-link type="primary" @click="checkRecord(row)">查看详情</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun } from "@/api/base";
  import record from "./components/record.vue";
  import { ERROR_STATUS, CONTINUECARRYING_STATUS, CONTINUECARRYING_STATUS_MAP } from "@/enums";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
    },
    data() {
      return {
        pageFlag: "list",
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/abnormalreporting/listPage",
        },
        showRecord: false,
        recordId: "",
        ERROR_STATUS,
        CONTINUECARRYING_STATUS,
        CONTINUECARRYING_STATUS_MAP,
        loading: false,
        showFilter: false,
        exceptionType: "",
        reportingTime: "",
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0 || arr === false).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        this.loading = true;
        try {
          let params = {
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            exceptionType: this.exceptionType,
            reportingTime: this.reportingTime,
            ...this.filterForm,
          };
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 详情
      checkRecord(row) {
        this.pageFlag = "check";
        this.showRecord = true;
        this.recordId = row.id;
      },
      closeRecord() {
        this.pageFlag = "list";
        this.showRecord = false;
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.exceptionType = "";
        this.reportingTime = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .header-left .el-form-item,
  .filter-content .el-form-item {
    margin-bottom: 0;
  }
</style>
