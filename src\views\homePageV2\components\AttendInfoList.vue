<template>
  <div class="backlog">
    <main-title title="今日收运车辆详情"></main-title>
    <vue-seamless-scroll
      v-if="listData && listData.length > 0"
      class="seaml-scroll"
      :data="listData"
      :class-option="seamlessScrollOption"
    >
      <ul class="data-list">
        <li class="data-item" v-for="(car, index) in listData" :key="index">
          <div class="routeName omit1">{{ car.routeName }}</div>
          <div class="plateNumber omit1">{{ car.plateNumber }}</div>
          <div class="driverName omit1">{{ car.driverName }}</div>
        </li>
      </ul>
    </vue-seamless-scroll>
    <div v-else class="EmptyWrapper">
      <Empty></Empty>
    </div>
  </div>
</template>

<script>
  import Empty from "./Empty.vue";

  import MainTitle from "./MainTitle.vue";
  export default {
    components: {
      MainTitle,
      Empty,
    },
    props: ["listData"],
    data() {
      return {
        seamlessScrollOption: {
          step: 2, // 数值越大速度滚动越快
          limitMoveNum: 6, // 开始无缝滚动的数据量 this.dataList.length
          hoverStop: true, // 是否开启鼠标悬停stop
          direction: 1, // 0向下 1向上 2向左 3向右
          openWatch: true, // 开启数据实时监控刷新dom
          singleHeight: 50, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
          singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
          waitTime: 2000, // 单步运动停止的时间(默认值1000ms)
        },
      };
    },
    async created() {},
    methods: {},
  };
</script>

<style lang="scss" scoped>
  .routeName {
    width: calc(132px + 74px);
    margin-right: 8px;
  }
  .plateNumber {
    width: calc(92px + 74px);
    margin-right: 8px;
  }
  .driverName {
    width: calc(480px - 100px - 148px - 140px);
    color: #dfbe16;
  }
  .EmptyWrapper {
    width: 480px;
    height: 235px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .omit1 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
  }
  .backlog {
    width: 480px;
    position: absolute;
    top: 732px;
    left: 40px;
    z-index: 1;
  }
  .seaml-scroll {
    height: 250px;
    overflow: hidden;
    width: 100%;
    font-size: 16px;
    line-height: 22px;
    font-weight: 400;
    color: #ffffff;
    margin-top: 20px;
    .data-list {
      .data-item {
        display: flex;
        padding: 9px 16px;
        margin-bottom: 10px;
        background: linear-gradient(270deg, rgba(93, 255, 203, 0) 0%, rgba(93, 255, 203, 0.1) 100%);
        .title {
          flex: 1;
          display: -webkit-box;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
        }
        .num {
          width: 80px;
          text-align: right;
        }
      }
    }
  }
</style>
