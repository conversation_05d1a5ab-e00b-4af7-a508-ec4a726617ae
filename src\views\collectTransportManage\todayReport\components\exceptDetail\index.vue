<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      width="80%"
      top="0"
      destroy-on-close
      :show-close="false"
      @open="initData"
    >
      <record :recordId="recordId" v-if="showRecord" @closeRecord="showRecord = false"></record>
      <div class="main-index" v-else>
        <main class="main">
          <baseTitle title="驾驶状态异常信息"></baseTitle>
          <div class="table-box">
            <el-table :data="drivingConditionList" :header-cell-style="{ background: '#F5F7F9' }" border>
              <el-table-column prop="plateNumber" label="车牌号" align="center"></el-table-column>
              <el-table-column prop="dateTime" label="上报时间" align="center"></el-table-column>
              <el-table-column prop="longitude" label="经纬度" align="center">
                <template #default="{ row }">
                  <el-link @click="checkLocation(row)">{{ row.longitude }} {{ row.latitude }}</el-link>
                </template>
              </el-table-column>
              <el-table-column prop="fatigueDegree" label="疲劳程度" align="center">
                <template #default="{ row }">{{ FATIGUE_DEGREE[row.fatigueDegree] }}</template>
              </el-table-column>
              <el-table-column prop="level" label="报警级别" align="center">
                <template #default="{ row }">{{ row.level }}级报警</template>
              </el-table-column>
              <el-table-column prop="speed" label="车速" align="center"></el-table-column>
              <el-table-column prop="type" label="异常类型" align="center">
                <template #default="{ row }">{{ row.type === 1 ? "主动上报" : "被动上报" }}</template>
              </el-table-column>
            </el-table>
          </div>
          <baseTitle title="主动上报异常信息" class="mt-20"></baseTitle>
          <div class="table-box">
            <el-table :data="activelyReportList" :header-cell-style="{ background: '#F5F7F9' }" border>
              <el-table-column prop="exceptionType" label="异常类型" align="center">
                <template #default="{ row }">{{ ERROR_STATUS[row.exceptionType] }}</template>
              </el-table-column>
              <el-table-column prop="reportingTime" label="上报时间" align="center"></el-table-column>
              <el-table-column prop="reportPerson" label="上报人" align="center"></el-table-column>
              <el-table-column prop="continueCarrying" label="是否可以继续收运" align="center">
                <template #default="{ row }">{{ CONTINUECARRYING_STATUS_MAP[row.continueCarrying] }}</template>
              </el-table-column>
              <el-table-column label="操作" align="center">
                <template #default="{ row }">
                  <el-link type="primary" @click="checkRecord(row)">查看详情</el-link>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </main>
      </div>
    </el-dialog>
    <pointRecord :value.sync="showPointDialog" :formItem="formItem"></pointRecord>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import moment from "moment";
  import baseTitle from "@/components/baseTitle";
  import { ERROR_STATUS, CONTINUECARRYING_STATUS_MAP, FATIGUE_DEGREE } from "@/enums";
  import record from "./record.vue";
  import pointRecord from "@/views/eventWatch/errorManager/components/pointRecord.vue";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      plateNumber: {
        type: String,
        default: "",
      },
      reportId: {
        type: String,
        default: "",
      },
    },
    components: {
      baseTitle,
      record,
      pointRecord,
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          info: "/api/logisticsReport/abnormal/info",
        },
        loading: false,
        drivingConditionList: [],
        activelyReportList: [],
        ERROR_STATUS,
        CONTINUECARRYING_STATUS_MAP,
        showRecord: false,
        recordId: "",
        showPointDialog: false,
        formItem: {},
        FATIGUE_DEGREE,
      };
    },
    methods: {
      async initData() {
        try {
          let res = await getListPageApiFun(
            {
              beginStatisticsDate: moment().format("YYYY-MM-DD"),
              endStatisticsDate: moment().format("YYYY-MM-DD"),
              plateNumber: this.plateNumber,
              reportId: this.reportId,
            },
            this.apis.info,
          );
          if (res.success) {
            this.drivingConditionList = res.data.driverAbnormalList;
            this.activelyReportList = res.data.activeReportAbnormalList;
          }
        } catch (error) {
          console.log(error);
        }
      },
      // 查看详情
      checkRecord(row) {
        this.recordId = row.id;
        this.showRecord = true;
      },
      // 查看定位
      checkLocation(row) {
        this.formItem = row;
        this.showPointDialog = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .table-box {
    margin-top: 20px;
  }
  .mt-20 {
    margin-top: 20px;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
