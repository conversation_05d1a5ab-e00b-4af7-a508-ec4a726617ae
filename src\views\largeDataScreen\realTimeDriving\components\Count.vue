<template>
  <div>
    <Title title="车辆今日收运量信息"></Title>
    <div class="count-container">
      <div class="count-box">
        <div class="count-value">{{ integerList[0]?.num }}</div>
      </div>
      <div class="count-comma">,</div>
      <div class="count-box" v-for="item in integerList?.slice(1, 4)" :key="item.id">
        <div class="count-value">{{ item.num }}</div>
      </div>
      <div class="count-comma">,</div>
      <div class="count-box" v-for="item in integerList?.slice(4, 7)" :key="item.id">
        <div class="count-value">{{ item.num }}</div>
      </div>
      <div class="count-comma">.</div>
      <div class="count-box" v-for="item in decimalsList" :key="item.id">
        <div class="count-value">{{ item.num }}</div>
      </div>
      <div class="count-unit">（kg）</div>
    </div>
  </div>
</template>

<script>
  import Title from "@/views/largeDataScreen/realTimeMonitor/components/Title";
  export default {
    components: {
      Title,
    },
    props: ["integerList", "decimalsList"],
    data() {
      return {};
    },
  };
</script>

<style lang="scss" scoped>
  .count-container {
    display: flex;
    align-items: flex-end;
    margin: 30px 0;
    cursor: pointer;
    pointer-events: auto;
  }
  .count-box {
    border: 1px solid #11e48a;
    background-color: rgba(17, 228, 138, 0.2);
    margin-right: 4px;
    border-radius: 4px;
    .count-value {
      font-size: 50px;
      font-weight: bold;
      color: #fff;
      padding: 0 4px;
      line-height: 54px;
    }
  }
  .count-comma {
    font-size: 30px;
    font-weight: bold;
    color: #dfdfdf;
    margin-right: 4px;
  }
  .count-unit {
    font-style: 20px;
    font-weight: bold;
    color: #fff;
  }
</style>
