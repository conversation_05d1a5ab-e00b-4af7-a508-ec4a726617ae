<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">发起收运</div>
      <baseTitle title="时效信息"></baseTitle>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="180px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="期望收运时间" prop="expectTime">
              <el-date-picker
                v-model="ruleForm.expectTime"
                type="datetime"
                placeholder="选择期望收运时间"
                value-format="yyyy-MM-dd HH:mm"
                format="yyyy-MM-dd HH:mm"
                :disabled="recordType == 2"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="发起人">
              <el-input
                :value="recordItem?.id ? recordItem.createFullname : userInfo.fullName"
                placeholder="请输入发起人"
                readonly
                :disabled="recordType == 2"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="车辆信息"></baseTitle>
        <el-row>
          <el-col>
            <el-form-item label="收运车辆" prop="defaultVehiclePlateNumber">
              <el-select
                v-model="ruleForm.defaultVehiclePlateNumber"
                placeholder="请选择收运车辆"
                clearable
                filterable
                @change="changePlateNumber"
                :disabled="recordType == 2"
              >
                <el-option
                  v-for="item in carOptions"
                  :key="item.id"
                  :label="item.plateNumber + (item.isFree === 1 ? '' : '（空闲）')"
                  :value="item.plateNumber"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="人员信息"></baseTitle>
        <el-row>
          <el-col :md="10" :lg="10">
            <el-form-item label="司机" prop="defaultDriverDossierId">
              <el-select
                v-model="ruleForm.defaultDriverDossierId"
                placeholder="请选择司机"
                clearable
                filterable
                @change="driverAndWorkerChange($event, 'defaultDriverDossierPhone', driverOptions)"
                :disabled="recordType == 2"
              >
                <el-option
                  v-for="item in driverOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14">
            <el-form-item label="" prop="defaultDriverDossierPhone">
              <el-input
                v-model="ruleForm.defaultDriverDossierPhone"
                placeholder="请输入司机联系方式"
                clearable
                :maxlength="11"
                show-word-limit
                :disabled="recordType == 2"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="10" :lg="10">
            <el-form-item label="押运工1" prop="supercargoDossierOneId">
              <el-select
                v-model="ruleForm.supercargoDossierOneId"
                placeholder="请选择押运工1"
                clearable
                filterable
                @change="driverAndWorkerChange($event, 'supercargoDossierOnePhone', shipWorkerOptions)"
                :disabled="recordType == 2"
              >
                <el-option
                  v-for="item in shipWorkerOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14">
            <el-form-item label="" prop="supercargoDossierOnePhone">
              <el-input
                v-model="ruleForm.supercargoDossierOnePhone"
                placeholder="请输入押运工联系方式"
                clearable
                :maxlength="11"
                show-word-limit
                :disabled="recordType == 2"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="10" :lg="10">
            <el-form-item label="押运工2" prop="supercargoDossierTwoId">
              <el-select
                v-model="ruleForm.supercargoDossierTwoId"
                placeholder="请选择押运工2"
                clearable
                filterable
                @change="driverAndWorkerChange($event, 'supercargoDossierTwoPhone', shipWorkerOptions)"
                :disabled="recordType == 2"
              >
                <el-option
                  v-for="item in shipWorkerOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14">
            <el-form-item label="" prop="supercargoDossierTwoPhone">
              <el-input
                v-model="ruleForm.supercargoDossierTwoPhone"
                placeholder="请输入押运工联系方式"
                clearable
                :maxlength="11"
                show-word-limit
                :disabled="recordType == 2"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="收运任务"></baseTitle>
        <el-form-item prop="sponsorWaybillsDetails" label-width="0">
          <el-table :data="ruleForm.sponsorWaybillsDetails" :header-cell-style="{ background: '#F5F7F9' }" border>
            <el-table-column label="点位收运任务" align="center">
              <el-table-column prop="code" label="点位编号" align="center"></el-table-column>
              <el-table-column label="产废单位名称" align="center">
                <template #default="{ row }">{{ row.name ? row.name : row.productionUnit }}</template>
              </el-table-column>
              <el-table-column label="产废单位经办人" align="center">
                <template #default="{ row }">{{ row.contact ? row.contact : row.productionUnitOperator }}</template>
              </el-table-column>
              <el-table-column prop="detailType" label="任务类型" align="center">
                <template #default="{ row, $index }">
                  <div v-if="recordType == 2">{{ POINT_TASK_TYPE[row.detailType] }}</div>
                  <el-form-item
                    :prop="`sponsorWaybillsDetails.${$index}.detailType`"
                    :rules="tableRules.detailType"
                    class="task-form-item"
                    v-else
                  >
                    <el-select
                      v-model="row.detailType"
                      placeholder="请选择任务类型"
                      clearable
                      filterable
                      class="full-width"
                    >
                      <el-option
                        v-for="(item, index) in POINT_TASK_TYPE"
                        :key="index"
                        :label="item"
                        :value="index"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column min-width="140" label="操作" align="center" v-if="recordType != 2">
                <template #default="{ row }">
                  <el-link type="danger" @click="deletePoint(row)">删除</el-link>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-button
          type="primary"
          class="mt-10 table-box"
          icon="el-icon-plus"
          size="small"
          @click="showPointList = true"
          v-if="recordType != 2"
          >增加收运点位</el-button
        >
        <baseTitle title="备注信息"></baseTitle>
        <el-input
          v-model="ruleForm.memo"
          placeholder="请输入备注信息"
          clearable
          type="textarea"
          rows="10"
          maxlength="500"
          show-word-limit
          :disabled="recordType == 2"
        ></el-input>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
      <el-button type="primary" @click="saveRecordThrottling" v-if="recordType != 2">完成</el-button>
    </div>
    <pointList
      :value.sync="showPointList"
      :pointArray="ruleForm.sponsorWaybillsDetails"
      @selectPoint="selectPoint"
      @deletePoint="deletePoint"
    ></pointList>
  </div>
</template>

<script>
  import { createApiFun, getListApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import { POINT_TASK_TYPE } from "@/enums";
  import pointList from "./pointList.vue";
  import { deepClone } from "logan-common/utils";
  export default {
    props: {
      recordItem: {
        type: Object,
        default: () => {},
      },
      recordType: {
        type: Number,
        default: 0,
      },
    },
    components: {
      baseTitle,
      pointList,
    },
    data() {
      return {
        ruleForm: {
          expectTime: "", //期望收运时间
          defaultVehiclePlateNumber: "", //收运车辆
          defaultDriverDossierId: "", //驾驶司机
          defaultDriverDossierPhone: "", //驾驶司机联系方式
          supercargoDossierOneId: "", //押运工1
          supercargoDossierOnePhone: "", //押运工1联系方式
          supercargoDossierTwoId: "", //押运工2
          supercargoDossierTwoPhone: "", //押运工2联系方式
          memo: "", //备注信息
          sponsorWaybillsDetails: [],
        },
        rules: {
          expectTime: [{ required: true, message: "请选择期望收运时间", trigger: "change" }],
          defaultVehiclePlateNumber: [{ required: true, message: "请选择收运车辆", trigger: "change" }],
          defaultDriverDossierId: [{ required: true, message: "请选择驾驶司机", trigger: "change" }],
          sponsorWaybillsDetails: [{ type: "array", required: true, message: "请添加点位收运任务", trigger: "change" }],
        },
        apis: {
          carList: "/api/vehicle/dossier/getFreeList",
          userList: "/api/baseuser/list",
          driverAndWorkerInfo: "/api/baseuser/getInfoByPlateNumberAndUserIdentity",
          create: "/api/waybill/sponsorWaybills",
          detailList: "/api/waybill/waybillDetail/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        carOptions: [],
        driverOptions: [],
        shipWorkerOptions: [],
        POINT_TASK_TYPE,
        showPointList: false,
        tableRules: {
          detailType: [{ required: true, message: "请选择任务类型", trigger: "change" }],
        },
        userInfo: {}, //用户信息
      };
    },
    async created() {
      this.userInfo = await window.LOGAN.getUserInfo();
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptions();
      if (this.recordItem?.id) {
        this.getDetailList();
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getListApiFun({ userIdentity: 4 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipWorkerOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },
      // 获取点位列表
      async getDetailList() {
        let res = await createApiFun({ waybillId: this.recordItem.id }, this.apis.detailList);
        if (res.success) {
          this.ruleForm = Object.assign({}, this.recordItem);
          this.ruleForm.defaultDriverDossierPhone = this.$sm2Decrypt(this.ruleForm.defaultDriverDossierPhone);
          this.ruleForm.supercargoDossierOnePhone = this.ruleForm.supercargoDossierOnePhone
            ? this.$sm2Decrypt(this.ruleForm.supercargoDossierOnePhone)
            : "";
          this.ruleForm.supercargoDossierTwoPhone = this.ruleForm.supercargoDossierTwoPhone
            ? this.$sm2Decrypt(this.ruleForm.supercargoDossierTwoPhone)
            : "";
          let sponsorWaybillsDetails = res.data.map((list) => {
            return { ...list, id: list.pickupPointId };
          });
          this.$set(this.ruleForm, "sponsorWaybillsDetails", sponsorWaybillsDetails);
        }
      },
      // 选择默认车牌号事件回调
      async changePlateNumber(value) {
        if (value) {
          let promiseList = [
            createApiFun({ userIdentity: "3", plateNumber: value }, this.apis.driverAndWorkerInfo),
            createApiFun({ userIdentity: "4", plateNumber: value }, this.apis.driverAndWorkerInfo),
          ];
          let res = await Promise.all(promiseList);
          let driverInfo = res[0].data;
          let workerInfo = res[1].data;
          if (driverInfo) {
            this.ruleForm.defaultDriverDossierId = driverInfo.lgUnionId;
            this.ruleForm.defaultDriverDossierPhone = this.$sm2Decrypt(driverInfo.phone);
          } else {
            this.ruleForm.defaultDriverDossierId = "";
            this.ruleForm.defaultDriverDossierPhone = "";
          }
          if (workerInfo) {
            this.ruleForm.supercargoDossierOneId = workerInfo.lgUnionId;
            this.ruleForm.supercargoDossierOnePhone = workerInfo.phone ? this.$sm2Decrypt(workerInfo.phone) : "";
          } else {
            this.ruleForm.supercargoDossierOneId = "";
            this.ruleForm.supercargoDossierOnePhone = "";
          }
        }
      },
      // 驾驶司机选择事件回调
      driverAndWorkerChange(value, field, options) {
        if (!value) {
          this.ruleForm[field] = "";
          return;
        }
        let filterItem = options.filter((o) => o.lgUnionId === value)[0];
        this.ruleForm[field] = filterItem.phone;
      },
      // 选择点位
      selectPoint(row) {
        this.ruleForm.sponsorWaybillsDetails.push({ ...row, detailType: "" });
      },
      // 删除点位
      deletePoint(row) {
        let index = this.ruleForm.sponsorWaybillsDetails.findIndex((list) => list.id === row.id);
        if (index >= 0) {
          this.ruleForm.sponsorWaybillsDetails.splice(index, 1);
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            let params = deepClone(this.ruleForm);
            params.sponsorWaybillsDetails = params.sponsorWaybillsDetails.map((list) => {
              return {
                pickupPointId: list.id,
                detailType: list.detailType,
              };
            });
            if (!this.recordItem?.id) {
              delete params.id;
            }
            this.loading = true;
            try {
              let res = await createApiFun(params, this.apis.create);
              if (res.success) {
                this.$message.success(`发起收运任务成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .w250 {
    width: 250px;
  }
  .table-box {
    margin-bottom: 20px;
  }
  .full-width {
    width: 100%;
  }
  ::v-deep .task-form-item .el-form-item__content {
    margin-left: 0 !important;
  }
</style>
