/**
 * 坐标转换工具函数
 * 提供度分秒转十进制度以及WGS84转GCJ02的功能
 */

const PI = 3.1415926535897932384626;
const a = 6378245.0; //卫星椭球坐标投影到平面地图坐标系的投影因子。
const ee = 0.00669342162296594323; //椭球的偏心率。

/**
 * 度分秒格式转换为GCJ02坐标系
 * 例如：["113°14'45.13150553\"", "23°22'07.15897634\""] => [113.xxxxx, 23.xxxxx]
 * @param {Array} dmsArray 包含经度和纬度的度分秒格式字符串的数组 [lngDMS, latDMS]
 * @returns {Array} GCJ02坐标系的经纬度数组 [lng, lat]
 */
export function dmsToDecimal(dmsArray) {
  if (!Array.isArray(dmsArray) || dmsArray.length !== 2) {
    throw new Error("输入必须是包含两个元素的数组 [经度, 纬度]");
  }

  const [lngDMS, latDMS] = dmsArray;

  // 处理经度
  // 移除所有空格
  const lngStr = lngDMS.replace(/\s/g, "");
  // 匹配度分秒格式
  const lngRegex = /(\d+)°(\d+)'(\d+(\.\d+)?)"/;
  const lngMatch = lngStr.match(lngRegex);
  if (!lngMatch) {
    throw new Error("无效的经度度分秒格式");
  }
  // 转换为十进制度
  const lngDegrees = parseFloat(lngMatch[1]);
  const lngMinutes = parseFloat(lngMatch[2]);
  const lngSeconds = parseFloat(lngMatch[3]);
  const lngDecimal = lngDegrees + lngMinutes / 60 + lngSeconds / 3600;

  // 处理纬度
  // 移除所有空格
  const latStr = latDMS.replace(/\s/g, "");
  // 匹配度分秒格式
  const latRegex = /(\d+)°(\d+)'(\d+(\.\d+)?)"/;
  const latMatch = latStr.match(latRegex);
  if (!latMatch) {
    throw new Error("无效的纬度度分秒格式");
  }
  // 转换为十进制度
  const latDegrees = parseFloat(latMatch[1]);
  const latMinutes = parseFloat(latMatch[2]);
  const latSeconds = parseFloat(latMatch[3]);
  const latDecimal = latDegrees + latMinutes / 60 + latSeconds / 3600;

  // 转换为GCJ02坐标系
  return wgs84togcj02(lngDecimal, latDecimal);
}

//转化经度
function transformlng(lng, lat) {
  lng = +lng;
  lat = +lat;
  let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
  ret += ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0) / 3.0;
  ret += ((20.0 * Math.sin(lng * PI) + 40.0 * Math.sin((lng / 3.0) * PI)) * 2.0) / 3.0;
  ret += ((150.0 * Math.sin((lng / 12.0) * PI) + 300.0 * Math.sin((lng / 30.0) * PI)) * 2.0) / 3.0;
  return ret;
}

//转化纬度
function transformlat(lng, lat) {
  lng = +lng;
  lat = +lat;
  let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
  ret += ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0) / 3.0;
  ret += ((20.0 * Math.sin(lat * PI) + 40.0 * Math.sin((lat / 3.0) * PI)) * 2.0) / 3.0;
  ret += ((160.0 * Math.sin((lat / 12.0) * PI) + 320 * Math.sin((lat * PI) / 30.0)) * 2.0) / 3.0;
  return ret;
}
//判断时候在国内还是国外
function out_of_china(lng, lat) {
  lng = +lng;
  lat = +lat;
  // 纬度3.86~53.55,经度73.66~135.05
  return !(lng > 73.66 && lng < 135.05 && lat > 3.86 && lat < 53.55);
}
//wgs84 to gcj02   地球坐标系 转 火星坐标系
export function wgs84togcj02(lng, lat) {
  lng = +lng;
  lat = +lat;
  if (out_of_china(lng, lat)) {
    return [lng, lat];
  }
  let dlat = transformlat(lng - 105.0, lat - 35.0);
  let dlng = transformlng(lng - 105.0, lat - 35.0);
  let radlat = (lat / 180.0) * PI;
  let magic = Math.sin(radlat);
  magic = 1 - ee * magic * magic;
  let sqrtmagic = Math.sqrt(magic);
  dlat = (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * PI);
  dlng = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * PI);
  let mglat = lat + dlat;
  let mglng = lng + dlng;
  return [mglng, mglat];
}

//gcj02 to wgs84  火星坐标系 转 地球坐标系
export function gcj02towgs84(lng, lat) {
  lng = +lng;
  lat = +lat;
  if (out_of_china(lng, lat)) {
    return [lng, lat];
  }
  const originalLngSign = Math.sign(lng);
  const originalLatSign = Math.sign(lat);
  lat = Math.abs(lat);
  lng = Math.abs(lng);
  let dlat = transformlat(lng - 105.0, lat - 35.0);
  let dlng = transformlng(lng - 105.0, lat - 35.0);
  let radlat = (lat / 180.0) * PI;
  let magic = Math.sin(radlat);
  magic = 1 - ee * magic * magic;
  let sqrtmagic = Math.sqrt(magic);
  dlat = (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * PI);
  dlng = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * PI);
  let mglat = lat + dlat;
  let mglng = lng + dlng;
  let lngs = lng * 2 - mglng;
  let lats = lat * 2 - mglat;
  let finalLng = originalLngSign * lngs;
  let finalLat = originalLatSign * lats;
  return [finalLng, finalLat];
}
