<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage hideReturnButton @closeRecord="closeRecord">
      <record
        v-if="showRecord"
        :recordId="recordId"
        :districtOptions="districtOptions"
        :cascaderProps="cascaderProps"
        @closeDetail="closeDetail"
        @refreshList="initData"
      ></record>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="keyword" placeholder="请输入路线名称/编号" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button size="small" type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button size="small" @click="resetFilter">重置</el-button>
          <el-button size="small" type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button class="mr-10" size="small" type="primary" @click="handleExport">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button>
            <el-popover placement="bottom" width="240" trigger="click" :append-to-body="false">
              <div class="reveal-box">
                <div class="reveal-header">
                  <div>展示全部</div>
                  <el-switch
                    :value="allItemChecked"
                    active-text="是"
                    inactive-text="否"
                    @change="changeAllReveal"
                  ></el-switch>
                </div>
                <ul class="reveal-list">
                  <li class="reveal-item" v-for="(item, index) in itemList" :key="index">
                    <el-checkbox :value="item.value" :disabled="index === 0" @change="changeReveal($event, item)">{{
                      item.label
                    }}</el-checkbox>
                  </li>
                </ul>
              </div>
              <el-button slot="reference">
                <div class="btn-inner">
                  <span class="el-icon-s-grid"></span>
                  <span>显示列</span>
                  <span class="el-icon-caret-bottom"></span>
                </div>
              </el-button>
            </el-popover>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <el-col :span="8">
            <el-form-item label="默认驾驶司机">
              <el-select
                v-model="filterForm.defaultDriverDossierId"
                placeholder="请选择默认驾驶司机"
                clearable
                filterable
              >
                <el-option
                  v-for="item in driverOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="默认车辆车牌号">
              <el-select
                v-model="filterForm.defaultVehiclePlateNumber"
                placeholder="请选择默认车辆车牌号"
                clearable
                filterable
              >
                <el-option v-for="item in carOptions" :key="item.id" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="路线属性">
              <el-select v-model="filterForm.type" placeholder="请选择路线属性" clearable filterable>
                <el-option
                  v-for="(item, index) in ROUTE_PROPERTY"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="路线状态">
              <el-select v-model="filterForm.status" placeholder="请选择路线状态" clearable filterable>
                <el-option v-for="(item, index) in ROUTE_STATUS" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属区域">
              <el-cascader
                v-model="filterForm.districtId"
                :options="districtOptions"
                clearable
                filterable
                :props="cascaderProps"
              ></el-cascader>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            height="100%"
            style="width: 100%"
            border
            ref="tableRef"
          >
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column
              prop="name"
              label="路线名称"
              align="center"
              min-width="120"
              v-if="itemList[0].value"
            ></el-table-column>
            <el-table-column
              prop="code"
              label="路线编号"
              align="center"
              min-width="120"
              v-if="itemList[1].value"
            ></el-table-column>
            <el-table-column
              prop="districtName"
              label="所属区域"
              align="center"
              v-if="itemList[2].value"
            ></el-table-column>
            <el-table-column prop="type" label="路线属性" align="center" min-width="120" v-if="itemList[3].value">
              <template #default="{ row }">{{ ROUTE_PROPERTY[row.type] }}</template>
            </el-table-column>
            <el-table-column
              prop="defaultVehiclePlateNumber"
              label="默认车辆"
              align="center"
              v-if="itemList[4].value"
            ></el-table-column>
            <el-table-column
              prop="defaultDriverDossierName"
              label="默认司机"
              align="center"
              v-if="itemList[5].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierOneName"
              label="押运工"
              align="center"
              v-if="itemList[6].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierTwoName"
              label="押运工2"
              align="center"
              v-if="itemList[7].value"
            ></el-table-column>
            <el-table-column prop="status" label="路线状态" align="center" v-if="itemList[8].value">
              <template #default="{ row }">{{ ROUTE_STATUS[row.status] }}</template>
            </el-table-column>
            <el-table-column prop="waybillType" label="收运方式" align="center" v-if="itemList[9].value">
              <template #default="{ row }">{{ WAYBILL_TYPE[row.waybillType] }}</template>
            </el-table-column>
            <el-table-column
              prop="pointNumber"
              label="点位数量"
              align="center"
              v-if="itemList[10].value"
            ></el-table-column>
            <el-table-column
              prop="defaultDriverDossierPhone"
              label="司机联系方式"
              align="center"
              min-width="120"
              v-if="itemList[11].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierOnePhone"
              label="押运工联系方式"
              align="center"
              min-width="120"
              v-if="itemList[12].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierTwoPhone"
              label="押运工2联系方式"
              align="center"
              min-width="120"
              v-if="itemList[13].value"
            ></el-table-column>
            <el-table-column prop="versionNumber" label="路线版本号" align="center" v-if="itemList[14].value">
              <template #default="{ row }">v{{ row.versionNumber.toFixed(1) }}</template>
            </el-table-column>
            <el-table-column prop="strategy" label="路线优化策略" align="center" v-if="itemList[15].value">
              <template #default="{ row }">{{ ROUTE_STRATEGY[row.strategy] }}</template>
            </el-table-column>
            <el-table-column
              prop="updateFullname"
              label="最近修改人"
              align="center"
              min-width="140"
              v-if="itemList[16].value"
            ></el-table-column>
            <el-table-column
              prop="updateTime"
              label="最近修改时间"
              align="center"
              min-width="160"
              v-if="itemList[17].value"
            ></el-table-column>
            <el-table-column min-width="100" label="操作" align="center" fixed="right">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row)">详情</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, BASE_API_URL, getInfoApiFunByParams, createApiFun, getListApiFun } from "@/api/base";
  import record from "./record.vue";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import { ROUTE_PROPERTY, ROUTE_STATUS, ROUTE_STRATEGY, WAYBILL_TYPE } from "@/enums";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        ROUTE_PROPERTY,
        ROUTE_STATUS,
        ROUTE_STRATEGY,
        WAYBILL_TYPE,
        tableData: [],
        apis: {
          listPage: "/api/pickup/pickupPath/backup/data/listPage",
          export: "/api/pickup/pickupPath/backup/data/exportPickupPath",
          regionList: "/api/region/regionList",
          carList: "/api/vehicle/dossier/list",
          userList: "/api/baseuser/list",
        },
        showRecord: false,
        recordId: "",
        loading: false,
        showDate: false,
        routeId: "",
        showAdjust: false,
        backupId: "",
        backupDate: "",
        showFilter: false,
        keyword: "",
        itemList: [
          { label: "路线名称", value: true },
          { label: "路线编号", value: true },
          { label: "所属区域", value: true },
          { label: "路线属性", value: true },
          { label: "默认车辆", value: true },
          { label: "默认司机", value: true },
          { label: "押运工", value: true },
          { label: "押运工2", value: true },
          { label: "路线状态", value: true },
          { label: "收运方式", value: true },
          { label: "点位数量", value: true },
          { label: "司机联系方式", value: false },
          { label: "押运工联系方式", value: false },
          { label: "押运工2联系方式", value: false },
          { label: "路线版本号", value: false },
          { label: "路线优化策略", value: false },
          { label: "最近修改人", value: false },
          { label: "最近修改时间", value: false },
        ],
        allItemChecked: false,
        carOptions: [],
        driverOptions: [],
        channelId: "",
        districtOptions: [
          { code: "440100000000", name: "广州市", children: [] },
          { code: "441500000000", name: "汕尾市", children: [] },
          { code: "445100000000", name: "潮州市", children: [] },
        ], //区域列表
        cascaderProps: {
          emitPath: false,
          value: "code",
          label: "name",
        },
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getInfoApiFunByParams({ pid: this.districtOptions[0].code }, this.apis.regionList),
          getInfoApiFunByParams({ pid: this.districtOptions[1].code }, this.apis.regionList),
          getInfoApiFunByParams({ pid: this.districtOptions[2].code }, this.apis.regionList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.districtOptions[0].children = res[2].data;
        this.districtOptions[1].children = res[3].data;
        this.districtOptions[2].children = res[4].data;
      },
      async initData(id, date, channelId) {
        if (id) {
          this.backupId = id;
          this.backupDate = date;
        }
        this.channelId = channelId;
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          ...this.filterForm,
          backupId: this.backupId,
          channelId: this.channelId,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas.map((item) => {
            return {
              ...item,
              defaultDriverDossierPhone: item.defaultDriverDossierPhone
                ? this.$sm2Decrypt(item.defaultDriverDossierPhone)
                : "",
              supercargoDossierOnePhone: item.supercargoDossierOnePhone
                ? this.$sm2Decrypt(item.supercargoDossierOnePhone)
                : "",
              supercargoDossierTwoPhone: item.supercargoDossierTwoPhone
                ? this.$sm2Decrypt(item.supercargoDossierTwoPhone)
                : "",
            };
          });
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.recordId = row.id;
      },
      closeRecord() {
        if (this.showRecord) {
          this.closeDetail();
          return;
        }
        this.page.pageNo = 1;
        this.page.pageSize = 10;
        this.$emit("changeType", { type: 0 });
      },
      // 关闭详情页
      closeDetail() {
        this.showRecord = false;
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 导出
      handleExport() {
        this.$confirm(`确认是否以当前筛选条件导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, {
                backupId: this.backupId,
                keyword: this.keyword,
                ...this.filterForm,
                channelId: this.channelId,
              });
              if (res.success) {
                createDownloadEvent(`${this.backupDate}路线档案.xlsx`, [res.data]);
                window.ELEMENT.Message.success("导出成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
      // 修改所有显示列
      changeAllReveal(value) {
        this.allItemChecked = value;
        this.itemList.slice(1, this.itemList.length).forEach((item) => (item.value = value));
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 列点击事件
      changeReveal(value, item) {
        item.value = value;
        let uncheckList = this.itemList.slice(1, this.itemList.length).filter((list) => !list.value);
        if (uncheckList.length === 0) {
          this.allItemChecked = true;
        } else {
          this.allItemChecked = false;
        }
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .w250 {
    width: 250px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
</style>
