<template>
  <div class="chat-container">
    <div class="content">
      <MsgContent @saveList="saveList" :AIlist="listObj[checkSide]"></MsgContent>
    </div>
  </div>
</template>

<script>
  import MsgContent from "./components/content.vue";
  export default {
    name: "Chat",
    components: { MsgContent },
    data() {
      return {
        checkSide: null,
        exmpleList: [],
        listObj: {},
      };
    },
    computed: {},
    watch: {},
    created() {},
    mounted() {},

    methods: {
      saveList(list) {
        this.listObj[this.checkSide] = list;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .chat-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    overflow: hidden;
    // user-select: none;
  }
  .content {
    flex: 1;
  }
</style>
