let record00 = () => import("./components/record00.vue");
let record01 = () => import("./components/record01.vue");
let record02 = () => import("./components/record02.vue");
let record03 = () => import("./components/record03.vue");
let record05 = () => import("./components/record05.vue");
let record06 = () => import("./components/record06.vue");
let record10 = () => import("./components/record10.vue");
let record11 = () => import("./components/record11.vue");
let record12 = () => import("./components/record12.vue");
let record13 = () => import("./components/record13.vue");
let record14 = () => import("./components/record14.vue");
let record20 = () => import("./components/record20.vue");
let record21 = () => import("./components/record21.vue");
let record23 = () => import("./components/record23.vue");
let record24 = () => import("./components/record24.vue");
let record28 = () => import("@/views/collectTransportManage/overtimeApproval/components/record");
let record30 = () => import("@/views/serviceManage/complaintRecord/components/record");
let record40 = () => import("./components/record40.vue");
let record41 = () => import("./components/record41.vue");
let record60 = () => import("./components/record60.vue");

export default {
  record00,
  record01,
  record02,
  record03,
  record05,
  record06,
  record10,
  record11,
  record12,
  record13,
  record14,
  record20,
  record21,
  record23,
  record24,
  record28,
  record40,
  record41,
  record60,
  record30,
};
