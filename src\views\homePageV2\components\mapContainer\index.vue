<template>
  <!-- <div class="backlog"> -->
  <div :id="mapId" class="map-container"></div>
  <!-- </div> -->
</template>
<script>
  import { getInfoApiFunByParams } from "@/api/base.js";

  import AMapLoader from "@amap/amap-jsapi-loader";
  export default {
    name: "map-view",
    props: {
      mapId: {
        type: String,
        default: "mapContainer",
      },
    },
    async mounted() {
      await this.getData();
      this.initAMap();
    },
    beforeDestroy() {
      this.map?.destroy();
      this.$emit("initMap", "");
    },
    data() {
      return {
        config: {
          viewMode: "3D",
          zoom: 14, //地图级别
          center: [113.2744826, 23.1820811], //广东生活环境无害化处理中心有限公司
        },
        apis: {
          getData: "/api/monitor/location",
        },
        accMap: {
          1: "正常",
          0: "ACC关闭",
        },
        carList: [],
        iconUrl: require("./carIcon.svg"),
      };
    },
    methods: {
      decimalToBinary32(decimalStr) {
        // 防止为null
        if (!decimalStr) {
          decimalStr = "0";
        }
        let decimal = parseInt(decimalStr);
        // 将十进制转换为二进制字符串
        let binary = decimal.toString(2);

        // 检查二进制字符串的长度，并补零以达到32位
        let padding = "0".repeat(32 - binary.length);
        binary = padding + binary;

        // 返回前32位（尽管这里其实不需要，因为已经确保是32位了）
        return binary.slice(0, 32);
      },
      async getData() {
        try {
          let res = await getInfoApiFunByParams("", this.apis.getData);
          this.carList = res.data;
          this.carList = this.carList.map((item) => {
            let binary = this.decimalToBinary32(item.status);

            return {
              ...item,
              binary: binary,
              acc: binary[31],
            };
          });
          if (this.carList && this.carList.length > 0) {
            this.config.center = [this.carList[0].longitude, this.carList[0].latitude];
          }
          console.log(this.carList, "this.carList");
        } catch (error) {
          console.log(error);
        }
      },
      initAMap() {
        let vm = this; // 保存对 Vue 组件的引用
        window.vm = vm;
        console.log(window.vm, "window.vm");
        AMapLoader.load({
          key: "35d693226b39bc43fd408e883f75f127",
          version: "2.0",
          plugins: [],
        })
          .then((AMap) => {
            vm.map = new AMap.Map(vm.mapId, vm.config);

            const infoWindow = new AMap.InfoWindow({
              offset: new AMap.Pixel(0, -20),
              content: "",
            });

            vm.map.on("complete", () => {
              vm.carList.forEach((car) => {
                const icon = new AMap.Icon({
                  image: require("./carIcon.svg"),
                  imageSize: new AMap.Size(135, 40),
                  size: new AMap.Size(100, 100),
                });

                const marker = new AMap.Marker({
                  icon: icon,
                  position: [car.longitude, car.latitude],
                  title: car.plateNumber,
                });
                let carStatus = this.accMap[car.acc];
                // 使用箭头函数来保持对 Vue 组件的引用
                marker.on("click", () => {
                  const content = `
              <div><strong>车牌:</strong> ${car.plateNumber}<br/>
                   <strong>行驶状态:</strong> ${carStatus || "-"}<br/>
       <a href="${window.location.href}Detail?plateNumber=${encodeURIComponent(car.plateNumber)}">视频监控</a>
            `;
                  infoWindow.setContent(content);
                  infoWindow.open(vm.map, marker.getPosition());
                });

                marker.setMap(vm.map);
              });
            });
          })
          .catch((e) => {
            console.error(e);
          });
      },
      jumpRouter(plateNumber) {
        console.log("查看视频监控:", plateNumber);
        // 这里可以添加跳转到另一个页面或执行其他操作的代码
      },
    },
  };
</script>
<style scoped>
  .map-container {
    width: 100%;
    height: 100%;
  }
  /* .backlog {
    width: 480px;
    position: absolute;
    top: 732px;
    left: 40px;
    z-index: 1;
  } */
</style>
