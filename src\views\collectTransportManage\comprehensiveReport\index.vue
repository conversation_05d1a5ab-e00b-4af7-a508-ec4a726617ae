<template>
  <div class="micro-app-sctmp_base full-height">
    <record v-if="showRecord" :recordItem="recordItem" @closeRecord="closeRecord"></record>
    <div class="micro-app-sctmp_base full-height" v-loading="loading" v-else>
      <defaultPage>
        <div class="main-index">
          <header class="header">
            <div class="header-left">
              <el-form ref="filterForm" :model="filterForm" label-suffix=":" label-width="80px" class="mr-10">
                <el-form-item label="生成日期">
                  <el-date-picker
                    v-model="generateDate"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    value-format="yyyy-MM-dd"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-form>
            </div>
            <div class="filter-box">
              <el-badge :value="filterNumber" class="filter-badge">
                <el-button type="primary" @click="showFilter = !showFilter">
                  <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                  筛选
                  <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
                </el-button>
              </el-badge>
            </div>
            <el-button @click="resetFilter">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
            <div class="header-right">
              <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord"
                >生成收运统计表</el-button
              >
              <!-- <el-button size="small" type="primary" @click="handleExport">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button> -->
            </div>
          </header>
          <FilterContent label-width="140px" v-show="showFilter">
            <InterviewChannel
              :value.sync="filterForm.channelId"
              :record.sync="channelRecord"
              :needCol="true"
              :colNum="12"
            ></InterviewChannel>
            <el-col :span="12">
              <el-form-item label="统计日期范围">
                <el-date-picker
                  v-model="filterForm.statisticalDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </FilterContent>
          <main class="main">
            <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
              <el-table-column type="index" align="center"></el-table-column>
              <el-table-column prop="createDate" label="报告生成日期" align="center"></el-table-column>
              <el-table-column label="报告统计日期范围" align="center">
                <template #default="{ row }">{{ row.beginStatisticsDate }} ~ {{ row.endStatisticsDate }}</template>
              </el-table-column>
              <el-table-column prop="channelId" label="渠道名称" align="center">
                <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
              </el-table-column>
              <el-table-column min-width="140" label="操作" align="center">
                <template #default="{ row }">
                  <el-link class="mr-10" type="primary" @click="editRecord(row)">报表详情</el-link>
                </template>
              </el-table-column>
            </el-table>
          </main>
          <Pagination :page="page" @pageChange="pageChange"></Pagination>
        </div>
      </defaultPage>
      <createDialog :value.sync="showCreateDialog" @refreshList="initData"></createDialog>
    </div>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, BASE_API_URL } from "@/api/base";
  import record from "./components/record.vue";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import createDialog from "./components/createDialog.vue";
  export default {
    components: {
      defaultPage,
      Pagination,
      createDialog,
      record,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/logisticsReport/listPage",
          export: "/api/vehicle/annualReview/export",
        },
        showRecord: false,
        recordItem: "",
        loading: false,
        showFilter: false,
        generateDate: "",
        showCreateDialog: false,
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          beginCreateDate: this.generateDate ? this.generateDate[0] : "",
          endCreateDate: this.generateDate ? this.generateDate[1] : "",
          beginStatisticsDate: this.filterForm.statisticalDate ? this.filterForm.statisticalDate[0] : "",
          endStatisticsDate: this.filterForm.statisticalDate ? this.filterForm.statisticalDate[1] : "",
          channelId: this.filterForm.channelId || "",
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showCreateDialog = true;
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.recordItem = row;
      },
      closeRecord() {
        this.showRecord = false;
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.generateDate = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 导出
      handleExport() {
        this.$confirm(`确认是否以当前筛选条件导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, {
                beginCreateDate: this.generateDate ? this.generateDate[0] : "",
                endCreateDate: this.generateDate ? this.generateDate[1] : "",
                beginStatisticsDate: this.filterForm.statisticalDate ? this.filterForm.statisticalDate[0] : "",
                endStatisticsDate: this.filterForm.statisticalDate ? this.filterForm.statisticalDate[1] : "",
              });
              if (res.success) {
                createDownloadEvent(`收运综合报表${Date.now()}.xlsx`, [res.data]);
                window.ELEMENT.Message.success("导出成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item,
  ::v-deep .header-left .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
