<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="" :visible.sync="dialogVisible" width="1400px" top="0" destroy-on-close @open="initData">
      <template #title>
        <header class="header">昨日超时未填报点位数量</header>
      </template>
      <div class="main-record" v-loading="loading">
        <div class="filter">
          <el-input class="input-box" v-model="keyword" placeholder="请输入点位名称/所属路线" clearable></el-input>
          <el-button type="primary" @click="getDataList">搜索</el-button>
          <el-button type="primary" @click="initData">重置</el-button>
        </div>
        <el-form ref="filterForm" inline :model="filterForm" label-suffix=":" label-width="100px" class="form-box">
          <el-form-item label="点位类型">
            <el-select v-model="filterForm.type" placeholder="请选择点位类型" clearable filterable>
              <el-option v-for="(item, index) in POINT_TYPE" :key="index" :label="item" :value="index"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="收运周期">
            <el-select v-model="filterForm.period" placeholder="请选择收运周期" clearable filterable>
              <el-option
                v-for="(item, index) in COLLECTION_CYCLE"
                :key="index"
                :label="item"
                :value="index"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div class="record-content">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column prop="code" label="点位编号" align="center"></el-table-column>
            <el-table-column prop="productionUnit" label="点位名称" align="center"></el-table-column>
            <el-table-column prop="effectiveDate" label="生效日期" align="center"></el-table-column>
            <el-table-column prop="endDate" label="截止日期" align="center"></el-table-column>
            <el-table-column prop="address" label="点位地址" align="center"></el-table-column>
            <el-table-column prop="productionUnitOperator" label="点位联系人" align="center"></el-table-column>
            <el-table-column prop="productionUnitOperatorPhone" label="点位联系方式" align="center"></el-table-column>
            <el-table-column prop="type" label="点位类型" align="center">
              <template #default="{ row }">{{ POINT_TYPE[row.type] }}</template>
            </el-table-column>
            <el-table-column prop="name" label="所属路线" align="center"></el-table-column>
            <el-table-column prop="period" label="收运周期" align="center">
              <template #default="{ row }">{{ COLLECTION_CYCLE[row.period] }}</template>
            </el-table-column>
            <el-table-column prop="frequency" label="收运频次" align="center"></el-table-column>
          </el-table>
        </div>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  import { POINT_TYPE, COLLECTION_CYCLE } from "@/enums";
  import moment from "moment";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        loading: false,
        keyword: "",
        type: "",
        apis: {
          list: "/api/monitor/collectPoint/info",
        },
        filterForm: {},
        tableData: [],
        POINT_TYPE,
        COLLECTION_CYCLE,
        pickerOptions: {
          disabledDate: (time) => {
            let startDate = new Date();
            startDate.setDate(1);
            let endDate = new Date();
            endDate.setMonth(endDate.getMonth() + 1);
            endDate.setDate(0);
            return (
              time.getTime() < new Date(`${moment(startDate).format("YYYY/MM/DD")} 00:00:00`).getTime() ||
              time.getTime() > new Date(`${moment(endDate).format("YYYY/MM/DD")} 23:59:59`).getTime()
            );
          },
        },
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
      };
    },
    methods: {
      initData() {
        this.keyword = "";
        this.filterForm = {};
        this.page = { pageNo: 1, pageSize: 10, total: 0 };
        this.getDataList();
      },
      async getDataList() {
        try {
          let res = await createApiFun(
            {
              keyword: this.keyword,
              ...this.filterForm,
              pageNo: this.page.pageNo,
              pageSize: this.page.pageSize,
              channelId: this.channelId,
            },
            this.apis.list,
          );
          if (res.success) {
            this.tableData = res.data.datas.map((item) => {
              return {
                ...item,
                productionUnitOperatorPhone: this.productionUnitOperatorPhone
                  ? this.$sm2Decrypt(this.productionUnitOperatorPhone)
                  : "",
              };
            });
            this.page.total = res.data.total;
          }
        } catch (error) {
          console.log(error);
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.getDataList();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .header {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
  }
  .filter {
    display: flex;
    align-items: center;
    .input-box {
      width: 40%;
      margin-right: 10px;
    }
  }
  .form-box {
    margin-top: 20px;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
