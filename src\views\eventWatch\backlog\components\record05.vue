<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">车辆投保待处理</div>
      <el-form label-width="180px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="上报时间" required>
              <el-date-picker
                :value="ruleFormEvent.createTime"
                type="datetime"
                placeholder="请选择上报时间"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                disabled
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="截止日期" required>
              <el-date-picker
                :value="ruleFormEvent.deadline"
                type="date"
                placeholder="请选择截止日期"
                clearable
                value-format="yyyy-MM-dd"
                disabled
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="基础信息"></baseTitle>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
          <el-row>
            <el-col :md="24" :lg="12">
              <el-form-item label="所属年份" prop="year">
                <el-input
                  v-model="ruleForm.year"
                  placeholder="请输入所属年份"
                  clearable
                  disabled
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="投保类型" prop="insuranceType">
                <el-select
                  disabled
                  v-model="ruleForm.insuranceType"
                  placeholder="请选择投保类型"
                  filterable
                  class="w-300"
                >
                  <el-option
                    v-for="(item, index) in INSURANCE_TYPE"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="12" :lg="12">
              <el-form-item label="车牌号" prop="plateNumber">
                <el-input
                  v-model="ruleForm.plateNumber"
                  placeholder="请输入车牌号"
                  clearable
                  disabled
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :md="24" :lg="12">
              <el-form-item label="经办人" prop="cyOperatorName">
                <el-input
                  v-model="ruleForm.cyOperatorName"
                  placeholder="请输入经办人姓名"
                  clearable
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单号" prop="cyPolicyNo">
                <el-input
                  v-model="ruleForm.cyPolicyNo"
                  placeholder="请输入保单号"
                  clearable
                  maxlength="30"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单金额(元)" prop="cyMoney">
                <el-input-number
                  v-model="ruleForm.cyMoney"
                  :precision="2"
                  :min="0"
                  :max="99999999999999"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保险公司" prop="cyInsuranceCompany">
                <el-input
                  v-model="ruleForm.cyInsuranceCompany"
                  placeholder="请输入保险公司"
                  clearable
                  maxlength="100"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单开始日期" prop="cyPolicyEffectiveDate">
                <el-date-picker
                  v-model="ruleForm.cyPolicyEffectiveDate"
                  type="date"
                  placeholder="请选择保单开始日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="保单结束日期" prop="cyPolicyTerminationDate">
                <el-date-picker
                  v-model="ruleForm.cyPolicyTerminationDate"
                  type="date"
                  placeholder="请选择保单结束日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :md="24">
              <el-form-item label="凭证" prop="cyVehicleFileList">
                <FileUpload
                  @uploadChange="uploadChangeFileList($event, 'cyVehicleFileList')"
                  :imageList="imageList['cyVehicleFileList']"
                  :limit="5"
                >
                  <template #tips><el-tag type="warning">请上传保单、投保凭证</el-tag></template>
                </FileUpload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
      <el-button type="primary" @click="saveRecordThrottling">完成</el-button>
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import FileUpload from "@/components/FileUpload";
  import { deepClone } from "logan-common/utils";
  import { INSURANCE_TYPE } from "@/enums";

  export default {
    props: {
      recordItem: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      baseTitle,
      FileUpload,
    },
    computed: {},
    data() {
      return {
        INSURANCE_TYPE,
        ruleFormEvent: {
          createTime: null,
          completionTime: null,
        },
        ruleForm: {
          plateNumber: "", //车牌号
          year: "", //所属年份
          insuranceType: 2, //投保类型
          operatorName: "", //交强险经办人
          policyNo: "", //交强险保单号
          money: "", //交强险保单金额
          insuranceCompany: "", //交强险保险公司
          policyEffectiveDate: "", //交强险保单开始日期
          policyTerminationDate: "", //交强险保单结束日期
          vehicleFileList: [], ///交强险投保凭证

          syOperatorName: "", //商业险经办人
          syPolicyNo: "", //商业险保单号
          syMoney: "", //商业险保单金额
          syInsuranceCompany: "", //商业险保险公司
          syPolicyEffectiveDate: "", //商业险保单开始日期
          syPolicyTerminationDate: "", //商业险保单结束日期
          syVehicleFileList: [], ///商业险投保凭证

          cyOperatorName: "", //承运险经办人
          cyPolicyNo: "", //承运险保单号
          cyMoney: "", //承运险保单金额
          cyInsuranceCompany: "", //承运险保险公司
          cyPolicyEffectiveDate: "", //承运险保单开始日期
          cyPolicyTerminationDate: "", //承运险保单结束日期
          cyVehicleFileList: [], ///承运险投保凭证
        },
        baseRuleForm: {
          id: null,
          plateNumber: "", //车牌号
          year: "", //所属年份
          insuranceType: "", //投保类型
          operatorName: "", //交强险经办人
          policyNo: "", //交强险保单号
          money: "", //交强险保单金额
          insuranceCompany: "", //交强险保险公司
          policyEffectiveDate: "", //交强险保单开始日期
          policyTerminationDate: "", //交强险保单结束日期
          vehicleFileList: [], ///交强险投保凭证

          syOperatorName: "", //商业险经办人
          syPolicyNo: "", //商业险保单号
          syMoney: "", //商业险保单金额
          syInsuranceCompany: "", //商业险保险公司
          syPolicyEffectiveDate: "", //商业险保单开始日期
          syPolicyTerminationDate: "", //商业险保单结束日期
          syVehicleFileList: [], ///商业险投保凭证

          cyOperatorName: "", //承运险经办人
          cyPolicyNo: "", //承运险保单号
          cyMoney: "", //承运险保单金额
          cyInsuranceCompany: "", //承运险保险公司
          cyPolicyEffectiveDate: "", //承运险保单开始日期
          cyPolicyTerminationDate: "", //承运险保单结束日期
          cyVehicleFileList: [], ///承运险投保凭证
        },
        rules: {
          // 交强险
          plateNumber: [{ required: true, message: "请选择车牌号", trigger: "change" }],
          insuranceType: [{ required: true, message: "请选择投保类型", trigger: "change" }],
          year: [{ required: true, message: "请选择所属年份", trigger: "change" }],
          operatorName: [{ required: true, message: "请输入经办人姓名", trigger: "blur" }],
          policyNo: [{ required: true, message: "请输入保单号", trigger: "blur" }],
          money: [{ required: true, message: "请输入保单金额", trigger: "blur" }],
          insuranceCompany: [{ required: true, message: "请输入保险公司", trigger: "blur" }],
          policyEffectiveDate: [{ required: true, message: "请选择保单开始日期", trigger: "change" }],
          policyTerminationDate: [{ required: true, message: "请选择保单结束日期", trigger: "change" }],
          vehicleFileList: [{ required: true, message: "请上传投保凭证", trigger: "change" }],
          // 商业险
          syOperatorName: [{ required: true, message: "请输入经办人姓名", trigger: "blur" }],
          syPolicyNo: [{ required: true, message: "请输入保单号", trigger: "blur" }],
          syMoney: [{ required: true, message: "请输入保单金额", trigger: "blur" }],
          syInsuranceCompany: [{ required: true, message: "请输入保险公司", trigger: "blur" }],
          syPolicyEffectiveDate: [{ required: true, message: "请选择保单开始日期", trigger: "change" }],
          syPolicyTerminationDate: [{ required: true, message: "请选择保单结束日期", trigger: "change" }],
          syVehicleFileList: [{ required: true, message: "请上传投保凭证", trigger: "change" }],
          // 承运险
          cyOperatorName: [{ required: true, message: "请输入经办人姓名", trigger: "blur" }],
          cyPolicyNo: [{ required: true, message: "请输入保单号", trigger: "blur" }],
          cyMoney: [{ required: true, message: "请输入保单金额", trigger: "blur" }],
          cyInsuranceCompany: [{ required: true, message: "请输入保险公司", trigger: "blur" }],
          cyPolicyEffectiveDate: [{ required: true, message: "请选择保单开始日期", trigger: "change" }],
          cyPolicyTerminationDate: [{ required: true, message: "请选择保单结束日期", trigger: "change" }],
          cyVehicleFileList: [{ required: true, message: "请上传投保凭证", trigger: "change" }],
        },
        apis: {
          recordInfo: "/api/task/get/",
          create: "/api/vehicleInsure/create",
          update: "/api/vehicleInsure/update",
          info: "/api/vehicleInsure/get/",
          findByPlateNumberAndYear: "/api/vehicleInsure/findByPlateNumberAndYear",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: {
          vehicleFileList: [],
          cyVehicleFileList: [],
          syVehicleFileList: [],
        },
        carOptions: [],
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      if (this.recordItem && this.recordItem.businessId) {
        this.getRecord();
        this.getBusiness();
      }
    },
    methods: {
      uploadChangeFileList(fileList, field) {
        if (fileList.length > 0) {
          this.ruleForm[field] = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
          this.$refs.ruleForm.clearValidate(field);
        } else {
          this.ruleForm[field] = [];
        }
      },
      // 根据车牌号、年份获取已存在的投保类型
      async getHasTypeByNumberAndYear(plateNumber, year) {
        let res = await createApiFun({ plateNumber: plateNumber, year: year }, this.apis.findByPlateNumberAndYear);
        if (res.data) {
          this.baseRuleForm = deepClone(res.data);
        }
      },
      // 获取业务详情，取原型需要的字段
      async getBusiness() {
        let res = await getInfoApiFun(this.recordItem.businessId, this.apis.info);
        if (res.success) {
          let plateNumber = res.data.plateNumber;
          let year = parseInt(res.data.year) + 1 + "";

          await this.getHasTypeByNumberAndYear(plateNumber, year);
          let obj = {
            plateNumber: plateNumber, //车牌号
            year: year, //所属年份
            insuranceType: 2, //投保类型
            operatorName: this.baseRuleForm.operatorName, //交强险经办人
            policyNo: this.baseRuleForm.policyNo, //交强险保单号
            money: this.baseRuleForm.money, //交强险保单金额
            insuranceCompany: this.baseRuleForm.insuranceCompany, //交强险保险公司
            policyEffectiveDate: this.baseRuleForm.policyEffectiveDate, //交强险保单开始日期
            policyTerminationDate: this.baseRuleForm.policyTerminationDate, //交强险保单结束日期
            vehicleFileList: this.baseRuleForm.vehicleFileList, ///交强险投保凭证

            syOperatorName: this.baseRuleForm.syOperatorName, //商业险经办人
            syPolicyNo: this.baseRuleForm.syPolicyNo, //商业险保单号
            syMoney: this.baseRuleForm.syMoney, //商业险保单金额
            syInsuranceCompany: this.baseRuleForm.syInsuranceCompany, //商业险保险公司
            syPolicyEffectiveDate: this.baseRuleForm.syPolicyEffectiveDate, //商业险保单开始日期
            syPolicyTerminationDate: this.baseRuleForm.syPolicyTerminationDate, //商业险保单结束日期
            syVehicleFileList: this.baseRuleForm.syVehicleFileList, ///商业险投保凭证

            cyOperatorName: "", //承运险经办人
            cyPolicyNo: "", //承运险保单号
            cyMoney: "", //承运险保单金额
            cyInsuranceCompany: "", //承运险保险公司
            cyPolicyEffectiveDate: "", //承运险保单开始日期
            cyPolicyTerminationDate: "", //承运险保单结束日期
            cyVehicleFileList: [], ///承运险投保凭证
          };
          this.ruleForm = deepClone(obj);
          console.log("this.ruleForm", this.ruleForm);
        }
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = {};
              if (!this.baseRuleForm.id) {
                res = await createApiFun(this.ruleForm, this.apis.create);
              } else {
                // 处理insuranceType字段
                let params = deepClone(this.ruleForm);
                // 使用 split(',') 按逗号分割字符串，得到一个包含三个字符串的数组
                let arrOfStrings = this.baseRuleForm.insuranceType.split(",");
                // 使用 map() 方法遍历数组，将每个字符串转换为数字
                let arrOfNumbers = arrOfStrings.map(Number);
                // 加入 对应类型
                arrOfNumbers.push(this.ruleForm.insuranceType);
                // 对数组进行排序
                let sortedArr = arrOfNumbers.sort((a, b) => a - b);
                params.insuranceType = sortedArr.join(",");
                params.id = this.baseRuleForm.id;
                res = await updateApiFun(params, this.apis.update);
              }
              if (res.success) {
                this.$message.success(`处理成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              console.log(error, "error");
              this.loading = false;
            }
          }
        });
      },
      // 获取待办详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordItem.id, this.apis.recordInfo);
        this.ruleFormEvent = deepClone(res.data);
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("returnIndex");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-box {
    margin-bottom: 20px;
  }
</style>
