<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      width="70%"
      top="0"
      destroy-on-close
      :show-close="false"
      @open="initData"
    >
      <div class="main-index">
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column prop="plateNumber" label="车牌号" align="center"></el-table-column>
            <el-table-column prop="createTime" label="上报时间" align="center"></el-table-column>
            <el-table-column label="经纬度" align="center">
              <template #default="{ row }">{{ row.longitude }},{{ row.latitude }}</template>
            </el-table-column>
            <el-table-column prop="fatigueDegree" label="疲劳程度" align="center">
              <template #default="{ row }">{{ FATIGUE_DEGREE[row.fatigueDegree] }}</template>
            </el-table-column>
            <el-table-column prop="level" label="报警级别" align="center">
              <template #default="{ row }">{{ row.level }}级报警</template>
            </el-table-column>
            <el-table-column prop="speed" label="车速" align="center">
              <template #default="{ row }">{{ row.speed }}km/h</template>
            </el-table-column>
            <el-table-column prop="type" label="异常类型" align="center">
              <template #default="{ row }">{{ row.type > 17 ? "用户自定义" : EXCEPT_DANGER_TYPE[row.type] }}</template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import moment from "moment";
  import { EXCEPT_DANGER_TYPE, FATIGUE_DEGREE } from "@/enums";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        loading: false,
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/logisticsReport/abnormal/third-party",
        },
        EXCEPT_DANGER_TYPE,
        FATIGUE_DEGREE,
      };
    },
    methods: {
      initData() {
        this.page = { pageNo: 1, pageSize: 10, total: 0 };
        this.getDataList();
      },
      async getDataList() {
        try {
          let params = {
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            beginStatisticsDate: moment().format("YYYY-MM-DD"),
            endStatisticsDate: moment().format("YYYY-MM-DD"),
            channelId: this.channelId,
          };
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
        } catch (error) {
          console.log(error, "error");
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.getDataList();
      },
      // 重置筛选
      resetFilter() {
        this.page.pageNo = 1;
        this.getDataList();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.getDataList();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header-title {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
  }
  .header {
    display: flex;
    align-items: center;
    .header-left {
      flex: 1;
    }
    .header-right {
      margin-top: 20px;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w-300 {
    width: 300px;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
