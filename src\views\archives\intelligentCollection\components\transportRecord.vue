<template>
  <div class="micro-app-sctmp_base full-height">
    <defaultPage>
      <backButton @closeRecord="closeRecord"></backButton>
      <div class="main-index">
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" style="width: 100%" border>
            <el-table-column type="index" width="55" align="center"></el-table-column>
            <el-table-column prop="transportNumber" label="运输编号" align="center"></el-table-column>
            <el-table-column prop="deviceName" label="设备名称" align="center"></el-table-column>
            <el-table-column prop="deviceNumber" label="设备编号" align="center"></el-table-column>
            <el-table-column prop="transportUnit" label="运输单位" align="center"></el-table-column>
            <el-table-column prop="transportPerson" label="运输人员" align="center"></el-table-column>
            <el-table-column prop="collectionWeight" label="收运重量（Kg）" align="center"></el-table-column>
            <el-table-column prop="collectionTime" label="收运时间" align="center"></el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun } from "@/api/base";
  import backButton from "@/components/backButton";
  export default {
    components: {
      defaultPage,
      Pagination,
      backButton,
    },
    data() {
      return {
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/holding/holdingTank/pickupRecordListPage",
          export: "",
        },
        showRecord: false,
        recordId: "",
        loading: false,
      };
    },
    mounted() {},
    methods: {
      async initData(id) {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          holdingTankId: id,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      closeRecord() {
        this.page.pageNo = 1;
        this.page.pageSize = 10;
        this.$emit("changeType", { type: 0 });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    .header-left {
      flex: 1;
    }
    .header-right {
      margin-top: 20px;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  ::v-deep .header-left .el-form-item {
    margin-bottom: 0;
    margin-top: 20px;
  }
</style>
