<template>
  <div class="main-record" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" label-suffix="：">
        <baseTitle title="路线基础信息："></baseTitle>
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="路线名称" prop="name">{{ ruleForm.name }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="路线编号" prop="code">{{ ruleForm.code }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="路线状态" prop="status">{{ ROUTE_STATUS[ruleForm.status] }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="所属区域" prop="districtId">{{ ruleForm.districtName }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="路线属性" prop="type">{{ ROUTE_PROPERTY[ruleForm.type] }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="收运方式" prop="waybillType">{{ WAYBILL_TYPE[ruleForm.waybillType] }}</el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="收运车辆、人员基础信息："></baseTitle>
        <el-row>
          <el-col>
            <el-form-item label="收运车辆" prop="defaultVehiclePlateNumber">{{
              ruleForm.defaultVehiclePlateNumber
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="驾驶司机" prop="defaultDriverDossierId">{{
              ruleForm.defaultDriverDossierName
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="司机联系方式" prop="defaultDriverDossierPhone">{{
              ruleForm.defaultDriverDossierPhone
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="押运工1" prop="supercargoDossierOneId">{{
              ruleForm.supercargoDossierOneName
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="押运工联系方式" prop="supercargoDossierOnePhone">{{
              ruleForm.supercargoDossierOnePhone
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="押运工2" prop="supercargoDossierTwoId">{{
              ruleForm.supercargoDossierTwoName
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="押运工联系方式" prop="supercargoDossierTwoPhone">{{
              ruleForm.supercargoDossierTwoPhone
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="收运人员轮值" prop="isChange">
              <el-switch
                v-model="ruleForm.isChange"
                active-text="开启"
                inactive-text="关闭"
                :active-value="1"
                :inactive-value="0"
                disabled
              ></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="ruleForm.isChange == 1">
          <baseTitle title="收运人员轮值信息："></baseTitle>
          <el-form-item label="轮值司机"></el-form-item>
          <el-form-item label-width="0">
            <el-table
              :data="ruleForm.driverChangeList"
              :header-cell-style="{ background: '#F5F7F9' }"
              style="width: 100%"
              border
            >
              <el-table-column prop="changeFullName" label="司机名称" align="center"></el-table-column>
              <el-table-column prop="changeUserPhone" label="司机联系方式" align="center">
                <template #default="{ row }">{{ row.changeUserPhone || "-" }}</template>
              </el-table-column>
              <el-table-column prop="changeWeek" label="轮值日" align="center">
                <template #default="{ row }">{{ weekList[row.changeWeek - 1] }}</template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item label="轮值押运工1"></el-form-item>
          <el-form-item label-width="0">
            <el-table
              :data="ruleForm.shipWorkerOneChangeList"
              :header-cell-style="{ background: '#F5F7F9' }"
              style="width: 100%"
              border
            >
              <el-table-column prop="changeFullName" label="押运工名称" align="center"></el-table-column>
              <el-table-column prop="changeUserPhone" label="押运工联系方式" align="center">
                <template #default="{ row }">{{ row.changeUserPhone || "-" }}</template>
              </el-table-column>
              <el-table-column prop="changeWeek" label="轮值日" align="center">
                <template #default="{ row }">{{ weekList[row.changeWeek - 1] }}</template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <el-form-item label="轮值押运工2"></el-form-item>
          <el-form-item label-width="0">
            <el-table
              :data="ruleForm.shipWorkerTwoChangeList"
              :header-cell-style="{ background: '#F5F7F9' }"
              style="width: 100%"
              border
            >
              <el-table-column prop="changeFullName" label="押运工名称" align="center"></el-table-column>
              <el-table-column prop="changeUserPhone" label="押运工联系方式" align="center">
                <template #default="{ row }">{{ row.changeUserPhone || "-" }}</template>
              </el-table-column>
              <el-table-column prop="changeWeek" label="轮值日" align="center">
                <template #default="{ row }">{{ weekList[row.changeWeek - 1] }}</template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </template>
        <baseTitle title="收运点位基础信息："></baseTitle>
        <el-form-item prop="tableData" label-width="0">
          <el-table
            class="point-table"
            :data="ruleForm.tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            style="width: 100%"
            border
          >
            <el-table-column type="index" label="收运顺序" align="center" width="80"></el-table-column>
            <el-table-column prop="name" label="点位名称" align="center" min-width="200">
              <template #default="{ row }">
                <el-tooltip effect="dark" :content="row.name" placement="top" :open-delay="100">
                  <div class="line-clamp line-clamp-2">{{ row.name }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="code" label="点位编号" align="center"></el-table-column>
            <el-table-column prop="address" label="点位地址" align="center" min-width="240">
              <template #default="{ row }">
                <el-tooltip effect="dark" :content="row.address" placement="top" :open-delay="100">
                  <div class="line-clamp line-clamp-2">{{ row.address }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="contact" label="点位联系人" align="center"></el-table-column>
            <el-table-column prop="contactPhone" label="点位联系方式" align="center"></el-table-column>
            <el-table-column prop="type" label="点位类型" align="center">
              <template #default="{ row }">{{ POINT_TYPE[row.type] }}</template>
            </el-table-column>
            <el-table-column prop="period" label="收运周期" align="center">
              <template #default="{ row }">{{ COLLECTION_CYCLE[row.period] }}</template>
            </el-table-column>
            <el-table-column prop="frequency" label="收运频次" align="center"></el-table-column>
            <el-table-column prop="isUndock" label="经营状态" align="center">
              <template #default="{ row }">{{ REMOVE_STATUS[row.isUndock] }}</template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun, getListApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import { ROUTE_STATUS, ROUTE_PROPERTY, WAYBILL_TYPE, POINT_TYPE, COLLECTION_CYCLE, REMOVE_STATUS } from "@/enums";
  export default {
    components: {
      baseTitle,
    },
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        apis: {
          info: "/api/pickup/pickupPath/get/",
          userList: "/api/baseuser/list",
        },
        driverOptions: [],
        shipWorkerOptions: [],
        ruleForm: {},
        rules: {
          name: [{ required: true, message: "请输入路线名称", trigger: "blur" }],
          code: [{ required: true, message: "请输入路线编号", trigger: "blur" }],
          status: [{ required: true, message: "请选择路线状态", trigger: "change" }],
          districtId: [{ required: true, message: "请选择所属区域", trigger: "change" }],
          type: [{ required: true, message: "请选择路线属性", trigger: "change" }],
          waybillType: [{ required: true, message: "请选择收运方式", trigger: "change" }],
          defaultVehiclePlateNumber: [{ required: true, message: "请选择收运车辆", trigger: "change" }],
          defaultDriverDossierId: [{ required: true, message: "请选择驾驶司机", trigger: "change" }],
          defaultDriverDossierPhone: [{ required: true, message: "请输入司机联系方式", trigger: "blur" }],
          tableData: [{ type: "array", required: true, message: "请添加收运点位信息", trigger: "change" }],
          historyRemark: [{ required: true, message: "请输入备注", trigger: "blur" }],
        },
        loading: false,
        ROUTE_STATUS,
        ROUTE_PROPERTY,
        WAYBILL_TYPE,
        POINT_TYPE,
        COLLECTION_CYCLE,
        REMOVE_STATUS,
        weekList: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
      };
    },
    async mounted() {
      await this.getOptions();
      this.getRecord();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getListApiFun({ userIdentity: 4 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.driverOptions = res[0].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipWorkerOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },
      // 获取详情
      async getRecord() {
        this.loading = true;
        try {
          let res = await getInfoApiFun(this.recordId, this.apis.info);
          if (res.success) {
            this.ruleForm = res.data;
            this.ruleForm.defaultDriverDossierPhone = this.$sm2Decrypt(this.ruleForm.defaultDriverDossierPhone);
            this.ruleForm.supercargoDossierOnePhone = this.ruleForm.supercargoDossierOnePhone
              ? this.$sm2Decrypt(this.ruleForm.supercargoDossierOnePhone)
              : "";
            this.ruleForm.supercargoDossierTwoPhone = this.ruleForm.supercargoDossierTwoPhone
              ? this.$sm2Decrypt(this.ruleForm.supercargoDossierTwoPhone)
              : "";
            this.ruleForm.tableData = res.data.pickupPointList.map((item) => {
              return {
                ...item,
                contactPhone: item.contactPhone ? this.$sm2Decrypt(item.contactPhone) : "",
              };
            });
            res.data.pickupPathChanges.forEach((list) => {
              if (list.type == 0) {
                let filterItem = this.driverOptions.filter((o) => o.lgUnionId === list.changeUserId)[0];
                list.changeUserPhone = filterItem.phone || "";
              } else {
                let filterItem = this.shipWorkerOptions.filter((o) => o.lgUnionId === list.changeUserId)[0];
                list.changeUserPhone = filterItem.phone || "";
              }
            });
            this.ruleForm.driverChangeList = res.data.pickupPathChanges.filter((list) => list.type == 0);
            this.ruleForm.shipWorkerOneChangeList = res.data.pickupPathChanges.filter((list) => list.type == 1);
            this.ruleForm.shipWorkerTwoChangeList = res.data.pickupPathChanges.filter((list) => list.type == 2);
            for (let key in this.ruleForm) {
              this.ruleForm[key] =
                this.ruleForm[key] || this.ruleForm[key] === 0 || this.ruleForm[key] === false
                  ? this.ruleForm[key]
                  : "-";
            }
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      closeRecord() {
        this.$emit("closeRecord");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .record-content {
    flex: 1;
    overflow-y: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
</style>
