<template>
  <div class="container">
    <div class="point-box" @click="emitter.emit('open-dialog', 'showPointDialog')">
      <div class="overtime-box">
        <div class="overtime-total">{{ collectInfo?.monthOverdueNum }}</div>
        <div class="overtime-unit">（个）</div>
      </div>
      <div class="overtime-text">昨日超时未填报点位数量</div>
    </div>
    <div class="time-box">
      <el-progress
        :width="100"
        type="circle"
        :percentage="roundReserveDecimals(collectInfo?.situation24.collectPercentage * 100) || 0"
        :stroke-width="10"
        define-back-color="#aaa"
        text-color="#fff"
        color="#CAF982"
      ></el-progress>
      <div class="right">
        <div class="right-title">今日24小时应收运点位总数</div>
        <div class="right-count">{{ collectInfo?.situation24.total }}</div>
        <div class="right-title mt-16">今日已收24小时收运点位数量</div>
        <div class="right-count">{{ collectInfo?.situation24.collectNum }}</div>
      </div>
    </div>
    <div class="time-box">
      <el-progress
        :width="100"
        type="circle"
        :percentage="roundReserveDecimals(collectInfo?.situation48.collectPercentage * 100) || 0"
        :stroke-width="10"
        define-back-color="#aaa"
        text-color="#fff"
        color="#CAF982"
      ></el-progress>
      <div class="right">
        <div class="right-title">今日48小时应收运点位总数</div>
        <div class="right-count">{{ collectInfo?.situation48.total }}</div>
        <div class="right-title mt-16">今日累计48小时点位已收运数量</div>
        <div class="right-count">{{ collectInfo?.situation48.collectNum }}</div>
      </div>
    </div>
  </div>
</template>

<script>
  import { roundReserveDecimals } from "@/utils";
  import emitter from "@/utils/mitt";
  export default {
    computed: {
      collectInfo() {
        return this.$store.state.formData.collectInfo;
      },
    },
    data() {
      return {
        roundReserveDecimals,
        emitter,
      };
    },
  };
</script>

<style lang="scss" scoped>
  .container {
    margin-top: 30px;
  }
  .overtime-box {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    color: #fff;
    .overtime-total {
      font-size: 40px;
      font-weight: bold;
      line-height: 30px;
    }
    .overtime-unit {
      font-size: 12px;
    }
  }
  .overtime-text {
    margin-top: 16px;
    text-align: center;
    font-size: 16px;
    color: #fff;
  }
  .time-box {
    display: flex;
    align-items: center;
    color: #fff;
    margin-top: 40px;
  }
  .right {
    margin-left: 60px;
  }
  .right-title {
    font-size: 16px;
  }
  .right-count {
    font-size: 40px;
    font-weight: bold;
    margin-top: 4px;
  }
  .mt-16 {
    margin-top: 16px;
  }
  .point-box {
    pointer-events: auto;
    cursor: pointer;
  }
</style>
