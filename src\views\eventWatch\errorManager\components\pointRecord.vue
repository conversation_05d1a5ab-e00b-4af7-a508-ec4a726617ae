<template>
  <div>
    <el-dialog title="" :visible.sync="dialogVisible" width="50%" destroy-on-close append-to-body @open="initData">
      <div class="micro-app-sctmp_base">
        <div class="map-box">
          <mapContainer @initMap="initMap"></mapContainer>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">返回</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import mapContainer from "@/components/mapContainer";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      formItem: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      mapContainer,
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    beforeDestroy() {
      if (this.map) {
        this.map.destroy();
        this.map = "";
      }
    },
    data() {
      return {
        map: "",
      };
    },
    methods: {
      initData() {
        if (!this.map) {
          this.initMap();
        } else {
          this.initPosition();
        }
      },
      // 初始化地图
      initMap(map) {
        if (map) {
          this.map = map;
          window.AMap.plugin("AMap.Scale", () => {
            let scale = new window.AMap.Scale(); //创建工具条插件实例
            map.addControl(scale); //添加工具条插件到页面
          });
          this.initPosition();
        }
      },
      // 初始化地点
      async initPosition() {
        this.map.clearInfoWindow();
        this.map.clearMap();
        if (!this.formItem.address && (!this.formItem.longitude || !this.formItem.latitude)) return;
        this.generateMark([this.formItem.longitude, this.formItem.latitude]);
      },
      // 生成点位
      generateMark(lnglat) {
        let marker = new window.AMap.Marker({
          map: this.map,
          position: lnglat,
        });
        this.generateInfoWindowContent(lnglat);
        marker.on("click", this.markerClick);
        this.map.setFitView(null, false, [150, 60, 100, 60], 14);
      },
      // 点位点击事件
      markerClick(e) {
        let lnglat = e.target.getPosition();
        this.generateInfoWindowContent(lnglat);
      },
      // 信息窗体结构生成
      generateInfoWindowContent(lnglat) {
        let infoWindowContent = `<div style="font-size: 16px;width: 400px">${this.formItem.address || ""}</div>
        <div style="margin: 10px 0"><span>经度：${this.formItem.longitude}</span>&nbsp;&nbsp;<span>纬度：${
          this.formItem.latitude
        }</span></div>`;
        let infoWindow = new window.AMap.InfoWindow({
          position: lnglat,
          offset: new window.AMap.Pixel(0, -40),
          autoMove: true, // 自动移动到标记位置
          content: infoWindowContent,
        });
        infoWindow.open(this.map);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
  }
  .search-box {
    display: flex;
  }
  .header-right {
    margin-left: 10px;
  }
  .map-box {
    height: 600px;
  }
</style>
