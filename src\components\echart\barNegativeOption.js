/**
 * @Date: 2022-06-09 11:11:12
 * @return {*}
 * @description:正负条形图
 */
let barNegativeOption = {
  tooltip: {
    //提示
    trigger: "axis",
    textStyle: {
      fontWeight: 400,
    },
  },

  legend: {
    //图例
    itemHeight: 8,
    itemWidth: 8,
    left: "0%",
    top: "0%",

    textStyle: {
      color: "#666",
      fontWeight: 400,
      fontSize: "12",
    },
  },

  grid: {
    //网格
    // top: "4%",
    bottom: "4%",
    left: "12%",
    right: "10%",
    containLabel: true,
  },

  xAxis: {
    type: "value",
    minInterval: 1, //最小刻度1，显示整数
    axisLabel: {
      fontSize: "10", //x轴字体大小
      fontWeight: 400,
      color: "#909399",
      margin: 20, //x轴数据到图之间的距离
      interval: 0, // 设置字体倾斜
      rotate: "0", //字体旋转度数
    },

    splitLine: {
      // 网格线
      lineStyle: {
        color: ["#F3F6F6"], // 颜色
      },
    },
    axisLine: {
      show: true, //显示x轴线
      lineStyle: {
        color: "#F3F6F6",
        width: 1,
      },
    },
    axisTick: {
      show: false, //关闭x轴刻度
    },
  },

  yAxis: {
    type: "category",
    axisLabel: {
      fontSize: "14", //y轴字体大小
      color: "#606266", //y轴字体颜色

      formatter: function (params) {
        //y轴超过6个字换行
        if (params.length > 6) {
          var newParamsName = "";
          var paramsNameNumber = params.length;
          var provideNumber = 6;
          var rowNumber = Math.ceil(paramsNameNumber / provideNumber);
          for (let row = 0; row < rowNumber; row++) {
            newParamsName += params.substring(row * provideNumber, (row + 1) * provideNumber) + "\n";
          }
          return newParamsName;
        }
        return params;
      },
    },

    //显示x轴线
    axisLine: {
      lineStyle: {
        color: "#F3F6F6",
        width: 1,
        // type: 'dashed', //虚线
      },
    },
    axisTick: {
      show: false, //关闭y轴刻度
    },
    data: [],
  },

  series: [],
  color: ["#4EB6A8", "#FFC069", "#5A7EF8", "#EA5252"],
};

export default barNegativeOption;
