<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <baseTitle title="车辆信息"></baseTitle>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
        <el-row>
          <el-col>
            <el-form-item label="收运车辆" prop="defaultVehiclePlateNumber">
              <el-select
                v-model="ruleForm.defaultVehiclePlateNumber"
                placeholder="请选择收运车辆"
                clearable
                filterable
                @change="changePlateNumber"
              >
                <el-option
                  v-for="item in carOptions"
                  :key="item.id"
                  :label="item.plateNumber + (item.isFree === 1 ? '' : '（空闲）')"
                  :value="item.plateNumber"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="人员信息"></baseTitle>
        <el-row>
          <el-col :md="10" :lg="10">
            <el-form-item label="司机" prop="defaultDriverDossierId">
              <el-select
                v-model="ruleForm.defaultDriverDossierId"
                placeholder="请选择司机"
                clearable
                filterable
                @change="driverAndWorkerChange($event, 'defaultDriverDossierPhone', driverOptions)"
              >
                <el-option
                  v-for="item in driverOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14">
            <el-form-item label="" prop="defaultDriverDossierPhone">
              <el-input
                v-model="ruleForm.defaultDriverDossierPhone"
                placeholder="请输入司机联系方式"
                clearable
                :maxlength="11"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="10" :lg="10">
            <el-form-item label="押运工1" prop="supercargoDossierOneId">
              <el-select
                v-model="ruleForm.supercargoDossierOneId"
                placeholder="请选择押运工1"
                clearable
                filterable
                @change="driverAndWorkerChange($event, 'supercargoDossierOnePhone', shipWorkerOptions)"
              >
                <el-option
                  v-for="item in shipWorkerOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14">
            <el-form-item label="" prop="supercargoDossierOnePhone">
              <el-input
                v-model="ruleForm.supercargoDossierOnePhone"
                placeholder="请输入押运工联系方式"
                clearable
                :maxlength="11"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="10" :lg="10">
            <el-form-item label="押运工2" prop="supercargoDossierTwoId">
              <el-select
                v-model="ruleForm.supercargoDossierTwoId"
                placeholder="请选择押运工2"
                clearable
                filterable
                @change="driverAndWorkerChange($event, 'supercargoDossierTwoPhone', shipWorkerOptions)"
              >
                <el-option
                  v-for="item in shipWorkerOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="14" :lg="14">
            <el-form-item label="" prop="supercargoDossierTwoPhone">
              <el-input
                v-model="ruleForm.supercargoDossierTwoPhone"
                placeholder="请输入押运工联系方式"
                clearable
                :maxlength="11"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <baseTitle title="收运任务"></baseTitle>
      <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" border class="table-box">
        <el-table-column label="收运任务" align="center">
          <el-table-column prop="code" label="点位编号" align="center"></el-table-column>
          <el-table-column prop="productionUnit" label="产废单位名称" align="center"></el-table-column>
          <el-table-column prop="productionUnitOperator" label="产废单位经办人" align="center"></el-table-column>
          <el-table-column prop="waybillStatus" label="收运状态" align="center">
            <template #default="{ row }">{{ RECEIVING_CONDITION[row.waybillStatus] }}</template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <baseTitle title="备注信息"></baseTitle>
      <el-input
        v-model="ruleForm.memo"
        placeholder="请输入备注信息"
        clearable
        type="textarea"
        rows="10"
        maxlength="500"
        show-word-limit
      ></el-input>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">完成</el-button>
    </div>
  </div>
</template>

<script>
  import { createApiFun, getListApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import { RECEIVING_CONDITION } from "@/enums";
  export default {
    components: {
      baseTitle,
    },
    props: {
      recordForm: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        RECEIVING_CONDITION,
        ruleForm: {
          defaultVehiclePlateNumber: "", //收运车辆
          defaultDriverDossierId: "", //司机
          defaultDriverDossierPhone: "", //司机联系方式
          supercargoDossierOneId: "", //押运工1
          supercargoDossierOnePhone: "", //押运工联系方式
          supercargoDossierTwoId: "", //押运工2
          supercargoDossierTwoPhone: "", //押运工联系方式
          memo: "",
        },
        rules: {
          defaultVehiclePlateNumber: [{ required: true, message: "请选择收运车辆", trigger: "change" }],
          defaultDriverDossierId: [{ required: true, message: "请选择司机", trigger: "change" }],
          defaultDriverDossierPhone: [{ required: true, message: "请输入司机联系方式", trigger: "blur" }],
        },
        apis: {
          update: "/api/waybill/temporaryUpdate",
          carList: "/api/vehicle/dossier/getFreeList",
          userList: "/api/baseuser/list",
          driverAndWorkerInfo: "/api/baseuser/getInfoByPlateNumberAndUserIdentity",
          detailList: "/api/waybill/waybillDetail/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        carOptions: [],
        driverOptions: [],
        shipWorkerOptions: [],
        tableData: [],
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
      this.ruleForm = Object.assign(this.ruleForm, this.recordForm);
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getListApiFun({ userIdentity: 4 }, this.apis.userList),
          createApiFun({ waybillStatus: 0, waybillId: this.recordForm.id }, this.apis.detailList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipWorkerOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.tableData = res[3].data;
      },
      // 选择默认车牌号事件回调
      async changePlateNumber(value) {
        if (value) {
          let promiseList = [
            createApiFun({ userIdentity: "3", plateNumber: value }, this.apis.driverAndWorkerInfo),
            createApiFun({ userIdentity: "4", plateNumber: value }, this.apis.driverAndWorkerInfo),
          ];
          let res = await Promise.all(promiseList);
          let driverInfo = res[0].data;
          let workerInfo = res[1].data;
          if (driverInfo) {
            this.ruleForm.defaultDriverDossierId = driverInfo.lgUnionId;
            this.ruleForm.defaultDriverDossierPhone = this.$sm2Decrypt(driverInfo.phone);
          } else {
            this.ruleForm.defaultDriverDossierId = "";
            this.ruleForm.defaultDriverDossierPhone = "";
          }
          if (workerInfo) {
            this.ruleForm.supercargoDossierOneId = workerInfo.lgUnionId;
            this.ruleForm.supercargoDossierOnePhone = workerInfo.phone ? this.$sm2Decrypt(workerInfo.phone) : "";
          } else {
            this.ruleForm.supercargoDossierOneId = "";
            this.ruleForm.supercargoDossierOnePhone = "";
          }
        }
      },
      // 驾驶司机选择事件回调
      driverAndWorkerChange(value, field, options) {
        if (!value) {
          this.ruleForm[field] = "";
          return;
        }
        let filterItem = options.filter((o) => o.lgUnionId === value)[0];
        this.ruleForm[field] = filterItem.phone;
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun(this.ruleForm, this.apis.update);
              if (res.success) {
                this.$message.success(`临时调整成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .number-tip {
    margin-left: 10px;
  }
  .car-table {
    width: 50%;
  }
  .table-box {
    margin-bottom: 20px;
  }
</style>
