<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title="联系人信息"
      :visible.sync="dialogVisible"
      width="500px"
      destroy-on-close
      :before-close="closeDialog"
      @open="initForm"
    >
      <div v-loading="loading">
        <el-form :model="formData" :rules="rules" ref="formRef" label-width="100px" label-suffix="：">
          <el-form-item label="真实姓名" prop="contact">
            <el-input
              v-model.trim="formData.contact"
              placeholder="请输入真实姓名"
              clearable
              maxlength="14"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="联系电话" prop="contactPhone">
            <el-input
              v-model.trim="formData.contactPhone"
              placeholder="请输入联系电话"
              clearable
              maxlength="11"
              show-word-limit
            ></el-input>
          </el-form-item>
        </el-form>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit("update:value", val);
        },
      },
    },
    data() {
      // 手机号验证规则
      const validatePhone = (rule, value, callback) => {
        if (!value) {
          callback(new Error("请输入联系电话"));
        } else if (!/^1[3-9]\d{9}$/.test(value)) {
          callback(new Error("请输入正确的手机号码"));
        } else {
          callback();
        }
      };
      return {
        loading: false,
        formData: {
          contact: "",
          contactPhone: "",
        },
        rules: {
          contact: [{ required: true, message: "请输入真实姓名", trigger: "blur" }],
          contactPhone: [{ required: true, validator: validatePhone, trigger: "blur" }],
        },
        apis: {
          createPointUser: "/api/baseuser/createPointUser",
        },
      };
    },
    methods: {
      // 初始化表单数据
      initForm() {
        // 重置表单
        this.formData = {
          contact: "",
          contactPhone: "",
        };
        // 重置表单验证
        this.$nextTick(() => {
          if (this.$refs.formRef) {
            this.$refs.formRef.clearValidate();
          }
        });
      },
      // 关闭弹窗
      closeDialog() {
        this.dialogVisible = false;
      },
      // 提交表单
      submitForm() {
        this.$refs.formRef.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            let params = { phone: this.formData.contactPhone, fullName: this.formData.contact };
            try {
              let res = await createApiFun(this.$sm2Encrypt(JSON.stringify(params)), this.apis.createPointUser);
              if (res.success) {
                this.$message.success("创建关联用户成功");
                this.$emit("submitSuccess", { ...this.formData, lgUnionId: res.data });
                this.closeDialog();
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  ::v-deep .el-dialog {
    border-radius: 4px;
  }

  ::v-deep .el-dialog__body {
    padding: 20px 20px 10px;
  }

  ::v-deep .el-form-item__label {
    font-weight: 500;
  }
</style>
