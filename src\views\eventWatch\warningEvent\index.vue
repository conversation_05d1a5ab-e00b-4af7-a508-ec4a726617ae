<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage @closeRecord="closeRecord">
      <record40
        v-if="showCode == 40"
        :businessId="businessId"
        :recordId="recordId"
        @closeRecord="closeRecord"
        @refreshList="initData"
      ></record40>
      <record70
        ref="record70"
        v-else-if="showCode == 70"
        :businessId="businessId"
        :recordId="recordId"
        @closeRecord70="closeRecord70"
        @refreshList="initData"
      ></record70>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-form ref="filterForm" inline label-suffix=":" label-width="80px">
              <el-form-item label="模块">
                <el-select clearable filterable v-model="codeList" placeholder="请选择模块">
                  <el-option v-for="item in WARNING_MODULE_LIST" :key="item.key" :label="item.name" :value="item.key">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="处理状态">
                <el-select clearable filterable v-model="status" placeholder="请选择处理状态">
                  <el-option v-for="item in WARNING_STATUS_LIST" :key="item.id" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
            :colNum="12"
          ></InterviewChannel>
          <!-- <el-col :span="12">
            <el-form-item label="截止日期范围">
              <el-date-picker
                v-model="filterForm.deadline"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="上报日期范围">
              <el-date-picker
                v-model="filterForm.createDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="处理日期范围">
              <el-date-picker
                v-model="filterForm.completionDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col> -->
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column
              show-overflow-tooltip
              width="120"
              prop="module"
              label="模块"
              align="center"
            ></el-table-column>
            <el-table-column
              show-overflow-tooltip
              min-width="240"
              prop="todoTask"
              label="内容"
              align="center"
            ></el-table-column>
            <el-table-column
              show-overflow-tooltip
              width="180"
              prop="createTime"
              label="上报时间"
              align="center"
            ></el-table-column>
            <!-- <el-table-column
              show-overflow-tooltip
              width="180"
              prop="deadline"
              label="截止日期"
              align="center"
            ></el-table-column>
            <el-table-column
              show-overflow-tooltip
              width="180"
              prop="completionTime"
              label="处理日期"
              align="center"
            ></el-table-column>
            <el-table-column prop="status" label="处理状态" align="center" width="200">
              <template #default="{ row }">
                <div class="line-clamp line-clamp-1">{{ WARNING_STATUS_MAP[row.status] }}</div>
              </template>
            </el-table-column> -->
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column width="180" label="操作" align="center">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="check(row)" v-if="['40', '70'].includes(row.code)"
                  >查看详情</el-link
                >
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun } from "@/api/base";
  import record40 from "./components/Record40";
  import record70 from "./components/Record70";

  import {
    WARNING_MODULE_LIST,
    WARNING_STATUS_LIST,
    WARNING_STATUS_MAP,
    EVENT_SUBTITLE_MAP,
    EVENT_LIST,
  } from "@/enums/eventWatch";
  export default {
    components: {
      defaultPage,
      Pagination,
      record40,
      record70,
    },
    data() {
      return {
        WARNING_STATUS_LIST,
        WARNING_MODULE_LIST,
        WARNING_STATUS_MAP,
        EVENT_SUBTITLE_MAP,
        EVENT_LIST,
        filterForm: {
          codeList: "",
          createDate: "",
          status: "",
          deadline: "",
          completionDate: "",
        },
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/task/listPage",
        },
        showCode: false,
        recordId: "",
        businessId: "",
        dialogVisible: false,
        loading: false,
        showFilter: "",
        codeList: [],
        status: "",
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        this.loading = true;
        try {
          let codeList = null;
          let codeListObj = this.WARNING_MODULE_LIST.find((item) => {
            return item.key === this.codeList;
          });
          if (codeListObj) {
            codeList = codeListObj.id;
          }
          let params = {
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            type: 1,
            deadlineBeginDate: this.filterForm.deadline ? this.filterForm.deadline[0] : "",
            deadlineEndDate: this.filterForm.deadline ? this.filterForm.deadline[1] : "",
            createBeginDate: this.filterForm.createDate ? this.filterForm.createDate[0] : "",
            createEndDate: this.filterForm.createDate ? this.filterForm.createDate[1] : "",
            completionBeginDate: this.filterForm.completionDate ? this.filterForm.completionDate[0] : "",
            completionEndDate: this.filterForm.completionDate ? this.filterForm.completionDate[1] : "",
            codeList: codeList,
            status: this.status,
            channelId: this.filterForm.channelId || "",
          };
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
            // this.tableData = [{ id: "1", code: 40, todoTask: "车辆日常安全检查异常" }];
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 查看详情
      check(row) {
        if (EVENT_LIST.includes(row.code)) {
          this.showCode = row.code;
          this.recordId = row.id;
          this.businessId = row.businessId;
        } else {
          this.$message.warning("暂时不支持该类型告警查看");
        }
      },
      closeRecord() {
        if (this.showCode == "70") {
          this.$refs.record70.closeRecord();
        } else {
          this.showCode = null;
          this.initData();
        }
      },
      closeRecord70() {
        this.showCode = null;
        this.initData();
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.codeList = [];
        this.status = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .header-left .el-form-item,
  .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
