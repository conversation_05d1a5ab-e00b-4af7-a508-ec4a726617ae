<template>
  <div class="micro-app-sctmp_base full-height">
    <defaultPage>
      <record
        v-if="showRecord"
        :recordId="recordId"
        :positionOptions="positionOptions"
        :roleOptions="roleOptions"
        @closeRecord="closeRecord"
        @refreshList="initData"
      ></record>
      <div class="main-index" v-else>
        <importDialog
          :value.sync="importDialogShow"
          title="车辆维保记录"
          :importApi="apis.import"
          :templateApi="apis.template"
          :isTemplateApi="true"
          :importDialogType="importDialogType"
          @importSuccess="searchFilter"
        ></importDialog>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="keyword" placeholder="请输入车牌号/经办人/维保单位" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
            <el-dropdown
              size="small"
              trigger="click"
              @command="handleDataImport"
              class="dropdown-button"
              placement="bottom-start"
            >
              <el-button size="small" type="primary">
                <span class="sctmp-iconfont icon-ic_daoru"></span>
                <span class="ml-5">导入</span>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">
                  <div class="has-padding-sm has-border-bottom">导入基础信息</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button size="small" type="primary" @click="handleExport">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
            :colNum="12"
          ></InterviewChannel>
          <el-col :span="12">
            <el-form-item label="维保类型">
              <el-select v-model="filterForm.type" placeholder="请选择维保类型" clearable filterable>
                <el-option
                  v-for="(item, index) in MAINTENANCE_TYPE"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最近维保日期范围">
              <el-date-picker
                v-model="filterForm.recentMaintenanceTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下次维保日期范围">
              <el-date-picker
                v-model="filterForm.nextMaintenanceTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column prop="plateNumber" label="车牌号" align="center"> </el-table-column>
            <el-table-column prop="operatorName" label="经办人" align="center"></el-table-column>
            <el-table-column prop="type" label="维保类型" align="center">
              <template #default="{ row }">{{ MAINTENANCE_TYPE[row.type] }}</template>
            </el-table-column>
            <el-table-column prop="organizationName" label="维保单位" align="center"></el-table-column>
            <el-table-column prop="costs" label="维保费用(元)" align="center"></el-table-column>
            <el-table-column prop="recentMaintenanceTime" label="最近维保日期" align="center"></el-table-column>
            <el-table-column prop="nextMaintenanceTime" label="下次维保日期" align="center"></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row)">编辑</el-link>
                <el-popconfirm title="确认删除该记录？" class="mlr-12" @confirm="deleteRecord(row)">
                  <el-link type="danger" slot="reference">删除</el-link>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import { CERT_TYPES, MAINTENANCE_TYPE } from "@/enums";
  import Pagination from "@/components/pagination-v";
  import importDialog from "@/components/importDialog";
  import { getListPageApiFun, deleteApiFun, BASE_API_URL } from "@/api/base";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import record from "./components/record.vue";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
      importDialog,
    },
    data() {
      return {
        topInfo: {
          buttonName: "新增",
          subTitle: "列表",
          buttonShow: true,
          buttonPermission: "",
        },
        importDialogShow: false,
        filterForm: {},
        CERT_TYPES,
        positionOptions: [],
        deptOptions: [],
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/vehicleMaintenance/listPage",
          positionList: "/api/dict/position/list",
          import: "/api/vehicleMaintenance/importVehicleMaintenance",
          export: "/api/vehicleMaintenance/exportExcelVehicleDiscard",
          delete: "/api/vehicleMaintenance/delete/",
          template: `/api/vehicleMaintenance/excleModel`,
        },
        showRecord: false,
        recordId: "",
        showRoleEdit: false,
        roleOptions: [], //角色列表
        lgUnionId: "",
        MAINTENANCE_TYPE,
        importDialogType: "",
        showFilter: false,
        keyword: "",
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          recentMaintenanceBeginTime: this.filterForm.recentMaintenanceTime
            ? this.filterForm.recentMaintenanceTime[0]
            : "",
          recentMaintenanceEndTime: this.filterForm.recentMaintenanceTime
            ? this.filterForm.recentMaintenanceTime[1]
            : "",
          nextMaintenanceBeginTime: this.filterForm.nextMaintenanceTime ? this.filterForm.nextMaintenanceTime[0] : "",
          nextMaintenanceEndTime: this.filterForm.nextMaintenanceTime ? this.filterForm.nextMaintenanceTime[1] : "",
          type: this.filterForm.type,
          channelId: this.filterForm.channelId || "",
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showRecord = true;
        this.recordId = "";
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.recordId = row.id;
      },
      //删除
      async deleteRecord(row) {
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      closeRecord() {
        this.showRecord = false;
      },
      //导入
      async handleImport() {
        this.importDialogShow = true;
      },
      //导出
      async handleExport() {
        try {
          let res = await exportFile(BASE_API_URL + this.apis.export, {
            keyword: this.keyword,
            recentMaintenanceBeginTime: this.filterForm.recentMaintenanceTime
              ? this.filterForm.recentMaintenanceTime[0]
              : "",
            recentMaintenanceEndTime: this.filterForm.recentMaintenanceTime
              ? this.filterForm.recentMaintenanceTime[1]
              : "",
            nextMaintenanceBeginTime: this.filterForm.nextMaintenanceTime ? this.filterForm.nextMaintenanceTime[0] : "",
            nextMaintenanceEndTime: this.filterForm.nextMaintenanceTime ? this.filterForm.nextMaintenanceTime[1] : "",
            type: this.filterForm.type,
            channelId: this.filterForm.channelId || "",
          });
          if (res.success) {
            createDownloadEvent(`车辆维保记录${Date.now()}.xlsx`, [res.data]);
            window.ELEMENT.Message.success("导出成功");
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 导入
      handleDataImport(command) {
        this.importDialogType = command;
        this.importDialogShow = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
