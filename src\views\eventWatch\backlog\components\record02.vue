<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">车辆年审待处理</div>
      <el-form label-width="180px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="上报时间" required>
              <el-date-picker
                :value="ruleFormEvent.createTime"
                type="datetime"
                placeholder="请选择上报时间"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                disabled
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="截止日期" required>
              <el-date-picker
                :value="ruleFormEvent.deadline"
                type="date"
                placeholder="请选择截止日期"
                clearable
                value-format="yyyy-MM-dd"
                disabled
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="基础信息"></baseTitle>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
          <el-row>
            <el-col :md="12" :lg="12">
              <el-form-item label="车牌号" prop="plateNumber">
                <el-input
                  v-model="ruleForm.plateNumber"
                  placeholder="请输入车牌号"
                  clearable
                  disabled
                  :maxlength="50"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :md="24" :lg="12">
              <el-form-item label="经办人" prop="operatorName">
                <el-input
                  v-model="ruleForm.operatorName"
                  placeholder="请输入经办人"
                  clearable
                  maxlength="20"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="年审金额(元)" prop="costs">
                <el-input-number
                  v-model="ruleForm.costs"
                  :precision="2"
                  :min="0"
                  :max="99999999999999"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="年审单位" prop="organizationName">
                <el-input
                  v-model="ruleForm.organizationName"
                  placeholder="请输入年审单位"
                  clearable
                  maxlength="100"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="年审单号" prop="odd">
                <el-input
                  v-model="ruleForm.odd"
                  placeholder="请输入年审单号"
                  clearable
                  maxlength="20"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="最近年审日期"
                prop="recentAnnualReviewTime"
                :rules="[{ required: true, message: `请选择最近年审日期`, trigger: 'change' }]"
              >
                <el-date-picker
                  v-model="ruleForm.recentAnnualReviewTime"
                  type="date"
                  placeholder="请选择最近年审日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                :rules="[{ required: true, message: `请选择下次年审日期`, trigger: 'change' }]"
                label="下次年审日期"
                prop="nextAnnualReviewTime"
              >
                <el-date-picker
                  v-model="ruleForm.nextAnnualReviewTime"
                  type="date"
                  placeholder="请选择下次年审日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
                <div>
                  <span class="el-icon-warning color-danger"></span>
                  <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
                </div>
              </el-form-item>
            </el-col>
            <el-col :md="24">
              <el-form-item label="备注" prop="remarks">
                <el-input
                  v-model="ruleForm.remarks"
                  placeholder="请输入备注"
                  clearable
                  type="textarea"
                  rows="10"
                  maxlength="500"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24">
              <el-form-item label="凭证" prop="fileList">
                <FileUpload @uploadChange="uploadChangeFileList" :imageList="imageList" :limit="5">
                  <template #tips><el-tag type="warning">请上传年审单据</el-tag></template>
                </FileUpload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
      <el-button type="primary" @click="saveRecordThrottling">完成</el-button>
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import FileUpload from "@/components/FileUpload";
  import { deepClone } from "logan-common/utils";

  export default {
    props: {
      recordItem: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      baseTitle,
      FileUpload,
    },
    computed: {},
    data() {
      return {
        ruleFormEvent: {
          createTime: null,
          completionTime: null,
        },
        ruleForm: {
          plateNumber: "", //车牌号
          operatorName: "", //经办人
          costs: "", //年审金额
          organizationName: "", //年审单位
          recentAnnualReviewTime: "", //最近年审时间
          nextAnnualReviewTime: "", //下次年审时间
          odd: "", //年审单号
          remarks: "", //备注
          fileList: [], //年审凭证
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号", trigger: "change" }],
          operatorName: [{ required: true, message: "请输入经办人", trigger: "blur" }],
          costs: [{ required: true, message: "请输入年审金额", trigger: "blur" }],
          organizationName: [{ required: true, message: "请输入年审单位", trigger: "blur" }],
          recentAnnualReviewTime: [{ required: true, message: "请选择最近年审日期", trigger: "change" }],
          nextAnnualReviewTime: [{ required: true, message: "请选择下次年审日期", trigger: "change" }],
          fileList: [{ required: true, message: "请上传年审凭证", trigger: "change" }],
        },
        apis: {
          recordInfo: "/api/task/get/",
          create: "/api/vehicle/annualReview/create",
          info: "/api/vehicle/annualReview/get/",
          carList: "/api/vehicle/dossier/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [],
        carOptions: [],
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptions();
      if (this.recordItem && this.recordItem.businessId) {
        this.getRecord();
        this.getBusiness();
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({ statusList: [0, 1] }, this.apis.carList)];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
      },
      uploadChangeFileList(fileList) {
        if (fileList.length > 0) {
          this.ruleForm.fileList = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
          this.$refs.ruleForm.clearValidate("fileList");
        } else {
          this.ruleForm.fileList = [];
        }
      },
      getCurrentDate() {
        const now = new Date();
        const year = now.getFullYear(); // 获取完整的年份(4位,1970-????)
        const month = String(now.getMonth() + 1).padStart(2, "0"); // 月份是从0开始的，所以要加1
        const day = String(now.getDate()).padStart(2, "0"); // 获取当前月份的日期
        return `${year}-${month}-${day}`;
      },
      // 获取业务详情，取原型需要的字段
      async getBusiness() {
        let res = await getInfoApiFun(this.recordItem.businessId, this.apis.info);
        if (res.success) {
          // 00 为 常规保养
          // let obj = {
          //   plateNumber: res.data.plateNumber,
          //   type: [1], //维保类型
          //   dataList: [
          //     {
          //       type: 1,
          //       operatorName: "", //经办人
          //       organizationName: "", //保养单位
          //       recentMaintenanceTime: this.getCurrentDate(), //最近保养日期
          //       nextMaintenanceTime: "", //下次保养日期
          //       driveMileage: "", //行驶总里程
          //       upkeepMileage: "", //下次保养里程
          //       costs: "", //保养费用
          //       vehicleFileList: "", //保养凭证
          //     },
          //   ],
          // };
          let obj = {
            plateNumber: res.data.plateNumber,
            operatorName: "", //经办人
            costs: "", //年审金额
            organizationName: "", //年审单位
            recentAnnualReviewTime: this.getCurrentDate(), //最近年审时间
            nextAnnualReviewTime: "", //下次年审时间
            odd: "", //年审单号
            remarks: "", //备注
            fileList: [], //年审凭证
          };
          this.ruleForm = deepClone(obj);
          console.log("this.ruleForm", this.ruleForm);
        }
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun(this.ruleForm, this.apis.create);
              if (res.success) {
                this.$message.success(`处理成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      // 获取待办详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordItem.id, this.apis.recordInfo);
        this.ruleFormEvent = deepClone(res.data);
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("returnIndex");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-box {
    margin-bottom: 20px;
  }
</style>
