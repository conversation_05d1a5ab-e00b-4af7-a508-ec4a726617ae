<template>
  <div class="safeModule-wrapper">
    <Title name="客服统计" :url="url" @click.native="$router.push('/sctmp_base/visitRecord')"></Title>
    <div style="flex: 1" id="followUpModule"></div>

    <!-- <div>本年各月回访率/满意率/投诉率分析</div> -->
    <!-- followUpSituation/satisfactionSituation/complaintSituation -->
  </div>
</template>
<script>
  import * as echarts from "echarts";
  import Title from "./ModuleTitle.vue";
  export default {
    components: { Title },
    props: ["summaryData"],
    data() {
      return {
        url: require("../images/ic_follow.svg"),
        xAxisData: [],
        seriesData1: [],
        seriesData2: [],
        seriesData3: [],
      };
    },
    computed: {},
    created() {},
    watch: {
      summaryData: {
        handler(newV) {
          if (newV?.followUpSituation && newV?.complaintSituation && newV?.satisfactionSituation) {
            this.dataCb();
          }
        },
        deep: true,
      },
    },
    methods: {
      dataCb() {
        this.getXAxisData();
        this.getSeriesData1();
        this.getSeriesData2();
        this.getSeriesData3();
        this.init();
      },
      getSeriesData1() {
        let length = this.xAxisData.length;
        const mappedUndefinedArray = new Array(length).fill(0);
        this.summaryData.followUpSituation.forEach((item) => {
          let value = parseFloat(item.followUpPrecentage.replace("%", ""));
          mappedUndefinedArray[item.month - 1] = value;
        });
        this.seriesData1 = mappedUndefinedArray;
      },
      getSeriesData2() {
        let length = this.xAxisData.length;
        const mappedUndefinedArray = new Array(length).fill(0);
        this.summaryData.complaintSituation.forEach((item) => {
          let value = parseFloat(item.complaintPrecentage.replace("%", ""));
          mappedUndefinedArray[item.month - 1] = value;
        });
        this.seriesData3 = mappedUndefinedArray;
      },
      getSeriesData3() {
        let length = this.xAxisData.length;
        const mappedUndefinedArray = new Array(length).fill(0);
        this.summaryData.satisfactionSituation.forEach((item) => {
          let value = parseFloat(item.satisfctionLevel.replace("%", ""));
          mappedUndefinedArray[item.month - 1] = value;
        });
        this.seriesData2 = mappedUndefinedArray;
      },
      getXAxisData() {
        // 获取当前日期
        const now = new Date();

        // 获取当前年份的当前月份（注意：月份是从0开始的，所以8月是7）
        const currentMonth = now.getMonth() + 1;

        // 使用Array.from()方法生成从1到当前月份的数组
        // 第二个参数是一个函数，它接受两个参数：index（当前索引）和array（当前数组，但在这个场景下我们不需要它）
        // 我们通过index+1来确保数组从1开始
        const monthsArray = Array.from({ length: currentMonth }, (_, index) => index + 1);
        this.xAxisData = monthsArray.map((item) => {
          return `${item}月`;
        });
      },
      init() {
        var myChart = echarts.init(document.getElementById("followUpModule"));
        myChart.setOption({
          color: ["#006BF2", "#25C68C", "#E72A2A"],

          title: {
            text: "本年各月回访率/满意率/投诉率分析",
            textStyle: {
              color: "#414D5A",
              fontStyle: "normal",
              fontWeight: "bold",
              fontFamily: "Microsoft YaHei",
              fontSize: "14",
              lineHeight: "20",
            },
            left: "center",
          },
          tooltip: {
            trigger: "axis", // 改为 'axis' 以在 x 轴上触发
            formatter: (params) => {
              let result = `<div style="border-bottom: 1px solid #E0E0E0; padding-bottom: 7px; margin-bottom: 7px;">  
            <span style="line-height: 30px;display:inline-block;margin-right:5px;border-radius: 3px;padding: 0 5px; color: #414D5A;">${params[0].seriesName}</span>  
            <span style="line-height: 30px;display:inline-block;">: ${params[0].value}%</span>  
        </div>`;

              // 假设 this.seriesData1, this.seriesData2, this.seriesData3 是按 xAxisData 顺序排列的
              const xAxisIndex = params[0].dataIndex; // 获取当前点的 xAxis 索引

              // 查找其他两个系列在相同 xAxis 值下的数据
              // 注意：这里假设 xAxisData 中的值是唯一的，或者你可以通过其他方式找到最接近的索引
              let seriesData2Value = this.seriesData2[xAxisIndex];
              let seriesData3Value = this.seriesData3[xAxisIndex];

              // 如果 xAxisData 不是唯一的，你可能需要遍历数组来找到最接近的值

              result += `<div style="border-bottom: 1px solid #E0E0E0; padding-bottom: 7px; margin-bottom: 7px;">  
            <span style="line-height: 30px;display:inline-block;margin-right:5px;border-radius: 3px;padding: 0 5px; color: #414D5A;">满意率</span>  
            <span style="line-height: 30px;display:inline-block;">: ${seriesData2Value}%</span>  
        </div>`;

              result += `<div>  
            <span style="line-height: 30px;display:inline-block;margin-right:5px;border-radius: 3px;padding: 0 5px; color:#414D5A;">投诉率</span>  
            <span style="line-height: 30px;display:inline-block;">: ${seriesData3Value}%</span>  
        </div>`;

              return result;
            },
          },
          legend: {
            itemWidth: 8,
            itemHeight: 8,
            top: "30",
            data: [
              { name: "回访率", icon: "rect" },
              { name: "满意率", icon: "rect" },
              { name: "投诉率", icon: "rect" },
            ],
          },
          xAxis: {
            type: "category",
            data: this.xAxisData,
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          yAxis: [
            {
              type: "value",
              axisLabel: {
                formatter: "{value} %",
              },
            },
          ],
          series: [
            {
              name: "回访率",
              type: "line",
              data: this.seriesData1,
              itemStyle: {
                normal: {
                  label: {
                    show: true, // 显示标签
                    position: "top", // 标签的位置，默认为'top'
                    formatter: "{c}%", // 格式化标签文本，'{c}'表示数值本身
                  },
                },
              },
            },
            {
              name: "满意率",
              type: "line",
              data: this.seriesData2,
              itemStyle: {
                normal: {
                  label: {
                    show: true, // 显示标签
                    position: "top", // 标签的位置，默认为'top'
                    formatter: "{c}%", // 格式化标签文本，'{c}'表示数值本身
                  },
                },
              },
            },
            {
              name: "投诉率",
              type: "line",
              data: this.seriesData3,
              itemStyle: {
                normal: {
                  label: {
                    show: true, // 显示标签
                    position: "top", // 标签的位置，默认为'top'
                    formatter: "{c}%", // 格式化标签文本，'{c}'表示数值本身
                  },
                },
              },
            },
          ],
        });
      },
    },
  };
</script>
<style scoped lang="scss">
  .safeModule-wrapper {
    flex-direction: column;
    width: 100%;
    display: flex;
    background-color: #fff;
    flex: 1;
    margin-left: 8px;
  }
</style>
