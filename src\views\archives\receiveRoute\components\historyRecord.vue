<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="" :visible.sync="dialogVisible" width="1400px" top="0" destroy-on-close @open="getHistoryRecord">
      <template v-if="ruleForm && ruleForm.id">
        <div class="card-header">
          <div>收运路线档案</div>
          <div class="version-number">版本号：v{{ ruleForm.versionNumber.toFixed(1) }}</div>
        </div>
        <el-form :model="ruleForm" ref="ruleForm" label-width="140px" label-suffix="：">
          <baseTitle title="路线基础信息："></baseTitle>
          <el-row>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="路线名称"
                prop="name"
                :class="{ active: historyColumns && historyColumns.includes('name') }"
              >
                <el-input
                  v-model="ruleForm.name"
                  placeholder="请输入路线名称"
                  clearable
                  maxlength="50"
                  show-word-limit
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="路线编号"
                prop="code"
                :class="{ active: historyColumns && historyColumns.includes('code') }"
              >
                <el-input
                  v-model="ruleForm.code"
                  placeholder="请输入路线编号"
                  clearable
                  maxlength="50"
                  show-word-limit
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="路线状态"
                prop="status"
                :class="{ active: historyColumns && historyColumns.includes('status') }"
              >
                <el-select v-model="ruleForm.status" placeholder="请选择路线状态" clearable filterable disabled>
                  <el-option
                    v-for="(item, index) in ROUTE_STATUS"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="所属区域"
                prop="districtId"
                :class="{ active: historyColumns && historyColumns.includes('districtId') }"
              >
                <el-cascader
                  disabled
                  v-model="ruleForm.districtId"
                  :options="districtOptions"
                  clearable
                  filterable
                  :props="cascaderProps"
                ></el-cascader>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item
                label="路线属性"
                prop="type"
                :class="{ active: historyColumns && historyColumns.includes('type') }"
              >
                <el-select v-model="ruleForm.type" placeholder="请选择路线属性" clearable filterable disabled>
                  <el-option
                    v-for="(item, index) in ROUTE_PROPERTY"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <baseTitle title="收运车辆、人员基础信息："></baseTitle>
          <el-row>
            <el-col>
              <el-form-item
                label="默认收运车辆"
                prop="defaultVehiclePlateNumber"
                :class="{ active: historyColumns && historyColumns.includes('defaultVehiclePlateNumber') }"
              >
                <el-select
                  v-model="ruleForm.defaultVehiclePlateNumber"
                  placeholder="请选择默认收运车辆"
                  clearable
                  filterable
                  disabled
                >
                  <el-option
                    v-for="item in carOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="默认驾驶司机"
                prop="defaultDriverDossierId"
                :class="{ active: historyColumns && historyColumns.includes('defaultDriverDossierId') }"
              >
                <el-select
                  v-model="ruleForm.defaultDriverDossierId"
                  placeholder="请选择默认驾驶司机"
                  clearable
                  filterable
                  @change="driverAndWorkerChange($event, 'defaultDriverDossierPhone', driverOptions)"
                  disabled
                >
                  <el-option
                    v-for="item in driverOptions"
                    :key="item.lgUnionId"
                    :label="item.fullName"
                    :value="item.lgUnionId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="司机联系方式"
                prop="defaultDriverDossierPhone"
                :class="{ active: historyColumns && historyColumns.includes('defaultDriverDossierPhone') }"
              >
                <el-input
                  v-model="ruleForm.defaultDriverDossierPhone"
                  placeholder="请输入司机联系方式"
                  clearable
                  :maxlength="11"
                  show-word-limit
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="默认押运工1"
                prop="supercargoDossierOneId"
                :class="{ active: historyColumns && historyColumns.includes('supercargoDossierOneId') }"
              >
                <el-select
                  v-model="ruleForm.supercargoDossierOneId"
                  placeholder="请选择默认押运工1"
                  clearable
                  filterable
                  @change="driverAndWorkerChange($event, 'supercargoDossierOnePhone', shipWorkerOptions)"
                  disabled
                >
                  <el-option
                    v-for="item in shipWorkerOptions"
                    :key="item.lgUnionId"
                    :label="item.fullName"
                    :value="item.lgUnionId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="押运工联系方式"
                prop="supercargoDossierOnePhone"
                :class="{ active: historyColumns && historyColumns.includes('supercargoDossierOnePhone') }"
              >
                <el-input
                  v-model="ruleForm.supercargoDossierOnePhone"
                  placeholder="请输入押运工联系方式"
                  clearable
                  :maxlength="11"
                  show-word-limit
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="默认押运工2"
                prop="supercargoDossierTwoId"
                :class="{ active: historyColumns && historyColumns.includes('supercargoDossierTwoId') }"
              >
                <el-select
                  v-model="ruleForm.supercargoDossierTwoId"
                  placeholder="请选择默认押运工2"
                  clearable
                  filterable
                  @change="driverAndWorkerChange($event, 'supercargoDossierTwoPhone', shipWorkerOptions)"
                  disabled
                >
                  <el-option
                    v-for="item in shipWorkerOptions"
                    :key="item.lgUnionId"
                    :label="item.fullName"
                    :value="item.lgUnionId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="押运工联系方式"
                prop="supercargoDossierTwoPhone"
                :class="{ active: historyColumns && historyColumns.includes('supercargoDossierTwoPhone') }"
              >
                <el-input
                  v-model="ruleForm.supercargoDossierTwoPhone"
                  placeholder="请输入押运工联系方式"
                  clearable
                  :maxlength="11"
                  show-word-limit
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <baseTitle
            title="收运点位基础信息："
            :styleObject="historyColumns && historyColumns.includes('pickupPointList') ? { color: '#d9001b' } : {}"
          ></baseTitle>
          <el-form-item prop="tableData" label-width="0">
            <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" style="width: 100%" border>
              <el-table-column type="index" label="收运顺序" align="center" width="80"></el-table-column>
              <el-table-column prop="name" label="点位名称" align="center" min-width="200">
                <template #default="{ row }">
                  <el-tooltip effect="dark" :content="row.name" placement="top" :open-delay="100">
                    <div class="line-clamp line-clamp-2">{{ row.name }}</div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="code" label="点位编号" align="center"></el-table-column>
              <el-table-column prop="address" label="点位地址" align="center" min-width="240">
                <template #default="{ row }">
                  <el-tooltip effect="dark" :content="row.address" placement="top" :open-delay="100">
                    <div class="line-clamp line-clamp-2">{{ row.address }}</div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="contact" label="点位联系人" align="center"></el-table-column>
              <el-table-column prop="contactPhone" label="点位联系方式" align="center"></el-table-column>
              <el-table-column prop="type" label="点位类型" align="center">
                <template #default="{ row }">{{ POINT_TYPE[row.type] }}</template>
              </el-table-column>
              <el-table-column prop="contractStatusName" label="合同状态" align="center"></el-table-column>
              <el-table-column prop="deviceStatusName" label="设备状态" align="center"></el-table-column>
              <el-table-column prop="isUndock" label="移除状态" align="center">
                <template #default="{ row }">{{ REMOVE_STATUS[row.isUndock] }}</template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun } from "@/api/base";
  import { ROUTE_PROPERTY, ROUTE_STATUS, POINT_TYPE, REMOVE_STATUS } from "@/enums";
  import baseTitle from "@/components/baseTitle";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      // 区域列表
      districtOptions: {
        type: Array,
        default: () => [],
      },
      // 历史记录id
      historyId: {
        type: String,
        default: "",
      },
      // 更改了的字段字符串
      historyColumns: {
        type: String,
        default: "",
      },
      // 车辆列表
      carOptions: {
        type: Array,
        default: () => [],
      },
      // 司机列表
      driverOptions: {
        type: Array,
        default: () => [],
      },
      // 押运工列表
      shipWorkerOptions: {
        type: Array,
        default: () => [],
      },
      cascaderProps: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      baseTitle,
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        apis: {
          historyRecord: "/api/pickup/pickupPath/history/getPickupPathHistoryById/",
          recordList: "/api/pickup/pickupPath/history/getPickupPathHistoryPointListByHistoryId",
        },
        ROUTE_PROPERTY,
        ROUTE_STATUS,
        POINT_TYPE,
        REMOVE_STATUS,
        ruleForm: {},
      };
    },

    methods: {
      // 获取用户已有角色列表
      async getHistoryRecord() {
        let promiseList = [
          getInfoApiFun(this.historyId, this.apis.historyRecord),
          createApiFun({ pickupPathHistoryId: this.historyId }, this.apis.recordList),
        ];
        let res = await Promise.all(promiseList);
        this.ruleForm = res[0].data;
        this.tableData = res[1].data.map((item) => {
          return {
            ...item,
            contactPhone: item.contactPhone ? this.$sm2Decrypt(item.contactPhone) : "",
          };
        });
        this.ruleForm.defaultDriverDossierPhone = this.$sm2Decrypt(this.ruleForm.defaultDriverDossierPhone);
        this.ruleForm.supercargoDossierOnePhone = this.ruleForm.supercargoDossierOnePhone
          ? this.$sm2Decrypt(this.ruleForm.supercargoDossierOnePhone)
          : "";
        this.ruleForm.supercargoDossierTwoPhone = this.ruleForm.supercargoDossierTwoPhone
          ? this.$sm2Decrypt(this.ruleForm.supercargoDossierTwoPhone)
          : "";
      },
    },
  };
</script>

<style lang="scss" scoped>
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
    position: relative;
    .version-number {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      font-size: 14px;
      color: #909399;
    }
  }
  ::v-deep .active .el-form-item__label {
    color: #d9001b;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
  }
  ::v-deep .el-dialog__body {
    padding: 40px 30px;
    height: calc(100% - 134px);
    overflow-y: auto;
    box-sizing: border-box;
  }
</style>
