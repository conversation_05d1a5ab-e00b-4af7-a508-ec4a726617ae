<template>
  <div class="chartsLeft-wrapper">
    <Left :summaryData="summaryData"> </Left>
    <Right :summaryData="summaryData"> </Right>
  </div>
</template>
<script>
  import Left from "./Left.vue";
  import Right from "./Right.vue";
  export default {
    components: { Left, Right },
    props: ["summaryData"],
    data() {
      return {};
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {},
    watch: {},
  };
</script>
<style scoped lang="scss">
  .chartsLeft-wrapper {
    flex: 532;
    height: 532px;
    width: 100%;
    background-color: #f7faf9;
    margin-top: 8px;
    display: flex;
  }
</style>
