<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="车牌号" prop="plateNumber">
              <el-select v-model="ruleForm.plateNumber" placeholder="请选择车牌号" clearable filterable>
                <el-option v-for="item in carOptions" :key="item.id" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="经办人" prop="operatorName">
              <el-input
                v-model="ruleForm.operatorName"
                placeholder="请输入经办人"
                clearable
                :maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="报废单位" prop="organizationName">
              <el-input
                v-model="ruleForm.organizationName"
                placeholder="请输入报废单位"
                clearable
                :maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="报废金额(元)" prop="costs">
              <el-input-number v-model="ruleForm.costs" :precision="2" :min="0" :max="99999999999999"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="报废日期" prop="discardTime">
              <el-date-picker
                v-model="ruleForm.discardTime"
                type="date"
                placeholder="请选择报废日期"
                align="right"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="24">
            <el-form-item label="备注" prop="memo">
              <el-input
                type="textarea"
                placeholder="请输入备注"
                v-model="ruleForm.memo"
                :maxlength="50"
                show-word-limit
                :autosize="{ minRows: 4 }"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="凭证" prop="vehicleFileList">
              <FileUpload @uploadChange="uploadChangeFront" :imageList="imageList">
                <template #tips><el-tag type="warning">请上传车辆报废凭证</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import FileUpload from "@/components/FileUpload";
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  export default {
    components: { FileUpload },
    props: {
      recordId: {
        type: String,
        default: "",
      },
      positionOptions: {
        type: Array,
        default: () => [],
      },
      roleOptions: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        ruleForm: {
          plateNumber: null,
          operatorName: "",
          organizationName: null,
          costs: null,
          operatorId: null,
          discardTime: null,
          memo: null,
          vehicleFileList: null,
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号", trigger: "change" }],
          organizationName: [{ required: true, message: "请输入报废单位", trigger: "blur" }],
          costs: [{ required: true, message: "请输入报废金额", trigger: "change" }],
          operatorName: [{ required: true, message: "请输入经办人", trigger: "change" }],
          discardTime: [{ required: true, message: "请选择报废日期", trigger: "change" }],
          vehicleFileList: [{ required: true, message: "请上传车辆报废凭证", trigger: "change" }],
        },
        options: [], //加油类型
        apis: {
          create: "/api/vehicleDiscard/create",
          update: "/api/vehicleDiscard/update",
          inofo: "/api/vehicleDiscard/get/",
          carList: "/api/vehicle/dossier/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [],
        carOptions: [],
        pickerOptions: {
          disabledDate: (time) => time.getTime() > new Date(new Date().setHours(23, 59, 59)),
        },
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptions();
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({ statusList: [0, 1] }, this.apis.carList)];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
      },
      uploadChangeFront(fileList) {
        this.ruleForm.vehicleFileList = fileList.map((list) => {
          return {
            name: list.name,
            size: list.size,
            ...list.file,
          };
        });
        this.$refs.ruleForm.validateField("fileList");
      },
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.inofo);
        if (res.success) {
          if (res.data.vehicleFileList) {
            this.imageList = res.data.vehicleFileList.map((i) => ({
              url: i.url,
              file: i,
            }));
          }
          this.ruleForm = res.data;
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = this.recordId
                ? await updateApiFun(this.ruleForm, this.apis.update)
                : await createApiFun(this.ruleForm, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}报废记录成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
  }
  ::v-deep .el-transfer-panel__body {
    height: 400px;
  }
  .w400 {
    width: 400px;
  }

  .w200 {
    width: 200px;
  }
</style>
