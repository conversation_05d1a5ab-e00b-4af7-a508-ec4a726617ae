<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="增加收运点位" :visible.sync="dialogVisible" width="60%" destroy-on-close @open="initData">
      <div v-loading="loading">
        <header class="header">
          <div class="header-input">
            <el-input
              class="w250"
              v-model="filterForm.keyword"
              placeholder="请输入点位编号/名称/点位联系人名称"
              clearable
            ></el-input>
          </div>
          <el-form ref="filterForm" :model="filterForm" label-suffix=":" label-width="80px" class="mr-10">
            <el-form-item label="点位类型">
              <el-select v-model="filterForm.type" placeholder="请选择点位类型" clearable filterable>
                <el-option v-for="(item, index) in POINT_TYPE" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
        </header>
        <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" border>
          <el-table-column type="index" align="center"></el-table-column>
          <el-table-column prop="code" label="点位编号" align="center"></el-table-column>
          <el-table-column prop="name" label="点位名称" align="center"></el-table-column>
          <el-table-column prop="contact" label="联系人名称" align="center"></el-table-column>
          <el-table-column prop="contactPhone" label="联系方式" align="center"></el-table-column>
          <el-table-column prop="type" label="点位类型" align="center">
            <template #default="{ row }">{{ POINT_TYPE[row.type] }}</template>
          </el-table-column>
          <el-table-column prop="baggingMethod" label="点位收运方式" align="center">
            <template #default="{ row }">{{ POINT_RECEIVING_METHOD[row.baggingMethod] }}</template>
          </el-table-column>
          <el-table-column prop="routeName" label="所属路线" align="center"></el-table-column>
          <el-table-column min-width="80" label="操作" align="center">
            <template #default="{ row }">
              <template v-if="(!row.routeName && row.routeName !== 0) || selectedPointIds.includes(row.id)">
                <el-link type="danger" @click="deletePoint(row)" v-if="pointIds.includes(row.id)">取消选择</el-link>
                <el-link type="primary" @click="selectPoint(row)" v-else>选择</el-link>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import { POINT_TYPE, POINT_RECEIVING_METHOD } from "@/enums";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      pointArray: {
        type: Array,
        default: () => [],
      },
      selectedPointIds: {
        type: Array,
        default: () => [],
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
      pointIds() {
        return this.pointArray.map((list) => list.id);
      },
    },
    data() {
      return {
        loading: false,
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/pickup/pickupPoint/listPage",
        },
        POINT_TYPE,
        POINT_RECEIVING_METHOD,
      };
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          ...this.filterForm,
          isUndock: 0,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas.map((item) => {
            return {
              ...item,
              contactPhone: item.contactPhone ? this.$sm2Decrypt(item.contactPhone) : "",
            };
          });
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 选择人员
      selectPoint(row) {
        this.$emit("selectPoint", row);
      },
      // 删除人员
      deletePoint(row) {
        this.$emit("deletePoint", row);
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .header-input {
      margin-right: 10px;
    }
  }
  .w250 {
    width: 250px;
  }
  ::v-deep .header .el-form-item {
    margin-bottom: 0;
  }
</style>
