<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" label-suffix="：">
        <el-row>
          <el-col>
            <el-form-item label="评价项名称" prop="name">
              <el-input
                v-model="ruleForm.name"
                placeholder="请输入评价项名称"
                clearable
                maxlength="255"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="类型" prop="type">
              <el-select v-model="ruleForm.type" placeholder="请选择类型" clearable filterable>
                <el-option v-for="(item, index) in EVALUATE_TYPE" :key="index" :label="item" :value="index">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="排序号" prop="sort">
              <el-input-number v-model="ruleForm.sort" :min="0" :max="99"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  import { EVALUATE_TYPE } from "@/enums";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        EVALUATE_TYPE,
        ruleForm: {
          name: "", //评价项名称
          type: "", //类型
          sort: "", //排序号
        },
        rules: {
          name: [{ required: true, message: "请输入评价项名称", trigger: "blur" }],
          type: [{ required: true, message: "请选择类型", trigger: "change" }],
        },
        apis: {
          create: "/api/vehicle/evaluationConfig/create",
          update: "/api/vehicle/evaluationConfig/update",
          info: "/api/vehicle/evaluationConfig/get/",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [],
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = this.recordId
                ? await updateApiFun(this.ruleForm, this.apis.update)
                : await createApiFun(this.ruleForm, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      uploadChangeFileList(fileList) {
        if (fileList.length > 0) {
          this.ruleForm.fileList = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
        } else {
          this.ruleForm.fileList = [];
        }
        this.$refs.ruleForm.validateField("fileList");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
  }
  ::v-deep .el-transfer-panel__body {
    height: 400px;
  }
</style>
