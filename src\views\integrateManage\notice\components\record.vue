<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" label-suffix="：">
        <el-row>
          <el-col :span="12">
            <el-form-item label="新闻名称" prop="title">
              <el-input
                v-model="ruleForm.title"
                placeholder="请输入新闻名称"
                clearable
                maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="通知类型" prop="noticeType">
              <el-select v-model="ruleForm.noticeType" placeholder="请选择通知类型" clearable filterable>
                <el-option v-for="(item, index) in NOTICE_TYPE" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="ruleForm.noticeType === 1">
          <el-row>
            <el-col :span="12">
              <el-form-item label="开始日期" prop="startTime">
                <el-date-picker
                  v-model="ruleForm.startTime"
                  type="date"
                  placeholder="请选择开始日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="结束日期" prop="endTime">
                <el-date-picker
                  v-model="ruleForm.endTime"
                  type="date"
                  placeholder="请选择结束日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="内容" prop="content">
                <Tinymce
                  v-model="ruleForm.content"
                  :ossBase="ossBase"
                  height="500px"
                  :hasImage="true"
                  :isCompress="false"
                ></Tinymce>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
        <el-row v-else-if="ruleForm.noticeType === 0">
          <el-col :span="12">
            <el-form-item label="关联公众号链接" prop="link">
              <el-input v-model="ruleForm.link" placeholder="请输入关联公众号链接" clearable></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun } from "@/api/base";
  import { NOTICE_TYPE } from "@/enums";
  import { ossBaseParameter } from "@/api/common";
  const Tinymce = () => import("logan-common/Tinymce");
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      Tinymce,
    },
    data() {
      return {
        NOTICE_TYPE,
        ruleForm: {
          title: "", //新闻名称
          noticeType: "", //通知类型
          link: "", //关联公众号链接
          startTime: "", //开始日期
          endTime: "", //结束日期
          content: "", //内容
        },
        rules: {
          title: [{ required: true, message: "请输入新闻名称", trigger: "blur" }],
          noticeType: [{ required: true, message: "请选择通知类型", trigger: "change" }],
          link: [{ required: true, message: "请输入关联公众号链接", trigger: "blur" }],
          startTime: [{ required: true, message: "请选择开始日期", trigger: "change" }],
          endTime: [{ required: true, message: "请选择结束日期", trigger: "change" }],
          content: [{ required: true, message: "请输入内容", trigger: "blur" }],
        },
        apis: {
          create: "/api/notice/save",
          update: "/api/notice/update",
          info: "/api/notice/get/",
        },
        saveRecordThrottling: () => {},
        loading: false,
        //阿里云上传参数
        ossBase: {},
      };
    },
    async created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
      await this.findOssBaseParameter();
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      //查找oss参数配置
      async findOssBaseParameter() {
        let res = await ossBaseParameter();
        let data = "";
        if (res.success) {
          try {
            data = JSON.parse(this.$sm2Decrypt(res.data));
          } catch (error) {
            data = {};
          }
        }
        data["bucket"] = data.bucketName;
        data["stsToken"] = data.stsSecurityToken;
        this.ossBase = data;
      },
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = this.recordId
                ? await createApiFun(this.ruleForm, this.apis.update)
                : await createApiFun(this.ruleForm, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}公司新闻成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
  }
  ::v-deep .el-transfer-panel__body {
    height: 400px;
  }
</style>
