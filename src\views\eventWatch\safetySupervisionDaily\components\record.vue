<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">{{ moment(recordDate).format("YYYY年MM月DD日") }}安全监管日报</div>
      <baseTitle title="驾驶状态异常信息"></baseTitle>
      <totalBox title="驾驶状态异常总数" :num="personAbnormalTotal"></totalBox>
      <div class="table-title">各类型异常统计</div>
      <div class="table-box">
        <el-table :data="drivingConditionList" :header-cell-style="{ background: '#F5F7F9' }" border>
          <el-table-column prop="plateNumber" label="车牌号" align="center"></el-table-column>
          <el-table-column prop="dateTime" label="上报时间" align="center"></el-table-column>
          <el-table-column prop="longitude" label="经纬度" align="center">
            <template #default="{ row }">
              <el-link @click="checkLocation(row)">{{ row.longitude }} {{ row.latitude }}</el-link>
            </template>
          </el-table-column>
          <el-table-column prop="fatigueDegree" label="疲劳程度" align="center">
            <template #default="{ row }">{{ FATIGUE_DEGREE[row.fatigueDegree] }}</template>
          </el-table-column>
          <el-table-column prop="level" label="报警级别" align="center">
            <template #default="{ row }">{{ row.level }}级报警</template>
          </el-table-column>
          <el-table-column prop="speed" label="车速" align="center"></el-table-column>
          <el-table-column prop="type" label="异常类型" align="center">
            <template #default="{ row }">{{ EXCEPT_DANGER_TYPE[row.type] }}</template>
          </el-table-column>
        </el-table>
      </div>
      <baseTitle class="mt-60" title="主动上报异常信息"></baseTitle>
      <totalBox title="主动上报异常总数" :num="activeReportAbnormalTotal"></totalBox>
      <div class="table-box">
        <el-table :data="activelyReportList" :header-cell-style="{ background: '#F5F7F9' }" border>
          <el-table-column prop="exceptionType" label="异常类型" align="center">
            <template #default="{ row }">{{ ERROR_STATUS[row.exceptionType] }}</template>
          </el-table-column>
          <el-table-column prop="reportingTime" label="上报时间" align="center"></el-table-column>
          <el-table-column prop="reportPerson" label="上报人" align="center"></el-table-column>
          <el-table-column prop="continueCarrying" label="是否可以继续收运" align="center">
            <template #default="{ row }">{{ CONTINUECARRYING_STATUS_MAP[row.continueCarrying] }}</template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="{ row }">
              <el-link type="primary" @click="checkRecord(row)">查看详情</el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
    </div>
    <activelyReportDialog :value.sync="showDialog" :recordId="activelyReportId"></activelyReportDialog>
    <pointRecord :value.sync="showPointDialog" :formItem="formItem"></pointRecord>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import totalBox from "./totalBox";
  import activelyReportDialog from "./activelyReportDialog";
  import { ERROR_STATUS, CONTINUECARRYING_STATUS_MAP, EXCEPT_DANGER_TYPE, FATIGUE_DEGREE } from "@/enums";
  import moment from "moment";
  import pointRecord from "@/views/eventWatch/errorManager/components/pointRecord.vue";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
      recordDate: {
        type: String,
        default: "",
      },
    },
    components: {
      baseTitle,
      totalBox,
      activelyReportDialog,
      pointRecord,
    },
    data() {
      return {
        apis: {
          info: "/api/safety/get/",
        },
        loading: false,
        drivingConditionList: [],
        activelyReportList: [],
        ERROR_STATUS,
        CONTINUECARRYING_STATUS_MAP,
        EXCEPT_DANGER_TYPE,
        showDialog: false,
        activelyReportId: "",
        moment,
        personAbnormalTotal: 0,
        activeReportAbnormalTotal: 0,
        formItem: {},
        showPointDialog: false,
        FATIGUE_DEGREE,
      };
    },
    mounted() {
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取详情
      async getRecord() {
        try {
          let res = await getInfoApiFun(this.recordId, this.apis.info);
          if (res.success) {
            this.personAbnormalTotal = res.data.personAbnormalTotal;
            this.activeReportAbnormalTotal = res.data.activeReportAbnormalTotal;
            this.drivingConditionList = res.data.driverAbnormalList;
            this.activelyReportList = res.data.activeReportAbnormalList;
          }
        } catch (error) {
          console.log(error);
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 查看详情
      checkRecord(row) {
        this.activelyReportId = row.id;
        this.showDialog = true;
      },
      // 查看定位
      checkLocation(row) {
        this.formItem = row;
        this.showPointDialog = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .card-header {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
  }
  .table-title {
    font-size: 16px;
  }
  .table-box {
    margin-top: 20px;
  }
  .record-footer {
    padding-top: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .mt-60 {
    margin-top: 60px;
  }
</style>
