<template>
  <div class="point">
    <MainTitle title="点位详细信息" class="point-title"></MainTitle>
    <div class="point-inner" :class="{ 'point-inner2': pointData.monitorWaybillDetailPoint }">
      <div class="point-item">
        <span class="title">点位名称：</span>
        <span class="value">{{ pointData.name }}</span>
      </div>
      <div class="flex-between">
        <div class="point-item">
          <span class="title">点位联系人：</span>
          <span class="value">{{ pointData?.contact || "--" }}</span>
        </div>
        <div class="point-item">
          <span class="title">联系方式：</span>
          <span class="value">{{ pointData?.contactPhone || "--" }}</span>
        </div>
      </div>
      <div class="point-item">
        <span class="title">地址：</span>
        <span class="value">{{ pointData.address || "--" }}</span>
      </div>
      <div class="flex-between">
        <div class="point-item">
          <span class="title">实际收运人：</span>
          <span class="value">{{ pointData?.waybillUserName ? pointData?.waybillUserName[0] : "--" }}</span>
        </div>
        <div class="point-item">
          <span class="title">联系方式：</span>
          <span class="value">{{ pointData?.waybillUserPhone ? pointData?.waybillUserPhone[0] : "--" }}</span>
        </div>
      </div>
      <div class="flex-between" v-if="pointData.monitorWaybillDetailPoint">
        <div class="point-item">
          <span class="title">实际收运人：</span>
          <span class="value">{{ pointData?.waybillUserName ? pointData?.waybillUserName[1] : "--" }}</span>
        </div>
        <div class="point-item">
          <span class="title">联系方式：</span>
          <span class="value">{{ pointData?.waybillUserPhone ? pointData?.waybillUserPhone[1] : "--" }}</span>
        </div>
      </div>

      <div class="flex-between">
        <div class="point-item">
          <span class="title">收运状态：</span>
          <span class="value" v-if="pointData.waybillTime">{{ pointData.hasWaybill == 0 ? "未收运" : "已收运" }}</span>
          <span
            class="value"
            v-else-if="pointData.monitorWaybillDetailPoint && pointData.monitorWaybillDetailPoint.waybillTime"
            >{{ pointData.monitorWaybillDetailPoint.hasWaybill == 0 ? "未收运" : "已收运" }}</span
          >
          <span class="value" v-else>未收运</span>
        </div>
        <div class="point-item">
          <span class="title">收运方式：</span>
          <span class="value" v-if="pointData.waybillTime">{{ WAYBILL_TYPE[pointData?.baggingMethod] }}</span>
          <span
            class="value"
            v-else-if="pointData.monitorWaybillDetailPoint && pointData.monitorWaybillDetailPoint.waybillTime"
            >{{ WAYBILL_TYPE[pointData.monitorWaybillDetailPoint?.baggingMethod] }}</span
          >
          <span class="value" v-else>--</span>
        </div>
      </div>
      <div class="flex-between">
        <div class="point-item">
          <span class="title">收运重量：</span>
          <span class="value">{{ pointData.hasWaybill == 1 ? pointData?.rubbishTotal + " (kg)" : "--" }}</span>
        </div>
        <div class="point-item">
          <span class="title">收运时间：</span>
          <span class="value" v-if="pointData.waybillTime">{{ pointData?.waybillTime }}</span>
          <span
            class="value"
            v-else-if="pointData.monitorWaybillDetailPoint && pointData.monitorWaybillDetailPoint.waybillTime"
            >{{ pointData.monitorWaybillDetailPoint?.waybillTime }}</span
          >

          <span class="value" v-else>--</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import MainTitle from "@/components/mainTitle/index.vue";

  import { WAYBILL_TYPE } from "@/enums/index";
  export default {
    components: {
      MainTitle,
    },

    props: {
      pointData: {
        type: Object,
        default: () => {
          return {
            // contact: "张三",
            // contactPhone: "123456789",
            // hasWaybill: 0,
            // way: 0,
            // weight: "100kg",
            // waybillTime: "2022-01-01 12:00:00",
          };
        },
      },

      pointName: {
        type: String,
        default: "点位名称",
      },
    },

    data() {
      return {
        WAYBILL_TYPE: WAYBILL_TYPE,
      };
    },

    created() {},

    mounted() {},

    methods: {},
  };
</script>
<style scoped lang="scss">
  .point {
    color: #fff;
    padding: 18px 0;
    height: 300px;
    display: flex;
    flex-direction: column;
    .point-inner {
      flex: 1;
      margin-top: 16px;
    }
    .point-inner2 {
      margin-top: 3px;
    }

    .point-title {
      padding-bottom: 20px;
    }
    .point-item {
      padding: 6px 8px;
      font-size: 16px;
      flex: 1;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      .value {
        font-weight: 600;
      }
    }
  }

  .flex-between {
    display: flex;
    justify-content: space-between;
  }

  .mt-16 {
    margin-top: 16px;
  }
</style>
