<template>
  <div class="fixed-pagination" :class="[flexClass]">
    <el-pagination
      :hide-on-single-page="false"
      background
      @size-change="sizeChange"
      @current-change="pageNoChange"
      :page-sizes="pageSizes"
      :layout="layout"
      :pager-count="maxCount"
      :total="page.total"
      :page-size="page.pageSize"
      :current-page="page.pageNo"
    >
    </el-pagination>
  </div>
</template>

<script>
  export default {
    name: "pagination",
    props: {
      page: {
        default: () => ({
          total: 0,
          pageSize: 10,
          pageNo: 1,
        }),
      },
      maxCount: {
        type: Number,
        default: 11,
      },
      total: {
        type: [Number, String],
        default: 0,
      },
      layout: {
        type: String,
        default: "total, sizes, prev, pager, next, jumper",
      },
      flexClass: {
        type: String,
        default: "flex-center-end",
      },
    },
    data() {
      return {
        pageSizes: [10, 20, 30],
      };
    },
    methods: {
      //分页size改变
      sizeChange(pageSize) {
        this.$emit("pageChange", {
          ...this.page,
          pageSize,
        });
      },
      //分页页码改变
      pageNoChange(pageNo) {
        this.$emit("pageChange", {
          ...this.page,
          pageNo,
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .fixed-pagination {
    background: #ffffff;
    z-index: 99;
    padding: 16px 16px 0 16px;
  }
  .flex-center-start {
    display: flex;
    justify-content: flex-start;
  }
  .flex-center {
    display: flex;
    justify-content: center;
  }
  .flex-center-end {
    display: flex;
    justify-content: flex-end;
  }
</style>
