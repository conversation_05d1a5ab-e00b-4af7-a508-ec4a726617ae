<template>
  <div style="display: flex; flex: 1">
    <div style="flex: 2; padding-bottom: 10px" :id="eChartsId"></div>
    <div style="margin-top: 20px; flex: 1" :id="eChartsId + '2'"></div>
    <!-- <div>已收点位{{ collectNum }}</div> -->
    <!-- <div>未收点位{{ unCollectNum }}</div> -->
    <!-- <div>总数{{ total }}</div> -->
    <!-- <div>收运率{{ collectNum / total }}</div> -->
  </div>
</template>
<script>
  import * as echarts from "echarts";

  export default {
    components: {},
    props: ["collectNum", "unCollectNum", "total", "eChartsId", "title"],
    data() {
      return {
        seriesData: 0,
      };
    },
    computed: {},
    created() {},
    mounted() {
      this.dataCb();
      this.init();
      this.init2();
    },
    methods: {
      dataCb() {
        this.getSeriesData();
      },
      getSeriesData() {
        if (this.total == 0) {
          this.seriesData = 0;
        } else {
          let num = (this.collectNum / this.total).toFixed(2);
          num = (num * 100).toFixed(2);
          num = parseFloat(num);
          this.seriesData = num;
        }
      },
      init() {
        let chartData = [
          {
            name: "已收点位",
            value: this.collectNum,
          },
          {
            name: "未收点位",
            value: this.unCollectNum,
          },
        ];
        var myChart = echarts.init(document.getElementById(this.eChartsId));
        myChart.setOption({
          color: ["#1A9CFF", "#A6BEFE"],
          title: {
            text: this.title,
            textStyle: {
              color: "#414D5A",
              fontStyle: "normal",
              fontWeight: "bold",
              fontFamily: "Microsoft YaHei",
              fontSize: "14",
              lineHeight: "20",
            },
            left: "22",
          },
          legend: {
            selectedMode: false,

            orient: "vertical",
            left: "20",
            top: "center",
            itemWidth: 8,
            itemHeight: 8,
            data: [
              { name: "已收点位", icon: "rect" },
              { name: "未收点位", icon: "rect" },
            ],
            textStyle: {
              rich: {
                title: {
                  fontSize: 14,
                  color: "#414D5A",
                },
                value: {
                  fontSize: 14,
                  color: "#414D5A",
                  padding: [0, 0, 0, 10],
                },
              },
            },
            formatter: function (val) {
              return "{title|" + val + "}" + "{value|" + chartData.find((item) => item.name === val).value + "}";
            },
          },
          series: [
            {
              center: ["70%", "60%"],
              name: "收运情况",
              type: "pie",
              emphasis: {
                // 取消放大效果
                scale: 0,
                // 可以设置其他悬停时的样式，比如标签显示
                label: {
                  show: false,
                },
              },
              radius: ["55%", "75%"],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: "center",
              },
              data: [
                { value: this.collectNum, name: "已收点位" },
                {
                  value: this.unCollectNum,
                  name: "",
                  itemStyle: {
                    color: "transparent",
                  },
                },
              ],
            },
            {
              center: ["70%", "60%"],
              name: "收运情况",
              type: "pie",
              emphasis: {
                // 取消放大效果
                scale: 0,
                // 可以设置其他悬停时的样式，比如标签显示
                label: {
                  show: false,
                },
              },
              radius: ["60%", "70%"],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: "center",
              },
              data: [
                {
                  value: this.collectNum,
                  name: "",
                  itemStyle: {
                    color: "transparent",
                  },
                },
                { value: this.unCollectNum, name: "未收点位" },
              ],
            },
            {
              center: ["70%", "60%"],
              type: "pie",
              selectedMode: "single",
              radius: ["0%", "20%"],
              silent: true,
              clockwise: false,
              label: {
                position: "center",
                formatter: `{value|${this.total}}\n {name|总数} `,
                rich: {
                  name: {
                    fontSize: 14,
                  },
                  value: {
                    fontSize: 26,
                    fontWeight: 500,
                    padding: [10, 0, 0, 0],
                    fontFamily: "PangMenZhengDao",
                  },
                },
              },
              itemStyle: {
                normal: {
                  color: "#fff",
                },
              },
              labelLine: {
                show: false,
              },
              data: [{ value: 0 }],
              right: "0%",
            },
          ],
        });
      },
      init2() {
        let myChart = echarts.init(document.getElementById(this.eChartsId + "2"));
        let value = this.seriesData;
        let max = 100;
        let option = {
          angleAxis: {
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            min: 0,
            max: 133.33,
            startAngle: 225,
          },
          radiusAxis: {
            type: "category",
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
          },
          polar: {
            radius: "100%", // 外环大小
          },
          series: [
            {
              type: "bar",
              data: [0, 0, value],
              z: 1,
              coordinateSystem: "polar",
              barMaxWidth: 10,
              name: "收运率",
              roundCap: true,
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                {
                  offset: 0,
                  color: "#57A9FB",
                },
                {
                  offset: 1,
                  color: "#57A9FB",
                },
              ]),
              barGap: "-100%", // 不同系列的柱间距离，为百分比 如果想要两个系列的柱子重叠，可以设置 barGap 为 '-100%'。这在用柱子做背景的时候有用。
            },
            {
              type: "bar",
              data: [0, 0, max],
              z: 0,
              // silent: true,
              coordinateSystem: "polar",
              barMaxWidth: 10,
              name: "C",
              roundCap: true,
              color: "#E8F7FF",
              barGap: "-100%",
            },
            {
              type: "gauge",
              radius: "90%", // 刻度大小
              splitNumber: 19,
              max: 100,
              detail: {
                show: false,
              },
              axisLine: {
                // 坐标轴线
                lineStyle: {
                  // 属性lineStyle控制线条样式
                  color: [
                    [0, "#9FD4FD"],
                    [1, "#9FD4FD"],
                  ],
                  width: 2,
                  opacity: 0, //刻度背景宽度
                },
              },
              data: [
                {
                  name: "",
                  value: value,
                },
              ],
              splitLine: {
                length: 0, //长刻度节点线长度
              },
              axisTick: {
                show: true,
                lineStyle: {
                  color: "#9FD4FD",
                  width: 2,
                },
                length: 8,
                splitNumber: 1,
              },
              axisLabel: {
                show: false,
              },
              pointer: {
                show: false,
              },
            },
            {
              center: ["50%", "50%"],
              type: "pie",
              selectedMode: "single",
              radius: ["0%", "20%"],
              silent: true,
              clockwise: false,
              label: {
                position: "center",
                formatter: `{value|${this.seriesData}%}\n {name|收运率} `,
                rich: {
                  name: {
                    fontSize: 14,
                  },
                  value: {
                    fontSize: 16,
                    fontWeight: 500,
                    padding: [10, 0, 0, 0],
                    fontFamily: "PangMenZhengDao",
                  },
                },
              },
              itemStyle: {
                normal: {
                  color: "#fff",
                },
              },
              labelLine: {
                show: false,
              },
              data: [{ value: 0 }],
              right: "0%",
            },
          ],
          tooltip: {
            show: false,
          },
        };
        myChart.setOption(option);
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss"></style>
