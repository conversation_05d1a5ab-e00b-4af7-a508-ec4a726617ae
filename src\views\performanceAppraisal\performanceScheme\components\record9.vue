<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">加班绩效规则</div>
      <baseTitle title="绩效明细信息"></baseTitle>
      <el-table :data="tableData" border style="width: 100%" class="table-data" :span-method="objectSpanMethod">
        <el-table-column prop="role" label="角色" align="center"></el-table-column>
        <el-table-column label="工作日（元/天）" align="center">
          <template #default="{ row }">
            <el-input-number
              v-model="ruleForm[row.workKey]"
              :min="0"
              :max="99999999"
              :step="1"
              step-strictly
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="周末（元/天）" align="center">
          <template #default="{ row }">
            <el-input-number
              v-model="ruleForm[row.weekKey]"
              :min="0"
              :max="99999999"
              :step="1"
              step-strictly
            ></el-input-number>
          </template>
        </el-table-column>
        <el-table-column label="法定节假日（元/天）" align="center">
          <el-table-column label="基准工资" align="center">
            <template #default="{ row }">
              <el-input-number
                v-model="ruleForm[row.holidayKey]"
                :min="0"
                :max="99999999"
                :step="1"
                step-strictly
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="倍数" align="center">
            <template #default="{ row }">
              <el-input-number
                v-if="row.role === '司机'"
                v-model="ruleForm.holidayMultiple"
                :min="0"
                :max="99999999"
                :step="1"
                step-strictly
              ></el-input-number>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";
  import { createApiFun, updateApiFun } from "@/api/base";
  export default {
    components: {
      baseTitle,
    },
    props: {
      ruleCode: {
        type: String,
        default: "",
      },
      ruleIndex: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        loading: false,
        spanArr: [],
        pos: 0,
        tableData: [
          {
            role: "司机",
            workKey: "normalDriverPrice",
            weekKey: "weekDriverPrice",
            holidayKey: "holidayDriverPrice",
          },
          {
            role: "押运工",
            workKey: "normalCargoPrice",
            weekKey: "holidayCargoPrice",
            holidayKey: "holidayCargoPrice",
          },
        ],
        ruleForm: {},
        apis: {
          info: "/api/assess/scheme/set/findByRuleCode",
          save: "/api/assess/scheme/set/updateOvertimeSet",
        },
        saveRecordThrottling: () => {},
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      objectSpanMethod({ rowIndex, columnIndex }) {
        // 倍数列的合并（最后一列）
        const lastColumnIndex = 4; // 倍数列的索引
        if (columnIndex === lastColumnIndex) {
          if (rowIndex === 0) {
            return {
              rowspan: 2,
              colspan: 1,
            };
          }
          return {
            rowspan: 0,
            colspan: 0,
          };
        }
      },
      // 获取详情
      async getRecord() {
        this.loading = true;
        try {
          let res = await createApiFun({ ruleCode: this.ruleCode }, this.apis.info);
          if (res.success) {
            this.ruleForm = res.data;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
          console.log(error);
        }
      },
      // 取消
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      async saveRecord() {
        this.loading = true;
        try {
          let res = await updateApiFun(this.ruleForm, this.apis.save);
          if (res.success) {
            this.$message.success(`保存成功`);
            this.closeRecord();
            this.$emit("refreshList");
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-data {
    margin-bottom: 20px;
  }
</style>
