<template>
  <div class="micro-app-sctmp_base" v-loading="loading">
    <el-dialog
      title="安全自检记录"
      :visible.sync="dialogVisible"
      width="1400px"
      top="0"
      destroy-on-close
      :modal-append-to-body="false"
      :before-close="closeDialog"
      @open="initData"
    >
      <div class="full-height" v-loading="loading">
        <div class="main-index">
          <!-- <header class="header">
            <div class="header-left mr-10">
              <el-input class="w400" v-model="keyword" placeholder="请输入车牌号/司机姓名" clearable></el-input>
            </div>
            <el-button @click="initData">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
          </header> -->
          <main class="main">
            <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
              <el-table-column type="index" align="center"></el-table-column>
              <el-table-column prop="plateNumber" label="车牌号" align="center"></el-table-column>
              <el-table-column prop="driverName" label="驾驶司机" align="center"></el-table-column>
              <el-table-column prop="inspectDate" label="自检日期" align="center"></el-table-column>
              <el-table-column label="安全自检信息" align="center">
                <el-table-column prop="beforeTime" label="出车前检时间" align="center"></el-table-column>
                <el-table-column prop="beforeStatus" label="出车前检状态" align="center">
                  <template #default="{ row }">{{ EVALUATE_STATUS[row.beforeStatus] }}</template>
                </el-table-column>
                <el-table-column prop="centerTime" label="出车中检时间" align="center"></el-table-column>
                <el-table-column prop="centerStatus" label="出车中检状态" align="center">
                  <template #default="{ row }">{{ EVALUATE_STATUS[row.centerStatus] }}</template>
                </el-table-column>
                <el-table-column prop="afterTime" label="出车后检时间" align="center"></el-table-column>
                <el-table-column prop="afterStatus" label="出车后检状态" align="center">
                  <template #default="{ row }">{{ EVALUATE_STATUS[row.afterStatus] }}</template>
                </el-table-column>
              </el-table-column>
            </el-table>
          </main>
          <Pagination :page="page" @pageChange="pageChange"></Pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import moment from "moment";
  import { EVALUATE_STATUS } from "@/enums";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      secureSize: {
        type: Number,
        default: 0,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        page: {
          pageNo: 1,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/vehicle/inspect/findGroupListPage",
        },
        loading: false,
        keyword: "",
        EVALUATE_STATUS,
      };
    },
    methods: {
      initData() {
        this.keyword = "";
        this.page.pageNo = 1;
        this.getDataList();
      },
      async getDataList() {
        this.loading = true;
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.secureSize,
          keyword: this.keyword,
          inspectBeginTime: moment().format("YYYY-MM-DD"),
          inspectEndTime: moment().format("YYYY-MM-DD"),
          noComplete: 1,
          channelId: this.channelId,
        };
        try {
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = this.secureSize;
          }
          this.loading = false;
        } catch (_) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.getDataList();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.getDataList();
      },
      closeDialog() {
        this.dialogVisible = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w400 {
    width: 400px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
