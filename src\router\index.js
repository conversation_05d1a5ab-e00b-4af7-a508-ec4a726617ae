import Vue from "vue";
import VueRouter from "vue-router";
import { routerPrefix } from "../../config/index";
import store from "@/store";
import { createCustomComponent } from "@/utils";
Vue.use(VueRouter);
const prefix = window.__POWERED_BY_QIANKUN__ ? routerPrefix : "/";
const routes = [
  {
    path: prefix + "/aIAssistant",
    component: createCustomComponent(prefix + "/aIAssistant", () => import("@/views/AI/index.vue")),
    meta: {
      title: "无废AI智能管家",
    },
  },
  {
    path: prefix + "/notBar/operationManagement",
    component: createCustomComponent(prefix + "/operationManagement", () =>
      import("@/views/largeDataScreen/operationManagement/index.vue"),
    ),
    meta: {
      title: "运营管理大屏",
    },
  },
  {
    path: prefix + "/notBar/vehiclesData",
    component: createCustomComponent(prefix + "/vehiclesData", () =>
      import("@/views/largeDataScreen/vehiclesData/index.vue"),
    ),
    meta: {
      title: "车辆管理大屏",
    },
  },
  {
    path: prefix + "/RTMLargeScreenV2",
    component: createCustomComponent(prefix + "/RTMLargeScreenV2", () => import("@/views/homePageV2/index.vue")),
    meta: {
      title: "实时监控",
    },
  },
  {
    path: prefix + "/RTMLargeScreenV3",
    component: createCustomComponent(prefix + "/RTMLargeScreenV3", () => import("@/views/RTMLargeScreenV3/index.vue")),
    meta: {
      title: "医疗废物智慧收运管理平台",
    },
  },
  {
    path: prefix + "/RTMLargeScreenV2Detail",
    component: createCustomComponent(prefix + "/RTMLargeScreenV2Detail", () => import("@/views/homePageV2/Detail.vue")),
    meta: {
      title: "实时监控",
    },
  },
  {
    path: prefix + "/homePage",
    component: createCustomComponent(prefix + "/homePage", () => import("@/views/homePage/index.vue")),
    meta: {
      title: "首页",
    },
  },
  {
    path: prefix + "/",
    component: () => import("@/views/Index.vue"),
    meta: {
      title: "乐庚子应用",
    },
  },
  // 基础数据管理 - 加油类型
  {
    path: prefix + "/refuelingType",
    name: "refuelingType",
    component: createCustomComponent(prefix + "/refuelingType", () =>
      import("@/views/baseData/refuelingType/Index.vue"),
    ),
    meta: {
      title: "加油类型",
    },
  },
  // 基础数据管理 - 职位管理
  {
    path: prefix + "/positionManage",
    name: "positionManage",
    component: createCustomComponent(prefix + "/positionManage", () =>
      import("@/views/baseData/positionManage/Index.vue"),
    ),
    meta: {
      title: "职位管理",
    },
  },
  // 基础数据管理 - 车辆型号
  {
    path: prefix + "/carModel",
    name: "carModel",
    component: createCustomComponent(prefix + "/carModel", () => import("@/views/baseData/carModel/Index.vue")),
    meta: {
      title: "车辆型号",
    },
  },
  // 基础数据管理 - 排放标准
  {
    path: prefix + "/emissionStandard",
    name: "emissionStandard",
    component: createCustomComponent(prefix + "/emissionStandard", () =>
      import("@/views/baseData/emissionStandard/Index.vue"),
    ),
    meta: {
      title: "排放标准",
    },
  },
  // 系统管理 - 人员管理
  {
    path: prefix + "/personManage",
    name: "personManage",
    component: createCustomComponent(prefix + "/personManage", () => import("@/views/userManage/personManage")),
    meta: {
      title: "人员管理",
    },
  },
  // 基础数据管理 - 证件管理
  {
    path: prefix + "/certificate",
    name: "certificate",
    component: createCustomComponent(prefix + "/certificate", () => import("@/views/archives/certificate/Index.vue")),
    meta: {
      title: "证件管理",
    },
  },
  // 车辆管理-车辆加油记录
  {
    path: prefix + "/carRefueling",
    name: "carRefueling",
    component: createCustomComponent(prefix + "/carRefueling", () =>
      import("@/views/carManage/carRefueling/Index.vue"),
    ),
    meta: {
      title: "车辆加油记录",
    },
  },
  // 车辆管理-车辆维保记录
  {
    path: prefix + "/carMaintenance",
    name: "carMaintenance",
    component: createCustomComponent(prefix + "/carMaintenance", () =>
      import("@/views/carManage/carMaintenance/Index.vue"),
    ),
    meta: {
      title: "车辆维保记录",
    },
  },
  // 车辆管理-车辆违章记录
  {
    path: prefix + "/carViolation",
    name: "carViolation",
    component: createCustomComponent(prefix + "/carViolation", () =>
      import("@/views/carManage/carViolation/Index.vue"),
    ),
    meta: {
      title: "车辆违章记录",
    },
  },
  // 车辆管理-车辆报废记录
  {
    path: prefix + "/carDiscard",
    name: "carDiscard",
    component: createCustomComponent(prefix + "/carDiscard", () => import("@/views/carManage/carDiscard/Index.vue")),
    meta: {
      title: "车辆报废记录",
    },
  },
  // 基础数据管理 - 证件管理
  {
    path: prefix + "/vehicles",
    name: "vehicles",
    component: createCustomComponent(prefix + "/vehicles", () => import("@/views/archives/vehicles/Index.vue")),
    meta: {
      title: "车辆管理",
    },
  },
  // 车辆管理 - 车辆年审记录
  {
    path: prefix + "/annualAudit",
    name: "annualAudit",
    component: createCustomComponent(prefix + "/annualAudit", () => import("@/views/carManage/annualAudit")),
    meta: {
      title: "车辆年审记录",
    },
  },
  // 车辆管理 - 车辆投保记录
  {
    path: prefix + "/insure",
    name: "insure",
    component: createCustomComponent(prefix + "/insure", () => import("@/views/carManage/insure")),
    meta: {
      title: "车辆投保记录",
    },
  },
  // 车辆管理 - 车辆状态评价配置
  {
    path: prefix + "/evaluateConfig",
    name: "evaluateConfig",
    component: createCustomComponent(prefix + "/evaluateConfig", () => import("@/views/carManage/evaluateConfig")),
    meta: {
      title: "车辆状态评价配置",
    },
  },
  // 车辆管理 - 车辆状态评价
  {
    path: prefix + "/carEvaluate",
    name: "carEvaluate",
    component: createCustomComponent(prefix + "/carEvaluate", () => import("@/views/carManage/evaluate")),
    meta: {
      title: "车辆状态评价",
    },
  },
  // 系统管理 - 企业管理
  {
    path: prefix + "/enterpriseManage",
    name: "enterpriseManage",
    component: createCustomComponent(prefix + "/enterpriseManage", () => import("@/views/userManage/enterpriseManage")),
    meta: {
      title: "企业管理",
    },
  },
  // 车辆管理 - 车辆进出记录
  {
    path: prefix + "/accessRecord",
    name: "accessRecord",
    component: createCustomComponent(prefix + "/accessRecord", () => import("@/views/carManage/accessRecord")),
    meta: {
      title: "车辆进出记录",
    },
  },
  // 安全管理 - 安全自检记录
  {
    path: prefix + "/selfTest",
    name: "selfTest",
    component: createCustomComponent(prefix + "/selfTest", () => import("@/views/safetyManage/selfTest")),
    meta: {
      title: "安全自检记录",
    },
  },
  // 安全管理 - 安全生产记录
  {
    path: prefix + "/safeProduction",
    name: "safeProduction",
    component: createCustomComponent(prefix + "/safeProduction", () => import("@/views/safetyManage/safeProduction")),
    meta: {
      title: "安全生产记录",
    },
  },
  // 安全管理 - 监督抽查记录
  {
    path: prefix + "/supervisoryTest",
    name: "supervisoryTest",
    component: createCustomComponent(prefix + "/supervisoryTest", () => import("@/views/safetyManage/supervisoryTest")),
    meta: {
      title: "监督抽查记录",
    },
  },
  // 安全管理 - 文件档案管理
  {
    path: prefix + "/documentFile",
    name: "documentFile",
    component: createCustomComponent(prefix + "/documentFile", () => import("@/views/safetyManage/documentFile")),
    meta: {
      title: "文件档案管理",
    },
  },
  // 档案管理 - 智能收集柜档案
  {
    path: prefix + "/intelligentCollection",
    name: "intelligentCollection",
    component: createCustomComponent(prefix + "/intelligentCollection", () =>
      import("@/views/archives/intelligentCollection"),
    ),
    meta: {
      title: "智能收集柜档案",
    },
  },
  // 档案管理 - 客商档案
  {
    path: prefix + "/merchant",
    name: "merchant",
    component: createCustomComponent(prefix + "/merchant", () => import("@/views/archives/merchant")),
    meta: {
      title: "客商档案",
    },
  },
  // 车辆管理 - 车辆行驶记录
  {
    path: prefix + "/drivingRecord",
    name: "drivingRecord",
    component: createCustomComponent(prefix + "/drivingRecord", () => import("@/views/carManage/drivingRecord")),
    meta: {
      title: "车辆行驶记录",
    },
  },
  // 合同管理 - 合同信息
  {
    path: prefix + "/contractInfo",
    name: "contractInfo",
    component: createCustomComponent(prefix + "/contractInfo", () => import("@/views/contractManage/contractInfo")),
    meta: {
      title: "合同信息",
    },
  },
  // 基础数据管理 - 合同状态
  {
    path: prefix + "/contractStatus",
    name: "contractStatus",
    component: createCustomComponent(prefix + "/contractStatus", () => import("@/views/baseData/contractStatus")),
    meta: {
      title: "合同状态",
    },
  },
  // 基础数据管理 - 合同类型
  {
    path: prefix + "/contractType",
    name: "contractType",
    component: createCustomComponent(prefix + "/contractType", () => import("@/views/baseData/contractType")),
    meta: {
      title: "合同类型",
    },
  },
  // 档案管理 - 收运点位档案
  {
    path: prefix + "/receiveShipPoint",
    name: "receiveShipPoint",
    component: createCustomComponent(prefix + "/receiveShipPoint", () => import("@/views/archives/receiveShipPoint")),
    meta: {
      title: "收运点位档案",
    },
  },
  // 档案管理 - 收运路线档案
  {
    path: prefix + "/receiveRoute",
    name: "receiveRoute",
    component: createCustomComponent(prefix + "/receiveRoute", () => import("@/views/archives/receiveRoute")),
    meta: {
      title: "收运路线档案",
    },
  },
  // 综合管理 - 排班管理
  {
    path: prefix + "/scheduleManage",
    name: "scheduleManage",
    component: createCustomComponent(prefix + "/scheduleManage", () =>
      import("@/views/integrateManage/scheduleManage/index.vue"),
    ),
    meta: {
      title: "排班管理",
    },
  },
  // 客服管理 - 回访登记表管理
  {
    path: prefix + "/visitForm",
    name: "visitForm",
    component: createCustomComponent(prefix + "/visitForm", () => import("@/views/serviceManage/visitForm")),
    meta: {
      title: "回访登记表管理",
    },
  },
  // 客服管理 - 回访记录管理
  {
    path: prefix + "/visitRecord",
    name: "visitRecord",
    component: createCustomComponent(prefix + "/visitRecord", () => import("@/views/serviceManage/visitRecord")),
    meta: {
      title: "回访记录管理",
    },
  },
  // 客服管理 - 投诉记录管理
  {
    path: prefix + "/complaintRecord",
    name: "complaintRecord",
    component: createCustomComponent(prefix + "/complaintRecord", () =>
      import("@/views/serviceManage/complaintRecord"),
    ),
    meta: {
      title: "投诉记录管理",
    },
  },
  // 客服管理 - 服务评价记录管理
  {
    path: prefix + "/serviceEvaluationRecord",
    name: "serviceEvaluationRecord",
    component: createCustomComponent(prefix + "/serviceEvaluationRecord", () =>
      import("@/views/serviceManage/serviceEvaluationRecord"),
    ),
    meta: {
      title: "服务评价记录管理",
    },
  },
  // 档案管理 - 历史收运路线档案
  {
    path: prefix + "/historyRoute",
    name: "historyRoute",
    component: createCustomComponent(prefix + "/historyRoute", () => import("@/views/archives/historyRoute")),
    meta: {
      title: "历史收运路线档案",
    },
  },
  // 事件监测 - 告警事项
  {
    path: prefix + "/warningEvent",
    name: "warningEvent",
    component: createCustomComponent(prefix + "/warningEvent", () => import("@/views/eventWatch/warningEvent")),
    meta: {
      title: "告警事项",
    },
  },
  // 事件监测 - 告警事项
  {
    path: prefix + "/errorManager",
    name: "errorManager",
    component: createCustomComponent(prefix + "/errorManager", () => import("@/views/eventWatch/errorManager")),
    meta: {
      title: "异常事件上报管理",
    },
  },
  // 收运管理 - 电子收运单管理
  {
    path: prefix + "/electronicWaybill",
    name: "electronicWaybill",
    component: createCustomComponent(prefix + "/electronicWaybill", () =>
      import("@/views/collectTransportManage/electronicWaybill"),
    ),
    meta: {
      title: "电子收运单管理",
    },
  },
  // 收运管理 - 电子收运单审核
  {
    path: prefix + "/electronicWaybillAudit",
    name: "electronicWaybillAudit",
    component: createCustomComponent(prefix + "/electronicWaybillAudit", () =>
      import("@/views/collectTransportManage/electronicWaybillAudit"),
    ),
    meta: {
      title: "电子收运单审核",
    },
  },
  // 收运管理 - 换班管理
  {
    path: prefix + "/shiftManage",
    name: "shiftManage",
    component: createCustomComponent(prefix + "/shiftManage", () =>
      import("@/views/collectTransportManage/shiftManage"),
    ),
    meta: {
      title: "换班管理",
    },
  },
  // 事件监测 - 待办事项
  {
    path: prefix + "/backlog",
    name: "backlog",
    component: createCustomComponent(prefix + "/backlog", () => import("@/views/eventWatch/backlog")),
    meta: {
      title: "待办事项",
    },
  },
  // 绩效考核 - 考核方案
  {
    path: prefix + "/examineScheme",
    name: "examineScheme",
    component: createCustomComponent(prefix + "/examineScheme", () =>
      import("@/views/performanceAppraisal/examineScheme"),
    ),
    meta: {
      title: "考核方案",
    },
  },
  // 绩效考核 - 考核台账
  {
    path: prefix + "/exmaineLedger",
    name: "exmaineLedger",
    component: createCustomComponent(prefix + "/exmaineLedger", () =>
      import("@/views/performanceAppraisal/exmaineLedger"),
    ),
    meta: {
      title: "考核台账",
    },
  },
  // 绩效考核 - 考核评分
  {
    path: prefix + "/examineMark",
    name: "examineMark",
    component: createCustomComponent(prefix + "/examineMark", () => import("@/views/performanceAppraisal/examineMark")),
    meta: {
      title: "考核评分",
    },
  },
  // 车辆管理 - 车辆称重档案
  {
    path: prefix + "/vehicleWeigh",
    name: "vehicleWeigh",
    component: createCustomComponent(prefix + "/vehicleWeigh", () => import("@/views/carManage/vehicleWeigh")),
    meta: {
      title: "车辆称重档案",
    },
  },
  // 数据大屏跳转首页
  {
    path: prefix + "/largeDataScreen",
    name: "largeDataScreen",
    component: createCustomComponent(prefix + "/largeDataScreen", () => import("@/views/largeDataScreen")),
    meta: {
      title: "数据大屏",
    },
  },
  {
    path: prefix + "/realTimeMonitor",
    name: "realTimeMonitor",
    component: createCustomComponent(prefix + "/realTimeMonitor", () =>
      import("@/views/largeDataScreen/realTimeMonitor"),
    ),
    meta: {
      title: "实时监控大屏",
    },
  },
  {
    path: prefix + "/realTimeDriving",
    name: "realTimeDriving",
    component: createCustomComponent(prefix + "/realTimeDriving", () =>
      import("@/views/largeDataScreen/realTimeDriving"),
    ),
    meta: {
      title: "实时监控大屏",
    },
  },
  // 综合管理 - 工作日历
  {
    path: prefix + "/workCalendar",
    name: "workCalendar",
    component: createCustomComponent(prefix + "/workCalendar", () => import("@/views/integrateManage/workCalendar")),
    meta: {
      title: "工作日历",
    },
  },
  // 收运管理 - 点位收运任务明细
  {
    path: prefix + "/pointTaskDetail",
    name: "pointTaskDetail",
    component: createCustomComponent(prefix + "/pointTaskDetail", () =>
      import("@/views/collectTransportManage/pointTaskDetail"),
    ),
    meta: {
      title: "点位收运任务明细",
    },
  },
  // 事件监测 - 安全监管日报
  {
    path: prefix + "/safetySupervisionDaily",
    name: "safetySupervisionDaily",
    component: createCustomComponent(prefix + "/safetySupervisionDaily", () =>
      import("@/views/eventWatch/safetySupervisionDaily"),
    ),
    meta: {
      title: "安全监管日报",
    },
  },
  // 绩效考核 - 绩效方案配置
  {
    path: prefix + "/performanceScheme",
    name: "performanceScheme",
    component: createCustomComponent(prefix + "/performanceScheme", () =>
      import("@/views/performanceAppraisal/performanceScheme"),
    ),
    meta: {
      title: "绩效方案配置",
    },
  },
  // 收运管理 - 今日收运综合报表
  {
    path: prefix + "/todayReport",
    name: "todayReport",
    component: createCustomComponent(prefix + "/todayReport", () =>
      import("@/views/collectTransportManage/todayReport"),
    ),
    meta: {
      title: "今日收运综合报表",
    },
  },
  // 综合管理 - 公司新闻管理
  {
    path: prefix + "/notice",
    name: "notice",
    component: createCustomComponent(prefix + "/notice", () => import("@/views/integrateManage/notice")),
    meta: {
      title: "公司新闻管理",
    },
  },
  // 收运管理 - 发起收运任务
  {
    path: prefix + "/initShipTask",
    name: "initShipTask",
    component: createCustomComponent(prefix + "/initShipTask", () =>
      import("@/views/collectTransportManage/initShipTask"),
    ),
    meta: {
      title: "发起收运任务",
    },
  },
  // 收运管理 - 收运综合报表
  {
    path: prefix + "/comprehensiveReport",
    name: "comprehensiveReport",
    component: createCustomComponent(prefix + "/comprehensiveReport", () =>
      import("@/views/collectTransportManage/comprehensiveReport"),
    ),
    meta: {
      title: "收运综合报表",
    },
  },
  // 收运管理 - 加班审批
  {
    path: prefix + "/overtimeApproval",
    name: "overtimeApproval",
    component: createCustomComponent(prefix + "/overtimeApproval", () =>
      import("@/views/collectTransportManage/overtimeApproval"),
    ),
    meta: {
      title: "加班审批",
    },
  },
  // 收运管理 - 收运信息变更审核
  {
    path: prefix + "/receiveInfoAudit",
    name: "receiveInfoAudit",
    component: createCustomComponent(prefix + "/receiveInfoAudit", () =>
      import("@/views/collectTransportManage/receiveInfoAudit"),
    ),
    meta: {
      title: "收运信息变更审核",
    },
  },
  // 综合管理 - 短信通知配置
  {
    path: prefix + "/smsConfig",
    name: "smsConfig",
    component: createCustomComponent(prefix + "/smsConfig", () => import("@/views/integrateManage/smsConfig")),
    meta: {
      title: "短信通知配置",
    },
  },
  // 综合管理 - 运营信息报表
  {
    path: prefix + "/operateInfoReport",
    name: "operateInfoReport",
    component: createCustomComponent(prefix + "/operateInfoReport", () =>
      import("@/views/integrateManage/operateInfoReport"),
    ),
    meta: {
      title: "运营信息报表",
    },
  },
];

const originalReplace = VueRouter.prototype.replace;
VueRouter.prototype.replace = function replace(location) {
  return originalReplace.call(this, location).catch((err) => err);
};

const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};

const createRouter = () =>
  new VueRouter({
    mode: "hash",
    routes: routes,
  });

const router = createRouter();
router.beforeEach(async (to, from, next) => {
  if (to.meta?.title) {
    document.title = to.meta.title;
  }
  store.dispatch("clearCancel");
  next();
});

export default router;
