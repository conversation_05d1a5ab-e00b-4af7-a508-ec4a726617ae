<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" label-suffix="：">
        <el-row>
          <el-col :span="12">
            <el-form-item label="模板编码" prop="templateCode">
              <el-input
                v-model="ruleForm.templateCode"
                placeholder="请输入模板编码"
                clearable
                maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="模板名称" prop="templateName">
              <el-input
                v-model="ruleForm.templateName"
                placeholder="请输入模板名称"
                clearable
                maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="备注" prop="memo">
              <el-input
                type="textarea"
                v-model="ruleForm.memo"
                placeholder="请输入备注"
                :rows="6"
                maxlength="500"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="用户列表">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" border>
            <el-table-column prop="fullName" label="姓名" align="center"> </el-table-column>
            <el-table-column prop="phone" label="联系电话" align="center">
              <template #default="{ row }">{{ row.phone.replace(row.phone.substring(3, 7), "****") }}</template>
            </el-table-column>
            <el-table-column prop="userIdentity" label="用户身份" align="center">
              <template #default="{ row }">
                <span v-for="(item, index) in row.userIdentity.split(',')" :key="item">
                  <span>{{ USER_IDENTITY[item] }}</span>
                  <span v-if="index < row.userIdentity.split(',').length - 1">，</span>
                </span>
              </template>
            </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <el-link type="danger" @click="deletePerson(row)">删除</el-link>
              </template>
            </el-table-column>
          </el-table>
          <div class="add-user">
            <el-button type="primary" @click="openUserDialog" icon="el-icon-plus">添加用户</el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
    <userDialog
      :value.sync="showUserDialog"
      :personList="tableData"
      @selectPerson="selectPerson"
      @deletePerson="deletePerson"
    ></userDialog>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  import { USER_IDENTITY } from "@/enums";
  import userDialog from "./userDialog.vue";
  export default {
    components: {
      userDialog,
    },
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        ruleForm: {
          templateCode: "",
          templateName: "",
          memo: "",
        },
        rules: {
          templateCode: [{ required: true, message: "请输入模板编码", trigger: "blur" }],
          templateName: [{ required: true, message: "请输入模板名称", trigger: "blur" }],
        },
        apis: {
          create: "/api/sms/template/create",
          update: "/api/sms/template/update",
          info: "/api/sms/template/get/",
        },
        saveRecordThrottling: () => {},
        loading: false,
        tableData: [],
        USER_IDENTITY,
        showUserDialog: false,
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.tableData = res.data.smsUsers.map((item) => {
            return {
              ...item,
              lgUnionId: item.businessId,
            };
          });
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            let smsUsers = [];
            if (this.tableData.length > 0) {
              smsUsers = this.tableData.map((item) => {
                return {
                  businessId: item.lgUnionId,
                };
              });
            }
            try {
              let res = this.recordId
                ? await updateApiFun({ ...this.ruleForm, smsUsers }, this.apis.update)
                : await createApiFun({ ...this.ruleForm, smsUsers }, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}短信模板成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      // 添加用户
      openUserDialog() {
        this.showUserDialog = true;
      },
      // 选择人员
      selectPerson(row) {
        this.tableData.push(row);
      },
      // 删除人员
      deletePerson(row) {
        this.tableData = this.tableData.filter((item) => item.lgUnionId !== row.lgUnionId);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .add-user {
    margin-top: 10px;
  }
</style>
