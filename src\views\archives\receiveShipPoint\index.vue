<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <record v-if="showType == 1" @closeRecord="closeRecord" @refreshList="initData" :recordId="recordId"></record>
      <merchant v-else-if="showType == 2" :recordId="merchantFileId" @closeRecord="closeRecord"></merchant>
      <contract v-else-if="showType == 3" :recordId="merchantFileId" @closeRecord="closeRecord"></contract>
      <intelligentRecord
        v-else-if="showType == 4"
        :holdingTankId="holdingTankId"
        @closeRecord="closeRecord"
      ></intelligentRecord>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input
              class="w400"
              v-model="keyword"
              placeholder="请输入点位编号/名称/点位联系人名称"
              clearable
            ></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
            <el-dropdown
              size="small"
              trigger="click"
              @command="handleDataImport"
              class="dropdown-button"
              placement="bottom-start"
            >
              <el-button size="small" type="primary">
                <span class="sctmp-iconfont icon-ic_daoru"></span>
                <span class="ml-5">导入</span>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">
                  <div class="has-padding-sm has-border-bottom">导入基础信息</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-popover placement="bottom" width="240" trigger="click" :append-to-body="false">
              <div class="reveal-box">
                <div class="reveal-header">
                  <div>展示全部</div>
                  <el-switch
                    :value="allItemChecked"
                    active-text="是"
                    inactive-text="否"
                    @change="changeAllReveal"
                  ></el-switch>
                </div>
                <ul class="reveal-list">
                  <li class="reveal-item" v-for="(item, index) in itemList" :key="index">
                    <el-checkbox :value="item.value" :disabled="index === 0" @change="changeReveal($event, item)">{{
                      item.label
                    }}</el-checkbox>
                  </li>
                </ul>
              </div>
              <el-button slot="reference">
                <div class="btn-inner">
                  <span class="el-icon-s-grid"></span>
                  <span>显示列</span>
                  <span class="el-icon-caret-bottom"></span>
                </div>
              </el-button>
            </el-popover>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
          ></InterviewChannel>
          <el-col :span="8">
            <el-form-item label="点位类型">
              <el-select v-model="filterForm.type" placeholder="请选择点位类型" clearable filterable>
                <el-option v-for="(item, index) in POINT_TYPE" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="经营状态状态">
              <el-select v-model="filterForm.isUndock" placeholder="请选择移除状态" clearable filterable>
                <el-option v-for="(item, index) in REMOVE_STATUS" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设备状态">
              <el-select v-model="filterForm.deviceStatus" placeholder="请选择设备状态" clearable filterable>
                <el-option v-for="(item, index) in DEVICE_STATUS" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属区域">
              <el-cascader
                v-model="filterForm.districtId"
                :options="districtOptions"
                clearable
                filterable
                :props="cascaderProps"
              ></el-cascader>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            height="100%"
            border
            ref="tableRef"
          >
            <el-table-column type="index" width="55" align="center"></el-table-column>
            <el-table-column prop="code" label="点位编号" align="center" v-if="itemList[0].value" min-width="120">
              <template #default="{ row }"
                >{{ row.code
                }}<el-tag
                  v-if="row.isInAgoSevenDays"
                  class="ml-10"
                  :type="row.createTime == row.updateTime ? 'warning' : ''"
                  >{{ row.createTime == row.updateTime ? "新增" : "已编辑" }}</el-tag
                ></template
              >
            </el-table-column>
            <el-table-column
              prop="name"
              label="点位名称"
              align="center"
              min-width="180"
              v-if="itemList[1].value"
            ></el-table-column>
            <el-table-column prop="address" label="点位地址" align="center" min-width="240" v-if="itemList[2].value">
              <template #default="{ row }">
                <el-tooltip effect="dark" :content="row.address" placement="top" :open-delay="100">
                  <div class="line-clamp line-clamp-2">{{ row.address }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              prop="contact"
              label="点位联系人"
              align="center"
              min-width="100"
              v-if="itemList[3].value"
            ></el-table-column>
            <el-table-column
              prop="contactPhone"
              label="点位联系人联系方式"
              align="center"
              min-width="150"
              v-if="itemList[4].value"
            ></el-table-column>
            <el-table-column prop="type" label="点位类型" align="center" v-if="itemList[5].value">
              <template #default="{ row }">{{ POINT_TYPE[row.type] }}</template>
            </el-table-column>
            <el-table-column
              prop="baggingMethod"
              label="点位收运方式"
              align="center"
              v-if="itemList[6].value"
              min-width="100"
            >
              <template #default="{ row }">{{ POINT_RECEIVING_METHOD[row.baggingMethod] }}</template>
            </el-table-column>
            <el-table-column prop="routeName" label="所属路线" align="center" v-if="itemList[7].value">
              <template #default="{ row }">
                <el-link type="primary" @click="toUpdateRoute(row)">{{ row.routeName }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="isUndock" label="经营状态" align="center" min-width="180" v-if="itemList[8].value">
              <template #default="{ row }">
                <el-switch
                  :value="row.isUndock"
                  active-text="正常"
                  inactive-text="暂停经营"
                  :active-value="0"
                  :inactive-value="1"
                  @change="changeRemoveStatus(row)"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column prop="isStartPoint" label="起终点" align="center" min-width="140" v-if="itemList[9].value">
              <template #default="{ row }">
                <el-switch
                  :value="row.isStartPoint"
                  active-text="是"
                  inactive-text="否"
                  :active-value="1"
                  :inactive-value="0"
                  @change="changePointStart(row)"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column
              prop="districtName"
              label="省市区"
              align="center"
              min-width="180"
              v-if="itemList[10].value"
            >
              <template #default="{ row }">{{ row.provinceName }}/{{ row.cityName }}/{{ row.districtName }}</template>
            </el-table-column>
            <el-table-column prop="longitude" label="经度" align="center" v-if="itemList[11].value"></el-table-column>
            <el-table-column prop="latitude" label="纬度" align="center" v-if="itemList[12].value"></el-table-column>
            <el-table-column
              prop="createTime"
              label="点位创建时间"
              align="center"
              min-width="140"
              v-if="itemList[13].value"
            ></el-table-column>
            <el-table-column
              prop="updateTime"
              label="点位修改时间"
              align="center"
              min-width="140"
              v-if="itemList[14].value"
            ></el-table-column>
            <el-table-column
              prop="dailyEmissions"
              label="日排放量（KG）"
              align="center"
              min-width="140"
              v-if="itemList[15].value"
            ></el-table-column>
            <el-table-column
              prop="monthEmissions"
              label="月排放量（KG）"
              align="center"
              min-width="140"
              v-if="itemList[16].value"
            ></el-table-column>
            <el-table-column prop="deviceStatus" label="设备状态" align="center" v-if="itemList[17].value">
              <template #default="{ row }">{{ DEVICE_STATUS[row.deviceStatus] }}</template>
            </el-table-column>
            <el-table-column prop="period" label="收运周期" align="center" min-width="120" v-if="itemList[18].value">
              <template #default="{ row }">{{ COLLECTION_CYCLE[row.period] }}</template>
            </el-table-column>
            <el-table-column
              prop="frequency"
              label="收运频次"
              align="center"
              min-width="120"
              v-if="itemList[19].value"
            ></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center" v-if="itemList[20].value">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="240" label="操作" align="center" fixed="right">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row, 1)">编辑</el-link>
                <el-popconfirm class="mr-10" title="确认删除当前收运点位档案？" @confirm="deleteRecord(row)">
                  <el-link type="danger" slot="reference">删除</el-link>
                </el-popconfirm>
                <el-link
                  v-if="row.merchantFileId || row.holdingTankId"
                  class="mr-10"
                  type="primary"
                  @click="editRecord(row, row.type === 2 ? 4 : 2)"
                  >关联信息</el-link
                >
                <el-link type="primary" @click="editRecord(row, 3)" v-if="row.merchantFileId">合同信息</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
    <importDialog
      :value.sync="importDialogShow"
      title="收运点位档案"
      :importApi="apis.import"
      :templateApi="apis.template"
      :importDialogType="importDialogType"
      :isTemplateApi="true"
      @importSuccess="searchFilter"
    ></importDialog>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, deleteApiFun, getInfoApiFun, createApiFun, getInfoApiFunByParams } from "@/api/base";
  import record from "./components/record.vue";
  import merchant from "./components/merchant.vue";
  import contract from "../merchant/components/contract.vue";
  import intelligentRecord from "./components/intelligentRecord.vue";
  import { REMOVE_STATUS, POINT_TYPE, DEVICE_STATUS, COLLECTION_CYCLE, POINT_RECEIVING_METHOD } from "@/enums";
  import importDialog from "@/components/importDialog";
  import { isSevenDaysAgo } from "@/utils";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
      merchant,
      contract,
      intelligentRecord,
      importDialog,
    },
    data() {
      return {
        filterForm: {},
        REMOVE_STATUS,
        POINT_TYPE,
        DEVICE_STATUS,
        COLLECTION_CYCLE,
        POINT_RECEIVING_METHOD,
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/pickup/pickupPoint/listPage",
          delete: "/api/pickup/pickupPoint/delete/",
          contractStatusList: "/api/dict/contractStatus/list",
          enable: "/api/pickup/pickupPoint/undock",
          setStartPoint: "/api/pickup/pickupPoint/setStartPoint",
          template: "/api/pickup/pickupPoint/excleModel",
          import: "/api/pickup/pickupPoint/importPickupPoint",
          regionList: "/api/region/regionList",
        },
        showType: 0,
        loading: false,
        statusOptions: [],
        recordId: "",
        merchantFileId: "",
        holdingTankId: "",
        importDialogShow: false,
        importDialogType: "",
        showFilter: false,
        keyword: "",
        itemList: [
          { label: "点位编号", value: true },
          { label: "点位名称", value: true },
          { label: "点位地址", value: true },
          { label: "点位联系人", value: true },
          { label: "点位联系人联系方式", value: true },
          { label: "点位类型", value: true },
          { label: "点位收运方式", value: true },
          { label: "所属路线", value: true },
          { label: "移除状态", value: true },
          { label: "起终点", value: true },
          { label: "省市区", value: true },
          { label: "经度", value: false },
          { label: "纬度", value: false },
          { label: "点位创建时间", value: false },
          { label: "点位修改时间", value: false },
          { label: "日排放量", value: false },
          { label: "月排放量", value: false },
          { label: "设备状态", value: false },
          { label: "收运周期", value: false },
          { label: "收运频次", value: false },
          { label: "渠道名称", value: true },
        ],
        allItemChecked: false,
        districtOptions: [
          { code: "440100000000", name: "广州市", children: [] },
          { code: "441500000000", name: "汕尾市", children: [] },
          { code: "445100000000", name: "潮州市", children: [] },
        ], //区域列表
        cascaderProps: {
          emitPath: false,
          value: "code",
          label: "name",
        },
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
      this.getOptions();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          getInfoApiFun("", this.apis.contractStatusList),
          getInfoApiFunByParams({ pid: this.districtOptions[0].code }, this.apis.regionList),
          getInfoApiFunByParams({ pid: this.districtOptions[1].code }, this.apis.regionList),
          getInfoApiFunByParams({ pid: this.districtOptions[2].code }, this.apis.regionList),
        ];
        let res = await Promise.all(promiseList);
        this.statusOptions = res[0].data;
        this.districtOptions[0].children = res[1].data;
        this.districtOptions[1].children = res[2].data;
        this.districtOptions[2].children = res[3].data;
      },
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas.map((list) => {
            return {
              ...list,
              isInAgoSevenDays: isSevenDaysAgo(list.updateTime),
              contactPhone: list.contactPhone ? this.$sm2Decrypt(list.contactPhone) : "",
            };
          });
          this.page.total = res.data.total;
          this.$nextTick(() => {
            this.$refs.tableRef.doLayout();
          });
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showType = 1;
        this.recordId = "";
      },
      // 编辑
      editRecord(row, type) {
        this.showType = type;
        switch (type) {
          case 1:
            this.recordId = row.id;
            break;
          case 2:
            this.merchantFileId = row.merchantFileId;
            break;
          case 3:
            this.merchantFileId = row.merchantFileId;
            break;
          case 4:
            this.holdingTankId = row.holdingTankId;
            break;
        }
      },
      closeRecord() {
        this.showType = 0;
      },
      // 删除
      async deleteRecord(row) {
        this.loading = true;
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            this.page.pageNo = this.pageDropProcess(this.page.pageNo, this.page.pageSize, this.page.total);
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
          this.loading = false;
        } catch (error) {
          console.warn(error);
          this.loading = false;
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 切换移除状态
      changeRemoveStatus(row) {
        this.$confirm(`是否确认切换经营状态`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await createApiFun({ id: row.id }, this.apis.enable);
              if (res.success) {
                this.$message.success(`切换成功`);
                this.initData();
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          })
          .catch(() => {});
      },
      changePointStart(row) {
        this.$confirm(`是否确认切换起终点`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await createApiFun(
                { id: row.id, isStartPoint: row.isStartPoint === 1 ? 0 : 1 },
                this.apis.setStartPoint,
              );
              if (res.success) {
                this.$message.success(`切换成功`);
                this.initData();
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          })
          .catch(() => {});
      },
      // 导入
      handleDataImport(command) {
        this.importDialogType = command;
        this.importDialogShow = true;
      },
      // 修改所有显示列
      changeAllReveal(value) {
        this.allItemChecked = value;
        this.itemList.slice(1, this.itemList.length).forEach((item) => (item.value = value));
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 列点击事件
      changeReveal(value, item) {
        item.value = value;
        let uncheckList = this.itemList.slice(1, this.itemList.length).filter((list) => !list.value);
        if (uncheckList.length === 0) {
          this.allItemChecked = true;
        } else {
          this.allItemChecked = false;
        }
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 跳转编辑路线档案
      toUpdateRoute(row) {
        this.$router.push({ name: "receiveRoute", params: { pickupPathId: row.pickupPathId } });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .filter-box {
    margin: 0 10px;
  }
  .w400 {
    width: 400px;
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .el-col {
    margin-top: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
</style>
