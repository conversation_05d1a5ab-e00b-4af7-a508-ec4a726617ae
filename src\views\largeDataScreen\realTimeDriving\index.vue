<template>
  <div>
    <v-scale-screen width="1920" height="1080" class="monitor-container">
      <mapContainer class="mapContainer" ref="mapContainer" @initMap="initMap"></mapContainer>
      <main class="main-container">
        <div class="header-container">
          <span class="back-icon el-icon-arrow-left" @click="$router.go(-1)"></span>
          <div>车辆实时监控</div>
        </div>
        <div class="statistic-box">
          <div class="statistic-left">
            <div class="tab-wrapper">
              <ul class="tab-list">
                <li
                  class="tab-item"
                  :class="{ active: activeTab == index }"
                  v-for="(item, index) in tabList"
                  :key="index"
                  @click="toggleTab(index)"
                  >{{ item }}</li
                >
              </ul>
            </div>

            <div v-show="activeTab == 0">
              <CarPerson v-bind="formData"></CarPerson>
              <Point v-bind="formData"></Point>
            </div>
            <div class="car-wrapper" v-show="activeTab == 1">
              <CarList :carList="carList" @itemClick="itemClick"></CarList>
            </div>
          </div>
          <div class="flex-6"></div>
          <div class="statistic-right">
            <Count :integerList="integerList" :decimalsList="decimalsList"></Count>
            <Surveillance v-if="$route.query.operationalNature == 0"></Surveillance>
            <Status v-bind="formData"></Status>
            <WaybillList></WaybillList>
            <div class="ai-img">
              <div class="currDate">{{ currDate }}</div>
              <img class="img-box" src="@/assets/images/new_ai.png" alt="" @click="openAIDrawer" />
            </div>
          </div>
        </div>
        <div class="tip-box">
          <div class="tip-item">
            <div class="tip-rect"></div>
            <div class="tip-title">车辆行驶过的路径</div>
          </div>
          <div class="tip-item">
            <img class="point-img" src="@/assets/images/collected_point.png" alt="" />
            <div class="tip-title">已完成收运点位</div>
          </div>
          <div class="tip-item">
            <img class="point-img" src="@/assets/images/uncollect_point.png" alt="" />
            <div class="tip-title">待收运点位</div>
          </div>
        </div>
      </main>
    </v-scale-screen>
    <TemporaryDialog :value.sync="showTemporary" :temporaryId="temporaryId"></TemporaryDialog>
    <AIDrawer :value.sync="showAIDrawer"></AIDrawer>
  </div>
</template>

<script>
  import VScaleScreen from "v-scale-screen";
  import mapContainer from "@/components/mapContainer";
  import { getInfoApiFun, createApiFun, getInfoApiFunByParams } from "@/api/base";
  import CarPerson from "./components/CarPerson.vue";
  import Point from "./components/Point.vue";
  import Count from "./components/Count.vue";
  import Surveillance from "./components/Surveillance.vue";
  import Status from "./components/Status.vue";
  import WaybillList from "./components/WaybillList.vue";
  import { nanoid } from "nanoid";
  import websocket from "@/utils/websocket";
  import { wgs84togcj02 } from "@/utils/coordinateTransform";
  import CarList from "../realTimeMonitor/components/CarList.vue";
  import AIDrawer from "../realTimeMonitor/components/AIDrawer.vue";
  import emitter from "@/utils/mitt";
  import TemporaryDialog from "./components/TemporaryDialog.vue";
  import moment from "moment";
  let map = "";
  export default {
    mixins: [websocket],
    components: {
      VScaleScreen,
      mapContainer,
      CarPerson,
      Point,
      Count,
      Surveillance,
      Status,
      WaybillList,
      CarList,
      TemporaryDialog,
      AIDrawer,
    },
    data() {
      return {
        apis: {
          info: "/api/monitor/vehicle/screen/info",
          getStartPoint: "/api/pickup/pickupPoint/getStartPoint",
          location: "/api/monitor/location",
          travel: "/api/monitor/travel",
        },
        startEndPoint: "",
        formData: "",
        routePath: [],
        integerList: [], //整数
        decimalsList: [], //小数
        receivedList: [], //已收点位
        unReceivedList: [], //待收点位
        websocketUrl: `${process.env.VUE_APP_WSS_URL}/sctmpbase/api/screen/real-time?location=1&plateNumber=`,
        unReceivedMarker: [],
        receivedMarker: [],
        polylineList: [],
        tabList: ["信息页", "车辆列表"],
        activeTab: 0,
        carList: [],
        showTemporary: false,
        temporaryId: "",
        showAIDrawer: false,
        carMarker: "", //当前车辆点位
        carTextMarker: "", //当前车辆文本点位
        carTravelList: [],
        currDate: "",
      };
    },
    watch: {
      $route(newV) {
        if (this.carMarker) {
          this.carMarker.remove();
          this.carTextMarker.remove();
          this.carMarker = "";
          this.carTextMarker = "";
        }
        this.setOncloseMessage();
        this.getRecord();
        this.getCarLocation();
        this.getCarTravel(newV.query.plateNumber);
      },
    },
    mounted() {
      emitter.on("open-temporary-dialog", (item) => {
        this.showTemporary = true;
        this.temporaryId = item.id;
      });
      moment.locale();
      this.getDate();
    },
    beforeDestroy() {
      map.clearInfoWindow();
      map.clearMap();
      map.destroy();
      clearInterval(this.timer);
      emitter.off("open-temporary-dialog");
      this.setOncloseMessage();
    },
    methods: {
      getDate() {
        let timer = setTimeout(() => {
          clearTimeout(timer);
          this.currDate = moment().format("YYYY年MM月DD日 | ") + moment().format("dddd");
          this.getDate();
        }, 1000);
      },
      toggleTab(index) {
        if (this.activeTab == index) return;
        this.activeTab = index;
      },
      // 列表点击事件
      itemClick(item) {
        map.clearInfoWindow();
        this.$router.replace(
          `/sctmp_base/realTimeDriving?plateNumber=${item.plateNumber}&operationalNature=${item.operationalNature}`,
        );
      },
      // 打开AI弹窗
      openAIDrawer() {
        this.showAIDrawer = true;
      },
      // 初始化地图
      async initMap(mapCom) {
        if (mapCom) {
          map = mapCom;
          await this.getStartAndEndPoint();
          this.getRecord();
          this.getCarLocation();
          this.getCarTravel(this.$route.query.plateNumber);
        }
      },
      // 获取起终点坐标
      async getStartAndEndPoint() {
        try {
          let res = await getInfoApiFun("", this.apis.getStartPoint);
          if (res.success) {
            res.data.position = wgs84togcj02(res.data.longitude, res.data.latitude);
            this.startEndPoint = res.data;
            this.drawStartEndPoint();
          }
        } catch (error) {
          console.log(error);
        }
      },
      async getRecord() {
        try {
          let res = await createApiFun({ plateNumber: this.$route.query.plateNumber }, this.apis.info);
          this.formData = res.data;
          this.formData.vehicleInfo.defaultDriverDossierPhone = this.$sm2Decrypt(
            this.formData.vehicleInfo.defaultDriverDossierPhone,
          );
          this.formData.vehicleInfo.supercargoDossierOnePhone = this.formData.vehicleInfo.supercargoDossierOnePhone
            ? this.$sm2Decrypt(this.formData.vehicleInfo.supercargoDossierOnePhone)
            : "";
          this.formData.vehicleInfo.supercargoDossierTwoPhone = this.formData.vehicleInfo.supercargoDossierTwoPhone
            ? this.$sm2Decrypt(this.formData.vehicleInfo.supercargoDossierTwoPhone)
            : "";
          this.handleVehicleInfo(this.formData.vehicleInfo);
          this.handleRubbishTotal(this.formData.rubbishTotal);
          res.data.mapDetail.forEach((item) => {
            item.lngLat = wgs84togcj02(item.longitude, item.latitude);
          });
          let mapDetailList = res.data.mapDetail.filter((list) => !(list.longitude == 0 || list.latitude == 0));
          this.unReceivedList = mapDetailList.filter((list) => list.waybillStatus == 0);
          this.receivedList = mapDetailList.filter((list) => list.waybillStatus == 1);
          this.drawMarker(this.unReceivedList, false, "unReceivedMarker");
          this.drawMarker(this.receivedList, true, "receivedMarker");
        } catch (error) {
          console.log(error);
        }
      },
      handleVehicleInfo(info) {
        let vehicleInfo = info;
        try {
          vehicleInfo.driverAvatar = JSON.parse(vehicleInfo?.driverAvatar)?.url;
          vehicleInfo.cargoOneAvatar = JSON.parse(vehicleInfo?.cargoOneAvatar)?.url;
          vehicleInfo.cargoTwoAvatar = JSON.parse(vehicleInfo?.cargoTwoAvatar)?.url;
        } catch (error) {
          vehicleInfo.driverAvatar = "";
          vehicleInfo.cargoOneAvatar = "";
          vehicleInfo.cargoTwoAvatar = "";
        }
        this.formData.vehicleInfo = vehicleInfo;
      },
      handleRubbishTotal(value) {
        let numList = value.split(".");
        this.integerList = numList[0].split("").map((item) => {
          return {
            id: nanoid(),
            num: item,
          };
        });
        if (this.integerList.length < 7) {
          let zeroNum = 7 - this.integerList.length;
          for (let i = 0; i < zeroNum; i++) {
            this.integerList.unshift({
              id: nanoid(),
              num: "0",
            });
          }
        }
        this.decimalsList = numList[1].split("").map((item) => {
          return {
            id: nanoid(),
            num: item,
          };
        });
      },
      // 获取车辆实时位置
      async getCarLocation() {
        this.polylineList.forEach((item) => {
          item.remove();
        });
        this.polylineList = [];
        this.routePath = [];
        try {
          let res = await getInfoApiFunByParams("", this.apis.location);
          this.carList = res.data.map((item) => {
            return {
              ...item,
              accStatus: Number(item.status) & (1 == 1) ? (item.speed ? 0 : 1) : 2,
              position: wgs84togcj02(item.longitude, item.latitude),
              driverName: item.driverName ? item.driverName : "",
            };
          });
          let carItem = this.carList.filter((dt) => dt.plateNumber === this.$route.query.plateNumber);
          if (carItem.length > 0) {
            this.dealWithLocation(carItem[0], true);
          }
        } catch (error) {
          console.log(error);
        }
      },
      // 获取车辆行驶路径
      async getCarTravel(plateNumber) {
        try {
          let res = await getInfoApiFunByParams({ plateNumber }, this.apis.travel);
          this.carTravelList = res.data.map((dt) => [dt.longitude, dt.latitude]);
          let pathList = res.data.map((dt) => {
            return wgs84togcj02(dt.longitude, dt.latitude);
          });
          pathList.unshift(this.startEndPoint.position);
          // let longLatList = this.cutArray(pathList);
          // await this.processArrayWithDelay(longLatList);
          this.drawRoutePath(pathList, "routePath");
          map.setFitView();
          let token = await window.LOGAN.getToken();
          this.initWebSocket(this.websocketUrl + plateNumber + `&token=${token}`);
        } catch (error) {
          console.log(error);
        }
      },
      drawRoutePath(path, routePathField) {
        // let path = this.parseRouteToPath(route);
        this[routePathField] = this[routePathField].concat(path);
        // 绘制轨迹
        let polyline = new window.AMap.Polyline({
          map: map,
          path,
          showDir: true,
          strokeColor: "#28F", //线颜色
          strokeWeight: 6, //线宽
          strokeOpacity: 1,
        });
        this.polylineList.push(polyline);
        // map.setFitView();
      },
      // 循环执行高德地图行驶路径api，每一次调用等待300ms
      async processArrayWithDelay(array, delayMs = 300) {
        for (let i = 0; i < array.length; i++) {
          // 如果是第一次之后的元素，则等待1秒
          if (i > 0) {
            await this.delay(delayMs);
          }
          this.handleDrive(
            array[i][0],
            array[i][array[i].length - 1],
            array[i].slice(1, array[i].length - 1),
            0,
            "routePath",
          );
        }
      },
      // 延时函数
      delay(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
      },
      // 车辆定位数据处理
      dealWithLocation(car, first) {
        if (this.carMarker) {
          this.updateMarker(car);
        } else {
          this.addMarker(car);
        }
        if (first) {
          map.setFitView();
        }
      },
      // 增加点位
      addMarker(item) {
        let accStatus = Number(item.status) & (1 == 1);
        let iconImage = "";
        if (item.operationalNature === 0) {
          iconImage = accStatus
            ? item.speed
              ? require("@/assets/images/cart_drive.png")
              : require("@/assets/images/cart_static.png")
            : require("@/assets/images/cart_offline.png");
        } else {
          iconImage = accStatus
            ? item.speed
              ? require("@/assets/images/trolley_drive.png")
              : require("@/assets/images/trolley_static.png")
            : require("@/assets/images/trolley_offline.png");
        }
        const icon = new window.AMap.Icon({
          image: iconImage, //Icon 的图像
          imageSize: item.operationalNature === 0 ? new window.AMap.Size(20, 58) : new window.AMap.Size(20, 40),
        });
        this.carMarker = new window.AMap.Marker({
          map: map,
          position: wgs84togcj02(item.longitude, item.latitude),
          icon: icon,
          offset: new window.AMap.Pixel(0, 0),
          extData: { item },
          angle: item.direction,
          anchor: "middle-right",
        });
        // let acc = accStatus ? (item.speed ? "行驶中" : "静止") : "离线";
        this.carTextMarker = new window.AMap.Text({
          text: accStatus && item.speed ? `${item.plateNumber} - ${item.speed || 0}km/h` : `${item.plateNumber}`,
          anchor: "middle-left", // 设置文本标记锚点
          cursor: "pointer",
          style: {
            "background-color": "rgba(255,255,255,0.8)",
            "text-align": "center",
            "font-size": "12px",
          },
          offset: new window.AMap.Pixel(4, 0),
          position: wgs84togcj02(item.longitude, item.latitude),
          extData: { item },
        });
        this.carTextMarker.setMap(map);
      },
      // 修改点位
      updateMarker(item) {
        let accStatus = Number(item.status) & (1 == 1);
        let iconImage = "";
        if (item.operationalNature === 0) {
          iconImage = accStatus
            ? item.speed
              ? require("@/assets/images/cart_drive.png")
              : require("@/assets/images/cart_static.png")
            : require("@/assets/images/cart_offline.png");
        } else {
          iconImage = accStatus
            ? item.speed
              ? require("@/assets/images/trolley_drive.png")
              : require("@/assets/images/trolley_static.png")
            : require("@/assets/images/trolley_offline.png");
        }
        const icon = new window.AMap.Icon({
          image: iconImage, //Icon 的图像
          imageSize: item.operationalNature === 0 ? new window.AMap.Size(20, 58) : new window.AMap.Size(20, 40),
        });
        this.carMarker.setIcon(icon);

        this.carMarker.setAngle(item.direction);
        this.carTextMarker.setText(
          accStatus && item.speed ? `${item.plateNumber} - ${item.speed || 0}km/h` : `${item.plateNumber}`,
        );
        // 加载动画插件
        window.AMap.plugin("AMap.MoveAnimation", () => {
          this.carMarker.moveTo(wgs84togcj02(item.longitude, item.latitude), {
            duration: 10000,
          });
          this.carTextMarker.moveTo(wgs84togcj02(item.longitude, item.latitude), {
            duration: 10000,
          });
        });
      },
      // 绘制起终点坐标
      drawStartEndPoint() {
        // 设置起始/终点坐标点位图标
        const startIcon = new window.AMap.Icon({
          // 图标尺寸
          size: new window.AMap.Size(25, 34),
          // 图标的取图地址
          image: "https://a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png",
          // 图标所用图片大小
          imageSize: new window.AMap.Size(135, 40),
          // 图标取图偏移量
          imageOffset: new window.AMap.Pixel(-9, -3),
        });
        let startMarker = new window.AMap.Marker({
          position: this.startEndPoint.position,
          icon: startIcon,
          offset: new window.AMap.Pixel(-13, -30),
        });

        const endIcon = new window.AMap.Icon({
          size: new window.AMap.Size(25, 34),
          image: "https://a.amap.com/jsapi_demos/static/demo-center/icons/dir-marker.png",
          imageSize: new window.AMap.Size(135, 40),
          imageOffset: new window.AMap.Pixel(-95, -3),
        });
        // 将 icon 传入 marker
        let endMarker = new window.AMap.Marker({
          position: this.startEndPoint.position,
          icon: endIcon,
          offset: new window.AMap.Pixel(-13, -30),
        });

        startMarker.content = `当前地点为：${this.startEndPoint.address}`;
        startMarker.on("click", this.markerClick);
        endMarker.content = `当前地点为：${this.startEndPoint.address}`;
        endMarker.on("click", this.markerClick);

        // 将 markers 添加到地图
        map.add([startMarker, endMarker]);

        map.setFitView();
      },
      // 点位点击事件
      markerClick(e) {
        let infoWindow = new window.AMap.InfoWindow({ offset: new window.AMap.Pixel(4, -4) });
        infoWindow.setContent(e.target.content);
        infoWindow.open(map, e.target.getPosition());
      },
      // 绘制点位
      drawMarker(dataList, collected, arrField) {
        if (this[arrField].length > 0) {
          this[arrField].forEach((item) => {
            item.remove();
          });
        }
        // 添加点位标记
        dataList.forEach((data) => {
          const icon = new window.AMap.Icon({
            image: collected
              ? require("@/assets/images/collected_point.png")
              : require("@/assets/images/uncollect_point.png"), //Icon 的图像
            imageSize: new window.AMap.Size(24, 24),
          });
          let marker = new window.AMap.Marker({
            icon,
            map: map,
            position: data.lngLat,
            extData: { data },
          });
          marker.on("click", this.pointClick);
          this[arrField].push(marker);
        });
      },
      // 点位点击事件
      pointClick(e) {
        let item = e.target.getExtData().data;
        let info = [];
        info.push(`<div style="font-size:14px;font-weight:bold">${item.productionUnit}</div>`);
        info.push(`<div style="font-size:12px">收运状态：${item.waybillStatus == 0 ? "未收运" : "已收运"}</div>`);
        if (item.waybillStatus == 1) {
          info.push(`<div style="font-size:12px">收运时间：${item.waybillTime}</div>`);
          info.push(`<div style="font-size:12px">收运重量：${item.rubbishTotal} （kg）</div>`);
        }
        let infoWindow = new window.AMap.InfoWindow({ offset: new window.AMap.Pixel(4, -4) });
        infoWindow.setContent(info.join(""));
        infoWindow.open(map, e.target.getPosition());
      },
      // 调用高德地图jsApi获取驾驶路线
      handleDrive(start, end, points, strategy, routePathField) {
        window.AMap.plugin("AMap.Driving", () => {
          let driving = new window.AMap.Driving({
            // map: this[mapRef],
            policy: strategy, //驾车路线规划策略，0是速度优先的策略
            // panel: "container",
          });
          driving.search(start, end, { waypoints: points }, (status, result) => {
            //status：complete 表示查询成功，no_data 为查询无结果，error 代表查询错误
            //查询成功时，result 即为对应的驾车导航信息
            if (status === "complete") {
              this.drawRoute(result.routes[0], routePathField);
            }
          });
        });
      },
      drawRoute(route, routePathField) {
        let path = this.parseRouteToPath(route);
        this[routePathField] = this[routePathField].concat(path);
        // 绘制轨迹
        let polyline = new window.AMap.Polyline({
          map: map,
          path,
          showDir: true,
          strokeColor: "#28F", //线颜色
          strokeWeight: 6, //线宽
          strokeOpacity: 1,
        });
        this.polylineList.push(polyline);
        // map.setFitView();
      },
      // 解析DrivingRoute对象，构造成AMap.Polyline的path参数需要的格式
      parseRouteToPath(route) {
        let path = [];
        for (let i = 0, l = route.steps.length; i < l; i++) {
          let step = route.steps[i];
          for (let j = 0, n = step.path.length; j < n; j++) {
            path.push(step.path[j]);
          }
        }
        return path;
      },
      // 按照数组长度为16切割，多余的数据存进最后一个数组
      cutArray(array) {
        let finalArray = [];
        // 商
        let quotient = Math.floor(array.length / 16);
        // 余数
        let remainder = array.length % 16;
        // 是否为16的倍数
        if (quotient > 0) {
          for (let i = 0; i < quotient; i++) {
            finalArray.push(array.slice(i * 16, (i + 1) * 16));
          }
          if (remainder > 0) {
            finalArray.push(array.slice(16 * quotient, array.length));
          }
          // 需要插入数组的索引数组
          let indexArr = [];
          // 需要插入数组的数据数组
          let valueArr = [];
          finalArray.forEach((arr, index) => {
            if (index >= 0 && index < finalArray.length - 1) {
              indexArr.push(index);
              valueArr.push([arr[15], finalArray[index + 1][0]]);
            }
          });
          // 将每个数组的终点坐标和起点坐标连接成数组并插入对应的位置
          indexArr.forEach((item, number) => {
            if (number >= 0 && number < indexArr.length) {
              finalArray.splice(item + number + 1, 0, valueArr[number]);
            }
          });
          finalArray = finalArray.filter((list) => list.length > 1);
        } else {
          finalArray = [array];
        }
        return finalArray;
      },
      handleCarList(item) {
        let index = this.carList.findIndex((list) => list.plateNumber === item.plateNumber);
        if (index >= 0) {
          this.carList[index].accStatus = Number(item.status) & (1 == 1) ? (item.speed ? 0 : 1) : 2;
          this.carList[index].speed = item.speed;
          this.carList[index].position = wgs84togcj02(item.longitude, item.latitude);
          this.carList.sort((a, b) => a.accStatus - b.accStatus || b.speed - a.speed);
        }
      },
      // websocket接收消息
      async setOnmessageMessage(res) {
        if (res.data === "token过期") {
          this.setOncloseMessage();
          await this.$store.dispatch("refreshAuthToken");
          let token = await window.LOGAN.getToken();
          this.initWebSocket(this.websocketUrl + this.$route.query.plateNumber + `&token=${token}`);
          return;
        }
        let rsp = "";
        try {
          rsp = JSON.parse(res.data);
        } catch (error) {
          rsp = "";
        }
        // console.log("rsp ==> ", rsp);
        if (rsp) {
          for (let key in rsp) {
            switch (key) {
              case "location":
                if (rsp[key][0]?.plateNumber === this.$route.query.plateNumber) {
                  this.dealWithLocation(rsp[key][0]);
                  this.drawCarLine(rsp[key][0]);
                }
                if (rsp[key].length > 0) {
                  this.handleCarList(rsp[key][0]);
                }
                break;
              case "rubbishTotal":
                this.handleRubbishTotal(rsp[key]);
                break;
              default:
                this.formData[key] = rsp[key];
                break;
            }
          }
        }
      },
      async drawCarLine(item) {
        let lastPosition = this.carTravelList[this.carTravelList.length - 1];
        if (item.longitude == lastPosition[0] && item.latitude == lastPosition[1]) return;
        let pathList = [wgs84togcj02(lastPosition[0], lastPosition[1]), wgs84togcj02(item.longitude, item.latitude)];
        this.drawRoutePath(pathList, "routePath");
        this.carTravelList.push([item.longitude, item.latitude]);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .monitor-container {
    position: relative;
  }
  .mapContainer {
    width: 100%;
    height: 100%;
  }
  .main-container {
    width: 100%;
    height: 100%;
    padding: 24px;
    position: absolute;
    top: 0;
    left: 0;
    background-image: radial-gradient(rgba(0, 0, 0, 0.02) 80%, #0d6d6e 100%);
    pointer-events: none;
  }
  .header-container {
    font-size: 32px;
    font-weight: bold;
    color: #fff;
    padding-bottom: 20px;
    display: flex;
    align-items: center;
    pointer-events: auto;
    .back-icon {
      cursor: pointer;
      margin-right: 10px;
    }
  }
  .statistic-box {
    display: flex;
    justify-content: space-between;
    height: calc(100% - 50px);
    .statistic-left,
    .statistic-right {
      width: 480px;
      padding: 20px;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      flex-direction: column;
      position: relative;
    }
    .flex-6 {
      flex: 6;
    }
  }
  .tip-box {
    position: absolute;
    left: 50%;
    bottom: 20px;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    .tip-item {
      display: flex;
      align-items: center;
      margin-left: 20px;
      .tip-rect {
        width: 100px;
        height: 16px;
        background-color: #28f;
      }
      .tip-title {
        font-size: 16px;
        margin-left: 10px;
      }
      .point-img {
        width: 30px;
        height: 30px;
        overflow: hidden;
      }
    }
  }
  .tab-wrapper {
    display: inline-block;
  }
  .tab-list {
    pointer-events: auto;
    background-color: rgba(0, 0, 0, 0.3);
    display: inline-flex;
    align-items: center;
    border-radius: 16px;
    margin-bottom: 12px;
    color: #fff;
    .tab-item {
      width: 80px;
      text-align: center;
      padding: 8px 0;
      font-size: 14px;
      cursor: pointer;
      border-radius: 16px;
      &.active {
        background-color: var(--color-primary);
      }
    }
  }
  .car-wrapper {
    flex: 1;
    overflow: hidden;
  }
  .ai-img {
    position: fixed;
    top: 14px;
    right: 24px;
    pointer-events: auto;
    display: flex;
    align-items: center;
    .currDate {
      font-size: 18px;
      color: #2e6929;
      line-height: 18px;
      font-weight: bold;
      font-style: normal;
      padding-right: 20px;
    }
    .img-box {
      width: 60px;
      height: 60px;
      overflow: hidden;
      cursor: pointer;
    }
  }
</style>
