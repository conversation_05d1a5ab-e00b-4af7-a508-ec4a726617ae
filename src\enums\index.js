// 性别枚举 0-男 1-女
export const SEX_OPTIONS = ["男", "女"];

// 用户身份 0-其他 1-客商 2-运营 3-司机 4-押运工
export const USER_IDENTITY = ["其他", "客商", "运营", "司机", "押运工"];

// 用户类型 0-内部用户 1-外部用户
export const USER_TYPE = ["内部用户", "外部用户"];

// 在职状态 0-在职 1-离职 2-停职
export const JOB_STATUS = ["在职", "离职", "停职"];

//证件类型 0-驾驶证，1-行驶证，2-营运证，3-从业资格证
export const CERT_TYPES = ["驾驶证", "行驶证", "营运证", "从业资格证"];

//运营状态 0-经营性 1-非经营性
export const OPERATIONAL_NATURE = ["经营性", "非经营性"];

//工作状态 0-空闲 1-非空闲
export const WORKING_STATUS = ["空闲中", "非空闲"];

//车辆状态 0-正常 1-维修 2-报废
export const CAR_STATUS = ["正常", "维修", "报废"];

//车辆能源类型 0-燃油车 1-电动车
export const VEHICLE_ENERGY_TYPE = ["燃油车", "电动车"];

//车辆管理- 险类 0-交强险 1-商业险 2-承运险
export const INSURANCE_TYPE = ["交强险", "商业险", "承运险"];

// 车辆违章处理状态 0-未处理 1-已处理
export const STATUS_LIST = [
  { id: 0, name: "未处理" },
  { id: 1, name: "已处理" },
];

// 车辆损坏程度 0-轻微 1-一般 2-严重 3-特别严重 4-无
export const DEGREE_LIST = [
  { id: 0, name: "轻微" },
  { id: 1, name: "一般" },
  { id: 2, name: "严重" },
  { id: 3, name: "特别严重" },
  { id: 4, name: "无" },
];

// 准驾车型 0-A2 1-B2 2-A1 3-C1
export const QUASI_DRIVING_TYPE = ["A2", "B2", "A1", "C1"];

// 检查状态/评价状态 0-正常 1-异常
export const EVALUATE_STATUS = ["正常", "异常"];

// 企业层级 0-总公司 1-子/分公司
export const ENTERPRISE_LEVEL = ["总公司", "子/分公司"];

// 维保类型 0-常规保养 1-二级维护 2-维修
export const MAINTENANCE_TYPE = ["常规保养", "二级维护", "维修"];

// 部门状态 0-正常运行 1-停运重组
export const DEPT_STATUS = ["正常运行", "停运重组"];

// 自检类型 0-车前检 1-车中检 2-车后检
export const SELFTEST_STATUS = ["车前检", "车中检", "车后检"];

// 自检记录标题 0-出车前检记录 1-出车中检记录 2-出车后检记录
export const SELFTEST_TITLE = ["出车前检记录", "出车中检记录", "出车后检记录"];

// 安全生产记录 活动类型 0-安全会议 1-安全培训 2-安全演练 3-安全检查
export const SAFEPRODUCTION_TYPE = ["安全会议", "安全培训", "安全演练", "安全检查"];

// 设备状态 0-设备正常 1-网络异常 2-门损坏 3-设备溢满 4-打印机故障 5-满溢红外故障 6-蓝牙秤故障 7-其他
export const DEVICE_STATUS = [
  "设备正常",
  "网络异常",
  "门损坏",
  "设备溢满",
  "打印机故障",
  "满溢红外故障",
  "蓝牙秤故障",
  "其他",
];

// 运行状态 0-启用 1-关闭
export const CURRENT_STATUS = ["启用", "关闭"];

// 废物状态 0-箱内 1-已清运
export const TRASH_STATUS = ["箱内", "已清运"];

// 废物类型 0-损伤性 1-病理性 2-药物性 3-感染性 4-化学性
export const TRASH_TYPE = ["损伤性", "病理性", "药物性", "感染性", "化学性"];

// 是否清空 0-是 1-否
export const WHETHER_EMPTIED = ["是", "否"];

// 评价配置类型 0-车辆状态评价 1-车辆安全自检
export const EVALUATE_TYPE = ["车辆状态评价", "车辆安全自检"];

// 车辆状态评价类型 0-车辆状态评价 1-抽查记录
export const EVALUATE_SUPERVISORY = ["车辆状态评价", "抽查记录"];

// 客商档案 客户类型 0-线下客户 1-小程序客户 2-办证客户
export const CUSTOMER_TYPE = ["线下客户", "小程序客户", "办证客户"];

// 客商档案 信用状态 0-正常 1-黑名单 2-白名单
export const CREDIT_STATUS = ["正常", "黑名单", "白名单"];

// 客商档案 客商状态 0-启用 1-禁用
export const CUSTOMER_STATUS = ["启用", "禁用"];

// 客商档案 医院性质 0-其他 1-国有 2-民营
export const HOSPITAL_NATURE = ["其他", "国有", "民营"];

// 收运点位档案 经营状态 0-正常 1-暂停经营
export const REMOVE_STATUS = ["正常", "暂停经营"];

// 收运点位档案 点位类型 0-大型床位医院 1-小诊所 2-智能收集柜 3-小型床位医院
export const POINT_TYPE = ["大型床位医院", "小诊所", "智能收集柜", "小型床位医院"];

// 收运点位档案 收运周期 0-24小时 1-48小时
export const COLLECTION_CYCLE = ["24小时", "48小时"];

// 车辆行驶记录 车辆状态 0-点火，定位
export const DRIVING_CAR_STATUS = ["点火，定位"];

// 收运路线档案 路线属性 0-大型床位医院收运路线 1-小型床位医院收运路线 2-小诊所收运路线
export const ROUTE_PROPERTY = ["大型床位医院收运路线", "小型床位医院收运路线", "小诊所收运路线"];

// 收运路线档案 路线状态 0-启用 1-未启用 2-删除
export const ROUTE_STATUS = ["启用", "未启用", "删除"];

// 收运路线档案 路线策略 0-速度优先 2-距离优先
export const ROUTE_STRATEGY = ["速度优先", "", "距离优先"];

//回访登记表管理 登记表状态 0-待发布 1-发布
export const VISIT_FORM_STATUS = ["待发布", "发布"];

//回访登记表管理 题目类型 0- 1-主观题 2-单选题 3=多选题
export const QUESTION_TYPE = ["", "主观题", "单选题", "多选题"];

// 回访记录管理 回访方式 0-电话 1-电子邮件 2-现场走访 3-系统填写 4-小程序填写
export const VISIT_RECORD_TYPE = ["电话", "电子邮件", "现场走访", "系统填写", "小程序填写"];

// 回访记录管理 处理情况 0-待处理 1-已处理
export const VISIT_HANDLE_STATUS = ["待处理", "已处理"];

// 投诉记录管理 处理情况 0-待处理 1-已处理
export const HANDLING_STATUS = ["待处理", "已处理"];

// 电子收运单管理 是否临时收运单 0-否 1-是
export const IS_TEMPORARY = ["否", "是"];

// 电子收运单管理 是否正常经营 0-否 1-是
export const IS_NORMAL_OPERATION = ["否", "是"];

// 电子收运单管理 收运状态 0-未收运 1-已收运
export const RECEIVING_CONDITION = ["未收运", "已收运"];

// 电子收运单管理 桶装/袋装 0-桶装 1-袋装
export const BARRELS_BAGS = ["桶装", "袋装"];

//  是否可以继续收运 0-是 1-否
export const INFLUENCEPICKUP_STATUS = [
  { id: 1, name: "是" },
  { id: 0, name: "否" },
];
// 异常类型 0-人员异常 1-车辆异常 2-车载设备异常 3-路况异常
export const ERROR_STATUS = ["人员异常", "车辆异常", "车载设备异常", "路况异常"];
// 异常上报 是否能继续收运 false -是 true -否
export const CONTINUECARRYING_STATUS = [
  { id: true, name: "是" },
  { id: false, name: "否" },
];
// 异常上报 是否能继续收运 false -是 true -否
export const CONTINUECARRYING_STATUS_MAP = {
  true: "是",
  false: "否",
};

// 考核方案 考核周期 0-月度 1-年度
export const EXAMINE_PERIOD = ["月度", "年度"];

// 考核方案 绩效规则 0-袋装收运月度绩效工资 1-按收运点数计算月度绩效工资 2-诊所组月度绩效工资 3-诊所组月度绩效工资（南沙）
export const PERFORMANCE_RULE = [
  "桶装/袋装收运月度绩效",
  "按收运点数计算月度绩效（小型床位）",
  "诊所组月度绩效",
  "诊所组月度绩效（南沙）",
  "特殊诊所组绩效",
  "诊所组+收运点数月度绩效（小型床位）",
  "诊所组（南沙）+收运点数月度绩效（小型床位）",
  "特殊诊所组+收运点数月度绩效（小型床位）",
  "顶班司机绩效规则",
];

// 考核等级数据配置 考核类型 0-月度考核 1-年度考核
export const ASSESSMENT_LEVEL_TYPE = ["月度考核", "年度考核"];

// 收运路线档案 收运方式 0-桶装收运 1-袋装收运 2-桶袋混合
export const WAYBILL_TYPE = ["桶装收运", "袋装收运", "桶袋混合"];

// 考核方案 考核流程
export const ASSESSMENT_FLOWS = {
  SELF_EVALUATION: "自评",
  DIRECT_SUPERVISOR_EVALUATION: "直属上级评价",
  DEPARTMENT_HEAD_EVALUATION: "部门负责人评价",
};

// 考核方案 考核状态
export const ASSESSMENT_STATUS = ["未发布", "已发布", "已终止"];

// 考核台账 考核流程 0-待确认 1-进行中 2-已完成
export const EXAMINE_FLOW = ["待确认", "进行中", "已完成"];

// 考核台账 扣分项 0-待确认 1-进行中 2-已完成
export const DEDUCTION_ITEM = ["被约谈", "被投诉", "缺勤", "三检表缺失", "收运数据作假"];

// 工作日历 状态 0-工作 1-休息
export const WORK_CALENDAR_STATUS = ["工作", "休息"];

// 点位收运任务明细 任务类型 0-常规任务 1-选做任务 2-加班任务
export const POINT_TASK_TYPE = ["常规任务", "选做任务", "加班任务", "个人申请加班任务"];

// 投诉记录 投诉类型 0-服务投诉 1-费用投诉 2-其他投诉
export const COMPLAINT_TYPE = ["服务投诉", "费用投诉", "其他投诉"];

// 电子收运单 流程状态 0-待确认 1-已确认 2-待核实
export const VERIFY_STATUS = ["待确认", "已确认", "待核实"];

// 绩效方案配置 计费类型 0-重量计费 1-点位计费
export const BILL_TYPE = ["重量计费", "点位计费"];

// 绩效方案配置 规则类型 0-常规 1-混合
export const RULE_TYPE = ["常规", "混合"];

// 两客一危 异常类型
export const EXCEPT_DANGER_TYPE = [
  "疲劳驾驶报警",
  "接打电话报警",
  "抽烟报警",
  "分神驾驶报警|不目视前方报警",
  "驾驶员异常报警",
  "探头遮挡报警",
  "用户自定义",
  "超时驾驶报警",
  "用户自定义",
  "未系安全带报警",
  "红外阻断型墨镜失效报警",
  "双脱把报警(双手同时脱离方向盘)",
  "玩手机报警",
  "用户自定义",
  "用户自定义",
  "自动抓拍事件",
  "驾驶员变更事件",
];

// 公司新闻管理 置顶状态
export const PINNED_STATUS = [
  {
    value: true,
    name: "置顶",
  },
  {
    value: false,
    name: "否",
  },
];

// 疲劳程度
export const FATIGUE_DEGREE = ["", "轻度", "轻度", "轻度", "中度", "中度", "中度", "重度", "重度", "重度"];

// 车辆性质 0-危运 1-非危运
export const VEHICLE_NATURE = ["危运", "非危运"];

// 监督抽查记录 0-未完成 1-已完成 2-无需整改
export const SUPERVISORY_TEST_COMPLETED = ["未完成", "已完成", "无需整改"];

// 收运点位档案 点位收运方式 0-桶装 1-袋装 2-桶袋混合
export const POINT_RECEIVING_METHOD = ["桶装", "袋装", "桶袋混合"];

// 加班审批 审批状态 0-待审批 1-已审批 2-已驳回
export const OVERTIME_APPROVAL_STATUS = ["待审批", "已审批", "已驳回"];

// 审核状态 0-待审核 1-审核通过 2-审核不通过
export const AUDIT_STATUS = ["待审核", "审核通过", "审核不通过"];

// 加班类型 0-工作日收运 1-周末收运 2-节假日收运
export const OVERTIME_TYPE = ["工作日收运", "周末收运", "节假日收运"];

// 加班类型 0-工作日加班 1-周末加班 2-节假日加班
export const OVER_TYPE = ["工作日加班", "周末加班", "节假日加班"];

// 通知类型 0-公众号 1-公告
export const NOTICE_TYPE = ["公众号", "公告"];
