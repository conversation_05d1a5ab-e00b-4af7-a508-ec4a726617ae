/**
 * @Date: 2022-06-09 11:11:12
 * @return {*}
 * @description:仪表盘
 */
// 引入 echarts 核心模块，核心模块提供了 echarts 使用必须要的接口。
import * as echarts from "echarts/core";

let gaugeOption = {
  backgroundColor: "",

  title: {
    text: "",

    textStyle: {
      color: "",
      fontSize: 18,
      lineHeight: 22,
      fontWeight: "bold",
    },
  },

  series: [
    {
      name: "",
      type: "gauge",

      data: [],

      center: ["50%", "45%"], // 仪表位置
      startAngle: 210, //开始角度
      endAngle: -30, //结束角度

      radius: "90%", //仪表大小
      axisLine: {
        show: true,
        lineStyle: {
          // 属性lineStyle控制线条样式

          color: [
            [
              1,
              new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                {
                  offset: 0.1,
                  color: "#14DE92",
                },
                {
                  offset: 0.5,
                  color: "#FABB28",
                },
                {
                  offset: 1,
                  color: "#E83F3F",
                },
              ]),
            ],
          ],

          width: 20, //表盘宽度
        },
      },
      splitLine: {
        //分割线
        show: false,
      },
      axisTick: {
        //刻度线
        show: false,
      },

      axisLabel: {
        //表盘刻度文字
        show: false,
      },

      anchor: {
        show: true,
        showAbove: true,
        size: 20,
        itemStyle: {
          color: "#2688EB",
        },
      },

      pointer: {
        show: true,
        icon: "path://M2.9,0.7L2.9,0.7c1.4,0,2.6,1.2,2.6,2.6v115c0,1.4-1.2,2.6-2.6,2.6l0,0c-1.4,0-2.6-1.2-2.6-2.6V3.3C0.3,1.9,1.4,0.7,2.9,0.7z",
        length: "90%",
        width: 4,
      },

      title: {
        //仪表盘标题
        show: false,
        offsetCenter: [0, 0], // x, y，单位px
        lineOverflow: "none",
        textStyle: {
          color: "#5B8EF9",
          fontSize: 14,
        },
      },

      detail: {
        show: true,
        offsetCenter: [0, "60%"],
        textStyle: {
          fontWeight: "bold",
          fontSize: 26,
          color: "#333",
        },
        formatter: "{value}%",
      },
    },
  ],

  color: ["#48A1FF", "#FBD44A", "#F3667C", "#63CC7C", "#9669F5", "#4DCCCB"],
};

export default gaugeOption;
