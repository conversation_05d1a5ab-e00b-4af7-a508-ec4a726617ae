<template>
  <div class="colors-container">
    <div class="colors-item" v-for="(item, index) in titleOptions" :key="index">
      <div class="item-circle" :style="{ backgroundColor: colorOptions[index] }"></div>
      <div class="item-text">{{ item }}</div>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      titleOptions: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        colorOptions: ["#546FC6", "#74C1DF"],
      };
    },
  };
</script>

<style lang="scss" scoped>
  .colors-container {
    display: flex;
    align-items: center;
    .colors-item {
      display: flex;
      align-items: center;
      margin-right: 20px;
      .item-circle {
        width: 12px;
        height: 12px;
        border-radius: 50%;
      }
      .item-text {
        font-size: 14px;
        margin-left: 10px;
      }
    }
  }
</style>
