<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title="设置生效日期"
      :visible.sync="dialogVisible"
      width="20%"
      destroy-on-close
      :close-on-click-modal="false"
      @open="initForm"
    >
      <div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="80px">
          <el-form-item prop="effectiveDate" label="生效日期">
            <el-date-picker
              v-model="ruleForm.effectiveDate"
              type="date"
              placeholder="请选择生效日期"
              clearable
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveThrottling">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      routeId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        ruleForm: {
          effectiveDate: "",
        },
        rules: {
          effectiveDate: [{ required: true, message: "请输入生效日期", trigger: "blur" }],
        },
        saveThrottling: () => {},
        apis: {
          setEffectiveDate: "/api/pickup/pickupPath/setEffectiveDate",
        },
        loading: false,
      };
    },
    created() {
      this.saveThrottling = this.$throttling(this.save, 500);
    },
    methods: {
      // 初始化
      initForm() {
        this.ruleForm.effectiveDate = "";
      },
      // 下一步
      save() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun(
                { id: this.routeId, effectiveDate: this.ruleForm.effectiveDate },
                this.apis.setEffectiveDate,
              );
              if (res.success) {
                this.$message.success("生效日期设置成功");
                this.dialogVisible = false;
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
