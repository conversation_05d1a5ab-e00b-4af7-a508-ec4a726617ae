<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content small-scroll">
      <!-- <div class="card-header">新收运任务待处理</div> -->
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
        <el-form-item label="预估废物总量（kg）" prop="">
          <el-input-number
            :value="ruleForm.rubbishTotal"
            placeholder="请输入预估废物总量（kg）"
            :precision="2"
            :controls="false"
          ></el-input-number>
        </el-form-item>
        <baseTitle title="业务调整信息"></baseTitle>
        <el-row>
          <el-col>
            <el-form-item label="收运车辆" prop="defaultVehiclePlateNumber">
              <el-select
                v-model="ruleForm.defaultVehiclePlateNumber"
                placeholder="请选择收运车辆"
                clearable
                filterable
                @change="changePlateNumber"
                @clear="changePlateNumber"
              >
                <el-option
                  v-for="item in carOptions"
                  :key="item.id"
                  :label="item.plateNumber + (item.isFree === 1 ? '' : '（空闲）')"
                  :value="item.plateNumber"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="6" :lg="6">
            <el-form-item label="司机" prop="defaultDriverDossierId">
              <el-select
                v-model="ruleForm.defaultDriverDossierId"
                placeholder="请选择司机"
                clearable
                filterable
                @change="driverAndWorkerChange($event, 'defaultDriverDossierPhone', driverOptions)"
              >
                <el-option
                  v-for="item in driverOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8">
            <el-form-item label="" prop="defaultDriverDossierPhone">
              <el-input
                v-model="ruleForm.defaultDriverDossierPhone"
                placeholder="请输入司机联系方式"
                clearable
                :maxlength="11"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="6" :lg="6">
            <el-form-item label="押运工1" prop="">
              <el-select
                v-model="ruleForm.supercargoDossierOneId"
                placeholder="请选择押运工1"
                clearable
                filterable
                @change="driverAndWorkerChange($event, 'supercargoDossierOnePhone', shipWorkerOptions)"
              >
                <el-option
                  v-for="item in shipWorkerOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8">
            <el-form-item label="" prop="">
              <el-input
                v-model="ruleForm.supercargoDossierOnePhone"
                placeholder="请输入押运工联系方式"
                clearable
                :maxlength="11"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="6" :lg="6">
            <el-form-item label="押运工2" prop="supercargoDossierTwoId">
              <el-select
                v-model="ruleForm.supercargoDossierTwoId"
                placeholder="请选择押运工2"
                clearable
                filterable
                @change="driverAndWorkerChange($event, 'supercargoDossierTwoPhone', shipWorkerOptions)"
              >
                <el-option
                  v-for="item in shipWorkerOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8">
            <el-form-item label="" prop="supercargoDossierTwoPhone">
              <el-input
                v-model="ruleForm.supercargoDossierTwoPhone"
                placeholder="请输入押运工联系方式"
                clearable
                :maxlength="11"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <baseTitle title="收运任务"></baseTitle>
      <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" border class="table-box">
        <el-table-column label="收运任务" align="center">
          <el-table-column prop="code" label="点位编号" align="center"></el-table-column>
          <el-table-column prop="name" label="产废单位名称" align="center"></el-table-column>
          <el-table-column prop="contact" label="产废单位经办人" align="center"></el-table-column>
          <el-table-column prop="waybillStatus" label="收运状态" align="center">
            <template #default="{ row }">{{ RECEIVING_CONDITION[row.hasWaybill] }}</template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <baseTitle title="备注信息"></baseTitle>
      <el-input
        v-model="ruleForm.memo"
        placeholder="请输入备注信息"
        clearable
        type="textarea"
        rows="5"
        maxlength="500"
        show-word-limit
      ></el-input>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">完成</el-button>
    </div>
  </div>
</template>

<script>
  import { createApiFun, getListApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import { ERROR_STATUS, CONTINUECARRYING_STATUS, RECEIVING_CONDITION } from "@/enums";
  export default {
    props: {
      recordItem: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      baseTitle,
    },
    data() {
      return {
        baseForm: {}, //业务基础信息
        ruleForm: {
          defaultVehiclePlateNumber: "", //收运车辆
          defaultDriverDossierId: "", //司机
          defaultDriverDossierPhone: "", //司机联系方式
          supercargoDossierOneId: "", //押运工1
          supercargoDossierOnePhone: "", //押运工联系方式
          supercargoDossierTwoId: "", //押运工2
          supercargoDossierTwoPhone: "", //押运工联系方式
          memo: "",
          rubbishTotal: 0,
        },
        rules: {
          defaultVehiclePlateNumber: [{ required: true, message: "请选择收运车辆", trigger: "change" }],
          defaultDriverDossierId: [{ required: true, message: "请选择司机", trigger: "change" }],
          defaultDriverDossierPhone: [{ required: true, message: "请输入司机联系方式", trigger: "change" }],
          supercargoDossierOneId: [{ required: true, message: "请选择押运工", trigger: "change" }],
          supercargoDossierOnePhone: [{ required: true, message: "请输入押运工联系方式", trigger: "change" }],
        },
        apis: {
          info: "/api/waybill/waybillDetail/listPage",
          carList: "/api/vehicle/dossier/getFreeList",
          userList: "/api/baseuser/list",
          driverAndWorkerInfo: "/api/baseuser/getInfoByPlateNumberAndUserIdentity",
          create: "/api/waybill/operationMonitorSponsorWaybill",
          // create: "/api/waybill/commissionTemporaryUpdate",
        },
        saveRecordThrottling: () => {},
        loading: false,
        carOptions: [],
        driverOptions: [],
        shipWorkerOptions: [],
        tableData: [],
        ERROR_STATUS,
        CONTINUECARRYING_STATUS,
        RECEIVING_CONDITION,
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptions();
      this.tableData = [this.recordItem];
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getListApiFun({ userIdentity: 4 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipWorkerOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },

      // 选择默认车牌号事件回调
      async changePlateNumber(value) {
        if (value) {
          let promiseList = [
            createApiFun({ userIdentity: "3", plateNumber: value }, this.apis.driverAndWorkerInfo),
            createApiFun({ userIdentity: "4", plateNumber: value }, this.apis.driverAndWorkerInfo),
          ];
          let res = await Promise.all(promiseList);
          let driverInfo = res[0].data;
          let workerInfo = res[1].data;
          if (driverInfo) {
            this.ruleForm.defaultDriverDossierId = driverInfo.lgUnionId;
            this.ruleForm.defaultDriverDossierPhone = this.$sm2Decrypt(driverInfo.phone);
          } else {
            this.ruleForm.defaultDriverDossierId = "";
            this.ruleForm.defaultDriverDossierPhone = "";
          }
          if (workerInfo) {
            this.ruleForm.supercargoDossierOneId = workerInfo.lgUnionId;
            this.ruleForm.supercargoDossierOnePhone = workerInfo.phone ? this.$sm2Decrypt(workerInfo.phone) : "";
          } else {
            this.ruleForm.supercargoDossierOneId = "";
            this.ruleForm.supercargoDossierOnePhone = "";
          }
        } else {
          this.ruleForm.defaultDriverDossierId = "";
          this.ruleForm.defaultDriverDossierPhone = "";
          this.ruleForm.supercargoDossierOneId = "";
          this.ruleForm.supercargoDossierOnePhone = "";
        }

        await this.$nextTick();
        this.$refs.ruleForm.clearValidate();
      },

      // 驾驶司机选择事件回调
      driverAndWorkerChange(value, field, options) {
        if (!value) {
          this.ruleForm[field] = "";
          return;
        }
        let filterItem = options.filter((o) => o.lgUnionId === value)[0];
        this.ruleForm[field] = filterItem.phone;
      },

      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },

      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun({ ...this.ruleForm, pickupPointId: this.recordItem.id }, this.apis.create);
              if (res.success) {
                this.$message.success(`操作成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    height: 863px;
    scrollbar-width: initial; /* Firefox 64+ */
    scrollbar-color: initial;
  }
  .record-content {
    flex: 1;
    overflow: auto;
    margin-bottom: 10px;
    padding-right: 10px;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-box {
    margin-bottom: 20px;
  }
</style>
