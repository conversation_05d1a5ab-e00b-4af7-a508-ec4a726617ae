<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" label-suffix="：">
        <el-row>
          <el-col>
            <el-form-item label="车牌号" prop="plateNumber">
              <el-select
                v-model="ruleForm.plateNumber"
                placeholder="请选择车牌号"
                clearable
                filterable
                :disabled="recordId ? true : false"
              >
                <el-option v-for="item in carOptions" :key="item.id" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="经办人" prop="operatorName">
              <el-input
                v-model="ruleForm.operatorName"
                placeholder="请输入经办人"
                clearable
                maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="年审金额(元)" prop="costs">
              <el-input-number v-model="ruleForm.costs" :precision="2" :min="0" :max="99999999999999"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="年审单位" prop="organizationName">
              <el-input
                v-model="ruleForm.organizationName"
                placeholder="请输入年审单位"
                clearable
                maxlength="100"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="年审单号" prop="odd">
              <el-input
                v-model="ruleForm.odd"
                placeholder="请输入年审单号"
                clearable
                maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="最近年审日期" prop="recentAnnualReviewTime">
              <el-date-picker
                v-model="ruleForm.recentAnnualReviewTime"
                type="date"
                placeholder="选择日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
              <div>
                <span class="el-icon-warning color-danger"></span>
                <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="下次年审日期" prop="nextAnnualReviewTime">
              <el-date-picker
                v-model="ruleForm.nextAnnualReviewTime"
                type="date"
                placeholder="选择日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
              <div>
                <span class="el-icon-warning color-danger"></span>
                <span class="date-tips">该日期信息会同步到车辆档案中，请仔细检查日期与车牌号是否准确。</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :md="24">
            <el-form-item label="备注" prop="remarks">
              <el-input
                v-model="ruleForm.remarks"
                placeholder="请输入备注"
                clearable
                type="textarea"
                rows="10"
                maxlength="500"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24">
            <el-form-item label="凭证" prop="fileList">
              <FileUpload @uploadChange="uploadChangeFileList" :imageList="imageList" :limit="5">
                <template #tips><el-tag type="warning">请上传年审单据</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import FileUpload from "@/components/FileUpload";
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      FileUpload,
    },
    data() {
      return {
        ruleForm: {
          plateNumber: "", //车牌号
          operatorName: "", //经办人
          costs: "", //年审金额
          organizationName: "", //年审单位
          recentAnnualReviewTime: "", //最近年审时间
          nextAnnualReviewTime: "", //下次年审时间
          odd: "", //年审单号
          remarks: "", //备注
          fileList: [], //年审凭证
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号", trigger: "change" }],
          operatorName: [{ required: true, message: "请输入经办人", trigger: "blur" }],
          costs: [{ required: true, message: "请输入年审金额", trigger: "blur" }],
          organizationName: [{ required: true, message: "请输入年审单位", trigger: "blur" }],
          recentAnnualReviewTime: [{ required: true, message: "请选择最近年审日期", trigger: "change" }],
          nextAnnualReviewTime: [{ required: true, message: "请选择下次年审日期", trigger: "change" }],
          fileList: [{ required: true, message: "请上传年审凭证", trigger: "change" }],
        },
        apis: {
          create: "/api/vehicle/annualReview/create",
          update: "/api/vehicle/annualReview/update",
          info: "/api/vehicle/annualReview/get/",
          carList: "/api/vehicle/dossier/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [],
        carOptions: [],
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptions();
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({ statusList: [0, 1] }, this.apis.carList)];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
      },
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.imageList = this.ruleForm.fileList.map((list) => {
            return {
              url: list.url,
              file: list,
            };
          });
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = this.recordId
                ? await updateApiFun(this.ruleForm, this.apis.update)
                : await createApiFun(this.ruleForm, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}年审记录成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      uploadChangeFileList(fileList) {
        if (fileList.length > 0) {
          this.ruleForm.fileList = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
          this.$refs.ruleForm.clearValidate("fileList");
        } else {
          this.ruleForm.fileList = [];
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
  }
  ::v-deep .el-transfer-panel__body {
    height: 400px;
  }
</style>
