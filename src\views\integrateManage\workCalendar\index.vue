<template>
  <div class="calendar-container" v-loading="loading">
    <div class="page-header">
      <h1 class="title">工作日历</h1>
      <el-date-picker
        v-model="currentDate"
        type="month"
        placeholder="选择月"
        :clearable="false"
        @change="handleDateChange"
        size="large"
      ></el-date-picker>
    </div>
    <div class="calendar-body">
      <div class="weekday-header">
        <div class="weekday-item" v-for="day in weekDays" :key="day">{{ day }}</div>
      </div>
      <div class="date-grid">
        <div
          v-for="(cell, index) in calendarList"
          :key="index"
          class="date-cell"
          :class="{
            holiday: cell.isHolidays || cell.isWeek,
            active: nowDate === cell.day,
            'other-month': cell.isOtherMonth,
          }"
          @click="openDialog(cell)"
        >
          <div class="cell-content">
            <div class="date-number">
              <div class="flex-1">{{ cell.date }}</div>
              <div class="date-name" v-if="cell.name">{{ cell.name }}</div>
            </div>
            <div class="work-status"
              >{{ cell.isHoliday ? "休息" : "工作" }}{{ cell.isSalaryHoliday ? "（x3）" : "" }}</div
            >
            <div class="work-status text-ellipsis" v-if="cell.remark">{{ cell.remark }}</div>
          </div>
        </div>
      </div>
    </div>
    <dateDialog :value.sync="showDialog" :dialogForm="dialogForm" @refreshCalendar="refreshCalendar"></dateDialog>
  </div>
</template>

<script>
  import moment from "moment";
  import { createApiFun } from "@/api/base";
  import dateDialog from "./components/dateDialog.vue";
  export default {
    name: "WorkCalendar",
    components: {
      dateDialog,
    },
    data() {
      return {
        currentDate: moment().format("YYYY-MM"),
        weekDays: ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"],
        apis: {
          holidayList: "/api/holiday/list",
        },
        nowYear: moment().format("YYYY"),
        nowDate: moment().format("YYYY-MM-DD"),
        showDialog: false,
        holidayObj: {},
        dialogForm: {},
        loading: false,
        calendarList: [],
      };
    },
    created() {
      this.refreshCalendar();
    },
    methods: {
      // 刷新数据
      async refreshCalendar() {
        await this.getHolidayList();
        this.generateCalendar();
      },
      // 获取节假日列表
      async getHolidayList() {
        this.loading = true;
        try {
          let res = await createApiFun({}, this.apis.holidayList);
          res.data.forEach((item) => {
            this.holidayObj[item.day] = item;
          });
          this.loading = false;
        } catch (_) {
          this.loading = false;
        }
      },
      // 生成月份日历列表
      generateCalendar() {
        const cells = [];
        const currentMoment = moment(this.currentDate);
        const firstDay = moment(currentMoment).startOf("month");
        const lastDay = moment(currentMoment).endOf("month");

        // 获取上个月需要显示的天数
        let firstDayWeekday = firstDay.day() || 7; // 转换周日的0为7
        const prevMonthDays = firstDayWeekday - 1;

        // 添加上个月的日期
        const prevMonth = moment(firstDay).subtract(1, "month");
        const prevMonthLastDay = prevMonth.daysInMonth();
        for (let i = prevMonthDays - 1; i >= 0; i--) {
          const date = prevMonthLastDay - i;
          const currentDate = moment(prevMonth).date(date);
          cells.push({
            date,
            day: currentDate.format("YYYY-MM-DD"),
            isOtherMonth: true,
          });
        }

        // 添加当前月的日期
        for (let date = 1; date <= lastDay.date(); date++) {
          const currentDate = moment(this.currentDate).date(date);
          const dateStr = currentDate.format("YYYY-MM-DD");
          const holiday = this.holidayObj[dateStr];

          cells.push({
            date,
            day: dateStr,
            isOtherMonth: false,
            isHoliday: holiday || this.isWeekend(currentDate),
            isSpecial: holiday?.isSpecial,
            holidayName: holiday?.name,
          });
        }

        // 添加下个月的日期
        const nextMonth = moment(lastDay).add(1, "month");
        const remainingCells = 42 - cells.length; // 保持6行
        for (let i = 1; i <= remainingCells; i++) {
          const currentDate = moment(nextMonth).date(i);
          cells.push({
            date: i,
            day: currentDate.format("YYYY-MM-DD"),
            isOtherMonth: true,
          });
        }
        let calendarList = [];
        cells.forEach((item) => {
          let obj = item;
          if (this.holidayObj[item.day]) {
            obj = Object.assign(obj, this.holidayObj[item.day]);
          }
          calendarList.push(obj);
        });
        this.calendarList = calendarList;
      },
      handleDateChange(date) {
        this.currentDate = date;
        this.generateCalendar();
      },
      isWeekend(date) {
        const day = moment(date).day();
        return day === 0 || day === 6;
      },
      // 打开修改日历弹窗
      openDialog(item) {
        if (moment(item.day).format("YYYY") === this.nowYear) {
          this.dialogForm = item;
          this.showDialog = true;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .calendar-container {
    height: 100%;
    background: #fff;
    padding: 16px;
  }
  .page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding: 0 8px;
    .title {
      font-size: 20px;
      font-weight: bold;
      margin: 0;
      color: #333;
    }
  }
  .calendar-body {
    background: #fff;
    border-radius: 4px;
    padding: 16px;
  }
  .weekday-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    margin-bottom: 8px;
    .weekday-item {
      padding: 12px;
      text-align: center;
      font-weight: normal;
      color: #333;
      background: #f5f7fa;
      border-radius: 8px;
      font-size: 14px;
    }
  }
  .date-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
  }
  .date-cell {
    background: #fff;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    min-height: 100px;
    position: relative;
    transition: all 0.3s;
    cursor: pointer;
    overflow: hidden;
    &.holiday {
      background-color: #fff1f0;
      .date-number,
      .work-status {
        color: #ff4d4f;
      }
    }
    &.active {
      background-color: var(--color-primary);
      .date-number,
      .work-status {
        color: #fff;
      }
    }
    &.other-month {
      opacity: 0.5;
    }
    .cell-content {
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
    .date-number {
      font-size: 16px;
      color: #333;
      font-weight: 500;
      display: flex;
      align-items: center;
    }
    .work-status {
      font-size: 14px;
      color: #666;
    }
  }
  .date-name {
    font-size: 14px;
  }
</style>
