<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="选择人员" :visible.sync="dialogVisible" width="60%" destroy-on-close @open="initData">
      <div v-loading="loading">
        <header class="header">
          <div class="header-left">
            <InterviewChannel :value.sync="filterForm.channelId" :record.sync="channelRecord"></InterviewChannel>
            <el-form ref="filterForm" inline :model="filterForm" label-suffix=":" label-width="80px">
              <el-form-item label="姓名">
                <el-input v-model="filterForm.fullName" placeholder="请输入姓名" clearable></el-input>
              </el-form-item>
              <el-form-item label="联系电话">
                <el-input v-model="filterForm.phone" placeholder="请输入联系电话" clearable></el-input>
              </el-form-item>
            </el-form>
          </div>
          <div class="header-right">
            <el-button @click="resetFilter">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
          </div>
        </header>
        <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" border>
          <el-table-column type="index" align="center"></el-table-column>
          <el-table-column prop="fullName" label="真实姓名" align="center"></el-table-column>
          <el-table-column prop="userIdentity" label="用户身份" align="center">
            <template #default="{ row }">{{ USER_IDENTITY[row.userIdentity] }}</template>
          </el-table-column>
          <el-table-column prop="phone" label="联系电话" align="center"></el-table-column>
          <el-table-column prop="userName" label="用户名" align="center"></el-table-column>
          <el-table-column prop="assessSchemeName" label="考核方案信息" align="center">
            <template #default="{ row }">{{
              row.assessSchemeName || row.assessSchemeName === 0 ? row.assessSchemeName : "-"
            }}</template>
          </el-table-column>
          <el-table-column prop="channelId" label="渠道名称" align="center">
            <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
          </el-table-column>
          <el-table-column min-width="80" label="操作" align="center">
            <template #default="{ row }">
              <template
                v-if="
                  assessSchemeId ? assessSchemeId === row.assessSchemeId || !row.assessSchemeId : !row.assessSchemeId
                "
              >
                <el-link type="danger" @click="deletePerson(row)" v-if="personIds.includes(row.lgUnionId)"
                  >删除</el-link
                >
                <el-link type="primary" @click="selectPerson(row)" v-else>选择</el-link>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import { USER_IDENTITY } from "@/enums";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      personList: {
        type: Array,
        default: () => [],
      },
      period: {
        type: Number,
        default: 0,
      },
      assessSchemeId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
      personIds() {
        return this.personList.map((list) => list.lgUnionId);
      },
    },
    data() {
      return {
        loading: false,
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/assess/scheme/getAssessSchemeSelectUserListPage",
        },
        USER_IDENTITY,
        channelRecord: {},
      };
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          ...this.filterForm,
          period: this.period,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 选择人员
      selectPerson(row) {
        this.$emit("selectPerson", row);
      },
      // 删除人员
      deletePerson(row) {
        this.$emit("deletePerson", row);
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .header-left {
      display: flex;
      flex: 1;
    }
    .header-right {
      margin-top: 20px;
    }
  }
  ::v-deep .header-left .el-form-item {
    margin-bottom: 0;
    margin-top: 20px;
  }
</style>
