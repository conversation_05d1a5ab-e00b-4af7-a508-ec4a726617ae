<template>
  <div>
    <el-dialog
      title="点位码"
      :visible.sync="dialogVisible"
      width="400px"
      destroy-on-close
      append-to-body
      @open="initData"
    >
      <div class="micro-app-sctmp_base" v-loading="loading">
        <div class="qrcode-container">
          <div class="qrcode-content">
            <canvas id="qrcode-canvas" ref="canvas"></canvas>
          </div>
          <div class="qrcode-footer">
            <el-button class="qrcode-btn" icon="el-icon-download" @click="downloadQrcode">下载二维码</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import QRCode from "qrcode";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      recordId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        loading: false,
      };
    },
    methods: {
      async initData() {
        await this.$nextTick();
        QRCode.toCanvas(
          document.getElementById("qrcode-canvas"),
          `${process.env.VUE_APP_H5_WEB_URL}qrcode?to=qrcode&id=${this.recordId}&isRefresh=true`,
          {
            width: 300,
            height: 300,
            errorCorrectionLevel: "H", //容错级别,指二维码被遮挡可以扫出结果的区域比例
            type: "image/png", //生成的二维码类型
            quality: 0.5, //二维码质量
            margin: 0, //二维码留白边距
            toSJISFunc: QRCode.toSJIS,
          },
          (error) => {
            console.log(error);
          },
        );
      },
      downloadQrcode() {
        this.loading = true;
        let base64Img = this.$refs.canvas.toDataURL("image/png");
        //创建下载标签，然后设置链接和图片名称
        let a = document.createElement("a");
        a.href = base64Img;
        a.download = "点位码" + Date.now();
        a.click();
        //销毁元素
        a.remove();
        this.$message.success("下载成功");
        this.loading = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .qrcode-container {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .qrcode-content {
    width: 100%;
    height: 360px;
    overflow: hidden;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .qrcode-footer {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
    .qrcode-btn {
      width: 100%;
    }
  }
  #qrcode-canvas {
    width: 300px;
    height: 300px;
  }
</style>
