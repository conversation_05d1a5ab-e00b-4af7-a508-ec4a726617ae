<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <template v-if="showDetail">
      <header class="header">
        <div class="header-left">
          <el-form ref="filterForm" inline :model="filterForm" label-suffix=":" label-width="140px" @submit.prevent>
            <el-form-item label="收运单编号">
              <el-input v-model="filterForm.waybillCode" placeholder="请输入收运单编号" clearable></el-input>
            </el-form-item>
            <el-form-item label="路线编号">
              <el-input v-model="filterForm.code" placeholder="请输入路线编号" clearable></el-input>
            </el-form-item>
            <el-form-item label="路线名称">
              <el-input v-model="filterForm.name" placeholder="请输入路线名称" clearable></el-input>
            </el-form-item>
            <el-form-item label="默认车辆车牌号">
              <el-input
                v-model="filterForm.defaultVehiclePlateNumber"
                placeholder="请输入车辆车牌号"
                clearable
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="header-right">
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
        </div>
      </header>
      <div class="record-content">
        <el-table
          :data="detailData"
          :header-cell-style="{ background: '#F5F7F9' }"
          height="100%"
          style="width: 100%"
          border
        >
          <el-table-column type="index" align="center"></el-table-column>
          <el-table-column prop="waybillCode" label="收运单编号" align="center" min-width="100"></el-table-column>
          <el-table-column prop="effectiveDate" label="收运单生效日期" align="center" min-width="160"></el-table-column>
          <el-table-column prop="isTemp" label="临时收运单" align="center" min-width="100">
            <template #default="{ row }">{{ IS_TEMPORARY[row.isTemp] }}</template>
          </el-table-column>
          <el-table-column prop="name" label="路线名称" align="center" min-width="120"></el-table-column>
          <el-table-column prop="code" label="路线编号" align="center" min-width="120"></el-table-column>
          <el-table-column prop="districtName" label="所属区域" align="center"></el-table-column>
          <el-table-column prop="type" label="路线属性" align="center" min-width="120">
            <template #default="{ row }">{{ ROUTE_PROPERTY[row.type] }}</template>
          </el-table-column>
          <el-table-column
            prop="defaultVehiclePlateNumber"
            label="默认车辆"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column prop="defaultDriverDossierName" label="默认司机" align="center"></el-table-column>
          <el-table-column
            prop="defaultDriverDossierPhone"
            label="司机联系方式"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column prop="supercargoDossierOneName" label="押运工" align="center"></el-table-column>
          <el-table-column
            prop="supercargoDossierOnePhone"
            label="押运工联系方式"
            align="center"
            min-width="120"
          ></el-table-column>
          <el-table-column prop="supercargoDossierTwoName" label="押运工2" align="center"></el-table-column>
          <el-table-column
            prop="supercargoDossierTwoPhone"
            label="押运工2联系方式"
            align="center"
            min-width="140"
          ></el-table-column>
          <el-table-column prop="versionNumber" label="路线版本号" align="center" min-width="100">
            <template #default="{ row }">v{{ row.versionNumber.toFixed(1) }}</template>
          </el-table-column>
          <el-table-column prop="status" label="路线状态" align="center">
            <template #default="{ row }">{{ ROUTE_STATUS[row.status] }}</template>
          </el-table-column>
          <el-table-column prop="pointNumber" label="点位数量" align="center"></el-table-column>
          <el-table-column prop="updateFullname" label="最近修改人" align="center" min-width="140"></el-table-column>
          <el-table-column prop="updateTime" label="最近修改时间" align="center" min-width="160"></el-table-column>
        </el-table>
      </div>
      <Pagination :page="page" @pageChange="pageChange"></Pagination>
    </template>
    <div class="record-content" v-else>
      <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" style="width: 100%" border>
        <el-table-column type="index" width="55" align="center"></el-table-column>
        <el-table-column prop="effectiveDate" label="生效日期" align="center"></el-table-column>
        <el-table-column prop="num" label="任务总量" align="center"></el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="{ row }">
            <el-popconfirm title="确认是否批量下发电子收运单？" @confirm="batchDelivery(row)">
              <el-link class="mr-10" type="primary" slot="reference">批量下发</el-link>
            </el-popconfirm>
            <el-link type="primary" @click="editRecord(row)">详情</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
  import { createApiFun, getListPageApiFun } from "@/api/base";
  import { IS_TEMPORARY, ROUTE_PROPERTY, ROUTE_STATUS } from "@/enums";
  export default {
    props: {
      recordItem: {
        type: Object,
        default: () => {},
      },
    },
    data() {
      return {
        IS_TEMPORARY,
        ROUTE_PROPERTY,
        ROUTE_STATUS,
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        apis: {
          list: "/api/waybill/statisticsEffectiveDateList",
          batch: "/api/waybill/issueWaybill",
          listPage: "/api/waybill/listPage",
        },
        loading: false,
        tableData: [],
        showDetail: false,
        effectiveDate: "",
        detailData: [],
      };
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      async getRecord() {
        let res = await createApiFun({ channelId: this.recordItem.channelId }, this.apis.list);
        if (res.success) {
          this.tableData = res.data;
        }
      },
      // 关闭弹窗
      closeRecord() {
        if (this.showDetail) {
          this.showDetail = false;
          return;
        }
        this.$emit("returnIndex");
      },
      // 详情
      editRecord(row) {
        this.showDetail = true;
        this.page.pageSize = 10;
        this.effectiveDate = row.effectiveDate;
        this.resetFilter();
      },
      // 批量下发
      async batchDelivery(row) {
        try {
          let res = await createApiFun({ effectiveDate: row.effectiveDate }, this.apis.batch);
          if (res.success) {
            this.$message.success("批量下发成功");
            this.getRecord();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          effectiveDate: this.effectiveDate,
          issueStatus: 0,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.detailData = res.data.datas.map((item) => {
            return {
              ...item,
              defaultDriverDossierPhone: item.defaultDriverDossierPhone
                ? this.$sm2Decrypt(item.defaultDriverDossierPhone)
                : "",
              supercargoDossierOnePhone: item.supercargoDossierOnePhone
                ? this.$sm2Decrypt(item.supercargoDossierOnePhone)
                : "",
              supercargoDossierTwoPhone: item.supercargoDossierTwoPhone
                ? this.$sm2Decrypt(item.supercargoDossierTwoPhone)
                : "",
            };
          });
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .header-left {
      flex: 1;
    }
    .header-right {
      margin-top: 20px;
    }
  }
  .mr-10 {
    margin-right: 10px;
  }
</style>
