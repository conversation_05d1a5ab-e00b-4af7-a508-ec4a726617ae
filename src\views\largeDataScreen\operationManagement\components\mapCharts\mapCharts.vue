<template>
  <div class="charts-style map-charts">
    <el-button class="charts-btn" type="primary" @click="back" v-show="showMap"> 返回上一级</el-button>

    <div class="search-container" :class="{ 'search-map': !showMap }">
      <el-select-v2
        v-model="searchVal"
        :options="getOptions"
        placeholder="请输入点位名称搜索"
        no-match-text="无点位匹配"
        no-data-text="无点位数据"
        loading-text="点位获取中..."
        clearable
        :loading="loading"
        filterable
        value-key="id"
        label-key="name"
        :min-item-size="60"
        :popper-append-to-body="false"
        @change="handleSearch"
        @clear="handleSearch"
        :remote="!showMap"
        :remote-method="querySearch"
        ref="selectMarkerRef"
      >
        <template slot-scope="{ item }">
          <div class="name">{{ item.name }}</div>
          <span class="addr">{{ item.address }}</span>
        </template>
      </el-select-v2>
    </div>

    <div class="marker-type" v-if="showMap">
      <template v-for="(item, key, index) in imgMap">
        <div
          class="marker-type-item"
          :key="index"
          v-if="index % 2 == 0"
          :class="{ 'marker-type-active': currentType === item.type }"
          @click="handleMarkerType(item)"
        >
          <img :src="item.url" alt="" />
          <span>{{ item.text }}</span>
        </div>
      </template>
    </div>

    <div class="gd-map" v-if="showMap">
      <mapContainer @initMap="initMap" mapId="mapContainer100"></mapContainer>
    </div>

    <div v-if="!showMap" class="charts-style">
      <div id="sexCharts" class="charts-style"></div>
    </div>

    <div class="map-info" v-if="showMap"
      >{{ currentName }} <span v-if="currentLineItem"> / {{ currentLineItem.name }} </span>
    </div>
  </div>
</template>

<script>
  import * as echarts from "echarts";
  import gzJson from "@/components/mapCharts/guangzhou.json";
  import czJson from "@/components/mapCharts/chaozhou.json";
  import swJson from "@/components/mapCharts/shanwei.json";
  import mapContainer from "@/components/mapContainer";
  import { getPointListByDistrictId, getWaybillDetailByPointId, pickupPointListApi } from "@/api/OperationManagement";
  import emitter from "@/utils/mitt";
  import { wgs84togcj02 } from "@/utils/coordinateTransform";
  import ElSelectV2 from "el-select-v2";
  let markerList = []; // 全部点位
  // let searchMarkerList = []; // 搜索的数组，因为可能连线时候某些点位没有展示，所以另外用个数组存
  let lineMarkerList = []; // 线路点位
  let _map = null;
  import store from "@/store";

  export default {
    props: {
      additiveLabel: {
        type: Object,
        default: () => {},
      },
      areaData: {
        type: Array,
        default: () => [],
      },
      channelList: {
        type: Array,
        default: () => [],
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    components: {
      mapContainer,
      ElSelectV2,
    },
    watch: {
      areaData: {
        handler() {
          if (!this.showMap) {
            this.initCharts();
          }
        },
        deep: true,
      },
      channelId() {
        this.searchMarkerList = [];
      },
    },
    data() {
      return {
        loading: false, // 是否加载中
        myChart: null,
        isShowBtn: false,
        showMap: false,
        map: "",
        routePath: [],
        gzData: [
          { name: "荔湾区", value: [113.227791, 23.088038, "荔湾区"], code: 440103 },
          { name: "海珠区", value: [113.326676, 23.082002, "海珠区"], code: 440105 },
          { name: "越秀区", value: [113.281396, 23.13291, "越秀区"], code: 440104 },
          { name: "白云区", value: [113.323943, 23.289328, "白云区"], code: 440111 },
          { name: "番禺区", value: [113.406928, 22.973597, "番禺区"], code: 440113 },
          { name: "南沙区", value: [113.544315, 22.736609, "南沙区"], code: 440115 },
          { name: "从化区", value: [113.680484, 23.677286, "从化区"], code: 440117 },
          { name: "花都区", value: [113.214795, 23.442737, "花都区"], code: 440114 },
        ],
        czData: [
          { name: "湘桥区", value: [116.711526, 23.670302, "湘桥区"], code: 445102 },
          { name: "潮安区", value: [116.67931, 23.461012, "潮安区"], code: 445103 },
          { name: "饶平县", value: [116.911862, 23.83412, "饶平县"], code: 445122 },
        ],
        swData: [
          { name: "城区", value: [115.426007, 22.76914, "城区"], code: 441502 },
          { name: "海丰县", value: [115.278327, 22.96101, "海丰县"], code: 441521 },
          { name: "陆河县", value: [115.622646, 23.277554, "陆河县"], code: 441523 },
          { name: "陆丰市", value: [115.784775, 22.95671, "陆丰市"], code: 441581 },
        ],
        gz: {
          lines: [
            {
              type: "lines",
              zlevel: 3, //设置这个才会有轨迹线的小尾巴
              polyline: true,
              lineStyle: {
                normal: {
                  color: "#44FFF5", //线条颜色
                  width: 1.5,
                  curveness: 0.2,
                  shadowColor: "#44FFF5",
                },
              },
              data: [
                {
                  coords: [
                    [113.227791, 23.088038],
                    [113.107791, 22.888038],
                    [112.987791, 22.888038],
                  ],
                },
                {
                  coords: [
                    [113.680484, 23.677286],
                    [113.780484, 23.757286],
                    [114.080484, 23.757286],
                  ],
                },
                {
                  coords: [
                    [113.281396, 23.13291],
                    [113.217791, 23.188038],
                    [113.067791, 23.188038],
                  ],
                },
                {
                  coords: [
                    [113.326676, 23.082002],
                    [113.726676, 23.082002],
                    [113.826676, 23.002002],
                  ],
                },
                {
                  coords: [
                    [113.214795, 23.442737],
                    [113.214795, 23.842737],
                    [113.004795, 23.842737],
                  ],
                },
                {
                  coords: [
                    [113.323943, 23.289328],
                    [113.623943, 23.469328],
                    [113.823943, 23.469328],
                  ],
                },
                {
                  coords: [
                    [113.406928, 22.973597],
                    [113.406928, 22.673597],
                    [113.306928, 22.573597],
                  ],
                },
                {
                  coords: [
                    [113.544315, 22.736609],
                    [113.844315, 22.736609],
                    [113.904315, 22.706609],
                  ],
                },
              ],
            },
          ],
          labelData: [
            {
              name: "荔湾区",
              coords: [
                [113.107791, 22.888038],
                [112.667791, 22.888038],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
            {
              name: "从化区",
              coords: [
                [113.780484, 23.757286],
                [114.080484, 23.757286],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
            {
              name: "越秀区",
              coords: [
                [113.217791, 23.188038],
                [112.747791, 23.188038],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
            {
              name: "海珠区",
              coords: [
                [113.726676, 23.082002],
                [113.826676, 23.002002],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
            {
              name: "花都区",
              coords: [
                [113.214795, 23.842737],
                [112.684795, 23.842737],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
            {
              name: "白云区",
              coords: [
                [113.623943, 23.469328],
                [113.823943, 23.469328],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
            {
              name: "番禺区",
              coords: [
                [113.406928, 22.673597],
                [112.986928, 22.573597],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
            {
              name: "南沙区",
              coords: [
                [113.844315, 22.736609],
                [113.904315, 22.706609],
              ], // 线条位置[开始位置，结束位置]
              value: [56, 4, 4],
            },
          ],
        },
        cz: {
          lines: [
            {
              type: "lines",
              zlevel: 3,
              polyline: true,
              lineStyle: {
                normal: {
                  color: "#44FFF5",
                  width: 1.5,
                  curveness: 0.2,
                  shadowColor: "#44FFF5",
                },
              },
              data: [
                {
                  coords: [
                    [116.711526, 23.670302],
                    [116.811526, 23.770302],
                    [116.911526, 23.770302],
                  ],
                },
                {
                  coords: [
                    [116.67931, 23.461012],
                    [116.57931, 23.361012],
                    [116.47931, 23.361012],
                  ],
                },
                {
                  coords: [
                    [116.911862, 23.83412],
                    [117.011862, 23.93412],
                    [117.111862, 23.93412],
                  ],
                },
              ],
            },
          ],
          labelData: [
            {
              name: "湘桥区",
              coords: [
                [116.811526, 23.770302],
                [116.911526, 23.770302],
              ],
              value: [56, 4, 4],
            },
            {
              name: "潮安区",
              coords: [
                [116.67931, 23.361012],
                [116.29931, 23.361012],
              ],
              value: [56, 4, 4],
            },
            {
              name: "饶平县",
              coords: [
                [117.011862, 23.93412],
                [117.111862, 23.93412],
              ],
              value: [56, 4, 4],
            },
          ],
        },
        sw: {
          lines: [
            {
              type: "lines",
              zlevel: 3,
              polyline: true,
              lineStyle: {
                normal: {
                  color: "#44FFF5",
                  width: 1.5,
                  curveness: 0.2,
                  shadowColor: "#44FFF5",
                },
              },
              data: [
                {
                  coords: [
                    [115.426007, 22.76914],
                    [115.326007, 22.66914],
                    [115.226007, 22.66914],
                  ],
                },
                {
                  coords: [
                    [115.278327, 22.96101],
                    [115.178327, 23.06101],
                    [115.078327, 23.06101],
                  ],
                },
                {
                  coords: [
                    [115.622646, 23.277554],
                    [115.722646, 23.377554],
                    [115.822646, 23.377554],
                  ],
                },
                {
                  coords: [
                    [115.784775, 22.95671],
                    [115.884775, 22.85671],
                    [115.984775, 22.85671],
                  ],
                },
              ],
            },
          ],
          labelData: [
            {
              name: "城区",
              coords: [
                [115.326007, 22.66914],
                [114.586007, 22.66914],
              ],
              value: [56, 4, 4],
            },
            {
              name: "海丰县",
              coords: [
                [115.178327, 23.06101],
                [114.438327, 23.06101],
              ],
              value: [56, 4, 4],
            },
            {
              name: "陆河县",
              coords: [
                [115.722646, 23.377554],
                [115.822646, 23.377554],
              ],
              value: [56, 4, 4],
            },
            {
              name: "陆丰市",
              coords: [
                [115.884775, 22.85671],
                [115.984775, 22.85671],
              ],
              value: [56, 4, 4],
            },
          ],
        },
        currentLineItem: "", // 当前绘制线
        searchVal: "",
        lineRouteList: [],
        polygon: null,
        districtSearch: "",
        currentName: "", // 当前点击的地区名称
        currentHighlightedMarker: null, // 当前搜索的点位
        isShowSearch: false,
        areaId: "", // 子区域id
        isCanSend: true,
        // 点位类型type: 0-大医院 1-小诊所 2-智能收集柜 3-小型床位
        imgMap: {
          a00: {
            url: require("@/assets/images/marker/large_yy.png"),
            text: "大型床位", // 未收运图
            type: 0,
          },
          a01: {
            url: require("@/assets/images/marker/large_yy_active.png"),
            text: "大型床位（已收运）", // 收运图
            type: 0,
          },
          a30: {
            url: require("@/assets/images/marker/small_yy_bed.png"),
            text: "小型床位", // 未收运图
            type: 3,
          },
          a31: {
            url: require("@/assets/images/marker/small_yy_bed_active.png"),
            text: "小型床位（已收运）", // 收运图
            type: 3,
          },
          a10: {
            url: require("@/assets/images/marker/small_zs.png"),
            text: "小诊所", // 未收运图
            type: 1,
          },
          a11: {
            url: require("@/assets/images/marker/small_zs_active.png"),
            text: "小诊所（已收运）", // 收运图
            type: 1,
          },
          a20: {
            url: require("@/assets/images/marker/gz.png"),
            text: "智能收集柜", // 未收运图
            type: 2,
          },
          a21: {
            url: require("@/assets/images/marker/gz_active.png"),
            text: "智能收集柜（已收运）", // 收运图
            type: 2,
          },
        },
        // 高亮点位图标
        imgHgMap: {
          a011: require("@/assets/images/marker/large_yy_gl.png"), // 高亮图
          a001: require("@/assets/images/marker/large_yy_gl.png"), // 高亮图
          a111: require("@/assets/images/marker/small_zs_gl.png"),
          a101: require("@/assets/images/marker/small_zs_gl.png"),
          a211: require("@/assets/images/marker/gz_gl.png"),
          a201: require("@/assets/images/marker/gz_gl.png"),
          a311: require("@/assets/images/marker/small_yy_bed_gl.png"),
          a301: require("@/assets/images/marker/small_yy_bed_gl.png"),
        },
        currentType: null,
        pickupPointId: "", // 点位id,用以记录地图外搜索时候的点位id
        searchMarkerList: [], //  搜索的数组，因为可能连线时候某些点位没有展示，所以另外用个数组存
      };
    },

    computed: {
      getOptions: function () {
        if (this.showMap) {
          return this.searchMarkerList.map((item) => item.extData);
        }
        return this.searchMarkerList;
      },
    },

    mounted() {
      setTimeout(() => {
        this.initCharts();
      }, 1000);
    },

    beforeDestroy() {
      if (_map) {
        _map.clearInfoWindow();
        _map.clearMap();
        _map.destroy();
        _map = "";
      }
    },

    methods: {
      initCharts() {
        let chartDom = document.getElementById("sexCharts");
        this.myChart = echarts.init(chartDom);
        this.isShowBtn = false;
        let chartKey = "gz";
        let dataJson = gzJson;
        if (this.channelId) {
          let filterItem = this.channelList.filter((list) => list.id === this.channelId)[0];
          if (filterItem.name.includes("潮州")) {
            chartKey = "cz";
            dataJson = czJson;
          }
          if (filterItem.name.includes("汕尾")) {
            chartKey = "sw";
            dataJson = swJson;
          }
        }
        echarts.registerMap(chartKey.toUpperCase(), dataJson);
        this.$emit("refreshData", null);
        var lines = [];
        if (this.additiveLabel && Object.keys(this.additiveLabel).length != 0) {
          lines = this[chartKey].lines;
        }
        let LableData = this[chartKey].labelData;

        const option = {
          geo: {
            map: chartKey.toUpperCase(),
            roam: true,
            zoom: 0.8,
            top: "16%",
            layoutSize: "50%",
            aspectScale: 0.85,
            scaleLimit: {
              min: 0.85,
              max: 0.85,
            },
            label: {
              color: "#ffffff",
              fontWeight: 600,
              // show: true,
              fontSize: 0,
            },
            itemStyle: {
              normal: {
                areaColor: "#27A369",
                borderWidth: 2,
                borderColor: "#A3E789",
              },
              emphasis: {
                borderColor: "#A3E789",
                borderWidth: 2,
                areaColor: "#065244",
              },
            },
          },
          series: [
            {
              type: "scatter",
              coordinateSystem: "geo",
              symbolSize: 0,
              encode: { value: 2 },
              symbol: "pin",
              zlevel: 1,
            },
            {
              type: "effectScatter",
              coordinateSystem: "geo",
              zlevel: 2,
              symbolSize: 6,
              rippleEffect: {
                //坐标点动画
                period: 3,
                scale: 5,
                brushType: "fill",
              },
              label: {
                normal: {
                  show: true,
                  position: "right",
                  formatter: "{b}",
                  color: "#b3e2f2",
                  fontWeight: "bold",
                  fontSize: 12,
                },
              },

              data: this[chartKey + "Data"],
              itemStyle: {
                //坐标点颜色
                normal: {
                  show: true,
                  color: "#44FFF5",
                  shadowBlur: 20,
                  shadowColor: "#44FFF5",
                },
                emphasis: {
                  areaColor: "#f00",
                },
              },
            },
            ...lines,
            {
              type: "lines",
              zlevel: 4,
              color: "#ff8003",
              label: this.additiveLabel,
              lineStyle: {
                type: "dashed",
                color: "#fff",
                width: 0.1,
                opacity: 1,
              },
              data: LableData,
            },
          ],
        };

        this.myChart.setOption(option);

        // 点击下钻
        this.myChart.off("click"); //关闭事件
        this.myChart.on("click", async (params) => {
          this.jumpChild(params);
        });
      },

      // 初始化地图
      initMap(map) {
        if (map) {
          _map = map;
          window.AMap.plugin("AMap.Scale", () => {
            let scale = new window.AMap.Scale(); //创建工具条插件实例
            map.addControl(scale); //添加工具条插件到页面
          });
          window.AMap.plugin("AMap.ToolBar", () => {
            let toolbar = new window.AMap.ToolBar(); //创建工具条插件实例
            map.addControl(toolbar); //添加工具条插件到页面
          });

          this.initMapCallBack();
        }
      },

      initMapCallBack() {
        window.AMap.plugin("AMap.DistrictSearch", () => {
          this.districtSearch = new window.AMap.DistrictSearch({
            subdistrict: 0, //获取边界不需要返回下级行政区
            extensions: "all", //返回行政区边界坐标组等具体信息
            level: "district", //查询行政级别为 市
          });
          this.drawDistrict(this.currentName);
        });

        // 重置值
        if (markerList) {
          this.showOrHiddenMarker(markerList, false);
        }
        this.resetActive("line");
        this.closeInfoWindow();
        this.searchVal = "";
        this.currentLineItem = "";
        this.currentHighlightedMarker = "";
        this.currentType = "";
        this.removeLine();
        this.getPointListByDistrictIdData(this.areaId);
        this.$emit("changeMap", this.areaId);
        this.sendDataToCollectionData({
          type: 1,
          areaId: this.areaId,
        });
      },

      // 绘制区域
      drawDistrict(keyword) {
        this.districtSearch.search(keyword, (_, result) => {
          if (this.polygon) {
            _map.remove(this.polygon); //清除上次结果
            this.polygon = null;
          }
          if (!result || !result.districtList || !result.districtList[0]) {
            // 区域数据错误
            return;
          }
          if (result.districtList[0].boundaries) {
            //生成行政区划polygon
            let bounds = result.districtList[0].boundaries.map((item) => [item]);
            if (bounds) {
              this.polygon = new window.AMap.Polygon({
                strokeWeight: 1,
                path: bounds,
                fillOpacity: 0.4,
                fillColor: "#80d8ff",
                strokeColor: "#0091ea",
              });
              _map.add(this.polygon);
              _map.setFitView(this.polygon); //视口自适应
            }
          }
        });
      },

      // 改成子级展示高德地图
      async jumpChild(params) {
        if (params.name === "") {
          return;
        }

        const data = this.areaData.find((item) => item.areaName == params.name);
        if (!data) {
          this.$message.error("区域名称获取错误");
          return;
        }

        this.currentName = params.name;
        this.areaId = data.areaId;

        // 地图已经展示了,右侧切换时候直接切区域就行,不需要重新初始化
        if (this.showMap) {
          this.initMapCallBack();
        }

        this.showMap = true;
        this.currentType = "";
        this.searchMarkerList = [];

        // 通知左侧组件高亮
        this.resetActive("area", data.areaId);
        this.drawing = true;
        this.$emit("changeChannel", false);
      },

      // 路线绘制，点位点击都会触发
      sendDataToCollectionData(data) {
        /**
         * 向 collectionData 组件传递
         *  type: 1,区域变化 2.路线变化 3.点位变化
         * **/
        emitter.emit("changeFromMapCharts", { ...data, activeTab: this.currentType });
      },

      // id: 高亮id, type: line 路线的高亮重置, area 区域的高亮重置
      resetActive(type, id) {
        this.$emit("resetActiveFromMapCharts", { type, id });
      },

      // 切换点位类型,外面的也要切换
      emitChangePointType(data) {
        this.$emit("changePointType", data);
      },

      // 返回上一级
      async back() {
        this.searchVal = "";
        this.showMap = false;
        this.currentLineItem = "";
        this.currentHighlightedMarker = "";
        this.currentType = "";
        this.areaId = "";
        this.searchMarkerList = [];
        await this.$nextTick();
        this.initCharts();
        this.sendDataToCollectionData({
          type: 1,
          areaId: "",
        });
        // 重置数据
        this.$emit("changeMap", "");
        this.resetActive("line");
        this.resetActive("area", this.areaId);
        this.$emit("changeChannel", true);
      },

      // 根据区域id获取区域点位列表
      async getPointListByDistrictIdData(id) {
        try {
          this.loading = true; // 是否加载中
          // 取消请求，防止切换过快点位还没获取回来导致渲染错误
          store.dispatch("clearCancel");
          const res = await getPointListByDistrictId(id);
          if (res.status == 200) {
            const list = res.data || [];

            let listFilter = list.filter((item) => {
              return item.longitude && item.latitude && item.longitude != 0 && item.latitude != 0;
            });

            listFilter = listFilter.map((item) => {
              return {
                ...item,
                position: wgs84togcj02(item.longitude, item.latitude),
              };
            });
            this.drawMarker(listFilter);
            this.drawing = false;
            this.loading = false; // 是否加载中
          }
        } catch (e) {
          this.loading = false; // 是否加载中
        }
      },

      // 根据点位id获取点位收运情况
      async getWaybillDetailByPointIdData(id) {
        try {
          const res = await getWaybillDetailByPointId(id);
          if (res.status == 200) {
            res.data.defaultDriverDossierPhone = res.data.defaultDriverDossierPhone
              ? this.$sm2Decrypt(res.data.defaultDriverDossierPhone)
              : "";
            res.data.supercargoDossierOnePhone = res.data.supercargoDossierOnePhone
              ? this.$sm2Decrypt(res.data.supercargoDossierOnePhone)
              : "";
            res.data.supercargoDossierTwoPhone = res.data.supercargoDossierTwoPhone
              ? this.$sm2Decrypt(res.data.supercargoDossierTwoPhone)
              : "";
            return res.data;
          }
        } catch (e) {
          return "";
        }
      },

      // 获取点位列表
      async pickupPointList(name) {
        try {
          // 每次开始新搜索之前，取消之前的请求（如果存在）
          if (this.controller) {
            this.controller.abort();
          }
          // 创建一个新的 AbortController 实例
          this.controller = new AbortController();
          const signal = this.controller.signal;
          this.loading = true;
          const res = await pickupPointListApi(
            {
              name: this.searchVal || name,
              channelId: this.channelId,
            },
            { signal },
          );
          if (res.status == 200) {
            const list = res.data || [];
            this.pointList = list.filter((item) => {
              return item.longitude && item.latitude && item.longitude != 0 && item.latitude != 0;
            });
            this.loading = false;
            return this.pointList;
          }
        } catch (e) {
          this.loading = false;
          return [];
        }
      },

      // 绘制点位
      async drawMarker(dataList) {
        // 置空
        markerList = [];
        lineMarkerList = [];
        this.searchMarkerList = [];

        // 添加点位标记
        dataList.forEach((data, index) => {
          const startIcon = new window.AMap.Icon({
            // 图标尺寸
            size: new window.AMap.Size(20, 20),
            // 图标的取图地址
            image: data.type == null ? this.imgMap["default"] : this.imgMap["a" + data.type + data.hasWaybill].url,
            // 图标所用图片大小
            imageSize: new window.AMap.Size(20, 20),
            // 图标取图偏移量
            imageOffset: new window.AMap.Pixel(-3, -3),
          });

          let marker = new window.AMap.Marker({
            // let marker = new window.AMap.LabelMarker({
            icon: startIcon,
            map: _map,
            position: data.position,
          });

          marker.id = data.id;
          marker.extData = data;
          marker.on("click", this.pointClick);
          markerList.push(marker);
        });
        // 第一次一样
        this.searchMarkerList = Object.freeze(markerList);
        // 有点位高亮点位，否则试图自适应
        if (this.pickupPointId) {
          await this.$nextTick();
          const marker = this.searchMarkerList.find((item) => item.extData.id === this.pickupPointId);
          if (!marker) {
            this.pickupPointId = "";
            return this.$message.warning("未找到匹配点位，请检查输入是否正确");
          }
          // 手动触发点击
          this.pointClick({
            target: marker,
            isSearch: true,
          });
          this.pickupPointId = "";
        } else {
          _map.setFitView();
        }
      },

      // 点位点击事件
      async pointClick(e) {
        let item = e.target.extData;
        this.isCanSend = true;
        // 不是搜索的置空输入值
        if (!e.isSearch) {
          this.searchVal = "";
        }
        const res = await this.getWaybillDetailByPointIdData(item.id);
        this.sendDataToCollectionData({
          ...item,
          ...res,
          type: 3,
          areaId: this.areaId,
          pickupPointId: item.id,
          pickupPathId: this.currentLineItem ? item.pickupPathId : "",
        });
        let info = [];
        info.push(`<div style="font-size:14px;font-weight:bold;margin-bottom:5px;margin-right:5px">${item.name}</div>`);
        info.push(
          `<div style="font-size:13px;font-weight:bold;color:#056ef7;cursor:pointer;" id="mapClickDialogId">发起收运任务</div>`,
        );
        // 添加关闭按钮到 InfoWindow 内容
        info.push(`<span class="amap-info-close1" id="mapCloseId" >×</span>`);
        this.infoWindow = new window.AMap.InfoWindow({
          offset: new window.AMap.Pixel(4, -4),
          isCustom: false,
          autoMove: true,
        });
        this.infoWindow.setContent(info.join(""));
        this.infoWindow.open(_map, e.target.getPosition());

        // 手动绑定点击时间,不用高德的监听关闭,会出现多次触发情况
        await this.$nextTick();
        document.getElementById("mapCloseId").addEventListener("click", this.closeInfoWindow);
        // 点位弹窗点击点位收运
        document.getElementById("mapClickDialogId").addEventListener("click", () => {
          this.$emit("showPointTaskDialog", { ...item, ...res });
        });

        // 高亮
        this.highlightMarker(e.target, e.isSearch);
      },

      // 手动关闭窗体
      closeInfoWindow() {
        if (this.timeId) {
          clearTimeout(this.timeId);
          this.timeId = null;
        }

        if (!this.infoWindow) {
          return;
        }

        this.searchVal = "";

        if (this.currentHighlightedMarker) {
          this.clearHighlight();
        }

        this.timeId = setTimeout(() => {
          // 窗体关闭--》发送通知
          if (this.isCanSend) {
            const sendData = this.currentLineItem || {};
            this.sendDataToCollectionData({
              type: this.currentLineItem ? 2 : 1,
              pickupPathId: this.currentLineItem ? this.currentLineItem.pickupPathId : "",
              areaId: this.areaId,
              ...sendData,
            });
          }

          if (this.infoWindow) {
            this.infoWindow.close();
            this.infoWindow = null; // 清除对旧 InfoWindow 的引用
          }
        }, 100);
      },

      // 搜索点位
      async handleSearch(item) {
        // 清空时候清理高亮
        if (!this.searchVal) {
          if (this.currentHighlightedMarker) {
            this.clearHighlight();
            this.closeInfoWindow();
          }
          return;
        }

        // 地图外的搜索，自动进入地图并定位点位
        if (!this.showMap) {
          const marker = this.searchMarkerList.find((temp) => temp.id === this.searchVal);
          this.pickupPointId = this.searchVal;
          this.searchMarkerList = [];
          this.jumpChild({ name: marker.districtName });
          await this.$nextTick();
          this.searchVal = "";
          this.$refs["selectMarkerRef"].blur();
          return;
        }

        const markerItem = this.searchMarkerList.find((temp) => temp.extData.id === this.searchVal);
        if (!markerItem) {
          return this.$message.warning("未找到匹配点位，请检查输入是否正确");
        }

        this.$nextTick(() => {
          this.$refs["selectMarkerRef"].blur();
        });

        // 手动触发点击
        this.pointClick({
          target: markerItem,
          isSearch: true,
        });
      },

      // 搜索建议
      async querySearch(queryString, cb) {
        if (!this.showMap) {
          this.searchMarkerList = Object.freeze(await this.pickupPointList(queryString));
        }
      },

      //  高亮点位,isCenter 是否需要自动居中,搜索的需要,手动点击点位的不需要
      highlightMarker(marker, isCenter) {
        this.clearHighlight();
        this.currentHighlightedMarker = marker;
        const activeIcon = new window.AMap.Icon({
          size: new window.AMap.Size(20, 20),

          image:
            this.imgHgMap[
              "a" + this.currentHighlightedMarker.extData.type + this.currentHighlightedMarker.extData.hasWaybill + "1"
            ],

          imageSize: new window.AMap.Size(20, 20),
          imageOffset: new window.AMap.Pixel(-3, -3),
        });

        // 设置地图中心
        //_map.setFitView([marker]);
        //_map.setZoom(13);
        //_map.setCenter(marker.extData.position);
        if (!isCenter) {
          marker.setIcon(activeIcon);
          return;
        }
        _map.setZoomAndCenter(18, marker.getPosition());
        setTimeout(() => {
          marker.setIcon(activeIcon);
          _map.setZoomAndCenter(18, marker.getPosition());
        }, 300);
      },

      // 清理点位高亮
      clearHighlight() {
        if (this.currentHighlightedMarker) {
          const normalIcon = new window.AMap.Icon({
            size: new window.AMap.Size(20, 20),
            image:
              this.imgMap[
                "a" + this.currentHighlightedMarker.extData.type + this.currentHighlightedMarker.extData.hasWaybill
              ].url,
            imageSize: new window.AMap.Size(20, 20),
            imageOffset: new window.AMap.Pixel(-3, -3),
          });

          this.currentHighlightedMarker.setIcon(normalIcon);
          this.currentHighlightedMarker = null;
        }
      },

      // 设置显示或者隐藏点位,isShow:true展示，false隐藏
      showOrHiddenMarker(list, isShow) {
        list.forEach((marker) => {
          marker.setMap(isShow ? _map : null); // 设置map为null，隐藏点位,设置map为当前地图实例，显示点位
        });
      },

      // 点击点位类型, 只展示对应点位,isOut是否外部调用的
      handleMarkerType(data, isOut) {
        if (!this.showMap) return;
        // 判断是不是有线路,有线路只处理有路线的点位
        const currentMarkerList = this.currentLineItem ? lineMarkerList : markerList;
        // 关闭infoWindow
        this.closeInfoWindow();
        // 再次点击同个类型就取消,或者外部的类型为空时候
        if (this.currentType === data.type || data.type === "") {
          this.showOrHiddenMarker(currentMarkerList, true);
          // 取消时候 还原可搜索点位,如果有路线那么就是路线,否则是全部
          this.searchMarkerList = Object.freeze(this.currentLineItem ? lineMarkerList : markerList);
          this.currentType = "";
          if (!isOut) {
            this.emitChangePointType({ type: "", name: "" });
          }
          return;
        }
        // 通知右侧联动
        if (!isOut) {
          this.emitChangePointType({ type: data.type, name: data.text });
        }

        this.currentType = data.type;
        // 拿到需要隐藏的点位
        const hiddenList = currentMarkerList.filter((item) => item.extData.type != data.type);
        // 搜索点位要实时变成当前页面展示的点位
        this.searchMarkerList = Object.freeze(currentMarkerList.filter((item) => item.extData.type == data.type));
        this.showOrHiddenMarker(hiddenList, false);
        this.showOrHiddenMarker(this.searchMarkerList, true);
      },

      // 绘制路线
      async drawLine(data) {
        // 防止外面路线点击太快,但是地图还没渲染好
        if (this.drawing) {
          this.resetActive("line", this.currentLineItem?.pickupPathId);
          return this.$message.warning("正在绘制中，请稍后点击");
        }

        this.removeLine();
        // 关闭infoWindow,这里关闭不需要发送更新通知，因为绘制路线的时候会重新发送
        this.isCanSend = false;
        this.closeInfoWindow();
        // 绘制路线，取消点位类型的筛选。优先路线
        this.currentType = "";

        // 点击同个就取消并显示所有点位,类型也不需要保留，都重置
        if (this.currentLineItem && this.currentLineItem.pickupPathId == data.pickupPathId) {
          this.currentLineItem = null;
          this.showOrHiddenMarker(markerList, true);
          this.searchMarkerList = Object.freeze(markerList);
          lineMarkerList = [];
          // 取消连线也要通知
          const extraData = this.currentHighlightedMarker ? this.currentHighlightedMarker.extData : {};
          this.sendDataToCollectionData({
            type: this.currentHighlightedMarker ? 3 : 1,
            areaId: this.areaId,
            pickupPathId: "",
            ...extraData,
          });
          _map.setFitView();
          return;
        }

        this.drawing = true;
        this.currentLineItem = data;
        this.sendDataToCollectionData({
          type: 2,
          areaId: this.areaId,
          pickupPathId: data.pickupPathId,
          ...data,
        });

        // 展示的点位
        this.searchMarkerList = Object.freeze(
          markerList.filter((item) => item.extData.pickupPathId == data.pickupPathId),
        );
        // 存路线点位
        lineMarkerList = this.searchMarkerList;

        this.showOrHiddenMarker(this.searchMarkerList, true);
        // 拿到需要隐藏的点位，就是非路线的
        const hiddenList = markerList.filter((item) => item.extData.pickupPathId != data.pickupPathId);
        this.showOrHiddenMarker(hiddenList, false);

        // 点位绘制路线
        const dataList = this.searchMarkerList.map((item) => item.extData);
        dataList.reverse();
        const lngLatList = dataList.map((item) => item.position);
        if (!lngLatList.length) {
          return;
        }
        let longLatList = this.cutArray(lngLatList);
        this.longLatListLen = longLatList.length || 0;
        this.drawSuccessLen = 0;
        // longLatList.forEach((list) => {
        //   this.handleDrive(list[0], list[list.length - 1], list.slice(1, list.length - 1), 0, "routePath", "map");
        // });
        this.processArrayWithDelay(longLatList);
      },

      async processArrayWithDelay(array, delayMs = 300) {
        for (let i = 0; i < array.length; i++) {
          // 如果是第一次之后的元素，则等待1秒
          if (i > 0) {
            await this.delay(delayMs);
          }
          this.handleDrive(
            array[i][0],
            array[i][array[i].length - 1],
            array[i].slice(1, array[i].length - 1),
            0,
            "routePath",
          );
        }
      },

      // 延时函数
      delay(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
      },

      // 移除绘制的路线
      removeLine() {
        if (this.lineRouteList.length) {
          this.lineRouteList.forEach((item) => {
            _map.remove(item);
          });
          this.lineRouteList = [];
        }
      },

      // 调用高德地图jsApi获取驾驶路线
      handleDrive(start, end, points, strategy, routePathField, mapRef) {
        window.AMap.plugin("AMap.Driving", () => {
          let driving = new window.AMap.Driving({
            policy: strategy, //驾车路线规划策略，0是速度优先的策略
          });
          driving.search(start, end, { waypoints: points }, (status, result) => {
            //status：complete 表示查询成功，no_data 为查询无结果，error 代表查询错误
            //查询成功时，result 即为对应的驾车导航信息
            if (status === "complete") {
              this.drawRoute(result.routes[0], routePathField, mapRef);
            } else {
              // this.drawing = false;
              this.drawSuccessLen += 1;
            }
          });
        });
      },

      drawRoute(route, routePathField, mapRef) {
        let path = this.parseRouteToPath(route);
        this[routePathField] = this[routePathField].concat(path);
        // 绘制轨迹
        const lineRoute = new window.AMap.Polyline({
          map: _map,
          path,
          showDir: true,
          strokeColor: "#28F", //线颜色
          strokeWeight: 6, //线宽
          strokeOpacity: 1,
        });

        _map.setFitView(lineMarkerList);
        this.lineRouteList.push(lineRoute);
        this.drawSuccessLen += 1;
        // 等待绘制完成
        if (this.drawSuccessLen === this.longLatListLen) {
          setTimeout(() => {
            this.drawing = false;
          }, 100);
        }
      },

      // 解析DrivingRoute对象，构造成AMap.Polyline的path参数需要的格式
      parseRouteToPath(route) {
        let path = [];
        for (let i = 0, l = route.steps.length; i < l; i++) {
          let step = route.steps[i];
          for (let j = 0, n = step.path.length; j < n; j++) {
            path.push(step.path[j]);
          }
        }
        return path;
      },

      // 按照数组长度为16切割，多余的数据存进最后一个数组
      cutArray(array) {
        let finalArray = [];
        // 商
        let quotient = Math.floor(array.length / 16);
        // 余数
        let remainder = array.length % 16;
        // 是否为16的倍数
        if (quotient > 0) {
          for (let i = 0; i < quotient; i++) {
            finalArray.push(array.slice(i * 16, (i + 1) * 16));
          }
          if (remainder > 0) {
            finalArray.push(array.slice(16 * quotient, array.length));
          }
          // 需要插入数组的索引数组
          let indexArr = [];
          // 需要插入数组的数据数组
          let valueArr = [];
          finalArray.forEach((arr, index) => {
            if (index >= 0 && index < finalArray.length - 1) {
              indexArr.push(index);
              valueArr.push([arr[15], finalArray[index + 1][0]]);
            }
          });
          // 将每个数组的终点坐标和起点坐标连接成数组并插入对应的位置
          indexArr.forEach((item, number) => {
            if (number >= 0 && number < indexArr.length) {
              finalArray.splice(item + number + 1, 0, valueArr[number]);
            }
          });
          finalArray = finalArray.filter((list) => list.length > 1);
        } else {
          finalArray = [array];
        }
        return finalArray;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .charts-style {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .charts-btn {
    position: absolute;
    top: 150px;
    left: 545px;
    z-index: 10;
  }

  .gd-map {
    width: 45%;
    height: calc(100% - 142px);
    margin-top: 115px;
  }

  .search-container {
    position: absolute;
    top: 150px;
    left: 668px;
    z-index: 10;
    .el-select {
      width: 400px;
    }
    ::v-deep .el-select-dropdown {
      width: 390px !important;
      min-width: 300px !important;
      top: 31px !important;
      .el-select-dropdown__item {
        height: 50px;
        line-height: 16px;
        .name {
          text-overflow: ellipsis;
          overflow: hidden;
          line-height: 28px;
          color: #2c2c2c;
          font-weight: 600;
          font-size: 16px;
        }
        .addr {
          font-size: 14px;
          color: #5e5d5d;
          line-height: 20px;
        }
        &.selected {
          .name {
            color: #4ca786;
          }
          .addr {
            color: #4ca786;
          }
        }
      }
    }
  }

  .search-map {
    top: 130px;
    left: 597px;
    z-index: 10;
    .el-select {
      width: 750px;
    }

    ::v-deep .vue-recycle-scroller {
      background-color: #001f17;
    }

    ::v-deep .el-select-dropdown {
      width: 740px !important;
      min-width: 500px !important;
      top: 31px !important;
      .el-select-dropdown__item {
        background-color: #001f17;
        .name {
          color: #fff;
        }
        .addr {
          color: #b4b4b4;
        }

        &:hover {
          background-color: #153730d1;
        }
      }
      .el-select-dropdown__empty {
        background-color: #001f17;
        color: #fff;
      }
    }

    ::v-deep .el-input__inner {
      background-color: #001f17;
      color: #fff;
    }
  }

  .marker-type {
    position: absolute;
    bottom: 34px;
    background: #6a686880;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    color: #fff;
    font-size: 12px;
    display: flex;
    align-items: center;

    &-item {
      cursor: pointer;
      display: flex;
      align-items: center;
      padding: 3px 8px;
      img {
        width: 18px;
        height: 18px;
        margin-right: 10px;
      }
    }

    &-active {
      color: #00fffc;
      font-weight: 600;
    }
  }

  .map-charts {
    ::v-deep .amap-info-content {
      .amap-info-close {
        display: none !important;
      }
      .amap-info-close1 {
        position: absolute;
        right: 5px;
        top: 5px;
        color: #c3c3c3;
        text-decoration: none;
        font: 700 16px/14px Tahoma, Verdana, sans-serif;
        width: 14px;
        height: 14px;
        cursor: pointer;
      }
    }
  }

  .map-info {
    position: fixed;
    top: 126px;
    left: 1643px;
    background: -webkit-linear-gradient(#ffffff, #9dfad8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 18px;
    padding-top: 10px;
    font-weight: bold;
    max-width: 249px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #00fffc;
    z-index: 300;
  }
</style>
