<template>
  <div>
    <Title title="车辆状态信息"></Title>
    <div class="top">
      <div class="status-box">
        <div class="status-left">
          <span class="el-icon-warning-outline"></span>
        </div>
        <div class="status-right">
          <div class="right-title">今日异常上报数量</div>
          <div class="right-value">{{ vehicleDayAbnormalInfo?.abnormalReportNum }}</div>
        </div>
      </div>
      <div class="status-box">
        <div class="status-left">
          <span class="el-icon-warning-outline"></span>
        </div>
        <div class="status-right">
          <div class="right-title">驾驶状态提醒</div>
          <!-- {{ vehicleDayAbnormalInfo?.driveAbnormalNum }} -->
          <div class="right-value">0</div>
        </div>
      </div>
    </div>
    <div class="status-box">
      <div class="status-left">
        <span class="el-icon-odometer"></span>
      </div>
      <div class="status-right">
        <div class="right-title">车辆百公里油耗</div>
        <div class="oil-box">
          <div class="oil-value">{{ vehicleDayAbnormalInfo?.fuelCost }}</div>
          <div class="oil-unit">（L/100km）</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import Title from "@/views/largeDataScreen/realTimeMonitor/components/Title";
  export default {
    components: {
      Title,
    },
    props: ["vehicleDayAbnormalInfo"],
  };
</script>

<style lang="scss" scoped>
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 30px 0;
  }
  .status-box {
    display: flex;
    align-items: center;
    color: #fff;
    .status-left {
      span {
        font-size: 64px;
      }
    }
    .status-right {
      margin-left: 10px;
      .right-title {
        font-size: 14px;
      }
      .right-value {
        font-size: 40px;
        font-weight: bold;
        line-height: 46px;
      }
    }
  }
  .oil-box {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    margin-top: 12px;
    .oil-value {
      font-size: 40px;
      font-weight: bold;
      line-height: 30px;
    }
    .oil-unit {
      font-size: 12px;
    }
  }
</style>
