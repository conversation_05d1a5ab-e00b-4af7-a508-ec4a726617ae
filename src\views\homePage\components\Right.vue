<template>
  <div class="Right-wrapper" style="">
    <Title name="运营管理" :url="url">运营管理</Title>
    <div style="width: 100%; display: flex; flex-direction: column; align-items: center">
      <div style="display: flex">
        <div class="card" style="margin-right: 5px" @click="$router.push('/sctmp_base/merchant')">
          <div class="text">客商总数</div>
          <div class="value">{{ summaryData?.merchantNum }}</div>
        </div>
        <div class="card" style="margin-left: 5px" @click="$router.push('/sctmp_base/receiveRoute')">
          <div class="text">收运路线总数</div>
          <div class="value">{{ summaryData?.pathNum }}</div>
        </div>
      </div>
    </div>
    <div style="flex: 1" id="run"></div>
  </div>
</template>
<script>
  import Title from "./ModuleTitle.vue";
  import * as echarts from "echarts";

  export default {
    components: { Title },
    props: ["summaryData"],
    data() {
      return {
        url: require("../images/ic_run.svg"),
        // seriesData: [10, 20, 30, 10, 20],
        seriesData: [0, 0, 0, 0, 0, 0],
        seriesPrecentage: ["", "", "", "", "", ""],
        total: 0,
      };
    },
    computed: {},
    created() {},
    watch: {
      summaryData: {
        handler(newV) {
          if (newV?.rublishSituation) {
            this.dataCb();
          }
        },
        deep: true,
      },
    },
    methods: {
      dataCb() {
        this.getSeriesData();
        this.init();
      },
      getSeriesData() {
        this.seriesData = [
          this.summaryData.rublishSituation.infectiousWaste,
          this.summaryData.rublishSituation.damaginWaste,
          this.summaryData.rublishSituation.chemicalWaste,
          this.summaryData.rublishSituation.pharmaceuticalWaste,
          this.summaryData.rublishSituation.pathologicalWaste,
          this.summaryData.rublishSituation.sludge,
        ];
        this.seriesPrecentage = [
          this.summaryData.rublishSituation.infectiousWastePrecentage,
          this.summaryData.rublishSituation.damaginWastePrecentage,
          this.summaryData.rublishSituation.chemicalWastePrecentage,
          this.summaryData.rublishSituation.pharmaceuticalWastePrecentage,
          this.summaryData.rublishSituation.pathologicalWastePrecentage,
          this.summaryData.rublishSituation.sludgePrecentage,
        ];
        // this.seriesData.forEach((item) => {
        //   this.total += item;
        // });
        this.total = this.summaryData.rublishSituation.total;
      },
      init() {
        const chartData = [
          {
            name: "感染性废物         ",
            value: this.seriesData[0],
            precentage: this.seriesPrecentage[0],
            color: "#1A9CFF",
          },
          {
            name: "感染性废物(污泥)",
            value: this.seriesData[5],
            precentage: this.seriesPrecentage[5],
            color: "#9FD4FD",
          },
          {
            name: "损伤性废物          ",
            value: this.seriesData[1],
            precentage: this.seriesPrecentage[1],
            color: "#FABB28",
          },
          {
            name: "化学性废物          ",
            value: this.seriesData[2],
            precentage: this.seriesPrecentage[2],
            color: "#33D1C9",
          },
          {
            name: "药物性废物          ",
            value: this.seriesData[3],
            precentage: this.seriesPrecentage[3],
            color: "#5CC78A",
          },
          {
            name: "病理性废物          ",
            value: this.seriesData[4],
            precentage: this.seriesPrecentage[4],
            color: "#7372E9",
          },
        ];
        const sum = chartData.reduce((a, b) => a + b.value, 0);
        // 自定义lengend的图标
        const legendData = [
          {
            name: "感染性废物         ",
            icon: "rect",
          },
          {
            name: "感染性废物(污泥)",
            icon: "rect",
          },
          {
            name: "损伤性废物          ",
            icon: "rect",
          },
          {
            name: "化学性废物          ",
            icon: "rect",
          },
          {
            name: "药物性废物          ",
            icon: "rect",
          },
          {
            name: "病理性废物          ",
            icon: "rect",
          },
        ];
        const pieSeries = [];
        chartData.forEach((v, i) => {
          pieSeries.push({
            name: "",
            type: "pie",
            emphasis: {
              // 取消放大效果
              scale: 0,
              // 可以设置其他悬停时的样式，比如标签显示
              label: {
                show: false,
                fontSize: "14",
                fontWeight: "bold",
              },
            },
            clockwise: false,
            radius: [75 - i * 10 + "%", 80 - i * 10 + "%"],
            center: ["30%", "50%"],
            startAngle: 90,
            label: {
              show: false,
            },

            data: [
              {
                value: v.value,
                name: v.name,
                itemStyle: {
                  color: v.color,
                },
              },
              {
                value: sum === 0 ? 0.001 : sum - v.value,
                name: "",
                itemStyle: {
                  color: "rgba(0,0,0,0)",
                },
              },
            ],
          });
          pieSeries.push({
            name: "",
            type: "pie",
            silent: false,
            emphasis: {
              // 取消放大效果
              scale: 0,
              // 可以设置其他悬停时的样式，比如标签显示
              label: {
                show: false,
                fontSize: "14",
                fontWeight: "bold",
              },
            },
            z: 1,
            clockwise: false, // 顺时加载
            radius: [75 - i * 10 + "%", 80 - i * 10 + "%"],
            center: ["30%", "50%"],
            label: {
              show: false,
            },
            itemStyle: {
              borderCap: "round",
              borderJoin: "round",
            },
            data: [
              {
                value: sum,
                itemStyle: {
                  color: "#F7F8FA",
                },
              },
            ],
          });
        });
        var myChart = echarts.init(document.getElementById("run"));
        myChart.setOption({
          title: {
            text: "今日收运量统计",
            textStyle: {
              color: "#414D5A",
              fontStyle: "normal",
              fontWeight: "bold",
              fontFamily: "Microsoft YaHei",
              fontSize: "14",
              lineHeight: "20",
            },
            left: "center",
          },
          legend: {
            orient: "vertical",
            right: 12,
            top: "center",
            itemWidth: 8,
            itemHeight: 8,
            data: legendData,
            formatter: function (val) {
              const item = chartData.find((chartDataItem) => chartDataItem.name === val);
              const valStr = val.toString();
              const valueStr = item.value.toString();
              const percentageStr = item.precentage.toString();
              return "{title|" + valStr + "}" + "{value|" + valueStr + "}" + "（kg）" + "{value|" + percentageStr + "}";
            },
            textStyle: {
              rich: {
                title: {
                  fontSize: 12,
                  color: "#414D5A",
                },
                value: {
                  fontSize: 12,
                  color: "#414D5A",
                  padding: [0, 0, 0, 10],
                },
              },
            },
          },
          series: [
            ...pieSeries,
            {
              center: ["30%", "50%"],
              type: "pie",
              selectedMode: "single",
              radius: ["0%", "20%"],
              silent: true,
              clockwise: false,
              label: {
                position: "center",
                formatter: `{value|${this.total}}{beside|（kg）}\n {name|总重量} `,
                rich: {
                  name: {
                    fontSize: 10,
                  },
                  value: {
                    fontSize: 12,
                    fontWeight: "bold",
                    padding: [10, 0, 0, 0],
                    fontFamily: "PangMenZhengDao",
                  },
                  beside: {
                    fontSize: 8,
                    lineHeight: 8,
                    padding: [10, 0, 0, 0],
                  },
                },
              },
              itemStyle: {
                normal: {
                  color: "#fff",
                },
              },
              labelLine: {
                show: false,
              },
              data: [{ value: 0 }],
              right: "0%",
            },
          ],
        });
      },
    },
  };
</script>
<style scoped lang="scss">
  .Right-wrapper {
    // 左右设计是1：1，按需求调整大致改为200：250
    flex: 250;
    background-color: #fff;
    margin-left: 8px;
    display: flex;
    flex-direction: column;
  }
  .card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 140px;
    width: 30%;
    height: 90px;
    // background: #f6fffc;
    background: linear-gradient(180deg, #ffffff 0%, #f7faf9 100%);
    border-radius: 8px;
  }
  .text {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 14px;
    color: #5c6663;
    line-height: 20px;
    margin-bottom: 4px;
  }
  .value {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 24px;
    color: #2e3432;
    line-height: 33px;
    text-align: right;
    font-style: normal;
  }
</style>
