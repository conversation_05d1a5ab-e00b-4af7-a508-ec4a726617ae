<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <backButton @closeRecord="closeRecord" v-if="showBack"></backButton>
    <div class="detail-box">
      <header class="header">
        <div class="header-left">
          <el-input class="w250" v-model="productionUnit" placeholder="请输入产废单位名称" clearable></el-input>
          <el-select v-model="filterForm.detailType" placeholder="请选择任务类型" clearable filterable class="ml-20">
            <el-option v-for="(item, index) in POINT_TASK_TYPE" :key="index" :label="item" :value="index"></el-option>
          </el-select>
        </div>
        <!-- <div class="filter-box">
          <el-badge :value="filterNumber" class="filter-badge">
            <el-button type="primary" @click="showFilter = !showFilter">
              <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
              筛选
              <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
            </el-button>
          </el-badge>
        </div> -->
        <el-button @click="resetFilter" class="ml-20">重置</el-button>
        <el-button type="primary" @click="searchFilter">查询</el-button>
        <div class="header-right">
          <span class="el-icon-info info-icon" v-if="recordForm?.memo" @click="dialogVisible = true"></span>
          <el-popover class="ml-10" placement="bottom" width="240" trigger="click">
            <div class="reveal-box">
              <div class="reveal-header">
                <div>展示全部</div>
                <el-switch
                  :value="allItemChecked"
                  active-text="是"
                  inactive-text="否"
                  @change="changeAllReveal"
                ></el-switch>
              </div>
              <ul class="reveal-list">
                <li class="reveal-item" v-for="(item, index) in itemList" :key="index">
                  <el-checkbox :value="item.value" :disabled="index === 0" @change="changeReveal($event, item)">{{
                    item.label
                  }}</el-checkbox>
                </li>
              </ul>
            </div>
            <el-button slot="reference">
              <div class="btn-inner">
                <span class="el-icon-s-grid"></span>
                <span>显示列</span>
                <span class="el-icon-caret-bottom"></span>
              </div>
            </el-button>
          </el-popover>
        </div>
      </header>
      <FilterContent label-width="160px" v-show="showFilter">
        <el-col :span="12">
          <el-form-item label="收运状态">
            <el-select v-model="filterForm.waybillStatus" placeholder="请选择收运状态" clearable filterable>
              <el-option
                v-for="(item, index) in RECEIVING_CONDITION"
                :key="index"
                :label="item"
                :value="index"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收运日期范围">
            <el-date-picker
              v-model="filterForm.waybillTime"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收运截止日期范围">
            <el-date-picker
              v-model="filterForm.endDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="流程状态">
            <el-select v-model="filterForm.verifyStatus" placeholder="请选择流程状态" clearable filterable>
              <el-option v-for="(item, index) in VERIFY_STATUS" :key="index" :label="item" :value="index"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务类型">
            <el-select v-model="filterForm.detailType" placeholder="请选择任务类型" clearable filterable>
              <el-option v-for="(item, index) in POINT_TASK_TYPE" :key="index" :label="item" :value="index"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </FilterContent>
      <div class="record-content">
        <el-table
          :data="tableData"
          :header-cell-style="{ background: '#F5F7F9' }"
          border
          ref="tableRef"
          :height="height"
          v-loading="loading"
        >
          <el-table-column type="index" align="center" label="顺序" width="60"></el-table-column>
          <el-table-column prop="code" label="点位编号" align="center" v-if="itemList[0].value"></el-table-column>
          <el-table-column
            prop="productionUnit"
            label="产废单位名称"
            align="center"
            width="120"
            v-if="itemList[1].value"
          ></el-table-column>
          <el-table-column
            prop="pickupPathName"
            label="所属路线"
            align="center"
            width="120"
            v-if="itemList[20].value"
          ></el-table-column>
          <el-table-column
            prop="productionUnitOperator"
            label="产废单位经办人"
            align="center"
            width="120"
            v-if="itemList[2].value"
          ></el-table-column>
          <el-table-column prop="operation" label="正常经营" align="center" v-if="itemList[19].value">
            <template #default="{ row }">{{ IS_NORMAL_OPERATION[row.operation] }}</template>
          </el-table-column>
          <el-table-column
            prop="firstCarrier"
            label="第一承运人"
            align="center"
            v-if="itemList[17].value"
          ></el-table-column>
          <el-table-column prop="detailType" label="任务类型" align="center" v-if="itemList[3].value">
            <template #default="{ row }">{{ POINT_TASK_TYPE[row.detailType] }}</template>
          </el-table-column>
          <el-table-column
            prop="rubbishTotal"
            label="废物总量（kg）"
            align="center"
            width="140"
            v-if="itemList[4].value"
          ></el-table-column>
          <el-table-column prop="waybillStatus" label="收运状态" align="center" v-if="itemList[5].value">
            <template #default="{ row }">{{ RECEIVING_CONDITION[row.waybillStatus] }}</template>
          </el-table-column>
          <el-table-column
            prop="waybillTime"
            label="收运时间"
            align="center"
            v-if="itemList[6].value"
          ></el-table-column>
          <el-table-column
            prop="effectiveDate"
            label="收运生效日期"
            align="center"
            v-if="itemList[21].value"
          ></el-table-column>
          <el-table-column
            prop="endDate"
            label="收运截止日期"
            align="center"
            width="120"
            v-if="itemList[7].value"
          ></el-table-column>
          <el-table-column prop="isClear" label="是否清空" align="center" v-if="itemList[8].value">
            <template #default="{ row }">{{ IS_NORMAL_OPERATION[row.isClear] }}</template>
          </el-table-column>
          <el-table-column prop="baggingMethod" label="桶装/袋装" align="center" v-if="itemList[9].value">
            <template #default="{ row }">{{ BARRELS_BAGS[row.baggingMethod] }}</template>
          </el-table-column>
          <el-table-column prop="verifyStatus" label="当前流程" align="center" v-if="itemList[10].value">
            <template #default="{ row }">{{ VERIFY_STATUS[row.verifyStatus] }}</template>
          </el-table-column>
          <el-table-column
            prop="verifyUserName"
            label="确认人"
            align="center"
            v-if="itemList[11].value"
          ></el-table-column>
          <el-table-column
            prop="verifyTime"
            label="确认时间"
            align="center"
            v-if="itemList[12].value"
          ></el-table-column>
          <el-table-column prop="userLongitude" label="经度" align="center" v-if="itemList[13].value"></el-table-column>
          <el-table-column prop="userLatitude" label="纬度" align="center" v-if="itemList[14].value"></el-table-column>
          <el-table-column prop="address" label="地址" align="center" v-if="itemList[15].value"></el-table-column>
          <el-table-column prop="image" label="图片" align="center">
            <template #default="{ row }">
              <el-badge
                v-if="row.picture && row.picture.length > 0"
                :value="row.picture.length > 1 ? row.picture.length : ''"
                class="picture-badge"
                type="success"
              >
                <el-image
                  class="picture-img"
                  fit="cover"
                  :src="row.picture[0].url"
                  :preview-src-list="row.picture.map((i) => i.url)"
                  v-if="row.picture"
                ></el-image>
              </el-badge>
            </template>
          </el-table-column>
          <el-table-column label="废物类型/重量（kg）" align="center" v-if="itemList[16].value">
            <el-table-column prop="infectiousWaste" label="感染性废物" align="center"></el-table-column>
            <el-table-column prop="damagingWaste" label="损伤性废物" align="center"></el-table-column>
            <el-table-column prop="pharmaceuticalWaste" label="药物性废物" align="center"></el-table-column>
            <el-table-column prop="pathologicalWaste" label="病理性废物" align="center"></el-table-column>
            <el-table-column prop="chemicalWaste" label="化学性废物" align="center"></el-table-column>
            <el-table-column prop="sludge" label="感染性废物一污泥" align="center" width="140"></el-table-column>
          </el-table-column>

          <el-table-column
            prop="residueRubbish"
            label="剩余垃圾"
            align="center"
            v-if="itemList[18].value"
          ></el-table-column>
        </el-table>
      </div>
      <Pagination :page="page" @pageChange="pageChange"></Pagination>
    </div>
    <el-dialog title="备注" :visible.sync="dialogVisible" width="30%" destroy-on-close>
      <el-form :model="ruleForm" label-width="0" label-suffix="：">
        <el-form-item label="">
          <el-input
            :value="recordForm.memo"
            placeholder=""
            type="textarea"
            rows="10"
            maxlength="500"
            show-word-limit
            readonly
          ></el-input>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import {
    TRASH_TYPE,
    IS_NORMAL_OPERATION,
    RECEIVING_CONDITION,
    BARRELS_BAGS,
    VERIFY_STATUS,
    POINT_TASK_TYPE,
  } from "@/enums";
  import backButton from "@/components/backButton";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
      recordForm: {
        type: Object,
        default: () => {},
      },
      showBack: {
        type: Boolean,
        default: true,
      },
      query: {
        type: Object,
        default: () => {},
      },

      height: {
        type: String,
        default: "710px",
      },
    },
    components: { backButton },
    data() {
      return {
        filterForm: {
          waybillStatus: undefined,
          startWaybillTime: null,
          endWaybillTime: null,
          startEndDate: null,
          endEndDate: null,
          verifyStatus: null,
          detailType: null,
        },
        TRASH_TYPE,
        IS_NORMAL_OPERATION,
        RECEIVING_CONDITION,
        BARRELS_BAGS,
        VERIFY_STATUS,
        POINT_TASK_TYPE,
        ruleForm: {
          remark: "", //备注
        },
        apis: {
          listPage: "/api/waybill/waybillDetail/listPage",
        },
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        loading: false,
        tableData: [],
        showFilter: false,
        productionUnit: "",
        itemList: [
          { label: "点位编号", value: true },
          { label: "产废单位名称", value: true },
          { label: "产废单位经办人", value: true },
          { label: "任务类型", value: true },
          { label: "废物总量", value: true },
          { label: "收运状态", value: true },
          { label: "收运时间", value: true },
          { label: "收运截止日期", value: true },
          { label: "是否清空", value: true },
          { label: "桶装/袋装", value: true },
          { label: "当前流程", value: true },
          { label: "确认人", value: true },
          { label: "确认时间", value: true },
          { label: "经度", value: false },
          { label: "纬度", value: false },
          { label: "地址", value: true },
          { label: "废物类型/重量", value: false },
          { label: "第一承运人", value: true },
          { label: "剩余垃圾", value: false },
          { label: "正常经营", value: true },
          { label: "所属路线", value: true },
          { label: "收运生效日期", value: true },
        ],
        allItemChecked: false,
        dialogVisible: false,
      };
    },
    mounted() {
      if (this.recordId || !this.showBack) {
        this.filterForm.waybillStatus = this.query.waybillStatus;
        this.initData();
      }
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    methods: {
      async initData() {
        try {
          this.loading = true;
          let params = {
            waybillId: this.recordId,
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            productionUnit: this.productionUnit,
            waybillStatus: this.filterForm.waybillStatus,
            startWaybillTime: this.filterForm.waybillTime ? this.filterForm.waybillTime[0] : "",
            endWaybillTime: this.filterForm.waybillTime ? this.filterForm.waybillTime[1] : "",
            startEndDate: this.filterForm.endDate ? this.filterForm.endDate[0] : "",
            endEndDate: this.filterForm.endDate ? this.filterForm.endDate[1] : "",
            verifyStatus: this.filterForm.verifyStatus,
            detailType: this.filterForm.detailType,
            districtId: this.query.districtId,
            effectiveEndBeginTime: this.query.effectiveEndBeginTime,
            effectiveEndEndTime: this.query.effectiveEndEndTime,
            type: this.query.type,
            pickupPathId: this.query.pickupPathId,
          };
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.tableData.forEach((data) => {
              try {
                data.picture = JSON.parse(data.picture);
              } catch (error) {
                data.picture = "";
              }
            });
            this.page.total = res.data.total;
          }
        } finally {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.productionUnit = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 修改所有显示列
      changeAllReveal(value) {
        this.allItemChecked = value;
        this.itemList.slice(1, this.itemList.length).forEach((item) => (item.value = value));
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 列点击事件
      changeReveal(value, item) {
        item.value = value;
        let uncheckList = this.itemList.slice(1, this.itemList.length).filter((list) => !list.value);
        if (uncheckList.length === 0) {
          this.allItemChecked = true;
        } else {
          this.allItemChecked = false;
        }
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .detail-back {
    margin-bottom: 20px;
  }
  .detail-box {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    padding-top: 20px;
  }
  .header {
    display: flex;
    align-items: center;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
  .info-icon {
    font-size: 30px;
    cursor: pointer;
  }
  .record-content {
    margin-top: 20px;
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .picture-img {
    width: 60px;
    height: 60px;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }

  ::v-deep .el-table {
    min-height: 200px;
  }

  .ml-20 {
    margin-left: 12px;
  }
</style>
