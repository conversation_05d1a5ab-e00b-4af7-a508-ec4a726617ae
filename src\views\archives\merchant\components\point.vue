<template>
  <div class="main-record micro-app-sctmp_base">
    <div class="record-content">
      <div class="card-header">收运点位</div>
      <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" style="width: 100%" border>
        <el-table-column type="index" width="55" align="center"></el-table-column>
        <el-table-column prop="code" label="点位编号" align="center"></el-table-column>
        <el-table-column prop="name" label="点位名称" align="center"></el-table-column>
        <el-table-column prop="address" label="点位地址" align="center"></el-table-column>
        <el-table-column prop="longitude" label="经度" align="center"></el-table-column>
        <el-table-column prop="latitude" label="纬度" align="center"></el-table-column>
        <el-table-column prop="contact" label="点位联系人" align="center"></el-table-column>
        <el-table-column prop="contactPhone" label="联系人联系方式" align="center"></el-table-column>
        <el-table-column prop="createTime" label="点位创建时间" align="center"></el-table-column>
        <el-table-column prop="updateTime" label="点位修改时间" align="center"></el-table-column>
        <el-table-column prop="type" label="点位类型" align="center">
          <template #default="{ row }">{{ POINT_TYPE[row.type] }}</template>
        </el-table-column>
        <el-table-column prop="dailyEmissions" label="日排放量（KG）" align="center"></el-table-column>
        <el-table-column prop="monthEmissions" label="月排放量（KG）" align="center"></el-table-column>
        <el-table-column prop="contractStatusName" label="合同状态" align="center"></el-table-column>
        <el-table-column prop="deviceStatusName" label="设备状态" align="center"></el-table-column>
        <el-table-column prop="isUndock" label="移除状态" align="center">
          <template #default="{ row }">{{ REMOVE_STATUS[row.isUndock] }}</template>
        </el-table-column>
      </el-table>
    </div>
    <div class="record-footer">
      <el-button @click="$emit('closeRecord')">返回</el-button>
    </div>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  import { POINT_TYPE, REMOVE_STATUS } from "@/enums";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        apis: {
          pointList: "/api/pickup/pickupPoint/list",
        },
        tableData: [],
        POINT_TYPE,
        REMOVE_STATUS,
      };
    },
    mounted() {},
    methods: {
      // 获取用户详情
      async getPointList(id) {
        let res = await createApiFun({ merchantFileId: id }, this.apis.pointList);
        if (res.success) {
          this.tableData = res.data.map((item) => {
            return {
              ...item,
              contactPhone: item.contactPhone ? this.$sm2Decrypt(item.contactPhone) : "",
            };
          });
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
</style>
