<template>
  <div class="micro-app-sctmp_base full-height">
    <defaultPage>
      <record v-if="showRecord" :recordId="recordId" @closeRecord="closeRecord" @refreshList="initData"></record>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-input">
            <el-input
              class="w250"
              v-model="filterForm.keyword"
              placeholder="请输入设备名称/编号/地址"
              clearable
            ></el-input>
          </div>
          <InterviewChannel :value.sync="filterForm.channelId" :record.sync="channelRecord"></InterviewChannel>
          <el-form ref="filterForm" :model="filterForm" label-suffix=":" label-width="80px" class="mr-10">
            <el-form-item label="设备状态">
              <el-select v-model="filterForm.deviceStatus" placeholder="请选择设备状态" clearable filterable>
                <el-option v-for="(item, index) in DEVICE_STATUS" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button type="primary" size="small" icon="el-icon-folder-opened">同步</el-button>
            <el-dropdown
              size="small"
              trigger="click"
              @command="handleDataImport"
              class="dropdown-button"
              placement="bottom-start"
            >
              <el-button size="small" type="primary">
                <span class="sctmp-iconfont icon-ic_daoru"></span>
                <span class="ml-5">导入</span>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">
                  <div class="has-padding-sm has-border-bottom">导入基础信息</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button size="small" type="primary" @click="handleExport">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button>
          </div>
        </header>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" width="55" align="center"></el-table-column>
            <el-table-column prop="companyName" label="所属公司" align="center"></el-table-column>
            <el-table-column prop="deviceName" label="设备名称" align="center"></el-table-column>
            <el-table-column prop="deviceCode" label="设备编号" align="center"></el-table-column>
            <el-table-column prop="runStatus" label="运行状态" align="center">
              <template #default="{ row }">{{ CURRENT_STATUS[row.runStatus] }}</template>
            </el-table-column>
            <el-table-column prop="deviceStatus" label="设备状态" align="center">
              <template #default="{ row }">{{ DEVICE_STATUS[row.deviceStatus] }}</template>
            </el-table-column>
            <el-table-column prop="deviceAddress" label="设备地址" align="center"></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="$emit('changeType', { type: 1, id: row.id })"
                  >投放记录</el-link
                >
                <el-link class="mr-10" type="primary" @click="$emit('changeType', { type: 2, id: row.id })"
                  >收运记录</el-link
                >
                <el-link class="mr-10" type="primary" @click="editRecord(row)">详情</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
    <importDialog
      :value.sync="importDialogShow"
      title="智能收集柜档案"
      :importApi="apis.import"
      :templateApi="apis.template"
      :importDialogType="importDialogType"
      :isTemplateApi="true"
      @importSuccess="searchFilter"
    ></importDialog>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import { DEVICE_STATUS, CURRENT_STATUS } from "@/enums";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, BASE_API_URL } from "@/api/base";
  import record from "./record.vue";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import importDialog from "@/components/importDialog";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
      importDialog,
    },
    data() {
      return {
        filterForm: {},
        DEVICE_STATUS,
        CURRENT_STATUS,
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/holding/holdingTank/listPage",
          export: "/api/holding/holdingTank/exportExcelHoldingTank",
          import: "/api/holding/holdingTank/importHoldingTank",
          template: "/api/holding/holdingTank/excleModel",
        },
        showRecord: false,
        recordId: "",
        loading: false,
        importDialogShow: false,
        importDialogType: "",
        channelRecord: {},
      };
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.recordId = row.id;
      },
      closeRecord() {
        this.showRecord = false;
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 导出
      handleExport() {
        this.$confirm(`确认是否以当前筛选条件导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, this.filterForm);
              if (res.success) {
                createDownloadEvent(`智能收集柜档案${Date.now()}.xlsx`, [res.data]);
                window.ELEMENT.Message.success("导出成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
      // 导入
      handleDataImport(command) {
        this.importDialogType = command;
        this.importDialogShow = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-input {
      margin-right: 10px;
    }
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w250 {
    width: 250px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  ::v-deep .header .el-form-item {
    margin-bottom: 0;
  }
</style>
