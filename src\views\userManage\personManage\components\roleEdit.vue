<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="添加权限" :visible.sync="dialogVisible" width="54%" destroy-on-close @open="initData">
      <div v-loading="loading">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
          <el-form-item prop="roleList">
            <el-transfer
              :titles="['可选账号角色', '已选账号角色']"
              v-model="ruleForm.roleList"
              :data="roleOptions"
              :button-texts="['删除角色', '添加角色']"
              :props="{
                key: 'roleId',
                label: 'roleName',
              }"
            >
            </el-transfer>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRoleThrottling">保存</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getInfoApiFunByParams, createApiFun, getInfoApiFun } from "@/api/base";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      lgUnionId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        ruleForm: {
          roleList: [],
        },
        rules: {
          roleList: [{ type: "array", required: true, message: "请选择账号角色", trigger: "change" }],
        },
        apis: {
          userHaveRole: "/api/userrole/roleListByLgUserId",
          setUserRole: "/api/userrole/setUserRole",
          roleList: "/api/userrole/roleList",
        },
        saveRoleThrottling: () => {},
        loading: false,
        roleOptions: [], //角色列表
      };
    },
    created() {
      this.saveRoleThrottling = this.$throttling(this.saveRole, 500);
    },
    methods: {
      async initData() {
        await this.getRoleList();
        await this.getUserHaveRoleList();
      },
      // 获取角色列表
      async getRoleList() {
        let res = await getInfoApiFun("", this.apis.roleList);
        if (res.success) {
          this.roleOptions = res.data.filter((item) => ![100, 168, 200].includes(item.level));
        }
      },
      // 获取用户已有角色列表
      async getUserHaveRoleList() {
        this.$nextTick(() => {
          this.$refs.ruleForm.clearValidate();
        });
        let res = await getInfoApiFunByParams({ lgUserId: this.lgUnionId }, this.apis.userHaveRole);
        if (res.success) {
          let roleList = res.data.map((list) => list.roleId);
          this.$set(this.ruleForm, "roleList", roleList);
        }
      },
      // 保存
      saveRole() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun({ userId: this.lgUnionId, ...this.ruleForm }, this.apis.setUserRole);
              if (res.success) {
                this.$message.success("权限保存成功");
                this.dialogVisible = false;
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  ::v-deep .el-transfer {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
    min-width: 240px;
  }
  ::v-deep .el-transfer-panel__body {
    height: 500px;
  }
  ::v-deep .el-transfer-panel__list {
    height: 100%;
  }
</style>
