<template>
  <v-scale-screen width="1920" height="1080" class="dp-first-content" v-loading="loading">
    <div class="video-container" :style="{ 'background-image': 'url(' + iconData.bg + ')' }">
      <Header></Header>
      <ul class="video-list">
        <li class="video-item">
          <video class="video-box" ref="flvPlayer1" autoplay muted></video>
          <div v-loading="loading1" class="videoSrc-bg">
            <div v-if="unSupported1">当前浏览器不支持，请更换其他浏览器</div>
            <div v-else-if="loadingEnd1">加载失败</div>
          </div>
        </li>
        <li class="video-item">
          <video class="video-box" ref="flvPlayer2" autoplay muted></video>
          <div v-loading="loading2" class="videoSrc-bg">
            <div v-if="unSupported2">当前浏览器不支持，请更换其他浏览器</div>
            <div v-else-if="loadingEnd2">加载失败</div>
          </div>
        </li>
        <li class="video-item">
          <video class="video-box" ref="flvPlayer3" autoplay muted></video>
          <div v-loading="loading3" class="videoSrc-bg">
            <div v-if="unSupported3">当前浏览器不支持，请更换其他浏览器</div>
            <div v-else-if="loadingEnd3">加载失败</div>
          </div>
        </li>
        <li class="video-item">
          <video class="video-box" ref="flvPlayer4" autoplay muted></video>
          <div v-loading="loading4" class="videoSrc-bg">
            <div v-if="unSupported4">当前浏览器不支持，请更换其他浏览器</div>
            <div v-else-if="loadingEnd4">加载失败</div>
          </div>
        </li>
      </ul>
    </div>
  </v-scale-screen>
</template>
<script>
  import VScaleScreen from "v-scale-screen";
  import Header from "./components/Header.vue";
  import { getListPageApiFun } from "@/api/base.js";
  import flvjs from "flv.js";
  export default {
    components: {
      VScaleScreen,
      Header,
    },
    props: {},
    data() {
      return {
        channel: 1,
        command: 1,
        oldChannel: null,
        summaryData: {},
        iconData: {
          bg: require("@/assets/images/<EMAIL>"),
        },
        apis: {
          postData: "/api/monitor/real-time",
        },
        plateNumber: "",
        maxTime: 60000,
        videoSrc1: "",
        videoSrc2: "",
        videoSrc3: "",
        videoSrc4: "",
        flvPlayer1: null,
        flvPlayer2: null,
        flvPlayer3: null,
        flvPlayer4: null,
        time1: null,
        time2: null,
        time3: null,
        time4: null,
        currentTime1: 0,
        currentTime2: 0,
        currentTime3: 0,
        currentTime4: 0,
        loading: false,
        loading1: false,
        loading2: false,
        loading3: false,
        loading4: false,
        loadingEnd1: false,
        loadingEnd2: false,
        loadingEnd3: false,
        loadingEnd4: false,
        unSupported1: false,
        unSupported2: false,
        unSupported3: false,
        unSupported4: false,
      };
    },
    computed: {},
    created() {},
    async mounted() {
      if (this.$route.query.channel) {
        this.channel = this.$route.query.channel;
      }
      if (this.$route.query.command) {
        this.command = this.$route.query.command;
      }
      if (this.$route.query.videoSrc) {
        this.videoSrc = this.$route.query.videoSrc;
      }
      if (this.$route.query.plateNumber) {
        this.plateNumber = this.$route.query.plateNumber;
      }
      await this.postData();
      try {
        this.initPlayer();
      } catch (error) {
        console.log(error, "error");
      }
    },
    async beforeDestroy() {
      // 如果acc未开始，正常来说是进不了这个页面
      // 如过location接口，返回的字段 错误，导致进入这个页面
      // real-time接口获取不到流地址，再就不用调用关流接口了
      await this.endData();
    },
    methods: {
      initPlayer() {
        if (flvjs.isSupported()) {
          for (let i = 1; i < 5; i++) {
            if (!this[`videoSrc${i}`]) {
              continue;
            }
            this[`loading${i}`] = true;
            this[`currentTime${i}`] = 0;
            this.loopFlvPlayer(i);
          }
        } else {
          for (let i = 0; i < 5; i++) {
            this[`loading${i}`] = false;
            this[`loadingEnd${i}`] = true;
            this[`unSupported${i}`] = true;
          }
        }
      },
      loopFlvPlayer(index) {
        let videoElement = this.$refs[`flvPlayer${index}`];
        if (!videoElement) {
          console.error("Video element not found");
          return;
        }
        this[`flvPlayer${index}`] = flvjs.createPlayer({
          type: "flv",
          url: this[`videoSrc${index}`],
        });
        this[`flvPlayer${index}`].attachMediaElement(videoElement);
        this[`flvPlayer${index}`].load();
        this[`time${index}`] = setInterval(() => {
          this[`currentTime${index}`] += 100;
          if (this[`flvPlayer${index}`].currentTime > 0) {
            // 假设如果currentTime大于0，视频已经开始播放
            this[`loading${index}`] = false; // 隐藏加载效果
            clearInterval(this[`time${index}`]);
          } else if (this[`currentTime${index}`] > this.maxTime) {
            this[`loading${index}`] = false; // 隐藏加载效果
            this[`loadingEnd${index}`] = true;
            this[`flvPlayer${index}`].pause();
            this[`flvPlayer${index}`].unload();
            clearInterval(this[`time${index}`]);
          }
        }, 100); // 你可以根据需要调整这个时间
        var playPromise = this[`flvPlayer${index}`].play();
        if (playPromise !== undefined) {
          playPromise
            .then((_) => {
              console.log(_, "_");
            })
            .catch((error) => {});
        }
      },
      async endData() {
        for (let i = 1; i < 5; i++) {
          clearInterval(this[`time${i}`]);
          if (this[`flvPlayer${i}`]) {
            this[`flvPlayer${i}`].destroy();
            this[`flvPlayer${i}`] = null;
          }
        }
        let promiseList = [
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 1,
              command: 0,
              streamType: 1,
            },
            this.apis.postData,
          ),
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 2,
              command: 0,
              streamType: 1,
            },
            this.apis.postData,
          ),
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 3,
              command: 0,
              streamType: 1,
            },
            this.apis.postData,
          ),
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 4,
              command: 0,
              streamType: 1,
            },
            this.apis.postData,
          ),
        ];
        try {
          this.loading = true;
          await Promise.all(promiseList);
          this.loading = false;
        } catch (error) {
          this.loading = false;
          console.log(error);
        }
      },
      async postData() {
        let promiseList = [
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 1,
              command: 1,
              streamType: 1,
            },
            this.apis.postData,
          ),
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 2,
              command: 1,
              streamType: 1,
            },
            this.apis.postData,
          ),
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 3,
              command: 1,
              streamType: 1,
            },
            this.apis.postData,
          ),
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 4,
              command: 1,
              streamType: 1,
            },
            this.apis.postData,
          ),
        ];
        try {
          this.loading = true;
          let res = await Promise.all(promiseList);
          this.videoSrc1 = res[0].data;
          this.videoSrc2 = res[1].data;
          this.videoSrc3 = res[2].data;
          this.videoSrc4 = res[3].data;
          this.loading = false;
        } catch (error) {
          this.loading = false;
          console.log(error);
        }
      },
    },
  };
</script>
<style lang="scss" scoped>
  .video-container {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
  }
  .video-list {
    flex: 1;
    overflow: hidden;
    display: grid;
    grid-gap: 20px;
    grid-template-columns: repeat(2, 1fr);
    padding: 120px 20px 0 20px;
    margin-bottom: 20px;
    .video-item {
      height: 470px;
      position: relative;
      .video-box {
        width: 100%;
        height: 100%;
        position: relative;
        z-index: 2;
      }
    }
  }
  .videoSrc {
    position: absolute;
    top: 128px;
    left: 40px;
    z-index: 2;
    width: 1840px;
    height: calc(912px - 56px);
  }
  .videoSrc-bg {
    display: flex;
    color: #fff;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
  }
  .button-wrapper {
    position: absolute;
    top: 984px;
    left: 40px;
    z-index: 1;
    width: 1840px;
    background-color: rgba(255, 255, 255, 0.9);
    height: 56px;
    display: flex;
    justify-content: space-between;
    .button-wrapper-left,
    .button-wrapper-right {
      padding: 0 20px;
      display: flex;
      align-items: center;
      height: 56px;
      .text {
        line-height: 21px;
        font-size: 16px;
        font-weight: bold;
        color: #1d2925;
        margin-right: 20px;
      }
    }
  }
</style>
