<template>
  <div class="fw-statistics">
    <div class="title" v-if="showTitle">{{ chartTitle }}今日收运量情况 (单位:KG)</div>
    <div class="chart-data" id="run100"> </div>
  </div>
</template>

<script>
  import * as echarts from "echarts";

  export default {
    components: {},

    name: "TodayFwStatistics",

    props: {
      summaryData: {
        type: Object,
        default: () => {
          return {
            pathologicalWaste: 0,
            sludge: 0,
            pharmaceuticalWastePrecentage: "0.00%",
            damaginWaste: 0,
            chemicalWastePrecentage: "0.00%",
            infectiousWaste: 0,
            total: 0,
            pathologicalWastePrecentage: "0.00%",
            chemicalWaste: 0,
            infectiousWastePrecentage: "0.00%",
            sludgePrecentage: "0.00%",
            pharmaceuticalWaste: 0,
            damaginWastePrecentage: "0.00%",
          };
        },
      },

      chartTitle: {
        type: String,
        default: "",
      },

      showTitle: {
        type: Boolean,
        default: true,
      },
    },

    data() {
      return {
        seriesData: [0, 0, 0, 0, 0, 0],
        seriesPrecentage: ["", "", "", "", "", ""],
        total: 0,
      };
    },

    mounted() {},

    watch: {
      summaryData: {
        handler(n) {
          if (n.total != void 0) {
            this.init();
          }
        },
        deep: true,
        immediate: true,
      },
    },

    methods: {
      getSeriesData() {
        const data = this.summaryData;
        this.seriesData = [
          data.infectiousWaste,
          data.damaginWaste,
          data.chemicalWaste,
          data.pharmaceuticalWaste,
          data.pathologicalWaste,
          data.sludge,
        ];
        this.seriesPrecentage = [
          data.infectiousWastePrecentage,
          data.damaginWastePrecentage,
          data.chemicalWastePrecentage,
          data.pharmaceuticalWastePrecentage,
          data.pathologicalWastePrecentage,
          data.sludgePrecentage,
        ];
        this.total = data.total;
      },

      init() {
        this.getSeriesData();

        const chartData = [
          {
            name: "感染性废物",
            value: this.seriesData[0],
            precentage: this.seriesPrecentage[0],
            color: "#1A9CFF",
          },
          {
            name: "感染性废物(污泥)",
            value: this.seriesData[5],
            precentage: this.seriesPrecentage[5],
            color: "#9FD4FD",
          },
          {
            name: "损伤性废物",
            value: this.seriesData[1],
            precentage: this.seriesPrecentage[1],
            color: "#FABB28",
          },
          {
            name: "化学性废物",
            value: this.seriesData[2],
            precentage: this.seriesPrecentage[2],
            color: "#33D1C9",
          },
          {
            name: "药物性废物",
            value: this.seriesData[3],
            precentage: this.seriesPrecentage[3],
            color: "#5CC78A",
          },
          {
            name: "病理性废物",
            value: this.seriesData[4],
            precentage: this.seriesPrecentage[4],
            color: "#7372E9",
          },
        ];

        // 自定义legend的图标
        const legendData = [
          {
            name: "感染性废物",
            icon: "rect",
          },
          {
            name: "感染性废物(污泥)",
            icon: "rect",
          },
          {
            name: "损伤性废物",
            icon: "rect",
          },
          {
            name: "化学性废物",
            icon: "rect",
          },
          {
            name: "药物性废物",
            icon: "rect",
          },
          {
            name: "病理性废物",
            icon: "rect",
          },
        ];

        const dataList = [
          { value: this.seriesData[0], name: "感染性废物", itemStyle: { color: "#1A9CFF" } },
          { value: this.seriesData[5], name: "感染性废物(污泥)", itemStyle: { color: "#9FD4FD" } },
          { value: this.seriesData[1], name: "损伤性废物", itemStyle: { color: "#FABB28" } },
          { value: this.seriesData[2], name: "化学性废物", itemStyle: { color: "#33D1C9" } },
          { value: this.seriesData[3], name: "药物性废物", itemStyle: { color: "#5CC78A" } },
          { value: this.seriesData[4], name: "病理性废物", itemStyle: { color: "#7372E9" } },
        ];

        const myChart = echarts.init(document.getElementById("run100"));

        myChart.setOption({
          tooltip: {
            trigger: "item",
            formatter: "{b}<br/>{c}(kg)",
          },

          legend: {
            orient: "vertical",
            right: 0,
            top: this.showTitle ? "38%" : "28%",
            itemWidth: 8,
            itemHeight: 8,
            data: legendData.map((item) => item.name), // 使用map提取name属性
            formatter: function (val) {
              const item = chartData.find((chartDataItem) => chartDataItem.name === val);
              if (item) {
                const valStr = val.toString();
                const valueStr = item.value.toString();
                const percentageStr = item.precentage?.toString();
                return `{title|${valStr}} {value|${valueStr}}`;
              }
              return val;
            },
            textStyle: {
              rich: {
                title: {
                  fontSize: 14,
                  color: "#fff",
                },
                value: {
                  fontSize: 14,
                  color: "#fff",
                  padding: [0, 0, 0, 10],
                },
              },
            },
          },
          title: {
            text: `{name|${this.total}(kg)\n\n总重量}`,
            left: "29%", // 调整这个值以使标题在饼图内部居中
            top: this.showTitle ? "53%" : "43%", // 垂直居中
            textStyle: {
              color: "#fff",
              fontSize: 14, // 根据需要调整字体大小
              rich: {
                name: {
                  width: 0,
                  fontSize: 14,
                  color: "#fff",
                  align: "center",
                },
              },
            },
          },

          series: [
            {
              type: "pie",
              radius: ["35%", "55%"],
              center: ["30%", this.showTitle ? "60%" : "50%"], // 饼图靠左
              label: {
                show: true,
                color: "inherit",
                formatter: "{d}%",

                // position: "center",
                // formatter: () => `{count|${this.total}(kg)}\n{name|总重量}`,
                // rich: {
                //   count: {
                //     fontSize: 14,
                //     padding: [5, 0, 0, 0],
                //     color: "#fff",
                //     textVerticalAlign: "middle",
                //   },
                //   name: {
                //     fontSize: 14,
                //     color: "#fff",
                //     padding: [10, 0, 0, 0],
                //   },
                // },
              },
              data: dataList,
            },
          ],
        });
      },
    },
  };
</script>

<style scoped lang="scss">
  .fw-statistics {
    width: 480px;
    position: relative;
    z-index: 1;
    height: 280px;

    .title {
      font-size: 16px;
      color: #fff;
      padding: 10px 0;
      position: absolute;
      top: 0;
      left: 5px;
    }
  }
  .chart-data {
    width: 100%;
    height: 280px;
    display: flex;
  }
</style>
