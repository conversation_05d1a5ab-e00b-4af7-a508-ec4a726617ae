/*
 * @Description: 结合webWork处理定时器在页面失活不准确问题
 * @Author: wuwl
 * @Date: 2024-05-30 14:18:23
 * @FilePath: \web\src\utils\webTick\main.js
 * @LastEditTime: 2024-05-30 14:20:00
 * @LastEditors: wuwl
 */

import { workerCode } from "./workerCode.js";
import { getUUID } from "./utils.js";

const SETTIMEOUT = "setTimeout";
const SETINTERVAL = "setInterval";
const CLEARTIMEOUT = "clearTimeout";
const CLEARINTERVAL = "clearInterval";

// 自定义执行,得到code的Url
const code = `(${workerCode})()`;
const blob = new Blob([code], { type: "application/javascript" });
const url = URL.createObjectURL(blob);

const worker = new Worker(url, { type: "module" });

// 定义一个map保存
const map = new Map();

// 监听worker的message
worker.onmessage = (event) => {
  const { uuid, id, finish } = event.data;

  const data = map.get(uuid);

  // 拿到worker传递的定时器id，保存
  if (uuid && id && data) {
    data.id = id;
    map.set(uuid, data);
  }

  // 指定定时器回调
  if (finish && data) {
    const { handle, args } = data;
    handle(...args);
  }
};

// 设置定时器
function setTimer(action, handle, timeout, ...args) {
  const uuid = getUUID();
  map.set(uuid, { handle, args });
  const workerData = {
    uuid,
    action,
    timeout,
    args,
  };
  worker.postMessage(workerData);
  return uuid;
}

function clearTimer(uuid, action) {
  const { id } = map.get(uuid);
  const workerData = {
    uuid,
    action: action === SETTIMEOUT ? CLEARTIMEOUT : CLEARINTERVAL,
    id,
  };
  // 通知清除
  worker.postMessage(workerData);
  // 移除记录
  map.delete(uuid);
}

function setTimeout(handle, timeout, ...args) {
  return setTimer(SETTIMEOUT, handle, timeout, ...args);
}

function setInterval(handle, timeout, ...args) {
  return setTimer(SETINTERVAL, handle, timeout, ...args);
}

function clearTimeout(uuid) {
  return clearTimer(uuid, SETTIMEOUT);
}

function clearInterval(uuid) {
  return clearTimer(uuid, SETINTERVAL);
}

export default {
  setInterval,
  setTimeout,
  clearInterval,
  clearTimeout,
};
