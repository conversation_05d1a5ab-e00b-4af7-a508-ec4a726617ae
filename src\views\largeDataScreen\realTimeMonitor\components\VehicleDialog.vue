<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      width="1400px"
      top="0"
      destroy-on-close
      @open="initData"
      :show-close="false"
    >
      <template #title>
        <header class="header">今日出车车辆表</header>
      </template>
      <div class="main-record" v-loading="loading">
        <div class="filter">
          <el-input class="input-box" v-model="keyword" placeholder="请输入车牌号" clearable></el-input>
          <el-button type="primary" @click="getDataList">搜索</el-button>
          <el-button type="primary" @click="initData">重置</el-button>
          <el-button type="primary" @click="filterIdentity(0)">只看经营性</el-button>
          <el-button type="primary" @click="filterIdentity(1)">只看非经营性</el-button>
        </div>
        <div class="title-box">
          <Title title="出车车辆" color="#000"></Title>
          <Title title="空闲车辆" color="#000"></Title>
        </div>
        <div class="record-content">
          <el-table :data="departureVehicleList" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column prop="plateNumber" label="车牌号" align="center"></el-table-column>
            <el-table-column prop="operationalNature" label="车辆属性" align="center">
              <template #default="{ row }">{{ OPERATIONAL_NATURE[row.operationalNature] }}</template>
            </el-table-column>
          </el-table>
          <el-table :data="unDepartureVehicleList" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column prop="plateNumber" label="车牌号" align="center"></el-table-column>
            <el-table-column prop="operationalNature" label="车辆属性" align="center">
              <template #default="{ row }">{{ OPERATIONAL_NATURE[row.operationalNature] }}</template>
            </el-table-column>
          </el-table>
        </div>
        <div class="record-footer">
          <el-button @click="dialogVisible = false">返回</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import Title from "./Title.vue";
  import { createApiFun } from "@/api/base";
  import { OPERATIONAL_NATURE } from "@/enums";
  export default {
    components: {
      Title,
    },
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        loading: false,
        keyword: "",
        type: "",
        apis: {
          list: "/api/monitor/departure/vehicle",
        },
        departureVehicleList: [],
        unDepartureVehicleList: [],
        OPERATIONAL_NATURE,
      };
    },
    methods: {
      initData() {
        this.type = "";
        this.keyword = "";
        this.getDataList();
      },
      filterIdentity(value) {
        this.type = value;
        this.getDataList();
      },
      async getDataList() {
        try {
          let {
            data: { departureVehicleList, unDepartureVehicleList },
          } = await createApiFun({ type: this.type, keyword: this.keyword, channelId: this.channelId }, this.apis.list);
          this.departureVehicleList = departureVehicleList;
          this.unDepartureVehicleList = unDepartureVehicleList;
        } catch (error) {
          console.log(error);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .record-content {
    flex: 1;
    overflow: auto;
    display: grid;
    grid-gap: 80px;
    grid-template-columns: repeat(2, 1fr);
    padding-bottom: 20px;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .header {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
  }
  .filter {
    display: flex;
    align-items: center;
    .input-box {
      width: 40%;
      margin-right: 10px;
    }
  }
  .title-box {
    margin: 40px 0;
    display: grid;
    grid-gap: 80px;
    grid-template-columns: repeat(2, 1fr);
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
