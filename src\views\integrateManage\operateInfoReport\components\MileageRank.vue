<template>
  <div class="rank-container">
    <el-row :gutter="10">
      <el-col>
        <SubTitle title="司机行驶里程排名"></SubTitle>
      </el-col>
      <el-col>
        <TimeScreen class="mt-16" @change="initData"></TimeScreen>
      </el-col>
    </el-row>
    <div class="rank-table">
      <el-table class="mt-16" :data="tableData" :header-cell-style="{ background: '#f9fafb' }" max-height="580" stripe>
        <el-table-column prop="rank" label="排名" align="center" width="80"></el-table-column>
        <el-table-column prop="driverName" label="司机姓名" align="center"></el-table-column>
        <el-table-column prop="totalMileage" label="行驶里程(km)" align="center" sortable></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
  import SubTitle from "./SubTitle.vue";
  import TimeScreen from "./TimeScreen.vue";
  import { createApiFun } from "@/api/base";
  export default {
    components: {
      SubTitle,
      TimeScreen,
    },
    props: {
      channelId: {
        type: String,
        default: "",
      },
    },
    watch: {
      channelId: {
        handler() {},
      },
    },
    data() {
      return {
        tableData: [],
      };
    },
    methods: {
      async initData(params) {
        try {
          let res = await createApiFun(
            { channelId: this.channelId, ...params },
            "/api/operation/info/getVehicleDailyRanking",
          );
          this.tableData = res.data;
        } catch (error) {
          console.log(error);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .rank-container {
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 16px;
    border-radius: 12px;
    height: 100%;
    overflow: hidden;
  }
</style>
