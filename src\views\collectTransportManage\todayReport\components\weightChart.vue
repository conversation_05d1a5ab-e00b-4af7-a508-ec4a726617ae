<template>
  <div class="chart-container" :id="chartId"></div>
</template>

<script>
  import * as echarts from "echarts";
  export default {
    props: {
      chartId: {
        type: String,
        default: "chart",
      },
      total: {
        type: Number,
        default: 0,
      },
      dataList: {
        type: Array,
        default: () => [],
      },
      radius: {
        type: Array,
        default: () => [60, 70],
      },
    },
    mounted() {
      this.init();
    },
    methods: {
      init() {
        let chartInstance = echarts.init(document.getElementById(this.chartId));
        let option = {
          tooltip: {
            trigger: "item",
            formatter: "{b}<br/>{c}(kg)",
          },
          title: {
            text: `${this.total}(kg)`,
            subtext: "总重量",
            left: "center",
            top: "42%",
          },
          series: [
            {
              type: "pie",
              radius: ["40%", "60%"],
              label: {
                show: true,
                color: "inherit",
                formatter: "{d}%",
              },
              data: this.dataList,
            },
          ],
        };
        chartInstance.setOption(option);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .chart-container {
    width: 100%;
    min-height: 300px;
  }
</style>
