<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">车辆营运证过期待处理</div>
      <el-form label-width="210px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="上报时间" required>
              <el-date-picker
                :value="recordItem.createTime"
                type="datetime"
                placeholder="请选择上报时间"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="截止日期" required>
              <el-date-picker
                :value="recordItem.deadline"
                type="date"
                placeholder="请选择截止日期"
                clearable
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="基础信息"></baseTitle>
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="车牌号" required>
              <el-input
                v-model="baseForm.plateNumber"
                class="w400"
                placeholder="请输入车牌号"
                clearable
                :maxlength="50"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆型号" required>
              <el-select v-model="baseForm.vehicleModel" placeholder="请选择车辆型号" clearable filterable disabled>
                <el-option
                  v-for="item in vehicleModelOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="品牌型号">
              <el-input
                v-model="baseForm.brandModel"
                class="w400"
                placeholder="请输入车辆品牌型号"
                clearable
                :maxlength="50"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="能源类型" required>
              <el-select
                v-model="baseForm.vehicleEnergyType"
                placeholder="请选择能源类型"
                clearable
                filterable
                disabled
              >
                <el-option
                  v-for="(item, index) in VEHICLE_ENERGY_TYPE"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="总质量(Kg)" required>
              <el-input-number
                v-model="baseForm.grossWeight"
                :min="0"
                :max="99999"
                :precision="2"
                disabled
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="加油/充电类型">
              <el-select v-model="baseForm.refuelType" placeholder="请选择加油/充电类型" clearable filterable disabled>
                <el-option
                  v-for="(item, index) in refuelTypeOptions"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="油箱(L)/电池容量(Kwh)" required>
              <el-input-number
                v-model="baseForm.fuelTankCapacity"
                :min="0"
                :max="9999"
                :precision="2"
                disabled
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="排放标准" required>
              <el-select v-model="baseForm.emissionStandard" placeholder="请选择排放标准" clearable filterable disabled>
                <el-option
                  v-for="(item, index) in emissionStandardOptions"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车架号" required>
              <el-input
                class="w400"
                v-model="baseForm.vin"
                placeholder="请输入车架号"
                :maxlength="50"
                show-word-limit
                clearable
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="发动机号" required>
              <el-input
                class="w400"
                v-model="baseForm.engineNumber"
                placeholder="请输入发动机号"
                :maxlength="50"
                show-word-limit
                clearable
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="装载体积(m³)">
              <el-input-number
                v-model="baseForm.loadVolume"
                :min="0"
                :max="9999"
                :precision="2"
                disabled
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="装载重量(Kg)" required>
              <el-input-number
                v-model="baseForm.loadCapacity"
                :min="0"
                :max="9999"
                :precision="2"
                disabled
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="购买日期" required>
              <el-date-picker
                v-model="baseForm.purchaseDate"
                type="date"
                placeholder="请选择购买日期"
                align="right"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="所属部门">
              <el-cascader
                v-model="baseForm.departmentId"
                placeholder="请选择所属部门"
                filterable
                clearable
                :options="deptOptions"
                :props="deptProps"
                :show-all-levels="false"
                disabled
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆状态" required>
              <el-select v-model="baseForm.status" placeholder="请选择车辆状态" clearable filterable disabled>
                <el-option
                  v-for="(item, index) in CAR_STATUS.slice(0, 2)"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="工作状态" required>
              <el-select v-model="baseForm.workingStatus" placeholder="请选择工作状态" clearable filterable disabled>
                <el-option
                  v-for="(item, index) in WORKING_STATUS"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="营运性质" required>
              <el-select
                v-model="baseForm.operationalNature"
                placeholder="请选择营运性质"
                clearable
                filterable
                disabled
              >
                <el-option
                  v-for="(item, index) in OPERATIONAL_NATURE"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="最近年审日期" required>
              <el-date-picker
                v-model="baseForm.recentAnnualReviewTime"
                type="date"
                placeholder="请选择最近年审日期"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="下次年审日期" required>
              <el-date-picker
                v-model="baseForm.nextAnnualReviewTime"
                type="date"
                placeholder="请选择下次年审日期"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="最近保养日期" required>
              <el-date-picker
                v-model="baseForm.recentMaintenanceTime"
                type="date"
                placeholder="请选择最近保养日期"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="下次保养日期">
              <el-date-picker
                v-model="baseForm.nextMaintenanceTime"
                type="date"
                placeholder="请选择下次保养日期"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="最近交强险开始日期" required>
              <el-date-picker
                class="w300"
                v-model="baseForm.lastMotcStartTime"
                type="date"
                placeholder="请选择最近交强险开始日期"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="交强险终保日期" required>
              <el-date-picker
                class="w300"
                v-model="baseForm.lastMotcEndTime"
                type="date"
                placeholder="请选择交强险终保日期"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="最近商业险开始日期" required>
              <el-date-picker
                class="w300"
                v-model="baseForm.lastCommercialStartTime"
                type="date"
                placeholder="请选择最近商业险开始日期"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="商业险终保日期" required>
              <el-date-picker
                class="w300"
                v-model="baseForm.lastCommercialEndTime"
                type="date"
                placeholder="请选择商业险终保日期"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 行驶证 -->
          <el-col>
            <el-form-item label="车辆行驶证编号" required>
              <el-input
                class="w400"
                v-model="baseForm.drivingCertNumber"
                placeholder="请输入车辆行驶证编号"
                :maxlength="50"
                show-word-limit
                clearable
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆行驶证有效期开始日期" required>
              <el-date-picker
                v-model="baseForm.drivingCertBeginDate"
                class="w300"
                type="date"
                placeholder="请选择车辆行驶证有效期开始日期"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆行驶证有效期结束日期" required>
              <el-date-picker
                v-model="baseForm.drivingCertEndDate"
                class="w300"
                type="date"
                placeholder="请选择车辆行驶证有效期结束日期"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="车辆行驶证登记日期" required>
              <el-date-picker
                v-model="baseForm.drivingCertRegDate"
                class="w300"
                type="date"
                placeholder="请选择车辆行驶证登记日期"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆行驶证件主页照片">
              <el-upload
                action="custom"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :file-list="photoImageList['drivingCertPhotoFront']"
                disabled
                class="base-upload"
                v-if="photoImageList['drivingCertPhotoFront'] && photoImageList['drivingCertPhotoFront'].length > 0"
              ></el-upload>
              <span v-else>-</span>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆行驶证件副页照片">
              <el-upload
                action="custom"
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :file-list="photoImageList['drivingCertPhotoBack']"
                disabled
                class="base-upload"
                v-if="photoImageList['drivingCertPhotoBack'] && photoImageList['drivingCertPhotoBack'].length > 0"
              ></el-upload>
              <span v-else>-</span>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="最近二级维护日期" required>
              <el-date-picker
                v-model="baseForm.lastSecMaintTime"
                class="w300"
                type="date"
                placeholder="请选择最近二级维护日期"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="下次二级维护日期" required>
              <el-date-picker
                v-model="baseForm.nextSecMaintTime"
                class="w300"
                type="date"
                placeholder="请选择下次二级维护日期"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="最近承运险开始日期" required>
              <el-date-picker
                v-model="baseForm.lastTransInsuranceTime"
                class="w300"
                type="date"
                placeholder="请选择最近承运险开始日期"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="承运险终保日期" required>
              <el-date-picker
                v-model="baseForm.finalTransInsuranceTime"
                class="w300"
                type="date"
                placeholder="请选择承运险终保日期"
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="两客一危设备ID号">
              <el-input
                class="w400"
                v-model="baseForm.deviceId"
                placeholder="请输入两客一危设备ID号"
                :maxlength="50"
                show-word-limit
                clearable
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="两客一危设备SIM卡号">
              <el-input
                class="w400"
                v-model="baseForm.deviceSimNumber"
                placeholder="请输入两客一危设备SIM卡号"
                :maxlength="50"
                show-word-limit
                clearable
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="180px" label-suffix="：">
        <el-row>
          <el-col>
            <el-form-item label="营运证编号" prop="operationCertNumber">
              <el-input
                class="w400"
                v-model="ruleForm.operationCertNumber"
                placeholder="请输入营运证编号"
                :maxlength="50"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="营运证有效期开始日期" prop="operationCertBeginDate">
              <el-date-picker
                v-model="ruleForm.operationCertBeginDate"
                class="w300"
                type="date"
                placeholder="请选择营运证有效期开始日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="营运证有效期结束日期" prop="operationCertEndDate">
              <el-date-picker
                v-model="ruleForm.operationCertEndDate"
                class="w300"
                type="date"
                placeholder="请选择营运证有效期结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="营运证登记日期" prop="operationCertRegDate">
              <el-date-picker
                v-model="ruleForm.operationCertRegDate"
                class="w300"
                type="date"
                placeholder="请选择营运证登记日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="营运证证件主页照片" prop="operationCertPhotoFront">
              <FileUpload
                @uploadChange="handleUploadChange($event, 'operationCertPhotoFront')"
                :imageList="photoImageList['operationCertPhotoFront']"
              >
                <template #tips><el-tag type="warning">请上传营运证证件主页照片</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="营运证证件副页照片" prop="operationCertPhotoBack">
              <FileUpload
                @uploadChange="handleUploadChange($event, 'operationCertPhotoBack')"
                :imageList="photoImageList['operationCertPhotoBack']"
              >
                <template #tips><el-tag type="warning">请上传营运证证件副页照片</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
      <el-button type="primary" @click="saveRecordThrottling">完成</el-button>
    </div>
    <el-image
      v-show="false"
      :src="dialogImageUrl"
      fit="cover"
      :preview-src-list="dialogImageList"
      ref="image__preview"
    ></el-image>
  </div>
</template>

<script>
  import { getInfoApiFun, updateApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import FileUpload from "@/components/FileUpload";
  import { CERT_TYPES, OPERATIONAL_NATURE, WORKING_STATUS, CAR_STATUS, VEHICLE_ENERGY_TYPE } from "@/enums";
  import { filterObjectByFieldArr } from "@/utils";
  export default {
    props: {
      recordItem: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      baseTitle,
      FileUpload,
    },
    data() {
      return {
        baseForm: {}, //业务基础信息
        ruleForm: {
          // 营运证
          operationCertNumber: null, //车辆营运证编号
          operationCertPhotoFront: null, //车辆营运证照片【主页】
          operationCertPhotoBack: null, //车辆营运证照片【反面】
          operationCertBeginDate: null, //车辆营运证有效期开始时间
          operationCertEndDate: null, //车辆营运证有效期结束时间
          operationCertRegDate: null, //车辆营运证登记日期
        },
        rules: {
          operationCertNumber: [{ required: true, message: "请输入营运证编号", trigger: "blur" }],
          operationCertBeginDate: [{ required: true, message: "请选择营运证有效期开始日期", trigger: "change" }],
          operationCertEndDate: [{ required: true, message: "请选择营运证有效期结束日期", trigger: "change" }],
          operationCertRegDate: [{ required: true, message: "请选择营运证登记日期", trigger: "change" }],
        },
        photoImageList: {
          operationCertPhotoFront: [],
          operationCertPhotoBack: [],
          drivingCertPhotoFront: [],
          drivingCertPhotoBack: [],
        },
        apis: {
          info: "/api/vehicle/dossier/get/",
          update: "/api/vehicle/dossier/update",
          refuelType: "/api/dict/refuelType/list", //加油类型
          emissionStandard: "/api/dict/emissionStandard/list", //排放标准
          deptList: "/api/company/structure/findIn",
          vehicleModel: "/api/dict/vehicleModel/list", //车辆型号
        },
        saveRecordThrottling: () => {},
        loading: false,
        OPERATIONAL_NATURE, //运营状态
        WORKING_STATUS, //工作状态
        CAR_STATUS, //车辆状态
        VEHICLE_ENERGY_TYPE, //车辆能源类型
        CERT_TYPES,
        refuelTypeOptions: [], //加油类型
        emissionStandardOptions: [], //排放标准
        vehicleModelOptions: [], //车辆型号
        deptOptions: [], //所属部门
        deptProps: {
          checkStrictly: true,
          value: "id",
          label: "name",
          emitPath: false,
        },
        dialogImageUrl: "",
        dialogImageList: [],
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptionsList();
      if (this.recordItem && this.recordItem.businessId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取数据列表
      async getOptionsList() {
        let promiseList = [
          getInfoApiFun("", this.apis.refuelType),
          getInfoApiFun("", this.apis.emissionStandard),
          getInfoApiFun("", this.apis.deptList),
          getInfoApiFun("", this.apis.vehicleModel),
        ];
        try {
          let res = await Promise.all(promiseList);
          this.refuelTypeOptions = res[0].data;
          this.emissionStandardOptions = res[1].data;
          this.deptOptions = res[2].data;
          this.vehicleModelOptions = res[3].data;
        } catch (error) {
          console.warn(error);
        }
      },
      // 获取详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordItem.businessId, this.apis.info);
        if (res.success) {
          this.baseForm = res.data;
          this.handleBackImage();
          this.ruleForm = filterObjectByFieldArr(res.data, [
            "operationCertNumber",
            "operationCertPhotoFront",
            "operationCertPhotoBack",
            "operationCertBeginDate",
            "operationCertEndDate",
            "operationCertRegDate",
          ]);
        }
      },
      handleBackImage() {
        for (const key in this.photoImageList) {
          if (Object.hasOwnProperty.call(this.photoImageList, key)) {
            const item = this.baseForm[key];
            if (this.baseForm[key]) {
              this.photoImageList[key] = [
                {
                  url: item.url,
                  file: item,
                },
              ];
            }
          }
        }
      },
      //图片预览
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogImageList = [file.url];
        this.$nextTick(() => {
          this.$refs.image__preview.clickHandler();
        });
      },
      handleUploadChange(fileList, field) {
        if (fileList.length && fileList[0].file) {
          this.ruleForm[field] = {
            name: fileList[0].name,
            size: fileList[0].size,
            ...fileList[0].file,
          };
        } else {
          this.ruleForm[field] = null;
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("returnIndex");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            let params = Object.assign(this.baseForm, this.ruleForm);
            try {
              let res = await updateApiFun(params, this.apis.update);
              if (res.success) {
                this.$message.success(`操作成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-box {
    margin-bottom: 20px;
  }
  .w400 {
    width: 400px;
  }
  .w300 {
    width: 300px;
  }
  ::v-deep .base-upload .el-upload--picture-card {
    display: none;
  }
</style>
