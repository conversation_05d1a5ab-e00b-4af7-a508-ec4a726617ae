<template>
  <div class="container micro-app-sctmp_base">
    <div class="title">车辆今日有效收运单</div>
    <div class="search">
      <el-input
        placeholder="输入收运单编号/路线名称/司机名称"
        v-model="keyword"
        clearable
        class="search-input"
      ></el-input>
      <el-link type="primary" class="search-button" @click="getDataList">搜索</el-link>
    </div>
    <ul class="header-list">
      <li v-for="(item, index) in headerList" :key="index">{{ item }}</li>
    </ul>
    <div class="line"></div>
    <vue-seamless-scroll
      v-if="listData && listData.length > 0"
      class="seaml-scroll"
      :data="listData"
      :class-option="seamlessScrollOption"
    >
      <ul>
        <div class="data-item" v-for="(item, index) in listData" :key="index">
          <div class="text-ellipsis">{{ item.waybillCode }}</div>
          <div class="text-ellipsis">{{ item.name }}</div>
          <div class="text-ellipsis">{{ item.defaultDriverDossierName }}</div>
          <div class="text-ellipsis">{{ item.defaultVehiclePlateNumber }}</div>
          <el-link type="primary" @click="viewDetail(item)">查看详情</el-link>
        </div>
      </ul>
    </vue-seamless-scroll>
    <el-empty description="暂无数据" v-else class="empty-container"></el-empty>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  export default {
    data() {
      return {
        keyword: "",
        seamlessScrollOption: {
          step: 2, // 数值越大速度滚动越快
          limitMoveNum: 6, // 开始无缝滚动的数据量 this.dataList.length
          hoverStop: true, // 是否开启鼠标悬停stop
          direction: 1, // 0向下 1向上 2向左 3向右
          openWatch: true, // 开启数据实时监控刷新dom
          singleHeight: 50, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
          singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
          waitTime: 2000, // 单步运动停止的时间(默认值1000ms)
        },
        headerList: ["收运单编号", "路线名称", "驾驶司机", "车牌号", "操作"],
        listData: [],
      };
    },
    mounted() {},
    methods: {
      async getDataList() {
        try {
          let res = await createApiFun(
            { keyword: this.keyword, plateNumber: this.$store.state.plateNumber },
            "/api/monitor/waybill/vehicle/list",
          );
          if (res.success) {
            this.listData = res.data;
          }
        } catch (error) {
          console.log(error);
        }
      },
      viewDetail(item) {
        const { href } = this.$router.resolve({
          path: `/sctmp_base/realTimeDriving?id=${item.id}`,
        });
        window.open(href, "_blank");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .container {
    padding: 20px;
    pointer-events: auto;
  }
  .title {
    color: #fff;
    font-size: 16px;
    font-weight: bold;
  }
  .search {
    display: flex;
    align-items: center;
    margin: 20px 0;
    .search-input {
      flex: 1;
      overflow: hidden;
    }
    .search-button {
      flex-shrink: 0;
      margin-left: 20px;
    }
  }
  .line {
    width: 100%;
    height: 1px;
    background-color: #fff;
    margin-top: 10px;
  }
  .header-list,
  .data-item {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    color: #fff;
    font-size: 12px;
    text-align: center;
    line-height: 16px;
    margin-bottom: 14px;
  }
  .seaml-scroll {
    height: 110px;
    overflow: hidden;
    width: 100%;
    margin-top: 10px;
  }
  .empty-container {
    height: 120px;
  }
  ::v-deep .search-input input {
    border: none;
    outline: none;
    background-color: rgba(153, 153, 153, 0.4);
    color: #fff;
  }
  ::v-deep .empty-container .el-empty__image {
    height: 80px;
  }
</style>
