import axios from "axios";
import qs from "qs";
import store from "@/store";

var instance = axios.create({
  baseURL: "",
  timeout: 300000,
});

// 添加请求拦截器
instance.interceptors.request.use(
  async (config) => {
    let token = "";
    token = await window.LOGAN.getToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    config.cancelToken = new axios.CancelToken((cancel) => {
      store.dispatch("pushCancel", { cancelToken: cancel });
    });
    // 发送请求之前
    let enc = new TextDecoder("utf-8");
    let res;

    try {
      res = JSON.parse(enc.decode(new Uint8Array(config)));
    } catch {
      res = config;
    }

    return res;
  },
  (error) => {
    // 请求错误
    return Promise.reject(error);
  },
);

// 添加响应拦截器
instance.interceptors.response.use(
  async (response) => {
    let enc = new TextDecoder("utf-8");
    let res;

    try {
      res = JSON.parse(enc.decode(new Uint8Array(response.data)));
    } catch {
      res = response.data;
    }
    if (Object.prototype.toString.call(res) !== "[object Object]") {
      res = {
        errcode: 0,
        data: res,
      };
    }
    res.success = false;
    res.errcode = res.errcode || res.status || 0;
    //返回文件流时，没有状态
    if (typeof res.success === "undefined" && typeof res.errcode === "undefined") {
      res.errcode = 0;
    }
    if (res.data?.type === "application/json") {
      let text = await res.data.text();
      try {
        text = JSON.parse(text);
      } catch (error) {
        text = "";
      }
      if (text) {
        res.errcode = text.status;
        res.message = text.message;
      }
    }

    const errCode = Number.parseInt(res.errcode);

    // 处理token失效的情况
    if (errCode === 402 || errCode === 403 || errCode === 409) {
      // 获取原始请求配置，用于重新发送请求
      const originalRequest = response.config;

      // 尝试刷新token
      try {
        // 调用store中的refreshAuthToken方法刷新token
        const newToken = await store.dispatch("refreshAuthToken");

        // 如果成功获取到新token
        if (newToken) {
          // 更新原始请求的Authorization头
          originalRequest.headers.Authorization = `Bearer ${newToken}`;

          // 重新发送原始请求
          return instance(originalRequest);
        }

        // 如果刷新失败，refreshAuthToken方法内部会处理登出逻辑
        return res;
      } catch (error) {
        // 发生错误，返回原始响应
        console.error("Error during token refresh:", error);
        return res;
      }
    } else if (errCode === 0 || errCode === 200) {
      res.success = true;
    } else {
      // 默认提示：后端返回的信息
      window.ELEMENT.Message({
        message: res.errmsg || res.message,
        type: "warning",
      });
    }

    return res;
  },
  (error) => {
    if (axios.isCancel(error)) {
      // 使用isCancel 判断是否是主动取消请求
      return new Promise(() => {});
    }
    // 请求错误：提示错误信息
    window.ELEMENT.Message({
      message: error,
      type: "error",
    });
    return Promise.reject(error);
  },
);

//1.地址，2.参数，3.配置，4.是否表单格式（默认json格式）
export function post(url, data, config = {}, formData = false) {
  // 如果配置中已经指定了Content-Type，则使用指定的
  if (!config.headers || !config.headers["Content-Type"]) {
    // json格式请求头
    const headerJSON = {
      "Content-Type": "application/json",
    };
    // FormData格式请求头
    const headerFormData = {
      "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
    };

    if (!formData) {
      config = Object.assign({}, config);
      config.headers = Object.assign({}, config.headers, headerJSON);
    } else {
      data = qs.stringify(data);
      config = Object.assign({}, config);
      config.headers = Object.assign({}, config.headers, headerFormData);
    }
  }

  return instance.post(url, data, config);
}

//1.地址，2.参数，3.配置，4.是否表单格式（默认json格式）
export function put(url, data, config = {}, formData = false) {
  // 如果配置中已经指定了Content-Type，则使用指定的
  if (!config.headers || !config.headers["Content-Type"]) {
    // json格式请求头
    const headerJSON = {
      "Content-Type": "application/json",
    };
    // FormData格式请求头
    const headerFormData = {
      "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
    };

    if (!formData) {
      config = Object.assign({}, config);
      config.headers = Object.assign({}, config.headers, headerJSON);
    } else {
      data = qs.stringify(data);
      config = Object.assign({}, config);
      config.headers = Object.assign({}, config.headers, headerFormData);
    }
  }

  return instance.put(url, data, config);
}

export function get(url, params, config = {}) {
  config = Object.assign(config, {
    params,
  });
  return instance.get(url, config);
}

export function del(url, params, data = false, config = {}) {
  if (!data) {
    config = Object.assign(config, {
      params,
    });
  } else {
    config = Object.assign(config, {
      data: params,
    });
  }

  return instance.delete(url, config);
}

export function uploadFile(url, params, upProgress) {
  //文件上传
  return instance({
    method: "POST",
    headers: { "Content-Type": "multipart/form-data" },
    data: params,
    url,
    timeout: 0,
    onUploadProgress: upProgress,
  });
}

//文件下载时接收为blob形式
export function getFile(url, params) {
  return instance({
    url: url,
    method: "get",
    params: params,
    responseType: "blob",
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

//文件下载时接收为blob形式
export function exportFile(url, params) {
  return instance({
    url: url,
    method: "post",
    data: params,
    responseType: "blob",
  });
}
