window.downXhr = []; //记录下载请求
/**
 * 构建文件下载事件，触发浏览器下载
 * @param {*} filename 文件名+后缀
 * @param {*} blobArr blob数据列表
 * @returns
 */
export function createDownloadEvent(filename, blobArr) {
  return new Promise((resolve) => {
    var fileBlob = new Blob(blobArr, {
      type: "application/octet-stream;charset=utf-8",
    });
    if (window.navigator.msSaveOrOpenBlob) {
      navigator.msSaveBlob(fileBlob, filename);
    } else {
      var url = URL.createObjectURL(fileBlob);
      const link = document.createElement("a");
      link.download = filename;
      link.target = "_blank";
      link.style.display = "none";
      link.href = url;
      document.body.appendChild(link);
      link.click();
      URL.revokeObjectURL(link.href);
      document.body.removeChild(link);
    }
    resolve();
  });
}

/**
 * 下载文件
 * @param {*} url 下载路径
 * @param {*} cb //下载进度回调
 * @param {*} total //可传入文件大小(bytes)，计算进度位置
 * @returns
 */
export function downloadFile(url, cb, total) {
  var xhr = new XMLHttpRequest();
  window.downXhr.push(xhr); //记录下载请求
  return new Promise((resolve, reject) => {
    xhr.onreadystatechange = function () {
      if (this.readyState == 4 && this.status == 200) {
        resolve(xhr.response);
      }
    };
    xhr.open("GET", url);
    xhr.responseType = "blob";
    xhr.onerror = function () {
      reject(xhr.response || "error");
    };
    xhr.onprogress = function (event) {
      if (event.lengthComputable) {
        var percentComplete = (event.loaded / event.total) * 100;
        // console.log("下载进度:", Math.floor(percentComplete));
        return typeof cb == "function" && cb(Math.floor(percentComplete));
      } else if (total) {
        var sum = (event.loaded / total) * 100;
        // console.log("下载进度:", Math.floor(sum));
        return typeof cb == "function" && cb(Math.floor(sum));
      }
    };
    xhr.send();
  });
}
