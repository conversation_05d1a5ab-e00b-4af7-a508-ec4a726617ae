<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-suffix="：" label-width="220px">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="车牌号" prop="plateNumber">
              <el-input
                v-model="ruleForm.plateNumber"
                class="w400"
                placeholder="请输入车牌号"
                clearable
                :maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆型号" prop="vehicleModel">
              <el-select v-model="ruleForm.vehicleModel" placeholder="请选择车辆型号" clearable filterable>
                <el-option
                  v-for="item in vehicleModelOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="品牌型号" prop="brandModel">
              <el-input
                v-model="ruleForm.brandModel"
                class="w400"
                placeholder="请输入车辆品牌型号"
                clearable
                :maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="能源类型" prop="vehicleEnergyType">
              <el-select v-model="ruleForm.vehicleEnergyType" placeholder="请选择能源类型" clearable filterable>
                <el-option
                  v-for="(item, index) in VEHICLE_ENERGY_TYPE"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="总质量(Kg)" prop="grossWeight">
              <el-input-number v-model="ruleForm.grossWeight" :min="0" :max="99999" :precision="2"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="加油/充电类型" prop="refuelType">
              <el-select v-model="ruleForm.refuelType" placeholder="请选择加油/充电类型" clearable filterable>
                <el-option
                  v-for="(item, index) in refuelTypeOptions"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="油箱(L)/电池容量(Kwh)" prop="fuelTankCapacity">
              <el-input-number
                v-model="ruleForm.fuelTankCapacity"
                :min="0"
                :max="9999"
                :precision="2"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="排放标准" prop="emissionStandard">
              <el-select v-model="ruleForm.emissionStandard" placeholder="请选择排放标准" clearable filterable>
                <el-option
                  v-for="(item, index) in emissionStandardOptions"
                  :key="index"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="24">
            <el-form-item label="车架号" prop="vin">
              <el-input
                class="w400"
                v-model="ruleForm.vin"
                placeholder="请输入车架号"
                :maxlength="50"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="24">
            <el-form-item label="发动机号" prop="engineNumber">
              <el-input
                class="w400"
                v-model="ruleForm.engineNumber"
                placeholder="请输入发动机号"
                :maxlength="50"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="装载体积(m³)" prop="loadVolume">
              <el-input-number v-model="ruleForm.loadVolume" :min="0" :max="9999" :precision="2"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="装载重量(Kg)" prop="loadCapacity">
              <el-input-number v-model="ruleForm.loadCapacity" :min="0" :max="9999" :precision="2"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="购买日期" prop="purchaseDate">
              <el-date-picker
                v-model="ruleForm.purchaseDate"
                type="date"
                placeholder="请选择购买日期"
                align="right"
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="所属部门" prop="departmentId">
              <el-cascader
                v-model="ruleForm.departmentId"
                placeholder="请选择所属部门"
                filterable
                clearable
                :options="deptOptions"
                :props="deptProps"
                :show-all-levels="false"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <template v-if="this.recordId">
            <el-col :md="24" :lg="12">
              <el-form-item label="车辆状态" prop="status">
                <el-select v-model="ruleForm.status" placeholder="请选择车辆状态" clearable filterable>
                  <el-option
                    v-for="(item, index) in CAR_STATUS.slice(0, 2)"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="工作状态" prop="workingStatus">
                <el-select v-model="ruleForm.workingStatus" placeholder="请选择工作状态" clearable filterable>
                  <el-option
                    v-for="(item, index) in WORKING_STATUS"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </template>
          <el-col>
            <el-form-item label="营运性质" prop="operationalNature">
              <el-select v-model="ruleForm.operationalNature" placeholder="请选择营运性质" clearable filterable>
                <el-option
                  v-for="(item, index) in OPERATIONAL_NATURE"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="最近年审日期" prop="recentAnnualReviewTime">
              <el-date-picker
                v-model="ruleForm.recentAnnualReviewTime"
                type="date"
                placeholder="请选择最近年审日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="下次年审日期" prop="nextAnnualReviewTime">
              <el-date-picker
                v-model="ruleForm.nextAnnualReviewTime"
                type="date"
                placeholder="请选择下次年审日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="最近保养日期" prop="recentMaintenanceTime">
              <el-date-picker
                v-model="ruleForm.recentMaintenanceTime"
                type="date"
                placeholder="请选择最近保养日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="下次保养日期" prop="nextMaintenanceTime">
              <el-date-picker
                v-model="ruleForm.nextMaintenanceTime"
                type="date"
                placeholder="请选择下次保养日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="最近交强险开始日期" prop="lastMotcStartTime">
              <el-date-picker
                class="w300"
                v-model="ruleForm.lastMotcStartTime"
                type="date"
                placeholder="请选择最近交强险开始日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="交强险终保日期" prop="lastMotcEndTime">
              <el-date-picker
                class="w300"
                v-model="ruleForm.lastMotcEndTime"
                type="date"
                placeholder="请选择交强险终保日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="最近商业险开始日期" prop="lastCommercialStartTime">
              <el-date-picker
                class="w300"
                v-model="ruleForm.lastCommercialStartTime"
                type="date"
                placeholder="请选择最近商业险开始日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="商业险终保日期" prop="lastCommercialEndTime">
              <el-date-picker
                class="w300"
                v-model="ruleForm.lastCommercialEndTime"
                type="date"
                placeholder="请选择商业险终保日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <!-- 行驶证 -->
          <el-col>
            <el-form-item label="车辆行驶证编号" prop="drivingCertNumber">
              <el-input
                class="w400"
                v-model="ruleForm.drivingCertNumber"
                placeholder="请输入车辆行驶证编号"
                :maxlength="50"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆行驶证有效期开始日期" prop="drivingCertBeginDate">
              <el-date-picker
                v-model="ruleForm.drivingCertBeginDate"
                class="w300"
                type="date"
                placeholder="请选择车辆行驶证有效期开始日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆行驶证有效期结束日期" prop="drivingCertEndDate">
              <el-date-picker
                v-model="ruleForm.drivingCertEndDate"
                class="w300"
                type="date"
                placeholder="请选择车辆行驶证有效期结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="车辆行驶证登记日期" prop="drivingCertRegDate">
              <el-date-picker
                v-model="ruleForm.drivingCertRegDate"
                class="w300"
                type="date"
                placeholder="请选择车辆行驶证登记日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="两客一危设备ID号" prop="deviceId">
              <el-input
                class="w400"
                v-model="ruleForm.deviceId"
                placeholder="请输入两客一危设备ID号"
                :maxlength="50"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="两客一危设备SIM卡号" prop="deviceSimNumber">
              <el-input
                class="w400"
                v-model="ruleForm.deviceSimNumber"
                placeholder="请输入两客一危设备SIM卡号"
                :maxlength="50"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="尾门设备ID号" prop="sternDoorDeviceId">
              <el-input
                class="w400"
                v-model="ruleForm.sternDoorDeviceId"
                placeholder="请输入尾门设备ID号"
                :maxlength="50"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="尾门设备SIM卡号" prop="sternDoorDeviceSimNumber">
              <el-input
                class="w400"
                v-model="ruleForm.sternDoorDeviceSimNumber"
                placeholder="请输入尾门设备SIM卡号"
                :maxlength="50"
                show-word-limit
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆行驶证件主页照片" prop="drivingCertPhotoFront">
              <FileUpload
                @uploadChange="handleUploadChange($event, 'drivingCertPhotoFront')"
                :imageList="photoImageList['drivingCertPhotoFront']"
              >
                <template #tips><el-tag type="warning">请上传行驶证件主页照片</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆行驶证件副页照片" prop="drivingCertPhotoBack">
              <FileUpload
                @uploadChange="handleUploadChange($event, 'drivingCertPhotoBack')"
                :imageList="photoImageList['drivingCertPhotoBack']"
              >
                <template #tips><el-tag type="warning">请上传行驶证件副页照片</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
          <template v-if="ruleForm.operationalNature === 0">
            <el-col :md="24" :lg="12">
              <el-form-item label="最近二级维护日期" prop="lastSecMaintTime">
                <el-date-picker
                  v-model="ruleForm.lastSecMaintTime"
                  class="w300"
                  type="date"
                  placeholder="请选择最近二级维护日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="下次二级维护日期" prop="nextSecMaintTime">
                <el-date-picker
                  v-model="ruleForm.nextSecMaintTime"
                  class="w300"
                  type="date"
                  placeholder="请选择下次二级维护日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="最近承运险开始日期" prop="lastTransInsuranceTime">
                <el-date-picker
                  v-model="ruleForm.lastTransInsuranceTime"
                  class="w300"
                  type="date"
                  placeholder="请选择最近承运险开始日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="承运险终保日期" prop="finalTransInsuranceTime">
                <el-date-picker
                  v-model="ruleForm.finalTransInsuranceTime"
                  class="w300"
                  type="date"
                  placeholder="请选择承运险终保日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <!-- 营运证 -->
            <el-col>
              <el-form-item label="营运证编号" prop="operationCertNumber">
                <el-input
                  class="w400"
                  v-model="ruleForm.operationCertNumber"
                  placeholder="请输入营运证编号"
                  :maxlength="50"
                  show-word-limit
                  clearable
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="营运证有效期开始日期" prop="operationCertBeginDate">
                <el-date-picker
                  v-model="ruleForm.operationCertBeginDate"
                  class="w300"
                  type="date"
                  placeholder="请选择营运证有效期开始日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="营运证有效期结束日期" prop="operationCertEndDate">
                <el-date-picker
                  v-model="ruleForm.operationCertEndDate"
                  class="w300"
                  type="date"
                  placeholder="请选择营运证有效期结束日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="营运证登记日期" prop="operationCertRegDate">
                <el-date-picker
                  v-model="ruleForm.operationCertRegDate"
                  class="w300"
                  type="date"
                  placeholder="请选择营运证登记日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="营运证证件主页照片" prop="operationCertPhotoFront">
                <FileUpload
                  @uploadChange="handleUploadChange($event, 'operationCertPhotoFront')"
                  :imageList="photoImageList['operationCertPhotoFront']"
                >
                  <template #tips><el-tag type="warning">请上传营运证证件主页照片</el-tag></template>
                </FileUpload>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="营运证证件副页照片" prop="operationCertPhotoBack">
                <FileUpload
                  @uploadChange="handleUploadChange($event, 'operationCertPhotoBack')"
                  :imageList="photoImageList['operationCertPhotoBack']"
                >
                  <template #tips><el-tag type="warning">请上传营运证证件副页照片</el-tag></template>
                </FileUpload>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import FileUpload from "@/components/FileUpload";
  import { CERT_TYPES, OPERATIONAL_NATURE, WORKING_STATUS, CAR_STATUS, VEHICLE_ENERGY_TYPE } from "@/enums";
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  export default {
    components: { FileUpload },
    props: {
      recordId: {
        type: String,
        default: "",
      },
      deptOptions: {
        type: Array,
        default: () => [],
      },
      vehicleModelOptions: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        pickerOptions: {
          disabledDate: (time) => time.getTime() > new Date(new Date().setHours(23, 59, 59)),
        },
        OPERATIONAL_NATURE, //运营状态
        WORKING_STATUS, //工作状态
        CAR_STATUS, //车辆状态
        VEHICLE_ENERGY_TYPE, //车辆能源类型
        ruleForm: {
          plateNumber: null, //车牌号
          departmentId: null, //所属部门id
          vehicleModel: null, //车辆型号id
          brandModel: "", //品牌型号
          emissionStandard: null, //排放标准id
          operationalNature: null, //营运性质
          grossWeight: null, //总质量
          vehicleEnergyType: null, //能源类型
          purchaseDate: null, //购买时间
          workingStatus: null, //工作状态
          status: null, //车辆状态
          recentAnnualReviewTime: null, //最近年审时间
          nextAnnualReviewTime: null, //下次年审时间
          recentMaintenanceTime: null, //最近保养时间
          nextMaintenanceTime: null, //下次保养时间
          lastMotcStartTime: "", //最近交强险投保时间
          lastMotcEndTime: "", //交强险终保时间
          lastCommercialStartTime: "", //最近商业险投保时间
          lastCommercialEndTime: "", //商业险终保时间
          vin: null, //车架号
          loadCapacity: null, //装载重量
          engineNumber: null, //发动机号
          loadVolume: null, //装载体积
          refuelType: null, //加油类型
          fuelTankCapacity: null, //油箱容量
          // 营运证
          operationCertNumber: null, //车辆营运证编号
          operationCertPhotoFront: null, //车辆营运证照片【主页】
          operationCertPhotoBack: null, //车辆营运证照片【反面】
          operationCertBeginDate: null, //车辆营运证有效期开始时间
          operationCertEndDate: null, //车辆营运证有效期结束时间
          operationCertRegDate: null, //车辆营运证登记日期
          // 行驶证
          drivingCertNumber: null, //车辆行驶证编号
          drivingCertPhotoFront: null, //车辆行驶证照片【主页】
          drivingCertPhotoBack: null, //车辆行驶证照片【反面】
          drivingCertBeginDate: null, //车辆行驶证有效期开始时间
          drivingCertEndDate: null, //车辆行驶证有效期结束时间
          drivingCertRegDate: null, //车辆行驶证登记日期
          lastSecMaintTime: "", //最近二级维护时间
          nextSecMaintTime: "", //下次二级维护时间
          lastTransInsuranceTime: "", //最近承运险投保时间
          finalTransInsuranceTime: "", //承运险终保时间
          deviceId: "", //两客一危设备ID号
          deviceSimNumber: "", //两客一危设备SIM卡号
          sternDoorDeviceId: "", //尾门设备ID号
          sternDoorDeviceSimNumber: "", //尾门设备SIM卡号
        },
        rules: {
          plateNumber: [{ required: true, message: "请输入车牌号", trigger: "blur" }],
          grossWeight: [{ required: true, message: "请输入总质量", trigger: "blur" }],
          vehicleModel: [{ required: true, message: "请选择车辆型号", trigger: "change" }],
          emissionStandard: [{ required: true, message: "请选择排放标准", trigger: "change" }],
          purchaseDate: [{ required: true, message: "请选择购买日期", trigger: "change" }],
          workingStatus: [{ required: true, message: "请选择工作状态", trigger: "change" }],
          status: [{ required: true, message: "请选择车辆状态", trigger: "change" }],
          vehicleEnergyType: [{ required: true, message: "请选择能源类型", trigger: "change" }],
          operationalNature: [{ required: true, message: "请选择营运性质", trigger: "change" }],
          recentAnnualReviewTime: [{ required: true, message: "请选择最近年审日期", trigger: "change" }],
          nextAnnualReviewTime: [{ required: true, message: "请选择下次年审日期", trigger: "change" }],
          recentMaintenanceTime: [{ required: true, message: "请选择最近保养日期", trigger: "change" }],
          lastMotcStartTime: [{ required: true, message: "请选择最近交强险开始日期", trigger: "change" }],
          lastMotcEndTime: [{ required: true, message: "请选择交强险终保日期", trigger: "change" }],
          lastCommercialStartTime: [{ required: true, message: "请选择最近商业险开始日期", trigger: "change" }],
          lastCommercialEndTime: [{ required: true, message: "请选择商业险终保日期", trigger: "change" }],
          vin: [{ required: true, message: "请输入车架号", trigger: "blur" }],
          loadCapacity: [{ required: true, message: "请输入装载重量", trigger: "blur" }],
          engineNumber: [{ required: true, message: "请输入发动机号", trigger: "blur" }],
          fuelTankCapacity: [{ required: true, message: "请选择油箱容量", trigger: "change" }],

          operationCertNumber: [{ required: true, message: "请输入营运证编号", trigger: "blur" }],
          operationCertBeginDate: [{ required: true, message: "请选择营运证有效期开始日期", trigger: "change" }],
          operationCertEndDate: [{ required: true, message: "请选择营运证有效期结束日期", trigger: "change" }],
          operationCertRegDate: [{ required: true, message: "请选择营运证登记日期", trigger: "change" }],
          drivingCertNumber: [{ required: true, message: "请输入车辆行驶证编号", trigger: "blur" }],
          drivingCertBeginDate: [{ required: true, message: "请选择车辆行驶证有效期开始日期", trigger: "change" }],
          drivingCertEndDate: [{ required: true, message: "请选择车辆行驶证有效期结束日期", trigger: "change" }],
          drivingCertRegDate: [{ required: true, message: "请选择车辆行驶证登记日期", trigger: "change" }],
          lastSecMaintTime: [{ required: true, message: "请选择最近二级维护日期", trigger: "change" }],
          nextSecMaintTime: [{ required: true, message: "请选择下次二级维护日期", trigger: "change" }],
          lastTransInsuranceTime: [{ required: true, message: "请选择最近承运险开始日期", trigger: "change" }],
          finalTransInsuranceTime: [{ required: true, message: "请选择承运险终保日期", trigger: "change" }],
        },
        photoImageList: {
          operationCertPhotoFront: [],
          operationCertPhotoBack: [],
          drivingCertPhotoFront: [],
          drivingCertPhotoBack: [],
        },
        CERT_TYPES,
        refuelTypeOptions: [], //加油类型
        emissionStandardOptions: [], //排放标准
        apis: {
          create: "/api/vehicle/dossier/create",
          update: "/api/vehicle/dossier/update",
          inofo: "/api/vehicle/dossier/get/",
          refuelType: "/api/dict/refuelType/list", //加油类型
          emissionStandard: "/api/dict/emissionStandard/list", //排放标准
        },
        saveRecordThrottling: () => {},
        loading: false,
        deptProps: {
          checkStrictly: true,
          value: "id",
          label: "name",
          emitPath: false,
        },
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptionsList();
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      handleUploadChange(fileList, field) {
        if (fileList.length && fileList[0].file) {
          this.ruleForm[field] = {
            name: fileList[0].name,
            size: fileList[0].size,
            ...fileList[0].file,
          };
        } else {
          this.ruleForm[field] = null;
        }
      },
      // 获取用户列表
      async getOptionsList() {
        let promiseList = [getInfoApiFun("", this.apis.refuelType), getInfoApiFun("", this.apis.emissionStandard)];
        try {
          let res = await Promise.all(promiseList);
          this.refuelTypeOptions = res[0].data;
          this.emissionStandardOptions = res[1].data;
        } catch (error) {
          console.warn(error);
        }
      },
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.inofo);
        if (res.success) {
          this.ruleForm = res.data;
          this.handleBackImage();
        }
      },
      handleBackImage() {
        for (const key in this.photoImageList) {
          if (Object.hasOwnProperty.call(this.photoImageList, key)) {
            const item = this.ruleForm[key];
            if (this.ruleForm[key]) {
              this.photoImageList[key] = [
                {
                  url: item.url,
                  file: item,
                },
              ];
            }
          }
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = this.recordId
                ? await updateApiFun(this.ruleForm, this.apis.update)
                : await createApiFun(this.ruleForm, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              console.warn(error);
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
  }
  ::v-deep .el-transfer-panel__body {
    height: 400px;
  }
  .w400 {
    width: 400px;
  }
  .w300 {
    width: 300px;
  }
</style>
