<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title="发送收运单统计数据短信"
      :visible.sync="dialogVisible"
      width="420px"
      top="0"
      destroy-on-close
      @open="initData"
    >
      <div v-loading="loading">
        <el-form class="form-box" :model="ruleForm" ref="ruleForm" :rules="rules" label-width="80px">
          <el-form-item label="统计类型" prop="type">
            <el-radio-group v-model="ruleForm.type">
              <el-radio :label="0">按日</el-radio>
              <el-radio :label="1">按周</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveThrottling">确定</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      smsTemplateId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        originalForm: {
          type: "",
        },
        ruleForm: {},
        rules: {
          type: [{ required: true, message: "请选择统计类型", trigger: "change" }],
        },
        saveThrottling: () => {},
        loading: false,
        apis: {
          send: "/api/waybill/statistics/sendSms",
        },
      };
    },
    created() {
      this.saveThrottling = this.$throttling(this.saveRecord, 500);
    },
    methods: {
      async initData() {
        this.ruleForm = Object.assign({}, this.originalForm);
        this.$nextTick(() => {
          this.$refs.ruleForm.clearValidate();
        });
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun(
                {
                  ...this.ruleForm,
                  smsTemplateId: this.smsTemplateId,
                },
                this.apis.send,
              );
              if (res.success) {
                this.$message.success("发送收运单统计数据短信成功");
                this.dialogVisible = false;
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .form-box {
    padding: 20px 0 10px 0;
  }
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  ::v-deep .el-dialog {
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
  }
  ::v-deep .el-dialog__body {
    padding: 0px 20px 20px 20px;
  }
</style>
