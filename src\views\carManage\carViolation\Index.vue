<template>
  <div class="micro-app-sctmp_base full-height">
    <defaultPage>
      <record
        v-if="showRecord"
        :recordId="recordId"
        :positionOptions="positionOptions"
        :roleOptions="roleOptions"
        :userOptions="userOptions"
        @closeRecord="closeRecord"
        @refreshList="initData"
      ></record>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="keyword" placeholder="请输入车牌号/司机姓名" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
            <el-dropdown
              size="small"
              trigger="click"
              @command="handleDataImport"
              class="import-dropdown-button"
              placement="bottom-start"
            >
              <el-button size="small" type="primary">
                <span class="sctmp-iconfont icon-ic_daoru"></span>
                <span class="ml-5">导入</span>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">
                  <div class="has-padding-sm has-border-bottom">导入基础信息</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button size="small" type="primary" @click="handleExport">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
            :colNum="12"
          ></InterviewChannel>
          <el-col :span="12">
            <el-form-item label="发生日期范围">
              <el-date-picker
                v-model="filterForm.occurrenceTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理状态">
              <el-select v-model="filterForm.status" placeholder="请选择处理状态" clearable filterable>
                <el-option v-for="(item, index) in STATUS_LIST" :key="index" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车辆损坏程度">
              <el-select v-model="filterForm.degree" placeholder="请选择车辆损坏程度" clearable filterable>
                <el-option v-for="(item, index) in DEGREE_LIST" :key="index" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column prop="plateNumber" label="车牌号" align="center"> </el-table-column>
            <el-table-column prop="driverName" label="司机" align="center"></el-table-column>
            <el-table-column prop="occurrenceTime" label="发生时间" align="center"></el-table-column>
            <el-table-column prop="location" label="发生地点" align="center"> </el-table-column>
            <el-table-column prop="description" label="事故描述" align="center" min-width="300">
              <template #default="{ row }">
                <el-tooltip effect="dark" :content="row.description" placement="top" :open-delay="100">
                  <div class="line-clamp line-clamp-3">{{ row.description }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="deductionPoint" label="扣分" align="center"></el-table-column>
            <el-table-column prop="penaltyAmount" label="罚款金额(元)" align="center"></el-table-column>
            <el-table-column prop="status" label="处理状态" align="center">
              <template #default="{ row }">
                {{ row.status ? "已处理" : "未处理" }}
              </template>
            </el-table-column>
            <el-table-column prop="degree" label="车辆损坏程度" align="center">
              <template #default="{ row }">
                {{ ["轻微", "一般", "严重", "特别严重", "无"][row.degree] }}
              </template>
            </el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row)">编辑</el-link>
                <el-popconfirm title="确认删除该记录？" class="mlr-12" @confirm="deleteRecord(row)">
                  <el-link type="danger" slot="reference">删除</el-link>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
    <importDialog
      :value.sync="importDialogShow"
      title="车辆违章事故记录"
      :importApi="apis.import"
      :templateApi="apis.template"
      :importDialogType="importDialogType"
      :isTemplateApi="true"
      @importSuccess="searchFilter"
    ></importDialog>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import { STATUS_LIST, DEGREE_LIST } from "@/enums";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, deleteApiFun, getListApiFun, BASE_API_URL } from "@/api/base";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import record from "./components/record.vue";
  import importDialog from "@/components/importDialog";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
      importDialog,
    },
    data() {
      return {
        filterForm: {},
        positionOptions: [],
        deptOptions: [],
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          userList: "/api/baseuser/list",
          listPage: "/api/vehicle/illegal/listPage",
          positionList: "/api/dict/position/list",
          export: "/api/vehicle/illegal/export",
          delete: "/api/vehicle/illegal/delete/",
          import: "/api/vehicle/illegal/import",
          template: "/api/vehicle/illegal/template",
        },
        STATUS_LIST,
        DEGREE_LIST,
        showRecord: false,
        recordId: "",
        showRoleEdit: false,
        roleOptions: [], //角色列表
        options: [], //用户列表
        userOptions: [], //用户列表
        lgUnionId: "",
        showFilter: false,
        keyword: "",
        channelRecord: {},
        importDialogShow: false,
        importDialogType: "",
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
      this.getUserList();
    },
    methods: {
      // 获取用户列表
      async getUserList() {
        let res = await getListApiFun({ userIdentity: 3 }, this.apis.userList);
        if (res.success) {
          this.userOptions = res.data.map((item) => {
            return {
              ...item,
              phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
              idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
            };
          });
        }
      },
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          occurrenceBeginDate: this.filterForm.occurrenceTime ? this.filterForm.occurrenceTime[0] : "",
          occurrenceEndDate: this.filterForm.occurrenceTime ? this.filterForm.occurrenceTime[1] : "",
          status: this.filterForm.status,
          degree: this.filterForm.degree,
          channelId: this.filterForm.channelId || "",
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showRecord = true;
        this.recordId = "";
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.recordId = row.id;
      },
      //删除
      async deleteRecord(row) {
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      closeRecord() {
        this.showRecord = false;
      },
      // 导入
      handleDataImport(command) {
        this.importDialogType = command;
        this.importDialogShow = true;
      },
      //导出
      async handleExport() {
        try {
          let res = await exportFile(BASE_API_URL + this.apis.export, {});
          if (res.success) {
            createDownloadEvent(`车辆违章记录${Date.now()}.xlsx`, [res.data]);
            window.ELEMENT.Message.success("导出成功");
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
