<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">新收运任务待处理</div>
      <el-form :model="ruleForm" :rules="rules" label-width="180px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="上报时间" required>
              <el-date-picker
                :value="taskInfo.createTime"
                type="datetime"
                placeholder="请选择上报时间"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                readonly
                :disabled="recordItem.taskStatus == 1"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="截止日期" required>
              <el-date-picker
                :value="taskInfo.deadline"
                type="date"
                placeholder="请选择截止日期"
                clearable
                value-format="yyyy-MM-dd"
                readonly
                :disabled="recordItem.taskStatus == 1"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="期望收运时间">
              <el-date-picker
                :value="expectTime"
                type="datetime"
                placeholder="选择日期时间"
                format="yyyy-MM-dd HH:mm"
                readonly
                :disabled="recordItem.taskStatus == 1"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="废物总量（kg）" required>
              <el-input-number
                :value="baseForm.rubbishTotal"
                :precision="2"
                :controls="false"
                disabled
              ></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <baseTitle title="业务调整信息"></baseTitle>
      <baseTitle title="车辆信息"></baseTitle>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
        <el-row>
          <el-col>
            <el-form-item label="收运车辆" prop="defaultVehiclePlateNumber">
              <el-select
                v-model="ruleForm.defaultVehiclePlateNumber"
                placeholder="请选择收运车辆"
                clearable
                filterable
                @change="changePlateNumber"
                :disabled="recordItem.taskStatus == 1"
              >
                <el-option
                  v-for="item in carOptions"
                  :key="item.id"
                  :label="item.plateNumber + (item.isFree === 1 ? '' : '（空闲）')"
                  :value="item.plateNumber"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="人员信息"></baseTitle>
        <el-row>
          <el-col :md="4" :lg="4">
            <el-form-item label="司机" prop="defaultDriverDossierId">
              <el-select
                v-model="ruleForm.defaultDriverDossierId"
                placeholder="请选择司机"
                clearable
                filterable
                @change="driverAndWorkerChange($event, 'defaultDriverDossierPhone', driverOptions)"
                :disabled="recordItem.taskStatus == 1"
              >
                <el-option
                  v-for="item in driverOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8">
            <el-form-item label="" prop="defaultDriverDossierPhone">
              <el-input
                v-model="ruleForm.defaultDriverDossierPhone"
                placeholder="请输入司机联系方式"
                clearable
                :maxlength="11"
                show-word-limit
                :disabled="recordItem.taskStatus == 1"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="4" :lg="4">
            <el-form-item label="押运工1" prop="supercargoDossierOneId">
              <el-select
                v-model="ruleForm.supercargoDossierOneId"
                placeholder="请选择押运工1"
                clearable
                filterable
                @change="driverAndWorkerChange($event, 'supercargoDossierOnePhone', shipWorkerOptions)"
                :disabled="recordItem.taskStatus == 1"
              >
                <el-option
                  v-for="item in shipWorkerOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8">
            <el-form-item label="" prop="supercargoDossierOnePhone">
              <el-input
                v-model="ruleForm.supercargoDossierOnePhone"
                placeholder="请输入押运工联系方式"
                clearable
                :maxlength="11"
                show-word-limit
                :disabled="recordItem.taskStatus == 1"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="4" :lg="4">
            <el-form-item label="押运工2" prop="supercargoDossierTwoId">
              <el-select
                v-model="ruleForm.supercargoDossierTwoId"
                placeholder="请选择押运工2"
                clearable
                filterable
                @change="driverAndWorkerChange($event, 'supercargoDossierTwoPhone', shipWorkerOptions)"
                :disabled="recordItem.taskStatus == 1"
              >
                <el-option
                  v-for="item in shipWorkerOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="8" :lg="8">
            <el-form-item label="" prop="supercargoDossierTwoPhone">
              <el-input
                v-model="ruleForm.supercargoDossierTwoPhone"
                placeholder="请输入押运工联系方式"
                clearable
                :maxlength="11"
                show-word-limit
                :disabled="recordItem.taskStatus == 1"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="收运任务"></baseTitle>
        <el-table :data="ruleForm.tableData" :header-cell-style="{ background: '#F5F7F9' }" border class="table-box">
          <el-table-column label="收运任务" align="center">
            <el-table-column prop="code" label="点位编号" align="center"></el-table-column>
            <el-table-column prop="productionUnit" label="产废单位名称" align="center"></el-table-column>
            <el-table-column prop="productionUnitOperator" label="产废单位经办人" align="center"></el-table-column>
            <el-table-column prop="detailType" label="任务类型" align="center">
              <template #default="{ row, $index }">
                <el-form-item label="" :prop="`tableData.${$index}.detailType`" :rules="tableRules.detailType">
                  <el-select v-model="row.detailType" placeholder="请选择任务类型" filterable clearable>
                    <el-option
                      v-for="(item, index) in POINT_TASK_TYPE"
                      :key="index"
                      :label="item"
                      :value="index"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>
      </el-form>
      <baseTitle title="备注信息"></baseTitle>
      <el-input
        v-model="ruleForm.memo"
        placeholder="请输入备注信息"
        clearable
        type="textarea"
        rows="10"
        maxlength="500"
        show-word-limit
        :disabled="recordItem.taskStatus == 1"
      ></el-input>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
      <el-button type="primary" @click="saveRecordThrottling" v-if="recordItem.taskStatus == 0">完成</el-button>
    </div>
  </div>
</template>

<script>
  import { createApiFun, getListApiFun, getInfoApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import { ERROR_STATUS, CONTINUECARRYING_STATUS, POINT_TASK_TYPE } from "@/enums";
  export default {
    props: {
      recordItem: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      baseTitle,
    },
    data() {
      return {
        baseForm: {}, //业务基础信息
        ruleForm: {
          defaultVehiclePlateNumber: "", //收运车辆
          defaultDriverDossierId: "", //司机
          defaultDriverDossierPhone: "", //司机联系方式
          supercargoDossierOneId: "", //押运工1
          supercargoDossierOnePhone: "", //押运工联系方式
          supercargoDossierTwoId: "", //押运工2
          supercargoDossierTwoPhone: "", //押运工联系方式
          memo: "",
          tableData: [],
        },
        rules: {
          defaultVehiclePlateNumber: [{ required: true, message: "请选择收运车辆", trigger: "change" }],
          defaultDriverDossierId: [{ required: true, message: "请选择司机", trigger: "change" }],
          defaultDriverDossierPhone: [{ required: true, message: "请输入司机联系方式", trigger: "blur" }],
        },
        tableRules: {
          detailType: [{ required: true, message: "请选择任务类型", trigger: "change" }],
        },
        apis: {
          info: "/api/waybill/waybillDetail/listPage",
          carList: "/api/vehicle/dossier/getFreeList",
          userList: "/api/baseuser/list",
          driverAndWorkerInfo: "/api/baseuser/getInfoByPlateNumberAndUserIdentity",
          create: "/api/waybill/commissionTemporaryUpdate",
          get: "/api/waybill/get/",
          taskInfo: "/api/task/getByBusinessId",
        },
        saveRecordThrottling: () => {},
        loading: false,
        carOptions: [],
        driverOptions: [],
        shipWorkerOptions: [],
        ERROR_STATUS,
        CONTINUECARRYING_STATUS,
        POINT_TASK_TYPE,
        expectTime: "",
        taskInfo: {},
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    async mounted() {
      if (this.recordItem && this.recordItem.businessId) {
        await this.getOptions();
        this.getRecord();
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getListApiFun({ userIdentity: 4 }, this.apis.userList),
          getInfoApiFun(this.recordItem.businessId, this.apis.get),
          createApiFun(
            { businessId: this.recordItem.businessId, code: 21, status: this.recordItem.taskStatus == 1 ? 1 : 0 },
            this.apis.taskInfo,
          ),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipWorkerOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.expectTime = res[3].data.expectTime;
        this.taskInfo = res[4].data;
        if (this.recordItem.taskStatus == 1) {
          this.ruleForm = res[3].data;
        }
      },
      // 获取详情
      async getRecord() {
        let res = await createApiFun({ pageNo: 1, pageSize: 1, waybillId: this.recordItem.businessId }, this.apis.info);
        if (res.success) {
          this.ruleForm.tableData = res.data.datas;
          if (this.recordItem.taskStatus == 0) {
            this.ruleForm.tableData.forEach((list) => {
              list.detailType = "";
            });
          }
          this.baseForm = res.data.datas[0];
          this.ruleForm.defaultDriverDossierPhone = this.ruleForm.defaultDriverDossierPhone
            ? this.$sm2Decrypt(this.ruleForm.defaultDriverDossierPhone)
            : "";
          this.ruleForm.supercargoDossierOnePhone = this.ruleForm.supercargoDossierOnePhone
            ? this.$sm2Decrypt(this.ruleForm.supercargoDossierOnePhone)
            : "";
          this.ruleForm.supercargoDossierTwoPhone = this.ruleForm.supercargoDossierTwoPhone
            ? this.$sm2Decrypt(this.ruleForm.supercargoDossierTwoPhone)
            : "";
        }
      },
      // 选择默认车牌号事件回调
      async changePlateNumber(value) {
        if (value) {
          let promiseList = [
            createApiFun({ userIdentity: "3", plateNumber: value }, this.apis.driverAndWorkerInfo),
            createApiFun({ userIdentity: "4", plateNumber: value }, this.apis.driverAndWorkerInfo),
          ];
          let res = await Promise.all(promiseList);
          let driverInfo = res[0].data;
          let workerInfo = res[1].data;
          if (driverInfo) {
            this.ruleForm.defaultDriverDossierId = driverInfo.lgUnionId;
            this.ruleForm.defaultDriverDossierPhone = this.$sm2Decrypt(driverInfo.phone);
          } else {
            this.ruleForm.defaultDriverDossierId = "";
            this.ruleForm.defaultDriverDossierPhone = "";
          }
          if (workerInfo) {
            this.ruleForm.supercargoDossierOneId = workerInfo.lgUnionId;
            this.ruleForm.supercargoDossierOnePhone = workerInfo.phone ? this.$sm2Decrypt(workerInfo.phone) : "";
          } else {
            this.ruleForm.supercargoDossierOneId = "";
            this.ruleForm.supercargoDossierOnePhone = "";
          }
        }
      },
      // 驾驶司机选择事件回调
      driverAndWorkerChange(value, field, options) {
        if (!value) {
          this.ruleForm[field] = "";
          return;
        }
        let filterItem = options.filter((o) => o.lgUnionId === value)[0];
        this.ruleForm[field] = filterItem.phone;
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("returnIndex");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun(
                { ...this.ruleForm, id: this.recordItem.businessId, detailType: this.ruleForm.tableData[0].detailType },
                this.apis.create,
              );
              if (res.success) {
                this.$message.success(`操作成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-box {
    margin-bottom: 20px;
  }
  ::v-deep .table-box .el-form-item__content {
    margin-left: 0 !important;
  }
  ::v-deep .table-box .el-form-item {
    margin-bottom: 0;
  }
</style>
