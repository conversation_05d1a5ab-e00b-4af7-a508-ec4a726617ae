<template>
  <div
    style="
      background-color: #fff;
      padding: 10px;
      margin-bottom: 8px;
      display: flex;
      flex: 94;
      justify-content: space-around;
      height: 94px;
      align-items: center;
    "
  >
    <template>
      <!-- summaryData.alarmNum -->
      <NumberItem
        :url="alarmNumUrl"
        text="告警通知数量"
        :number="0"
        @click.native="$router.push('/sctmp_base/warningEvent')"
      ></NumberItem>
      <!-- summaryData.todoNum -->
      <!-- summaryData.expiredNum -->
      <NumberItem
        :url="todoNumUrl"
        text="待办事项数量"
        beside="超时未处理数"
        :besideValue="0"
        :number="0"
        @click.native="$router.push('/sctmp_base/backlog')"
      >
      </NumberItem>
      <!-- summaryData.vehicleAbnormalNum -->
      <NumberItem
        :url="vehicleAbnormalNumUrl"
        text="今日车辆异常上报数量"
        :number="0"
        @click.native="$router.push('/sctmp_base/errorManager')"
      ></NumberItem>
      <!-- summaryData.overdueNum -->
      <NumberItem
        :url="overdueNumUrl"
        text="今日超时未收运点位数量"
        :number="0"
        @click.native="$router.push('/sctmp_base/pointTaskDetail')"
      ></NumberItem>
    </template>
  </div>
</template>
<script>
  import NumberItem from "./NumberItem.vue";
  export default {
    components: { NumberItem },
    props: ["summaryData"],
    data() {
      return {
        alarmNumUrl: require("../images/ic_alarmNum.png"),
        todoNumUrl: require("../images/ic_todoNum.png"),
        vehicleAbnormalNumUrl: require("../images/ic_vehicleAbnormalNum.png"),
        overdueNumUrl: require("../images/ic_overdueNum.png"),
      };
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {},
    watch: {},
  };
</script>
<style scoped lang="scss"></style>
