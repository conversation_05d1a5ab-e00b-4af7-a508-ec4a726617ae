<template>
  <div v-loading="!summaryData" :key="refreshKey" class="default-page-container">
    <div class="flex-top-wrapper">
      <div class="flex-top-left-wrapper">
        <NumberTop :summaryData="summaryData"></NumberTop>
        <ChartsLeft :summaryData="summaryData"></ChartsLeft>
      </div>
      <ChartsRight
        :summaryData="summaryData"
        :channelList="channelList"
        :channel="channel"
        :value.sync="channelId"
        @toggleChannel="toggleChannel"
      ></ChartsRight>
    </div>
    <div class="flex-bottom-wrapper">
      <SafeModule :summaryData="summaryData"></SafeModule>
      <FollowUpModule :summaryData="summaryData"></FollowUpModule>
    </div>
  </div>
</template>
<script>
  import { getInfoApiFunByParams, getInfoApiFun } from "@/api/base.js";
  import NumberTop from "./components/NumberTop.vue";
  import ChartsLeft from "./components/ChartsLeft.vue";
  import ChartsRight from "./components/ChartsRight.vue";
  import SafeModule from "./components/SafeModule.vue";
  import FollowUpModule from "./components/FollowUpModule.vue";
  import { getLocalStorage } from "@/utils/storage";
  import { INTERVIEW_CHANNEL } from "@/utils/commonValue"; //访问渠道
  export default {
    components: { NumberTop, ChartsLeft, ChartsRight, SafeModule, FollowUpModule },
    props: {},
    data() {
      return {
        apis: {
          summaryFollowUp: "/api/task/summaryFollowUp",
          summaryRubbish: "/api/task/summaryRubbish",
          summarySafety: "/api/task/summarySafety",
          summaryPoint: "/api/task/summaryPoint",
          summaryStatistic: "/api/task/summaryStatistic",
          summaryVehicle: "/api/task/summaryVehicle",
        },
        refreshKey: 0,
        summaryData: null,
        timer: null, // 在组件的data中声明timer
        channelId: "",
        channelList: [],
        channel: "",
      };
    },
    computed: {},
    async created() {
      this.channel = getLocalStorage(INTERVIEW_CHANNEL);
      if (!this.channel) {
        await this.getChannelList();
      }
      this.getData();
    },
    mounted() {
      this.$nextTick(() => {
        window.addEventListener("resize", this.handleResize);
      });
    },
    methods: {
      // 获取渠道列表
      async getChannelList() {
        let res = await getInfoApiFun(this.$DNACONFIG.dna, "/api/base/dna/listByDna/");
        this.channelList = this.channelList.concat(res.data);
        this.channelId = this.channelList[0].id;
      },
      // 切换渠道
      toggleChannel() {
        this.summaryData = null;
        this.getData();
      },
      handleResize() {
        if (this.timer !== null) {
          clearTimeout(this.timer);
        }

        this.timer = setTimeout(() => {
          this.refreshKey++;
          // console.log("窗口大小已变化");
          // 这里可以执行更多操作，比如更新组件数据等
          // 但要注意，如果组件已经销毁，这些操作可能不会被执行或导致错误
        }, 1000);
      },
      getData() {
        // 遍历apis对象，并为每个API发起请求
        Object.keys(this.apis).forEach((key) => {
          this.fetchApiData(this.apis[key], key);
        });
      },

      // 获取单个API的数据
      async fetchApiData(apiUrl) {
        try {
          let res = await getInfoApiFunByParams({ channelId: this.channelId }, apiUrl);
          if (res.success) {
            // 创建一个新对象，确保响应式
            this.summaryData = { ...(this.summaryData || {}), ...res.data };
          }
        } catch (error) {
          console.log(error);
        }
      },
    },
    beforeDestroy() {
      // 组件销毁前清除监听器和定时器
      window.removeEventListener("resize", this.handleResize);

      // 清除定时器（在这个例子中可能不是必需的，但展示如何操作）
      if (this.timer !== null) {
        clearTimeout(this.timer);
        this.timer = null; // 重置为null是个好习惯
      }
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .default-page-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #f7faf9;
    padding: 16px;
  }
  .flex-top-wrapper {
    height: 642px;
    flex: auto;
    background-color: #f7faf9;
    margin-bottom: 16px;
    display: flex;
  }
  .flex-top-left-wrapper {
    margin-right: 8px;
    // 设计比例左 1254px 右 512px
    flex: auto;
    width: 1254px;
    background-color: #f7faf9;
    display: flex;
    flex-direction: column;
  }
  .flex-bottom-wrapper {
    height: 340px;
    flex: auto;
    display: flex;
    background-color: #f7faf9;
  }
</style>
