<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title="视频监控"
      :visible.sync="dialogVisible"
      width="80%"
      top="0"
      destroy-on-close
      @open="postData"
      :before-close="closeRecord"
    >
      <div class="video-container" v-loading="loading">
        <ul class="video-list">
          <li class="video-item">
            <video class="video-box" ref="flvPlayer1" autoplay muted></video>
            <div v-loading="loading1" class="videoSrc-bg" :class="{ fail: loadingEnd1 }">
              <div v-if="unSupported1">当前浏览器不支持，请更换其他浏览器</div>
              <div class="loading-box" v-else-if="loadingEnd1">
                <span>加载失败</span>
                <span class="loading-button" @click="reloadVideo(1)">重新加载</span>
              </div>
            </div>
          </li>
          <li class="video-item">
            <video class="video-box" ref="flvPlayer2" autoplay muted></video>
            <div v-loading="loading2" class="videoSrc-bg" :class="{ fail: loadingEnd2 }">
              <div v-if="unSupported2">当前浏览器不支持，请更换其他浏览器</div>
              <div class="loading-box" v-else-if="loadingEnd2">
                <span>加载失败</span>
                <span class="loading-button" @click="reloadVideo(2)">重新加载</span>
              </div>
            </div>
          </li>
          <li class="video-item">
            <video class="video-box" ref="flvPlayer3" autoplay muted></video>
            <div v-loading="loading3" class="videoSrc-bg" :class="{ fail: loadingEnd3 }">
              <div v-if="unSupported3">当前浏览器不支持，请更换其他浏览器</div>
              <div class="loading-box" v-else-if="loadingEnd3">
                <span>加载失败</span>
                <span class="loading-button" @click="reloadVideo(3)">重新加载</span>
              </div>
            </div>
          </li>
          <li class="video-item">
            <video class="video-box" ref="flvPlayer4" autoplay muted></video>
            <div v-loading="loading4" class="videoSrc-bg" :class="{ fail: loadingEnd4 }">
              <div v-if="unSupported4">当前浏览器不支持，请更换其他浏览器</div>
              <div class="loading-box" v-else-if="loadingEnd4">
                <span>加载失败</span>
                <span class="loading-button" @click="reloadVideo(4)">重新加载</span>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base.js";
  import flvjs from "flv.js";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      plateNumber: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        apis: {
          postData: "/api/monitor/real-time",
        },
        maxTime: 60000,
        videoSrc1: "",
        videoSrc2: "",
        videoSrc3: "",
        videoSrc4: "",
        flvPlayer1: null,
        flvPlayer2: null,
        flvPlayer3: null,
        flvPlayer4: null,
        time1: null,
        time2: null,
        time3: null,
        time4: null,
        currentTime1: 0,
        currentTime2: 0,
        currentTime3: 0,
        currentTime4: 0,
        loading: false,
        loading1: false,
        loading2: false,
        loading3: false,
        loading4: false,
        loadingEnd1: false,
        loadingEnd2: false,
        loadingEnd3: false,
        loadingEnd4: false,
        unSupported1: false,
        unSupported2: false,
        unSupported3: false,
        unSupported4: false,
      };
    },
    methods: {
      initPlayer() {
        if (flvjs.isSupported()) {
          for (let i = 1; i < 5; i++) {
            if (!this[`videoSrc${i}`]) {
              continue;
            }
            this[`loading${i}`] = true;
            this[`loadingEnd${i}`] = false;
            this[`currentTime${i}`] = 0;
            this.loopFlvPlayer(i);
          }
        } else {
          for (let i = 0; i < 5; i++) {
            this[`loading${i}`] = false;
            this[`loadingEnd${i}`] = true;
            this[`unSupported${i}`] = true;
          }
        }
      },
      loopFlvPlayer(index) {
        let videoElement = this.$refs[`flvPlayer${index}`];
        if (!videoElement) {
          console.error("Video element not found");
          return;
        }
        this[`flvPlayer${index}`] = flvjs.createPlayer({
          type: "flv",
          url: this[`videoSrc${index}`],
        });
        this[`flvPlayer${index}`].attachMediaElement(videoElement);
        this[`flvPlayer${index}`].load();
        this[`time${index}`] = setInterval(() => {
          this[`currentTime${index}`] += 100;
          if (this[`flvPlayer${index}`].currentTime > 0) {
            // 假设如果currentTime大于0，视频已经开始播放
            this[`loading${index}`] = false; // 隐藏加载效果
            clearInterval(this[`time${index}`]);
          } else if (this[`currentTime${index}`] > this.maxTime) {
            this[`loading${index}`] = false; // 隐藏加载效果
            this[`loadingEnd${index}`] = true;
            this[`flvPlayer${index}`].pause();
            this[`flvPlayer${index}`].unload();
            clearInterval(this[`time${index}`]);
          }
        }, 100); // 你可以根据需要调整这个时间
        var playPromise = this[`flvPlayer${index}`].play();
        if (playPromise !== undefined) {
          playPromise
            .then((_) => {
              console.log(_, "_");
            })
            .catch((error) => {});
        }
      },
      async postData() {
        if (!this.plateNumber) return;
        let promiseList = [
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 1,
              command: 1,
              streamType: 1,
            },
            this.apis.postData,
          ),
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 2,
              command: 1,
              streamType: 1,
            },
            this.apis.postData,
          ),
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 3,
              command: 1,
              streamType: 1,
            },
            this.apis.postData,
          ),
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 4,
              command: 1,
              streamType: 1,
            },
            this.apis.postData,
          ),
        ];
        try {
          this.loading = true;
          let res = await Promise.all(promiseList);
          this.videoSrc1 = res[0].data;
          this.videoSrc2 = res[1].data;
          this.videoSrc3 = res[2].data;
          this.videoSrc4 = res[3].data;
          this.loading = false;
          this.initPlayer();
        } catch (error) {
          this.loading = false;
          console.log(error);
        }
      },
      async endData() {
        if (!this.plateNumber) return;
        for (let i = 1; i < 5; i++) {
          clearInterval(this[`time${i}`]);
          if (this[`flvPlayer${i}`]) {
            this[`flvPlayer${i}`].destroy();
            this[`flvPlayer${i}`] = null;
          }
        }
        let promiseList = [
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 1,
              command: 0,
              streamType: 1,
            },
            this.apis.postData,
          ),
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 2,
              command: 0,
              streamType: 1,
            },
            this.apis.postData,
          ),
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 3,
              command: 0,
              streamType: 1,
            },
            this.apis.postData,
          ),
          getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: 4,
              command: 0,
              streamType: 1,
            },
            this.apis.postData,
          ),
        ];
        try {
          this.loading = true;
          await Promise.all(promiseList);
          this.loading = false;
          this.loading1 = false;
          this.loading2 = false;
          this.loading3 = false;
          this.loading4 = false;
          this.loadingEnd1 = false;
          this.loadingEnd2 = false;
          this.loadingEnd3 = false;
          this.loadingEnd4 = false;
          this.unSupported1 = false;
          this.unSupported2 = false;
          this.unSupported3 = false;
          this.unSupported4 = false;
        } catch (error) {
          this.loading = false;
          this.loading1 = false;
          this.loading2 = false;
          this.loading3 = false;
          this.loading4 = false;
          this.loadingEnd1 = false;
          this.loadingEnd2 = false;
          this.loadingEnd3 = false;
          this.loadingEnd4 = false;
          this.unSupported1 = false;
          this.unSupported2 = false;
          this.unSupported3 = false;
          this.unSupported4 = false;
          console.log(error);
        }
      },
      async closeRecord(done) {
        await this.endData();
        done();
      },
      // 重新加载视频
      async reloadVideo(i) {
        clearInterval(this[`time${i}`]);
        if (this[`flvPlayer${i}`]) {
          this[`flvPlayer${i}`].destroy();
          this[`flvPlayer${i}`] = null;
        }
        try {
          this.loading = true;
          await getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: i,
              command: 0,
              streamType: 1,
            },
            this.apis.postData,
          );
          let res = await getListPageApiFun(
            {
              plateNumber: this.plateNumber,
              channel: i,
              command: 1,
              streamType: 1,
            },
            this.apis.postData,
          );
          this.loading = false;
          this[`videoSrc${i}`] = res.data;
          if (this[`videoSrc${i}`]) {
            this[`loading${i}`] = true;
            this[`loadingEnd${i}`] = false;
            this[`unSupported${i}`] = false;
            this[`currentTime${i}`] = 0;
            this.loopFlvPlayer(i);
          }
        } catch (error) {
          this.loading = false;
          this[`loading${i}`] = false;
          this[`loadingEnd${i}`] = false;
          this[`unSupported${i}`] = false;
          console.log(error);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .video-container {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    display: flex;
    flex-direction: column;
  }
  .video-list {
    flex: 1;
    overflow: hidden;
    display: grid;
    grid-gap: 20px;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(2, 1fr);
    .video-item {
      width: 100%;
      height: 100%;
      overflow: hidden;
      position: relative;
      .video-box {
        width: 100%;
        height: 100%;
        position: relative;
        z-index: 2;
      }
    }
  }
  .videoSrc-bg {
    display: flex;
    color: #fff;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    &.fail {
      z-index: 4;
    }
    .loading-box {
      display: flex;
      align-items: center;
      .loading-button {
        margin-left: 4px;
        color: #f56c6c;
        cursor: pointer;
        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
