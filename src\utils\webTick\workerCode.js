export default workerCode;

export function workerCode() {
  // eslint-disable-next-line no-undef
  const { self } = globalThis;

  self.onmessage = ({ data }) => {
    // 拿到主线程传递的uuid,action,和timeout
    const { uuid, id, action, timeout } = data;

    const timerFunctions = {
      setTimeout() {
        const timerId = setTimeout(() => {
          self.postMessage({ uuid, finish: true });
        }, timeout);
        // 先通知主线程，拿到定时器timerId
        self.postMessage({ uuid, id: timerId });
      },
      setInterval() {
        const timerId = setInterval(() => {
          self.postMessage({ uuid, finish: true });
        }, timeout);

        self.postMessage({ uuid, id: timerId });
      },
      clearTimeout() {
        clearTimeout(id);
      },
      clearInterval() {
        clearInterval(id);
      },
    };

    if (timerFunctions[action]) {
      timerFunctions[action]();
    }
  };
}
