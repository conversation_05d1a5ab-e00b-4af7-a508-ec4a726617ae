<template>
  <div class="backlog">
    <main-title title="当月车辆监督抽查异常"></main-title>
    <div class="num-flex-wrapper mr-top-36">
      <img class="img" src="../images/num.png" alt="" />
      <div class="num-wrapper">
        <div class="num">{{ num }}</div>
        <div class="beside">次</div>
      </div>
    </div>
  </div>
</template>

<script>
  import MainTitle from "./MainTitle.vue";

  export default {
    components: {
      MainTitle,
    },
    props: ["num"],

    data() {
      return {};
    },
    async created() {},
    methods: {},
  };
</script>

<style lang="scss" scoped>
  .num {
    font-weight: bold;
    line-height: 48px;
    font-size: 32px;
    color: #fff;
  }
  .beside {
    margin-left: 8px;
    margin-top: 18px;
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 4px;
    color: #fff;
  }
  .num-wrapper {
    width: 480px;
    height: 200px;
    // background-color: #fff;
    left: 0px;
    top: 15px;
    color: #fff;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .backlog {
    width: 480px;
    position: absolute;
    top: 128px;
    left: 1400px;
    z-index: 1;
  }
  .img {
    width: 230px;
    margin: auto;
  }
  .mr-top-36 {
    margin-top: 36px;
  }
  .mr-top-56 {
    margin-top: 56px;
  }
  .num-flex-wrapper {
    position: relative;
    width: 480px;

    display: flex;
    flex-wrap: wrap;
    /* 其他样式 */
  }
</style>
