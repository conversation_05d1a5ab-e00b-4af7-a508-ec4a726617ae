<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">
        <div>加班审批详情</div>
        <el-tag class="card-header-tag" :type="overTimeApprovalStatusType[recordForm.verifyStatus]" effect="dark">
          {{ OVERTIME_APPROVAL_STATUS[recordForm.verifyStatus] }}
        </el-tag>
      </div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="180px" label-suffix="：">
        <el-row v-if="recordItem">
          <el-col :md="24" :lg="12">
            <el-form-item label="上报时间" required>
              <el-date-picker
                :value="recordItem.createTime"
                type="datetime"
                placeholder="请选择上报时间"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="截止日期" required>
              <el-date-picker
                :value="recordItem.deadline"
                type="date"
                placeholder="请选择截止日期"
                clearable
                value-format="yyyy-MM-dd"
                readonly
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="基础信息"></baseTitle>
        <el-row>
          <el-col>
            <el-form-item label="申请编号">{{ recordForm.applyNumber }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="提交时间">{{ recordForm.applyDate }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆信息">{{ recordForm.defaultVehiclePlateNumber }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="加班类型">
              <span v-for="(item, index) in recordForm?.type?.split(',')" :key="index">
                <span>{{ POINT_TYPE[item] }}</span>
                <span v-if="index != recordForm?.type?.split(',').length - 1">,</span>
              </span>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="提交人">{{ recordForm.submitUserName }}</el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="收运点数">{{ recordForm.pointNumber }}</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="4">
            <el-form-item label="司机姓名">{{ recordForm.defaultDriverDossierName }}</el-form-item>
          </el-col>
          <template v-if="recordForm.defaultDriverDossierId">
            <el-col :md="4">
              <el-form-item label="" label-width="0">
                <el-checkbox-group
                  v-model="ruleObj[recordForm.defaultDriverDossierId].rules"
                  :disabled="recordForm.verifyStatus != 0"
                  :min="1"
                >
                  <el-checkbox :label="0">加班绩效规则</el-checkbox>
                  <el-checkbox :label="1">常规绩效规则</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :md="4">
              <el-form-item label="" label-width="0">
                <el-radio-group
                  v-model="ruleObj[recordForm.defaultDriverDossierId].useShift"
                  :disabled="recordForm.verifyStatus != 0"
                >
                  <el-radio :label="0.5">0.5班次</el-radio>
                  <el-radio :label="1">1班次</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
        <el-row>
          <el-col :md="4">
            <el-form-item label="押运工1">{{ recordForm.supercargoDossierOneName }}</el-form-item>
          </el-col>
          <template v-if="recordForm.supercargoDossierOneId">
            <el-col :md="4">
              <el-form-item label="" label-width="0">
                <el-checkbox-group
                  v-model="ruleObj[recordForm.supercargoDossierOneId].rules"
                  :disabled="recordForm.verifyStatus != 0"
                  :min="1"
                >
                  <el-checkbox :label="0">加班绩效规则</el-checkbox>
                  <el-checkbox :label="1">常规绩效规则</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :md="4">
              <el-form-item label="" label-width="0">
                <el-radio-group
                  v-model="ruleObj[recordForm.supercargoDossierOneId].useShift"
                  :disabled="recordForm.verifyStatus != 0"
                >
                  <el-radio :label="0.5">0.5班次</el-radio>
                  <el-radio :label="1">1班次</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
        <el-row>
          <el-col :md="4">
            <el-form-item label="押运工2">{{ recordForm.supercargoDossierTwoName }}</el-form-item>
          </el-col>
          <template v-if="recordForm.supercargoDossierTwoId">
            <el-col :md="4">
              <el-form-item label="" label-width="0">
                <el-checkbox-group
                  v-model="ruleObj[recordForm.supercargoDossierTwoId].rules"
                  :disabled="recordForm.verifyStatus != 0"
                  :min="1"
                >
                  <el-checkbox :label="0">加班绩效规则</el-checkbox>
                  <el-checkbox :label="1">常规绩效规则</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :md="4">
              <el-form-item label="" label-width="0">
                <el-radio-group
                  v-model="ruleObj[recordForm.supercargoDossierTwoId].useShift"
                  :disabled="recordForm.verifyStatus != 0"
                >
                  <el-radio :label="0.5">0.5班次</el-radio>
                  <el-radio :label="1">1班次</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
        <baseTitle title="收运点位信息"></baseTitle>
        <el-table class="mb-10" :data="recordForm.pointList" :header-cell-style="{ background: '#F5F7F9' }" border>
          <el-table-column label="点位名称" prop="productionUnit" align="center"></el-table-column>
          <el-table-column label="点位编号" prop="code" align="center"></el-table-column>
          <el-table-column label="点位类型" prop="type" align="center">
            <template #default="{ row }">{{ POINT_TYPE[row.type] }}</template>
          </el-table-column>
          <el-table-column label="收运方式" prop="baggingMethod" align="center">
            <template #default="{ row }">{{ POINT_RECEIVING_METHOD[row.baggingMethod] }}</template>
          </el-table-column>
          <el-table-column label="收运重量" prop="rubbishTotal" align="center"></el-table-column>
          <el-table-column label="图片" align="center">
            <template #default="{ row }">
              <el-badge
                v-if="row.picture && row.picture.length > 0"
                :value="row.picture.length > 1 ? row.picture.length : ''"
                class="picture-badge"
                type="success"
              >
                <el-image
                  class="picture-img"
                  fit="cover"
                  :src="row.picture[0].url"
                  :preview-src-list="row.picture.map((i) => i.url)"
                  v-if="row.picture"
                ></el-image>
              </el-badge>
            </template>
          </el-table-column>
        </el-table>
        <baseTitle title="审批信息"></baseTitle>
        <el-form-item label-width="0" prop="opinion" v-if="recordForm.verifyStatus == 0">
          <div class="opinion-box">
            <el-input
              class="opinion-left"
              v-model="ruleForm.opinion"
              placeholder="请输入审批意见，例如'同意申请，准许加班收运'或'驳回申请，理由：点位信息不完整'"
              clearable
              type="textarea"
              rows="6"
              maxlength="500"
              show-word-limit
            ></el-input>
            <div class="opinion-right">
              <div>
                <el-button type="primary" @click="saveRecordThrottling(1)" size="large">通过</el-button>
              </div>
              <div>
                <el-button type="danger" @click="saveRecordThrottling(2)" size="large">驳回</el-button>
              </div>
            </div>
          </div>
        </el-form-item>
        <el-input
          class="opinion-left"
          v-model="recordForm.opinion"
          placeholder="请输入审批意见，例如'同意申请，准许加班收运'或'驳回申请，理由：点位信息不完整'"
          clearable
          type="textarea"
          rows="6"
          maxlength="500"
          show-word-limit
          readonly
          v-else
        ></el-input>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
    </div>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import { POINT_TYPE, OVERTIME_APPROVAL_STATUS, POINT_RECEIVING_METHOD } from "@/enums";
  export default {
    props: {
      recordItem: {
        type: [String, Object],
        default: "",
      },
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      baseTitle,
    },
    data() {
      return {
        ruleForm: {
          opinion: "", //审批意见
        },
        rules: {
          opinion: [{ required: true, message: "请填写审批意见", trigger: "blur" }],
        },
        recordForm: {},
        apis: {
          info: "/api/overtime/info/",
          verify: "/api/overtime/verify",
        },
        saveRecordThrottling: () => {},
        loading: false,
        POINT_TYPE,
        OVERTIME_APPROVAL_STATUS,
        overTimeApprovalStatusType: ["warning", "success", "danger"],
        POINT_RECEIVING_METHOD,
        ruleObj: {},
      };
    },
    async created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      async getRecord() {
        let res = await getInfoApiFun(this.recordItem ? this.recordItem.businessId : this.recordId, this.apis.info);
        if (res.success) {
          this.recordForm = res.data;
          if (this.recordForm.verifyStatus == 0) {
            if (this.recordForm.defaultDriverDossierId) {
              this.$set(this.ruleObj, this.recordForm.defaultDriverDossierId, {
                rules: [0],
                useShift: 1,
              });
            }
            if (this.recordForm.supercargoDossierOneId) {
              this.$set(this.ruleObj, this.recordForm.supercargoDossierOneId, {
                rules: [0],
                useShift: 1,
              });
            }
            if (this.recordForm.supercargoDossierTwoId) {
              this.$set(this.ruleObj, this.recordForm.supercargoDossierTwoId, {
                rules: [0],
                useShift: 1,
              });
            }
          } else {
            if (this.recordForm.ruleList.length > 0) {
              this.recordForm.ruleList.forEach((item) => {
                this.$set(this.ruleObj, item.lgUnionId, {
                  rules: JSON.parse(item.rules),
                  useShift: item.useShift,
                });
              });
            }
          }
        }
      },
      // 关闭弹窗
      closeRecord() {
        if (this.recordItem) {
          this.$emit("returnIndex");
        } else {
          this.$emit("closeRecord");
        }
      },
      // 保存
      saveRecord(type) {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            let ruleList = [];
            Object.keys(this.ruleObj).forEach((item) => {
              ruleList.push({
                lgUnionId: item,
                rules: this.ruleObj[item].rules,
                useShift: this.ruleObj[item].useShift,
              });
            });
            this.loading = true;
            try {
              let res = await createApiFun(
                {
                  id: this.recordItem ? this.recordItem.businessId : this.recordId,
                  verifyStatus: type,
                  opinion: this.ruleForm.opinion.trim(),
                  ruleList,
                },
                this.apis.verify,
              );
              if (res.success) {
                this.$message.success(`审批已${type == 1 ? "通过" : "驳回"}`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
    position: relative;
    .card-header-tag {
      position: absolute;
      right: 0;
      top: 0;
    }
  }
  .w250 {
    width: 250px;
  }
  .table-box {
    margin-bottom: 20px;
  }
  .full-width {
    width: 100%;
  }
  .opinion-box {
    display: flex;
    .opinion-left {
      flex: 1;
      overflow: hidden;
    }
    .opinion-right {
      padding-left: 20px;
      align-self: stretch;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }
  }
  .picture-img {
    width: 60px;
    height: 60px;
  }
  ::v-deep .task-form-item .el-form-item__content {
    margin-left: 0 !important;
  }
</style>
