<template>
  <div class="management">
    <main-title title="今日异常上报一览"></main-title>
    <vue-seamless-scroll class="seaml-scroll" :data="listData" :class-option="seamlessScrollOption">
      <ul class="data-list">
        <li class="data-item" v-for="(item, index) in listData" :key="index">
          <div class="title">{{ formatTime(item.time) }}</div>
          <div class="title">{{ item.areaName }}</div>
          <div class="title">{{ item.plateNumber }}</div>
          <div class="username">{{ item.fullName }}</div>
        </li>
      </ul>
    </vue-seamless-scroll>
  </div>
</template>

<script>
  import MainTitle from "./MainTitle.vue";
  import moment from "moment";
  export default {
    components: {
      MainTitle,
    },
    props: ["vehicleAbnormalDay"],
    watch: {
      vehicleAbnormalDay: {
        handler(newV) {
          this.listData = newV;
        },
        immediate: true,
        deep: true,
      },
    },
    data() {
      return {
        listData: [],
        seamlessScrollOption: {
          step: 2, // 数值越大速度滚动越快
          limitMoveNum: 6, // 开始无缝滚动的数据量 this.dataList.length
          hoverStop: true, // 是否开启鼠标悬停stop
          direction: 1, // 0向下 1向上 2向左 3向右
          openWatch: true, // 开启数据实时监控刷新dom
          singleHeight: 50, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
          singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
          waitTime: 2000, // 单步运动停止的时间(默认值1000ms)
        },
      };
    },
    methods: {
      formatTime(time) {
        return moment(time).format("MM-DD HH:mm");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .management {
    width: 480px;
    position: absolute;
    top: 640px;
    right: 40px;
    z-index: 1;
  }

  .seaml-scroll {
    height: 350px;
    overflow: hidden;
    width: 100%;
    font-size: 16px;
    line-height: 22px;
    font-weight: 400;
    color: #ffffff;
    margin-top: 10px;
    .data-list {
      .data-item {
        display: flex;
        padding: 9px 16px;
        margin-bottom: 10px;
        background: linear-gradient(270deg, rgba(93, 255, 203, 0) 0%, rgba(93, 255, 203, 0.1) 100%);
        .title {
          display: -webkit-box;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
        }
        .username {
          flex: 1;
          text-align: right;
          color: #fdae3f;
          display: -webkit-box;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
        }
      }
    }
  }
</style>
