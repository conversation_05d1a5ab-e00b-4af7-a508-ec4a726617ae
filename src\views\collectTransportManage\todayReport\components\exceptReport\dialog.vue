<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      width="70%"
      top="0"
      destroy-on-close
      :before-close="closeRecord"
      :show-close="false"
      @open="initData"
    >
      <record v-if="showRecord" :recordId="recordId" @closeRecord="closeRecord" @refreshList="getDataList"></record>
      <div class="main-index" v-else>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column prop="exceptionType" label="异常类型" align="center">
              <template #default="{ row }">{{ ERROR_STATUS[row.exceptionType] }}</template>
            </el-table-column>
            <el-table-column prop="reportingTime" label="上报日期" align="center"></el-table-column>
            <el-table-column width="200" prop="continueCarrying" label="是否可以继续收运" align="center">
              <template #default="{ row }">{{ CONTINUECARRYING_STATUS_MAP[row.continueCarrying] }}</template>
            </el-table-column>
            <el-table-column width="180" label="操作" align="center">
              <template #default="{ row }">
                <el-link type="primary" @click="checkRecord(row)">查看详情</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import { ERROR_STATUS, CONTINUECARRYING_STATUS, CONTINUECARRYING_STATUS_MAP } from "@/enums";
  import record from "./record.vue";
  import moment from "moment";
  export default {
    components: {
      record,
    },
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        loading: false,
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/abnormalreporting/listPage",
        },
        showRecord: false,
        recordId: "",
        ERROR_STATUS,
        CONTINUECARRYING_STATUS,
        CONTINUECARRYING_STATUS_MAP,
      };
    },
    methods: {
      initData() {
        this.recordId = "";
        this.showRecord = false;
        this.page = { pageNo: 1, pageSize: 10, total: 0 };
        this.getDataList();
      },
      async getDataList() {
        try {
          let params = {
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            reportingTime: moment().format("YYYY-MM-DD"),
            channelId: this.channelId,
          };
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
        } catch (error) {
          console.log(error, "error");
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.getDataList();
      },
      // 详情
      checkRecord(row) {
        this.showRecord = true;
        this.recordId = row.id;
      },
      closeRecord(done) {
        if (this.showRecord) {
          this.showRecord = false;
          return;
        }
        done();
      },
      // 重置筛选
      resetFilter() {
        this.page.pageNo = 1;
        this.getDataList();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.getDataList();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header-title {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
  }
  .header {
    display: flex;
    align-items: center;
    .header-left {
      flex: 1;
    }
    .header-right {
      margin-top: 20px;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w-300 {
    width: 300px;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
