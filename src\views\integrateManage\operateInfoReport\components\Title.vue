<template>
  <div class="flex-h ptb-20">
    <div class="title-left"></div>
    <div class="title-right">{{ title }}</div>
  </div>
</template>

<script>
  export default {
    props: {
      title: {
        type: String,
        default: "",
      },
    },
  };
</script>

<style lang="scss" scoped>
  .title-left {
    width: 3px;
    height: 36px;
    background-color: #797979;
  }
  .title-right {
    font-size: 20px;
    margin-left: 6px;
    font-weight: bold;
  }
  .ptb-20 {
    padding: 20px 0;
  }
</style>
