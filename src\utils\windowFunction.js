/**
 * @Author: 赵锦俊
 * @Date: 2022-03-08 10:22:45
 * @description: 底座方法建议封装写在这里，大家一起用
 */
import {
  LoginInfoKey,
  UserInfoKey,
  DnaInfoKey,
  RoleInfoKey,
  TOKEN_KEY,
  menusCacheKey,
  PERM_FUNS_KEY,
} from "@/enums/commonValue";
import store from "@/store";
import { getLocalStorage } from "./storage";
/**
 * @description: window.LOGAN 中的方法
 * @param {*}
 * @return {*}
 */
export default {
  install(Vue) {
    /**
     * 获取token
     * @return {string} token
     */
    Vue.prototype.getToken = function () {
      return new Promise((resolve, reject) => {
        let token;
        try {
          token = JSON.parse(localStorage.getItem(LoginInfoKey)) || "";
          if (token !== "" && token !== null && token.accessToken) {
            resolve(token.accessToken);
          } else {
            resolve(null);
          }
        } catch (err) {
          reject(err);
        }
      });
    };

    /**
     * 设置token
     * @param {object} tokenInfo
     * {
     *  accessToken,//token
     *  refreshToken,//刷新token
     *  surplusTime,//有效期
     * }
     * @return {boolean}
     */
    Vue.prototype.setToken = function (tokenInfo) {
      return new Promise((resolve, reject) => {
        try {
          if (tokenInfo && Object.prototype.toString.call(tokenInfo) === "[object Object]") {
            localStorage.setItem(LoginInfoKey, JSON.stringify(tokenInfo));
            localStorage.setItem(TOKEN_KEY, "Bearer " + tokenInfo.accessToken);
            resolve(true);
          } else {
            reject(false);
          }
        } catch (err) {
          console.error(err);
          reject(false);
        }
      });
    };

    /**
     * @description: 获取用户信息
     * @return {object} userInfo
     */
    Vue.prototype.getUserInfo = function () {
      return new Promise((resolve, reject) => {
        try {
          let obj = JSON.parse(localStorage.getItem(UserInfoKey));
          if (obj.avatar) {
            obj.avatar = JSON.parse(obj.avatar);
          }
          resolve(obj);
        } catch (err) {
          reject(err);
        }
      });
    };

    /**
     * @description: 获取dna信息
     * @return {object} dnaInfo
     */
    Vue.prototype.getDna = function () {
      return new Promise((resolve, reject) => {
        try {
          resolve(JSON.parse(localStorage.getItem(DnaInfoKey)));
        } catch (err) {
          reject(err);
        }
      });
    };

    /**
     * @description: 获取角色列表
     * @return {array} roleList
     */
    Vue.prototype.getRoleInfo = function () {
      return new Promise((resolve, reject) => {
        try {
          resolve(JSON.parse(localStorage.getItem(RoleInfoKey)));
        } catch (err) {
          reject(err);
        }
      });
    };

    /**
     * @description: 获取菜单列表
     * @return {array}
     */
    Vue.prototype.getMenus = function () {
      return new Promise((resolve, reject) => {
        try {
          resolve(JSON.parse(localStorage.getItem(menusCacheKey)));
        } catch (err) {
          reject(err);
        }
      });
    };

    /**
     * 退出登录
     */
    Vue.prototype.logout = async function () {
      try {
        await store.dispatch("logout");
        return Promise.resolve();
      } catch (err) {
        console.warn("err", err);
        return Promise.reject(err);
      }
    };

    /*
     * @description: 是否有对应权限
     * @return {array}
     */
    Vue.prototype.getHasFuns = function (fun = "") {
      return new Promise((resolve, reject) => {
        try {
          let hasFun = getLocalStorage(PERM_FUNS_KEY).filter((i) => i.code == fun);
          resolve(hasFun[0]?.url || fun);
        } catch (err) {
          reject(err);
        }
      });
    };
  },
};
