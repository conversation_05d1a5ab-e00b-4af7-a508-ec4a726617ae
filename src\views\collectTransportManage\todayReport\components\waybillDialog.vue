<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      width="70%"
      top="0"
      destroy-on-close
      :before-close="closeRecord"
      :show-close="false"
      @open="resetFilter"
    >
      <record v-if="showRecord" :recordId="recordId" :recordForm="recordForm" @closeRecord="closeRecord"></record>
      <div class="main-index" v-else>
        <header class="header">
          <template v-if="waybillType === 0">
            <div class="header-left">
              <el-input
                class="w400"
                v-model="keyword"
                placeholder="请输入收运单编号/路线编号/路线名称"
                clearable
              ></el-input>
            </div>
            <div class="filter-box">
              <el-badge :value="filterNumber" class="filter-badge">
                <el-button type="primary" @click="showFilter = !showFilter">
                  <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                  筛选
                  <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
                </el-button>
              </el-badge>
            </div>
            <el-button @click="resetFilter">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
          </template>
          <div class="header-right">
            <el-popover class="ml-10" placement="bottom" width="240" trigger="click">
              <div class="reveal-box">
                <div class="reveal-header">
                  <div>展示全部</div>
                  <el-switch
                    :value="allItemChecked"
                    active-text="是"
                    inactive-text="否"
                    @change="changeAllReveal"
                  ></el-switch>
                </div>
                <ul class="reveal-list">
                  <li class="reveal-item" v-for="(item, index) in itemList" :key="index">
                    <el-checkbox :value="item.value" :disabled="index === 0" @change="changeReveal($event, item)">{{
                      item.label
                    }}</el-checkbox>
                  </li>
                </ul>
              </div>
              <el-button slot="reference">
                <div class="btn-inner">
                  <span class="el-icon-s-grid"></span>
                  <span>显示列</span>
                  <span class="el-icon-caret-bottom"></span>
                </div>
              </el-button>
            </el-popover>
          </div>
        </header>
        <FilterContent label-width="160px" v-show="showFilter">
          <el-col :span="12">
            <el-form-item label="默认司机">
              <el-input
                class="w400"
                v-model="filterForm.defaultDriverDossierName"
                placeholder="请输入司机姓名"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="默认车辆车牌号">
              <el-input
                class="w400"
                v-model="filterForm.defaultVehiclePlateNumber"
                placeholder="请输入车辆车牌号"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收运单生效日期范围">
              <el-date-picker
                v-model="filterForm.effectiveDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            height="100%"
            border
            ref="tableRef"
          >
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column
              prop="waybillCode"
              label="收运单编号"
              align="center"
              min-width="100"
              v-if="itemList[0].value"
            ></el-table-column>
            <el-table-column
              prop="effectiveDate"
              label="收运单生效日期"
              align="center"
              min-width="160"
              v-if="itemList[1].value"
            ></el-table-column>
            <el-table-column prop="isTemp" label="临时收运单" align="center" min-width="100" v-if="itemList[2].value">
              <template #default="{ row }">{{ IS_TEMPORARY[row.isTemp] }}</template>
            </el-table-column>
            <el-table-column prop="name" label="路线名称" align="center" v-if="itemList[3].value"></el-table-column>
            <el-table-column
              prop="districtName"
              label="所属区域"
              align="center"
              v-if="itemList[4].value"
            ></el-table-column>
            <el-table-column prop="type" label="路线属性" align="center" min-width="120" v-if="itemList[5].value">
              <template #default="{ row }">{{ ROUTE_PROPERTY[row.type] }}</template>
            </el-table-column>
            <el-table-column
              prop="defaultVehiclePlateNumber"
              label="默认车辆"
              align="center"
              min-width="120"
              v-if="itemList[6].value"
            ></el-table-column>
            <el-table-column
              prop="defaultDriverDossierName"
              label="默认司机"
              align="center"
              v-if="itemList[7].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierOneName"
              label="押运工"
              align="center"
              v-if="itemList[8].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierTwoName"
              label="押运工2"
              align="center"
              v-if="itemList[9].value"
            ></el-table-column>
            <el-table-column prop="waybillType" label="收运方式" align="center" v-if="itemList[10].value">
              <template #default="{ row }">{{ WAYBILL_TYPE[row.waybillType] }}</template>
            </el-table-column>
            <el-table-column
              prop="pointNumber"
              label="点位数量"
              align="center"
              v-if="itemList[11].value"
            ></el-table-column>
            <el-table-column
              prop="completeCount"
              label="已收运点位数"
              align="center"
              v-if="itemList[12].value"
            ></el-table-column>
            <el-table-column
              prop="code"
              label="路线编号"
              align="center"
              min-width="120"
              v-if="itemList[13].value"
            ></el-table-column>
            <el-table-column
              prop="defaultDriverDossierPhone"
              label="司机联系方式"
              align="center"
              min-width="120"
              v-if="itemList[14].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierOnePhone"
              label="押运工联系方式"
              align="center"
              min-width="120"
              v-if="itemList[15].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierTwoPhone"
              label="押运工2联系方式"
              align="center"
              min-width="140"
              v-if="itemList[16].value"
            ></el-table-column>
            <el-table-column
              prop="versionNumber"
              label="路线版本号"
              align="center"
              min-width="100"
              v-if="itemList[17].value"
            >
              <template #default="{ row }">v{{ row.versionNumber.toFixed(1) }}</template>
            </el-table-column>
            <el-table-column
              prop="updateFullname"
              label="最近修改人"
              align="center"
              min-width="140"
              v-if="itemList[18].value"
            ></el-table-column>
            <el-table-column
              prop="updateTime"
              label="最近修改时间"
              align="center"
              min-width="160"
              v-if="itemList[19].value"
            ></el-table-column>
            <el-table-column label="操作" align="center" fixed="right" min-width="200">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row)">详情</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import { IS_TEMPORARY, ROUTE_PROPERTY, WAYBILL_TYPE } from "@/enums";
  import record from "@/views/collectTransportManage/electronicWaybill/components/record.vue";
  import moment from "moment";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      waybillType: {
        type: Number,
        default: 1,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    components: {
      record,
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    data() {
      return {
        filterForm: {},
        IS_TEMPORARY,
        ROUTE_PROPERTY,
        WAYBILL_TYPE,
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/waybill/listPage",
          export: "",
        },
        recordType: 0,
        recordId: "",
        loading: false,
        recordForm: {},
        showFilter: false,
        keyword: "",
        itemList: [
          { label: "收运单编号", value: true },
          { label: "收运单生效日期", value: true },
          { label: "临时收运单", value: true },
          { label: "路线名称", value: true },
          { label: "所属区域", value: true },
          { label: "路线属性", value: true },
          { label: "默认车辆", value: true },
          { label: "默认司机", value: true },
          { label: "押运工", value: true },
          { label: "押运工2", value: true },
          { label: "收运方式", value: true },
          { label: "点位数量", value: true },
          { label: "已收运点位数", value: true },
          { label: "路线编号", value: false },
          { label: "司机联系方式", value: false },
          { label: "押运工联系方式", value: false },
          { label: "押运工2联系方式", value: false },
          { label: "路线版本号", value: false },
          { label: "最近修改人", value: false },
          { label: "最近修改时间", value: false },
        ],
        allItemChecked: false,
        showRecord: false,
      };
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          issueStatus: 1,
          keyword: this.keyword,
          defaultDriverDossierName: this.filterForm.defaultDriverDossierName,
          defaultVehiclePlateNumber: this.filterForm.defaultVehiclePlateNumber,
          effectiveBeginTime: moment().format("YYYY-MM-DD"),
          effectiveEndTime: moment().format("YYYY-MM-DD"),
          channelId: this.channelId,
        };
        if (this.waybillType === 1) {
          params.isNew = true;
        } else {
          params.temp = true;
        }
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas.map((item) => {
            return {
              ...item,
              defaultDriverDossierPhone: item.defaultDriverDossierPhone
                ? this.$sm2Decrypt(item.defaultDriverDossierPhone)
                : "",
              supercargoDossierOnePhone: item.supercargoDossierOnePhone
                ? this.$sm2Decrypt(item.supercargoDossierOnePhone)
                : "",
              supercargoDossierTwoPhone: item.supercargoDossierTwoPhone
                ? this.$sm2Decrypt(item.supercargoDossierTwoPhone)
                : "",
            };
          });
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 修改所有显示列
      changeAllReveal(value) {
        this.allItemChecked = value;
        this.itemList.slice(1, this.itemList.length).forEach((item) => (item.value = value));
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 列点击事件
      changeReveal(value, item) {
        item.value = value;
        let uncheckList = this.itemList.slice(1, this.itemList.length).filter((list) => !list.value);
        if (uncheckList.length === 0) {
          this.allItemChecked = true;
        } else {
          this.allItemChecked = false;
        }
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 查看详情
      editRecord(row) {
        this.recordId = row.id;
        this.recordForm = row;
        this.showRecord = true;
      },
      closeRecord(done) {
        if (this.showRecord) {
          this.showRecord = false;
          return;
        }
        done();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w400 {
    width: 400px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
