export default {
  name: "websocket",
  data() {
    return {
      socket: null,
      socketOpen: false, // 标识是否已经打开了websocket通道
      socketMsgQueue: [], // 用于存放websocket关闭状态下的消息发送队列
      websocket_url_cp: "",
      tryCount: 3, // 重连次数
      tryInterval: 3000, // 重连间隔
      canReconnect: true, // 是否可以重连
      canSendHeartbeat: true, // 是否开启心跳包--因为正常需要后端配合响应心跳，先关闭吧
      heartbeatTimer: null, // 心跳定时器
      heartbeatInterval: 1000 * 30, // 心跳间隔--30秒
    };
  },

  mounted() {},

  methods: {
    // 初始化连接
    initWebSocket(url) {
      // 保存地址
      this.websocket_url_cp = url || this.websocket_url_cp;
      this.socket = new WebSocket(this.websocket_url_cp);
      //连接成功
      this.socket.onopen = this.setOnopenMessage;
      //收到消息的回调
      this.socket.onmessage = this.onMessage;
      //连接关闭的回调
      this.socket.onclose = this.onClose;
      //连接错误
      this.socket.onerror = this.onErrorMessage;
      //监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
      window.onbeforeunload = this.onbeforeunload;
    },

    //连接成功
    setOnopenMessage() {
      console.log("socket---连接成功....");
      this.socketOpen = true;
      this.tryCount = 3;
      // 开始心跳
      this.startHeartbeat();
      for (let i = 0; i < this.socketMsgQueue.length; i++) {
        this.sendSocketMessage(this.socketMsgQueue[i]);
      }
      this.socketMsgQueue = [];
    },

    //发送消息
    sendSocketMessage(msg) {
      // 判断，如果已经链接成功，正常允许，才能发送消息
      if (this.socketOpen) {
        this.socket.send(JSON.stringify(msg));
      } else {
        this.socketMsgQueue.push(msg);
      }
    },

    // 收到消息
    onMessage(msg) {
      this.resetHeartbeat();
      this.setOnmessageMessage(msg);
    },

    // socket的关闭，可能浏览器意外断开或者特别情况断开的保险
    onClose() {
      console.log("socket关闭--->close");
      this.stopHeartbeat();
      // 有重连次数并允许重连情况下才可以重连
      if (this.tryCount > 0 && this.canReconnect) {
        this.reconnect();
      } else {
        this.setOncloseMessage();
      }
    },

    // 连接关闭--手动调用关闭，关闭不会自动重连
    setOncloseMessage() {
      this.socketOpen = false;
      this.canReconnect = false;
      this.stopHeartbeat();
      if (this.socket) {
        this.socket.close();
        this.socket = null;
        this.tryCount = 3;
      }
    },

    //监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接。
    onbeforeunload() {
      this.setOncloseMessage();
    },

    // 失败
    onErrorMessage() {
      console.log("webSocket---->连接失败----");
      this.stopHeartbeat();
    },

    // 重连
    reconnect() {
      console.log("socket---触发重连....");
      setTimeout(() => {
        this.initWebSocket();
        this.tryCount--;
      }, this.tryInterval);
    },

    // 设置是否允许重连
    setCanReconnect(bool) {
      this.canReconnect = !!bool;
    },

    // 开始心跳检测
    startHeartbeat() {
      if (!this.canSendHeartbeat) {
        return;
      }
      this.heartbeatTimer = setInterval(() => {
        if (this.socketOpen) {
          // 发送心跳消息
          this.sendSocketMessage("ping");
        }
      }, this.heartbeatInterval);
    },

    // 重置心跳检测
    resetHeartbeat() {
      clearInterval(this.heartbeatTimer);
      this.startHeartbeat();
    },

    stopHeartbeat() {
      clearInterval(this.heartbeatTimer);
    },
  },
};
