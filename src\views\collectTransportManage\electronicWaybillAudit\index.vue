<template>
  <div class="micro-app-sctmp_base full-height">
    <defaultPage @closeRecord="closeRecord">
      <record
        v-if="showRecord"
        :effectiveDate="effectiveDate"
        :channelId="filterForm.channelId"
        @closeRecord="closeRecord"
      ></record>
      <div class="main-index" v-else>
        <header class="header">
          <InterviewChannel :value.sync="filterForm.channelId" :record.sync="channelRecord"></InterviewChannel>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
        </header>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" width="55" align="center"></el-table-column>
            <el-table-column prop="effectiveDate" label="生效日期" align="center"></el-table-column>
            <el-table-column prop="num" label="任务总量" align="center"></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="{ row }">
                <el-popconfirm title="确认是否批量下发电子收运单？" @confirm="batchDelivery(row)">
                  <el-link class="mr-10" type="primary" slot="reference">批量下发</el-link>
                </el-popconfirm>
                <el-link type="primary" @click="editRecord(row)">详情</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import record from "./components/record";
  import { createApiFun } from "@/api/base";
  export default {
    components: {
      defaultPage,
      record,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          list: "/api/waybill/statisticsEffectiveDateList",
          batch: "/api/waybill/issueWaybill",
        },
        showRecord: false,
        effectiveDate: "",
        loading: false,
        importDialogShow: false,
        importDialogType: "",
        channelRecord: {},
      };
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let res = await createApiFun({ channelId: this.filterForm.channelId || "" }, this.apis.list);
        if (res.success) {
          this.tableData = res.data;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      closeRecord() {
        this.showRecord = false;
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 批量下发
      async batchDelivery(row) {
        try {
          let res = await createApiFun({ effectiveDate: row.effectiveDate }, this.apis.batch);
          if (res.success) {
            this.$message.success("批量下发成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 详情
      editRecord(row) {
        this.showRecord = true;
        this.effectiveDate = row.effectiveDate;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  ::v-deep .header-left .el-form-item {
    margin-bottom: 0;
    margin-top: 20px;
  }
</style>
