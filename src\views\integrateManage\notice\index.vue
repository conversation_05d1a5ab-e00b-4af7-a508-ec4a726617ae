<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <record v-if="showRecord" :recordId="recordId" @closeRecord="closeRecord" @refreshList="initData"></record>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="keyword" placeholder="请输入新闻名称" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <el-col :span="12">
            <el-form-item label="通知类型">
              <el-select v-model="filterForm.noticeType" placeholder="请选择通知类型" clearable filterable>
                <el-option v-for="(item, index) in NOTICE_TYPE" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="置顶状态">
              <el-select v-model="filterForm.top" placeholder="请选择置顶状态" clearable filterable>
                <el-option
                  v-for="(item, index) in PINNED_STATUS"
                  :key="index"
                  :label="item.name"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="新闻发布日期范围">
              <el-date-picker
                v-model="filterForm.createDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column prop="title" label="新闻名称" align="center"> </el-table-column>
            <el-table-column prop="noticeType" label="通知类型" align="center">
              <template #default="{ row }">{{ NOTICE_TYPE[row.noticeType] }}</template>
            </el-table-column>
            <el-table-column prop="createTime" label="新闻发布时间" align="center"> </el-table-column>
            <el-table-column prop="top" label="置顶状态" align="center">
              <template #default="{ row }">{{ row.top ? "置顶" : "否" }}</template>
            </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <el-popconfirm :title="`确认${row.top ? '取消置顶' : '置顶'}当前新闻？`" @confirm="pinnedRecord(row)">
                  <el-link type="primary" class="mr-10" slot="reference">{{ row.top ? "取消置顶" : "置顶" }}</el-link>
                </el-popconfirm>
                <el-link class="mr-10" type="primary" @click="editRecord(row)">编辑</el-link>
                <el-popconfirm title="确认删除当前新闻？" @confirm="deleteRecord(row)">
                  <el-link type="danger" slot="reference">删除</el-link>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, deleteApiFun, createApiFun } from "@/api/base";
  import record from "./components/record.vue";
  import { PINNED_STATUS, NOTICE_TYPE } from "@/enums";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
    },
    data() {
      return {
        PINNED_STATUS,
        NOTICE_TYPE,
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/notice/listPage",
          delete: "/api/notice/delete/",
          pinned: "/api/notice/top",
        },
        showRecord: false,
        recordId: "",
        loading: false,
        showFilter: false,
        keyword: "",
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          title: this.keyword,
          top: this.filterForm.top,
          noticeType: this.filterForm.noticeType,
          createBeginDate: this.filterForm.createDate ? this.filterForm.createDate[0] : "",
          createEndDate: this.filterForm.createDate ? this.filterForm.createDate[1] : "",
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showRecord = true;
        this.recordId = "";
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.recordId = row.id;
      },
      closeRecord() {
        this.showRecord = false;
      },
      // 删除
      async deleteRecord(row) {
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            this.page.pageNo = this.pageDropProcess(this.page.pageNo, this.page.pageSize, this.page.total);
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 置顶/取消置顶
      async pinnedRecord(row) {
        try {
          let res = await createApiFun({ id: row.id }, this.apis.pinned);
          if (res.success) {
            window.ELEMENT.Message.success(`${row.top ? "取消置顶" : "置顶"}成功`);
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
