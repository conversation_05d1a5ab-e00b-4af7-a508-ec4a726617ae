<template>
  <div
    class="container"
    :class="{
      'container-type3': changeType == 1 && queryParams.type == 2,
      'dialog-index': showDialog || showPointDialog,
    }"
  >
    <!-- 区域相关 -->
    <template v-if="changeType == 1">
      <MainTitle title="收运情况数据"></MainTitle>
      <SwitchTab :tabList="tabList" @toggleTab="toggleTab" :activeTab="activeTab"></SwitchTab>
      <CollectionChart
        :chartTitle="chartTitle"
        typeName="线路"
        :chartData="chartData"
        chartId="collectChartId"
        class="chart"
        @handleLend="(data) => handleLend(data, 'line')"
        v-if="queryParams.type != 2"
      ></CollectionChart>
      <CollectionChart
        :chartTitle="chartTitle"
        :chartData="chartDataByPoint"
        chartId="collectChartId2"
        class="chart chart2"
        @handleLend="(data) => handleLend(data, 'point')"
      ></CollectionChart>
    </template>

    <!-- 路线相关信息 -->
    <template v-if="changeType == 2">
      <CarPerson :vehicleInfo="vehicleInfo"></CarPerson>
      <MainTitle title="收运情况数据" class="mt-20"></MainTitle>
      <SwitchTab class="switch-tab2" :tabList="tabList" @toggleTab="toggleTab" :activeTab="activeTab"></SwitchTab>

      <!-- 今日收运率情况 -->
      <CollectionChart
        :chartTitle="chartTitle"
        :chartData="chartDataByPoint"
        chartId="collectChartId3"
        class="chart chart2"
        @handleLend="(data) => handleLend(data, 'point')"
      ></CollectionChart>
    </template>

    <!-- 点位信息 -->
    <template v-if="changeType == 3">
      <CarPerson :vehicleInfo="vehicleInfo"></CarPerson>
      <PointDetail :pointData="pointData" v-if="changeType == 3" ref="pointDetailRef"></PointDetail>
      <!-- 今日收运量情况 -->
      <MainTitle title="今日收运量情况"></MainTitle>
    </template>

    <TodayFwStatics
      :showTitle="changeType != 3"
      :class="{ 'mt-30': changeType == 1 }"
      class="chart-"
      :chartTitle="chartTitle"
      :summaryData="rubbishWeightStatisticsData"
    ></TodayFwStatics>

    <CollectionDialog :value.sync="showDialog" :query="dialogQuery" :title="dialogTitle"> </CollectionDialog>
    <PointDialog :value.sync="showPointDialog" :query="dialogQuery" :title="dialogTitle"> </PointDialog>
  </div>
</template>

<script>
  import SwitchTab from "@/components/switchTab";
  import CollectionChart from "./CollectionChart.vue";
  import TodayFwStatics from "./TodayFwStatistics.vue";
  import CollectionDialog from "./RouteDialog";
  import CarPerson from "./CarPerson";
  import PointDetail from "./PointDetail.vue";
  import PointDialog from "./PointDialog.vue";

  import emitter from "@/utils/mitt";
  import MainTitle from "@/components/mainTitle/index.vue";

  import { getTodayWaybillApi, rubbishWeightStatistics, getPathWaybillApi } from "@/api/OperationManagement";
  import { formatTime } from "@/utils/index";
  export default {
    components: {
      MainTitle,
      SwitchTab,
      CollectionChart,
      TodayFwStatics,
      CollectionDialog,
      CarPerson,
      PointDetail,
      PointDialog,
    },
    props: {
      channelId: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        tabList: [
          {
            id: "",
            name: "总情况",
            type: "",
          },
          {
            id: 0,
            name: "大型床位",
            type: 0,
          },
          {
            id: 3,
            name: "小型床位",
            type: 3,
          },
          {
            id: 1,
            name: "小诊所",
            type: 1,
          },
          {
            id: 2,
            name: "智能收集柜",
            type: 2,
          },
        ],

        activeTab: "",

        chartTitle: "",
        // 图表数据
        chartData: {
          percent: 0,
          all: 0,
          seriesData: [
            { value: 0, name: "已收运" },
            { value: 0, name: "待收运" },
          ],
        },
        chartDataByPoint: {
          percent: 0,
          all: 0,
          seriesData: [
            { value: 0, name: "已收运" },
            { value: 0, name: "待收运" },
          ],
        },
        showDialog: false,
        showPointDialog: false,

        // 车辆信息
        vehicleInfo: {},
        // 废物量统计
        rubbishWeightStatisticsData: {},
        // 点位数据
        pointData: {},
        // 查询参数
        queryParams: {
          type: "", // 类型 0-大医院 1-小诊所 2-智能收集柜 3-小型床位
          districtId: "", // 区县id
          pickupPathId: "", // 路线id
          pickupPointId: "", // 点位id
        },

        changeType: 1, // 当前选择的展示类型，1 区域 2.线路 3.点位

        dialogQuery: {
          waybillStatus: null,
          completed: null,
          districtId: null,
          // 点位时间
          effectiveEndBeginTime: null,
          effectiveEndEndTime: null,
          type: "",
          pickupPathId: "",
        },
        dialogTitle: "",
        // socket参数推送
        socketData: [
          {
            propName: "getTodayWaybill",
            params: [null, null, null, null],
          },
          {
            propName: "getTodayRubbishTotalInfo",
            params: [null, null, null, null],
          },
          {
            propName: "getPathWaybill",
            params: [null, null],
          },
        ],
      };
    },
    watch: {
      channelId() {
        this.getTodayWaybill();
        this.getRubbishWeightStatistics();
      },
    },
    created() {
      this.getTodayWaybill();
      this.getRubbishWeightStatistics();
    },

    mounted() {
      // 监听来自 MapCharts 的通知
      emitter.on("changeFromMapCharts", async (data) => {
        this.queryParams.districtId = data.areaId;
        this.queryParams.pickupPathId = data.pickupPathId;
        this.queryParams.pickupPointId = data.pickupPointId;
        this.changeType = data.type;
        // 点位类型
        this.activeTab = data.activeTab;
        this.queryParams.type = data.activeTab;
        this.chartTitle = "";

        // 单纯区域切换
        if (data.type == 1) {
          this.getTodayWaybill();
          this.getRubbishWeightStatistics();
          // this.resetSocketParamsMsg("getPathWaybill");
          // this.sendSocketMsg();
        }

        // 选择路线
        if (data.type == 2) {
          this.vehicleInfo = data;
          this.getPathWaybill();
          this.getRubbishWeightStatistics();
          //this.resetSocketParamsMsg("getTodayWaybill");
          //this.sendSocketMsg();
        }

        // 点位点击
        if (data.type == 3) {
          this.vehicleInfo = data;
          this.pointData = data;
          this.pointData.contactPhone = this.pointData.contactPhone
            ? this.$sm2Decrypt(this.pointData.contactPhone)
            : "";
          this.getRubbishWeightStatistics();
        }
        this.vehicleInfo.defaultDriverDossierPhone = this.$sm2Decrypt(this.vehicleInfo.defaultDriverDossierPhone);
        this.vehicleInfo.supercargoDossierOnePhone = this.vehicleInfo.supercargoDossierOnePhone
          ? this.$sm2Decrypt(this.vehicleInfo.supercargoDossierOnePhone)
          : "";
        this.vehicleInfo.supercargoDossierTwoPhone = this.vehicleInfo.supercargoDossierTwoPhone
          ? this.$sm2Decrypt(this.vehicleInfo.supercargoDossierTwoPhone)
          : "";
        if (this.vehicleInfo?.monitorWaybillDetailPoint) {
          this.vehicleInfo.monitorWaybillDetailPoint.defaultDriverDossierPhone = this.$sm2Decrypt(
            this.vehicleInfo.monitorWaybillDetailPoint.defaultDriverDossierPhone,
          );
          this.vehicleInfo.monitorWaybillDetailPoint.supercargoDossierOnePhone = this.vehicleInfo
            .monitorWaybillDetailPoint.supercargoDossierOnePhone
            ? this.$sm2Decrypt(this.vehicleInfo.monitorWaybillDetailPoint.supercargoDossierOnePhone)
            : "";
          this.vehicleInfo.monitorWaybillDetailPoint.supercargoDossierTwoPhone = this.vehicleInfo
            .monitorWaybillDetailPoint.supercargoDossierTwoPhone
            ? this.$sm2Decrypt(this.vehicleInfo.monitorWaybillDetailPoint.supercargoDossierTwoPhone)
            : "";
        }
      });
    },

    beforeDestroy() {
      emitter.off("changeFromMapCharts");
    },

    methods: {
      // 获取收运图表收运率数据
      async getTodayWaybill() {
        try {
          const res = await getTodayWaybillApi({ ...this.queryParams, channelId: this.channelId });
          this.socketData[0].params = [
            this.queryParams.districtId || null,
            this.queryParams.type === "" ? null : this.queryParams.type,
          ];

          if (res.status == 200) {
            this.waybillFormatter(res.data);
            this.sendSocketMsg("getTodayWaybill");
          }
        } catch (err) {
          console.log("err==>", err);
        }
      },

      // 获取路线收运图表收运率数据
      async getPathWaybill() {
        try {
          const res = await getPathWaybillApi({
            pickupPathId: this.queryParams.pickupPathId || null,
            type: this.queryParams.type === "" ? null : this.queryParams.type,
            districtId: this.queryParams.districtId || null,
          });

          this.socketData[2].params = [this.queryParams.pickupPathId, this.queryParams.type];
          if (res.status == 200) {
            this.waybillFormatter(res.data);
            this.sendSocketMsg("getPathWaybill");
          }
        } catch (err) {
          console.log("err==>", err);
        }
      },

      // 获取废物量统计
      async getRubbishWeightStatistics() {
        try {
          const res = await rubbishWeightStatistics({ ...this.queryParams, channelId: this.channelId });
          this.socketData[1].params = [
            this.queryParams.pickupPathId || null,
            this.queryParams.pickupPointId || null,
            this.queryParams.districtId || null,
            this.queryParams.type === "" ? null : this.queryParams.type,
          ];
          if (res.status == 200) {
            this.rubbishWeightStatisticsData = res.data;
            this.pointData.rubbishTotal = res.data?.total;
            this.$set(
              this.pointData,
              "waybillUserName",
              res.data?.waybillUserName ? res.data?.waybillUserName.split(",") : "",
            );
            this.$set(
              this.pointData,
              "waybillUserPhone",
              res.data?.waybillUserPhone ? res.data?.waybillUserPhone.split(",") : "",
            );
            this.sendSocketMsg("getTodayRubbishTotalInfo");
          }
        } catch (err) {
          console.log("err==>", err);
        }
      },

      // 收运数据格式化
      waybillFormatter(data) {
        // 路线
        this.chartData = {
          percent: data.completePickupRate || 0,
          all: data.pickupPathCount || 0,
          seriesData: [
            { value: data.completePickupCount || 0, name: "已收运", completed: true, waybillStatus: 1 },
            { value: data.noCompletePickupCount || 0, name: "待收运", completed: false, waybillStatus: 0 },
          ],
        };
        // 点位
        this.chartDataByPoint = {
          percent: data.completePickupPointRate || 0,
          all: data.pickupPointCount || 0,
          seriesData: [
            { value: data.completePickupPointCount || 0, name: "已收运", completed: true, waybillStatus: 1 },
            { value: data.noCompletePickupPointCount || 0, name: "待收运", completed: false, waybillStatus: 0 },
          ],
        };
      },

      // 切换类型，更新数据 isOut是否外部调用的
      toggleTab(item, isOut) {
        this.chartTitle = item.type === "" ? "" : item.name;
        this.queryParams.type = item.type;
        this.activeTab = item.type;
        // 区域
        if (this.changeType == 1) {
          this.getTodayWaybill();
          // this.resetSocketParamsMsg("getPathWaybill");
        }
        // 路线
        if (this.changeType == 2) {
          this.getPathWaybill();
          // this.resetSocketParamsMsg("getTodayWaybill");
        }
        this.getRubbishWeightStatistics();
        if (!isOut) {
          this.$emit("changeType", item);
        }
      },

      // 点击图表，展示对应弹窗数据
      handleLend(data, type) {
        switch (type) {
          case "line":
            this.showDialog = true;
            this.dialogTitle = "线路" + data.name + "数量";
            break;
          case "point":
            this.showPointDialog = true;
            this.dialogTitle = "点位" + data.name + "数量";
            break;
        }

        this.dialogQuery = data;
        this.dialogQuery.districtId = this.queryParams.districtId;
        this.dialogQuery.effectiveEndEndTime = formatTime(new Date(), "ymd");
        this.dialogQuery.effectiveEndBeginTime = formatTime(new Date(), "ymd");
        this.dialogQuery.type = this.queryParams.type;
        this.dialogQuery.pickupPathId = this.queryParams.pickupPathId;
      },

      // webSocket更新数据
      refreshData(type, data) {
        switch (type) {
          case "getTodayRubbishTotalInfo":
            this.rubbishWeightStatisticsData = data;
            break;
          case "getTodayWaybill":
            if (this.changeType == 1) {
              this.waybillFormatter(data);
            }
            break;
          case "getPathWaybill":
            if (this.changeType == 2) {
              this.waybillFormatter(data);
            }
            break;
        }
      },

      // 发送参数给后端
      sendSocketMsg(type) {
        let params = [];
        switch (type) {
          case "getTodayWaybill":
            params[0] = this.socketData[0];
            break;
          case "getTodayRubbishTotalInfo":
            params[0] = this.socketData[1];
            break;
          case "getPathWaybill":
            params[0] = this.socketData[2];
            break;
          default:
            params = this.socketData;
        }
        params.forEach((item) => {
          item.params.push(this.channelId);
        });
        this.$emit("sendSocketMsg", params);
      },

      // 重置socket参数
      resetSocketParamsMsg(type) {
        switch (type) {
          case "getTodayWaybill":
            this.socketData[0].params = [null, null, null, null];
            break;
          case "getTodayRubbishTotalInfo":
            this.socketData[1].params = [null, null, null, null];
            break;
          case "getPickupPathWaybill":
            this.socketData[2].params = [null, null];
            break;
          default:
            this.socketData[0].params = [null, null, null, null];
            this.socketData[1].params = [null, null, null, null];
            this.socketData[2].params = [null, null];
        }
      },
    },
  };
</script>

<style scoped lang="scss">
  .container {
    width: 480px;
    position: absolute;
    top: 130px;
    right: 40px;
    z-index: 200;
    padding: 0 5px;

    .tab-list {
      grid-template-columns: repeat(5, 1fr);
    }

    .chart {
      width: 100%;
      height: 220px;
      margin-top: 20px;
    }
  }

  .container-type3 {
    .chart2 {
      margin-top: 90px !important;
    }
    .fw-statistics {
      margin-top: 165px;
    }
  }

  .dialog-index {
    z-index: 400;
  }
  .switch-tab2 {
    margin: 12px 0;
  }

  .chart2 {
    margin-top: 0 !important;
  }

  .mt-20 {
    margin-top: 20px;
  }
  .mt-30 {
    margin-top: 32px;
  }
</style>
