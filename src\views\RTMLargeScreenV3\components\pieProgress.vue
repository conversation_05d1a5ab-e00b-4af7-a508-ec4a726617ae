<template>
  <div class="container container-position">
    <div class="box">
      <div class="title">司机总数</div>
      <div class="top">
        <el-progress
          :width="100"
          type="circle"
          :percentage="calculatePercentage(32, 72)"
          :show-text="false"
          :stroke-width="10"
          define-back-color="#F7D84D"
          text-color="#00FFFC"
          color="#00FFFC"
        ></el-progress>
        <div class="top-right">
          <div>
            <div class="count-box">
              <div class="count-value">8</div>
            </div>
            <div class="count-box">
              <div class="count-value">0</div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="inactive-box">
          <div class="inactive-square"></div>
          <div class="bottom-text">空闲司机 80</div>
        </div>
        <div class="active-box">
          <div class="active-square"></div>
          <div class="bottom-text">出车司机 0</div>
        </div>
      </div>
    </div>
    <div class="box">
      <div class="title">押运工总数</div>

      <div class="top">
        <el-progress
          :width="100"
          type="circle"
          :percentage="calculatePercentage(51, 82)"
          :show-text="false"
          :stroke-width="10"
          define-back-color="#F7D84D"
          text-color="#00FFFC"
          color="#00FFFC"
        ></el-progress>
        <div class="top-right">
          <div>
            <div class="count-box">
              <div class="count-value">8</div>
            </div>
            <div class="count-box">
              <div class="count-value">2</div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="inactive-box">
          <div class="inactive-square"></div>
          <div class="bottom-text">空闲押运工 82</div>
        </div>
        <div class="active-box">
          <div class="active-square"></div>
          <div class="bottom-text">出车押运工 0</div>
        </div>
      </div>
    </div>
    <div class="box">
      <div class="title">今日出车数</div>

      <div class="top">
        <el-progress
          :width="100"
          type="circle"
          :percentage="calculatePercentage(1, 32)"
          :show-text="false"
          :stroke-width="10"
          define-back-color="#F7D84D"
          text-color="#00FFFC"
          color="#00FFFC"
        ></el-progress>
        <div class="top-right">
          <div>
            <div class="count-box">
              <div class="count-value">6</div>
            </div>
            <div class="count-box">
              <div class="count-value">2</div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="inactive-box">
          <div class="inactive-square"></div>
          <div class="bottom-text">经营性 48</div>
        </div>
        <div class="active-box">
          <div class="active-square"></div>
          <div class="bottom-text">非经营性 14</div>
        </div>
      </div>
    </div>
    <div class="box">
      <div class="title">今日机动车辆</div>

      <div class="top">
        <el-progress
          :width="100"
          type="circle"
          :percentage="25"
          :show-text="false"
          :stroke-width="10"
          define-back-color="#F7D84D"
          text-color="#00FFFC"
          color="#00FFFC"
        ></el-progress>
        <div class="top-right">
          <div>
            <div class="count-box">
              <div class="count-value">1</div>
            </div>
            <div class="count-box">
              <div class="count-value">8</div>
            </div>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="inactive-box">
          <div class="inactive-square"></div>
          <div class="bottom-text">经营性 13</div>
        </div>
        <div class="active-box">
          <div class="active-square"></div>
          <div class="bottom-text">非经营性 5</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    methods: {
      calculatePercentage(value, max) {
        // 计算并限制 percentage 的值
        const percentage = (value * 100) / max;
        return Math.min(100, Math.max(0, percentage));
      },
    },
  };
</script>

<style lang="scss" scoped>
  .container {
    margin: 16px 0;
    display: grid;
    grid-gap: 20px;
    grid-template-columns: repeat(2, 1fr);
    margin-bottom: 40px;
  }
  .top {
    display: flex;
  }
  .top-right {
    display: flex;
    flex-direction: column;
    margin-left: 12px;
    margin-top: 20px;

    .count-box {
      display: inline-block;
      border: 1px solid #00fffc;
      margin-right: 4px;
      border-radius: 4px;
      .count-value {
        font-size: 40px;
        font-weight: bold;
        color: #fff;
        padding: 10px 4px;
        line-height: 44px;
      }
    }
  }
  .bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
  }
  .inactive-box,
  .active-box {
    display: flex;
    align-items: center;
  }
  .inactive-square {
    width: 14px;
    height: 14px;
    background-color: #f7d84d;
  }
  .active-square {
    width: 14px;
    height: 14px;
    background-color: #56f1f1;
  }
  .bottom-text {
    font-size: 12px;
    color: #fff;
    margin-left: 4px;
  }
  .container-position {
    width: 440px;
    position: absolute;
    z-index: 1;
    top: 20px;
    left: 1440px;
    background-color: #000;
    padding: 20px;
  }
  .title {
    font-size: 14px;
    color: #fff;
    margin-bottom: 20px;
  }
</style>
