<template>
  <el-dialog
    :title="recordId ? '修改' : '新增' + $route.meta?.title"
    :visible.sync="dialogVisible"
    width="50%"
    destroy-on-close
  >
    <el-form :model="recordForm" :rules="recordRules" ref="recordForm" label-width="120px">
      <el-form-item :label="$route.meta?.title + '名称'" prop="name">
        <el-input
          v-model="recordForm.name"
          placeholder="请输入名称"
          maxlength="10"
          clearable
          show-word-limit
        ></el-input>
      </el-form-item>
      <el-form-item label="序号" prop="sort">
        <el-input-number v-model="recordForm.sort" :min="0" :max="100" :step="1" step-strictly></el-input-number>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="saveRecord">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
  import { createApiFun, updateApiFun, getInfoApiFun } from "@/api/base.js";
  export default {
    name: "courseTypeRecord",
    props: {
      apis: {
        type: Object,
        default: () => {},
      },
      value: {
        type: Boolean,
        default: false,
      },
      recordId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        recordForm: {
          name: "", //字典值
          sort: 0, //序号
        },
        recordRules: {
          name: [{ required: true, message: "请输入名称", trigger: "blur" }],
        },
      };
    },
    watch: {
      dialogVisible: function () {
        this.recordForm.name = "";
        this.recordForm.sort = 0;
        if (this.recordId) {
          this.getRecord(this.recordId);
        }
      },
    },
    methods: {
      // 获取详情数据
      async getRecord(params) {
        let res = await getInfoApiFun(params, this.apis.info);
        this.recordForm = res.data;
      },
      saveRecord() {
        this.$refs.recordForm.validate(async (valid) => {
          if (valid) {
            try {
              let params = this.recordForm;
              const api = this.recordId ? this.apis.update : this.apis.create;
              let res;
              if (this.recordId) {
                res = await updateApiFun(params, api);
              } else {
                res = await createApiFun(params, api);
              }
              if (!res || !res.data) return;
              this.$message.success(`${this.recordId ? "修改" : "新增"}成功`);
              this.$refs.recordForm.clearValidate();
              this.$emit("refresh");
            } catch (error) {
              console.log(error);
            }
          } else {
            this.$message.error("表单验证不通过");
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
