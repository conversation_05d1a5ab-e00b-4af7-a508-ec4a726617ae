/**
 * @Date: 2022-06-09 11:11:12
 * @return {*}
 * @description:饼图
 */
let pieOption = {
  backgroundColor: "",

  title: {
    text: "",
    subtext: "",

    textStyle: {
      fontSize: 38,
      color: "#333",
      fontWeight: "400",
    },

    subtextStyle: {
      fontWeight: "bold",
      fontSize: 28,
      color: "",
    },
  },

  tooltip: {
    show: false,
    trigger: "item",
    borderColor: "#fff",
    textStyle: {
      fontWeight: 400,
    },
    // formatter: '{a} <br/>{b} : {c} ({d}%)'
  },

  legend: {
    show: false,
    itemHeight: 6,
    itemWidth: 6,
    bottom: "0%",
    left: "center",
    textStyle: {
      color: "#fff",
      fontWeight: 400,
      fontSize: 14,
    },
  },

  series: [
    {
      type: "pie",
      name: "",
      avoidLabelOverlap: false,
      hoverAnimation: false,
      label: {
        show: false,
        position: "center",
      },
    },
  ],
  color: ["#60B7F7", "#FABB28", "#FA8128", "#63CC7C", "#9669F5", "#4DCCCB"],
};

export default pieOption;
