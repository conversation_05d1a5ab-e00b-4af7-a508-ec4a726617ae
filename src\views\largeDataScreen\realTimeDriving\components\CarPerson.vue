<template>
  <div>
    <Title title="车辆/人员基础信息"></Title>
    <div class="box">
      <div class="label">收运车辆：</div>
      <div class="value">
        <div>{{ vehicleInfo?.defaultVehiclePlateNumber }}</div>
        <div class="nature">{{ $route.query.operationalNature == 0 ? "桶袋混合" : "桶装收运" }}</div>
      </div>
    </div>
    <div class="box">
      <div class="label">收运司机：</div>
      <div class="value">
        <img class="value-img" v-if="vehicleInfo?.driverAvatar" :src="vehicleInfo.driverAvatar" />
        <img class="value-img" v-else src="@/assets/images/default_avatar.png" />
        <div class="value-box">
          <div class="name">{{ vehicleInfo?.defaultDriverDossierName }}</div>
          <div class="phone">{{ vehicleInfo?.defaultDriverDossierPhone }}</div>
        </div>
      </div>
    </div>
    <div class="box" v-if="vehicleInfo?.supercargoDossierOneName">
      <div class="label">收运押运工：</div>
      <div class="value">
        <img class="value-img" v-if="vehicleInfo?.cargoOneAvatar" :src="vehicleInfo.cargoOneAvatar" />
        <img class="value-img" v-else src="@/assets/images/default_avatar.png" />
        <div class="value-box">
          <div class="name">{{ vehicleInfo?.supercargoDossierOneName }}</div>
          <div class="phone">{{ vehicleInfo?.supercargoDossierOnePhone }}</div>
        </div>
      </div>
    </div>
    <div class="box" v-if="vehicleInfo?.supercargoDossierTwoName">
      <div class="label">收运押运工2：</div>
      <div class="value">
        <img class="value-img" v-if="vehicleInfo?.cargoTwoAvatar" :src="vehicleInfo.cargoTwoAvatar" />
        <img class="value-img" v-else src="@/assets/images/default_avatar.png" />
        <div class="value-box">
          <div class="name">{{ vehicleInfo?.supercargoDossierTwoName }}</div>
          <div class="phone">{{ vehicleInfo?.supercargoDossierTwoPhone }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import Title from "@/views/largeDataScreen/realTimeMonitor/components/Title";
  export default {
    components: {
      Title,
    },
    props: ["vehicleInfo"],
  };
</script>

<style lang="scss" scoped>
  .box {
    display: flex;
    align-items: center;
    margin: 30px 0;
    .label {
      flex: 1;
      font-size: 18px;
      font-weight: bold;
      color: #fff;
      line-height: 18px;
    }
    .value {
      flex: 2;
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
      color: #fff;
      line-height: 20px;
      .value-img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
      }
      .value-box {
        margin-left: 30px;
      }
      .phone {
        font-size: 14px;
        color: #fff;
      }
      .nature {
        flex: 1;
        text-align: right;
      }
    }
  }
</style>
