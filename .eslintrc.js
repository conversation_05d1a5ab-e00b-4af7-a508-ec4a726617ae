module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  extends: ["plugin:vue/essential", "eslint:recommended", "plugin:prettier/recommended"],
  parserOptions: {
    parser: "@babel/eslint-parser",
  },
  rules: {
    "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-unreachable": "error", //不能有无法执行的代码
    "no-unused-vars": "warn", //不能有声明后未被使用的变量或参数
    "no-delete-var": "error", //不能对var声明的变量使用delete操作符
    "no-else-return": 2, //如果if语句里面有return,后面不能跟else语句
    "no-const-assign": 2, //禁止修改const声明的变量
    "no-class-assign": 2, //禁止给类赋值
    "no-func-assign": 2, //禁止重复的函数声明
    "no-unused-expressions": 2, //禁止无用的表达式
    "no-var": "off", //禁止使用var定义变量
    "prettier/prettier": ["error", { endOfLine: "auto" }],
    // 指定数组的元素之间要以空格隔开(,后面)， never参数：[ 之前和 ] 之后不能带空格，always参数：[ 之前和 ] 之后必须带空格
    "array-bracket-spacing": [2, "never"],
    strict: 2, //使用严格模式,
    "vue/multi-word-component-names": ["off", {}], //组件使用多单词命名
  },
};
