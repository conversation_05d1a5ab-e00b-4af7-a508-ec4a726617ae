<template>
  <div class="screen-header">
    <div class="bg-img" :style="{ 'background-image': 'url(' + iconData.bg + ')' }">
      <div class="currDate">{{ currDate }}</div>
      <div class="screen-title">医疗废物智慧收运实时监控大屏</div>
      <div class="weather">
        <!-- <span class="temperature">28°C</span> 多云 -->
      </div>
    </div>
  </div>
</template>

<script>
  import moment from "moment";
  export default {
    data() {
      return {
        iconData: {
          bg: require("@/assets/images/header-bg.png"),
        },
        currDate: "",
      };
    },
    created() {
      moment.locale();
      this.getDate();
    },
    methods: {
      getDate() {
        let timer = setTimeout(() => {
          clearTimeout(timer);
          this.currDate = moment().format("YYYY年MM月DD日 | ") + moment().format("dddd");
          this.getDate();
        }, 1000);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .screen-header {
    width: 100%;
    position: absolute;
    z-index: 1;
  }
  .bg-img {
    position: relative;
    width: 100%;
    height: 120px;
    display: flex;
  }
  .currDate {
    flex: 1;
    font-size: 16px;
    color: #a5d3c2;
    line-height: 21px;
    font-style: normal;
    padding-top: 12px;
    text-align: left;
    padding-left: 40px;
  }
  .screen-title {
    flex: 1;
    font-size: 34px;
    line-height: 40px;
    font-weight: bold;
    background: -webkit-linear-gradient(#ffffff, #9dfad8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
    padding-top: 24px;
  }
  .weather {
    flex: 1;
    text-align: right;
    color: #a5d3c2;
    padding-right: 40px;
    padding-top: 10px;

    .temperature {
      font-weight: 500;
      font-size: 20px;
      color: #ffffff;
      line-height: 24px;
      margin-right: 5px;
    }
  }
</style>
