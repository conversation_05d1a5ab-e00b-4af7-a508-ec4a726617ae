<template>
  <el-popover
    v-model="visible"
    placement="top"
    width="400"
    trigger="click"
    :append-to-body="false"
    v-clickoutside="() => (visible = false)"
  >
    <ul class="date-list">
      <li
        class="date-item"
        :class="{ active: content === item }"
        v-for="item in dateList.slice(0, 4)"
        :key="item"
        @click="changeOption(item)"
        >{{ item }}</li
      >
    </ul>
    <ul class="date-list">
      <li
        class="date-item"
        :class="{ active: content === item }"
        v-for="item in dateList.slice(4, 8)"
        :key="item"
        @click="changeOption(item)"
        >{{ item }}</li
      >
    </ul>
    <span class="content-text" slot="reference">{{ content }}</span>
  </el-popover>
</template>

<script>
  import Clickoutside from "element-ui/src/utils/clickoutside";
  export default {
    directives: {
      Clickoutside,
    },
    props: {
      content: {
        type: String,
        default: "",
      },
      options: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        value: "",
        visible: true,
        dateList: ["今天", "本周", "本月", "本年", "昨天", "上周", "上个月", "去年"],
      };
    },
    methods: {
      changeOption(value) {
        if (value === this.content) return;
        this.visible = false;
        this.$emit("changeOption", value);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .full-width {
    width: 100%;
  }
  .date-list {
    display: flex;
    align-items: center;
    margin-top: 4px;
    .date-item {
      padding: 4px 6px;
      margin-right: 4px;
      &.active {
        background-color: #4ca786;
        border-radius: 14px;
        color: #fff;
      }
    }
  }
  .content-text {
    color: #4ca786;
    &:hover {
      text-decoration: underline;
    }
  }
</style>
