<template>
  <div class="Echarts2-wrapper" :id="eChartsId"> </div>
</template>
<script>
  import * as echarts from "echarts";
  export default {
    components: {},
    // props: ["eChartsId", "total", "title", "label1", "label2", "label3"],
    props: ["eChartsId", "attend", "unAttend", "total", "title", "label1", "label2", "label3"],

    data() {
      return {
        // attend: 56,
        // unAttend: 24,
      };
    },
    computed: {},
    created() {},
    mounted() {
      this.init();
    },
    methods: {
      init() {
        var myChart = echarts.init(document.getElementById(this.eChartsId));
        let chartData = [
          {
            name: this.label1,
            value: this.attend,
          },
          {
            name: this.label2,
            value: this.unAttend,
          },
        ];
        myChart.setOption({
          color: ["#E72A2A", "#25C68C"],

          title: {
            text: this.title,
            textStyle: {
              color: "#414D5A",
              fontStyle: "normal",
              fontWeight: "bold",
              fontFamily: "Microsoft YaHei",
              fontSize: "14",
              lineHeight: "20",
            },
            left: "center",
          },
          legend: {
            selectedMode: false,
            top: "25",
            left: "center",
            itemWidth: 8,
            itemHeight: 8,
            data: [
              { name: this.label1, icon: "rect" },
              { name: this.label2, icon: "rect" },
            ],
            formatter: function (val) {
              const item = chartData.find((chartDataItem) => chartDataItem.name === val);
              const valStr = val.toString();
              const valueStr = item.value.toString();
              return "{title|" + valStr + "}" + "{value|" + valueStr + "}";
            },
            textStyle: {
              rich: {
                title: {
                  fontSize: 12,
                  color: "#414D5A",
                },
                value: {
                  fontSize: 12,
                  color: "#414D5A",
                  padding: [0, 0, 0, 10],
                },
              },
            },
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          tooltip: {
            trigger: "item", // 触发类型，数据项图形触发
            formatter: (params) => {
              // console.log(params, "params");
              // params 是 tooltip 的参数，包含了当前数据的信息
              // 自定义返回的字符串，用于显示 tooltip

              //  处理大小圆环 重新自定义tooltip问题
              // 如果data中 data加上对应就对导致legend异常，固将nane置为空
              // 将name置为空，又会导致tooltip中去不到{params.name}
              // 因此加上
              let name = "";
              if (params.color == "transparent") {
                name = this.label1;
              } else {
                name = params.name;
              }
              return `${params.seriesName}<br/>${name}: ${params.value} (${params.percent}%)`;
            },
            textStyle: {
              color: "F7FAF9",
            },
            borderColor: "F7FAF9",
          },
          series: [
            {
              center: ["50%", "60%"],
              type: "pie",
              selectedMode: "single",
              radius: ["0%", "20%"],
              silent: true,
              clockwise: false,
              label: {
                position: "center",
                formatter: `{value|${this.total}}\n {name|${this.label3}} `,
                rich: {
                  name: {
                    fontSize: 14,
                  },
                  value: {
                    fontSize: 26,
                    fontWeight: 500,
                    padding: [10, 0, 0, 0],
                    fontFamily: "PangMenZhengDao",
                  },
                },
              },
              itemStyle: {
                normal: {
                  color: "#fff",
                },
              },
              labelLine: {
                show: false,
              },
              data: [{ value: 0 }],
              right: "0%",
            },
            // 小圆环 出勤车辆
            {
              emphasis: {
                // 取消放大效果
                scale: 0,
                // 可以设置其他悬停时的样式，比如标签显示
                label: {
                  show: false,
                  fontSize: "14",
                  fontWeight: "bold",
                },
              },
              name: this.label3,
              type: "pie",
              center: ["50%", "60%"],

              radius: ["60%", "70%"],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: "center",
              },
              data: [
                {
                  value: this.attend,
                  name: this.label1,
                  itemStyle: {
                    color: "#25C68C",
                  },
                },
                {
                  value: this.unAttend,
                  name: "",
                  itemStyle: {
                    color: "transparent",
                  },
                },
              ],
            },
            // 小圆环 未出勤车辆
            {
              emphasis: {
                // 取消放大效果
                scale: 0,
                // 可以设置其他悬停时的样式，比如标签显示
                label: {
                  show: false,
                  fontSize: "14",
                  fontWeight: "bold",
                },
              },
              name: this.title,
              type: "pie",
              center: ["50%", "60%"],

              radius: ["57%", "73%"],
              avoidLabelOverlap: false,
              label: {
                show: false,
                position: "center",
              },

              data: [
                {
                  value: this.attend,
                  name: "",
                  itemStyle: {
                    color: "transparent",
                  },
                },
                {
                  value: this.unAttend,
                  name: this.label2,
                  itemStyle: {
                    color: "#E72A2A",
                  },
                },
              ],
            },
          ],
        });
      },
    },

    watch: {},
  };
</script>
<style scoped lang="scss">
  .Echarts2-wrapper {
    width: 100%;
    flex: 1;
  }
</style>
