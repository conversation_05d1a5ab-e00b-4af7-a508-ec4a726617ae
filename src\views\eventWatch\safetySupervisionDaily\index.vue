<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <record
        v-if="showRecord"
        :recordId="recordId"
        :recordDate="recordDate"
        @closeRecord="closeRecord"
        @refreshList="initData"
      ></record>
      <div class="main-index" v-else>
        <header class="header">
          <InterviewChannel :value.sync="filterForm.channelId" :record.sync="channelRecord"></InterviewChannel>
          <div class="header-left mr-10">
            <el-form ref="filterForm" label-suffix=":" label-width="140px">
              <el-form-item label="报告生成日期范围">
                <el-date-picker
                  v-model="filterForm.complaintDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-form>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
        </header>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column prop="createDate" label="生成报告日期" align="center"></el-table-column>
            <el-table-column prop="abnormalTotal" label="异常总数" align="center"></el-table-column>
            <el-table-column prop="personAbnormalTotal" label="人员异常总数" align="center"></el-table-column>
            <el-table-column prop="activeReportAbnormalTotal" label="主动上报异常数量" align="center"></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row)">报告详情</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import { getListPageApiFun } from "@/api/base";
  import record from "./components/record.vue";
  export default {
    components: {
      defaultPage,
      record,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/safety/listPage",
        },
        showRecord: false,
        recordId: "",
        recordDate: "",
        loading: false,
        channelRecord: {},
      };
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          beginCreateDate: this.filterForm.complaintDate ? this.filterForm.complaintDate[0] : "",
          endCreateDate: this.filterForm.complaintDate ? this.filterForm.complaintDate[1] : "",
          channelId: this.filterForm.channelId || "",
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.recordId = row.id;
        this.recordDate = row.createDate;
      },
      closeRecord() {
        this.showRecord = false;
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  ::v-deep .header-left .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
