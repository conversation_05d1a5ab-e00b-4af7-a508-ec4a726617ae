<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="扣分项详情" :visible.sync="dialogVisible" width="60%" destroy-on-close @open="initData">
      <el-table
        class="table-bottom"
        :data="dataList"
        :header-cell-style="{ background: '#F5F7F9' }"
        border
        max-height="400px"
      >
        <el-table-column prop="type" label="扣分项" align="center">
          <template #default="{ row }">{{ DEDUCTION_ITEM[row.type] }}</template>
        </el-table-column>
        <el-table-column prop="deductSalary" label="扣除绩效工资金额" align="center"></el-table-column>
        <el-table-column prop="deductScore" label="扣除总分" align="center"></el-table-column>
        <el-table-column prop="memo" label="备注" align="center"></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">返回</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  import { DEDUCTION_ITEM } from "@/enums";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      id: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        apis: {
          info: "/api/access/record/demerit/",
        },
        DEDUCTION_ITEM,
        dataList: [],
      };
    },
    methods: {
      async initData() {
        let res = await getInfoApiFun(this.id, this.apis.info);
        if (res.success) {
          this.dataList = res.data;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .table-bottom {
    margin-bottom: 20px;
  }
  .deduction-list {
    max-height: 800px;
    overflow-y: auto;
  }
</style>
