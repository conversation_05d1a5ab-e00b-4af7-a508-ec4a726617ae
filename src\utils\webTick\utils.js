/*
 * @Description:
 * @Author: wuwl
 * @Date: 2024-06-05 15:14:44
 * @FilePath: \web\src\utils\webTick\utils.js
 * @LastEditTime: 2024-06-06 09:33:15
 * @LastEditors: wuwl
 */
export function getUUID() {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
    var r = ((window.crypto.getRandomValues(new Uint8Array(1)) / 255) * 16) | 0,
      v = c == "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
