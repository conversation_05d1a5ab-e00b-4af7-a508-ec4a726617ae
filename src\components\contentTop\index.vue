<template>
  <div class="content-top">
    <div class="top-flex">
      <span
        class="el-icon-back content-top-back"
        v-if="!topInfo.buttonShow || hideReturnButton"
        @click="closeRecord"
      ></span>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item v-for="(item, index) in titls" :key="index">{{ item }}</el-breadcrumb-item>
        <el-breadcrumb-item v-if="topInfo.subTitle"
          ><b>{{ topInfo.subTitle }}</b></el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div v-if="topInfo.buttonShow">
      <el-button
        type="primary"
        size="small"
        icon="el-icon-plus"
        @click="createRecord"
        v-permission="topInfo.buttonPermission"
        >{{ topInfo.buttonName }}</el-button
      >
      <slot></slot>
    </div>
  </div>
</template>

<script>
  export default {
    name: "contentTop",
    props: {
      topInfo: {
        type: Object,
      },
      hideReturnButton: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        titls: [],
      };
    },
    mounted() {
      this.getMenu();
    },
    methods: {
      async getMenu() {
        let user = await this.getUserInfo();
        if (!user) {
          this.logout();
          return;
        }
        let rootPath = this.$route.path;
        let menus = await this.getMenus();
        for (let i = 0; i < menus.length; i++) {
          let curList = [];
          let menu = menus[i];
          curList.push(menu.name);
          if (menu.url && menu.url == rootPath) {
            this.titls = curList;
          } else if (menu.menus) {
            let flag = this.nextMenus(menu.menus, curList, rootPath);
            if (flag) {
              break;
            }
          }
        }
      },
      nextMenus(menus, curList, rootPath) {
        for (let i = 0; i <= menus.length; i++) {
          let menu = menus[i];
          if (!menu) {
            break;
          }
          //console.log("menu:",menu);

          //console.log("url",menu.url);
          if (this.compareUrl(menu.url, rootPath)) {
            curList.push(menu.name);
            this.titls = curList;
            return true;
          } else if (menu.menus) {
            let flage = this.nextMenus(menu.menus, curList, rootPath);
            if (flage) {
              curList.push(menu.name);
              this.titls = curList;
              return true;
            }
          }
        }
      },
      //比较两个地址是否是相等
      compareUrl: function (url1, url2) {
        //去掉地址斜杠
        if (url1 && url2) {
          url1 = url1.replace(/\//g, "");
          url2 = url2.replace(/\//g, "");
          if (url1 == url2) {
            return true;
          }
        }

        return false;
      },
      createRecord() {
        this.$emit("createRecord");
      },
      // 返回列表
      closeRecord() {
        this.$emit("closeRecord");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .content-top {
    background: #fff;
    padding-left: 20px;
    padding-right: 20px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .content-top-back {
    cursor: pointer;
    margin-right: 10px;
  }
  // .content-top-btton-icon {
  //   font-size: 14px;
  //   margin-right: 5px;
  // }
  .top-flex {
    display: flex;
    align-items: center;
  }
</style>
