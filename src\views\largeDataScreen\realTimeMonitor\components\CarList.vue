<template>
  <div class="car-container micro-app-sctmp_base">
    <header class="car-header">
      <el-input v-model="keyword" placeholder="请输入车牌号/司机姓名" clearable></el-input>
    </header>
    <div class="mt-10 pl-4">
      <el-radio
        :value="radioValue"
        :label="index"
        v-for="(item, index) in radioOptions"
        :key="index"
        @click.native.prevent="handleChangeRadio(index)"
        >{{ item }}</el-radio
      >
    </div>
    <Title title="车辆信息列表" class="mtb-18"></Title>
    <ul class="header-list">
      <li v-for="(item, index) in headerList" :key="index">{{ item }}</li>
    </ul>
    <ul class="data-list small-scroll">
      <li
        class="data-item"
        :class="{ active: item.plateNumber === $route.query.plateNumber }"
        v-for="item in dataList"
        :key="item.id"
        @click="toCarDetail(item)"
      >
        <img class="item-img" :src="carImg[item.accStatus]" alt="" />
        <div class="text-ellipsis">{{ item.plateNumber }}</div>
        <div class="text-ellipsis">{{ item.driverName }}</div>
        <div class="text-ellipsis">{{ radioOptions[item.operationalNature] }}</div>
        <div class="text-ellipsis">{{ statusName[item.accStatus] }}</div>
        <div class="text-ellipsis">{{ item.accStatus == 0 ? item.speed : 0 }}km/h</div>
      </li>
    </ul>
  </div>
</template>

<script>
  import Title from "./Title";
  export default {
    props: {
      carList: {
        type: Array,
        default: () => [],
      },
    },
    components: {
      Title,
    },
    computed: {
      dataList() {
        let valueList = this.carList;
        if (this.keyword.trim()) {
          valueList = valueList.filter(
            (list) => list.driverName.includes(this.keyword.trim()) || list.plateNumber.includes(this.keyword.trim()),
          );
        }
        if (this.radioValue || this.radioValue === 0) {
          valueList = valueList.filter((list) => list.operationalNature == this.radioValue);
        }
        return valueList;
      },
    },
    data() {
      return {
        keyword: "",
        headerList: ["", "车牌号", "驾驶司机", "车辆性质", "车辆状态", "行驶速度"],
        statusName: ["行驶中", "静止", "作业中"],
        radioOptions: ["危运", "非危运"],
        radioValue: "",
        carImg: [
          require("@/assets/images/drive_car.png"),
          require("@/assets/images/static_car.png"),
          require("@/assets/images/offline_car.png"),
        ],
      };
    },
    mounted() {},
    methods: {
      // 跳转车辆详情
      toCarDetail(item) {
        this.$emit("itemClick", item);
      },
      handleChangeRadio(value) {
        if (this.radioValue === value) {
          this.radioValue = "";
          return;
        }
        this.radioValue = value;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .car-container {
    pointer-events: auto;
    margin-top: 10px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .car-header {
    display: flex;
    align-items: center;
  }
  .header-list,
  .data-item {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    color: #fff;
    font-size: 14px;
    text-align: center;
    line-height: 16px;
    padding: 6px 0;
    border-bottom: 1px solid #fff;
    cursor: pointer;
    align-items: center;
    &.active {
      color: #faa94b;
    }
  }
  .header-list {
    padding-right: 10px;
  }
  .data-list {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 20px;
    padding-right: 10px;
  }
  .item-img {
    width: 27px;
    height: 16px;
    overflow: hidden;
    margin-left: 10px;
  }
  .mtb-18 {
    margin: 18px 0;
  }
  .pl-4 {
    padding-left: 4px;
  }
  ::v-deep .el-radio {
    color: #fff;
  }
</style>
