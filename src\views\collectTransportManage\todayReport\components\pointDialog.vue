<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      width="80%"
      top="0"
      destroy-on-close
      :show-close="false"
      @open="initData"
    >
      <div class="main-index">
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="productionUnit" placeholder="请输入产废单位名称" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="initData">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-popover placement="bottom" width="240" trigger="click">
              <div class="reveal-box">
                <div class="reveal-header">
                  <div>展示全部</div>
                  <el-switch
                    :value="allItemChecked"
                    active-text="是"
                    inactive-text="否"
                    @change="changeAllReveal"
                  ></el-switch>
                </div>
                <ul class="reveal-list">
                  <li class="reveal-item" v-for="(item, index) in itemList" :key="index">
                    <el-checkbox :value="item.value" :disabled="index === 0" @change="changeReveal($event, item)">{{
                      item.label
                    }}</el-checkbox>
                  </li>
                </ul>
              </div>
              <el-button slot="reference">
                <div class="btn-inner">
                  <span class="el-icon-s-grid"></span>
                  <span>显示列</span>
                  <span class="el-icon-caret-bottom"></span>
                </div>
              </el-button>
            </el-popover>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <el-col :span="12">
            <el-form-item label="任务类型">
              <el-select v-model="filterForm.detailType" placeholder="请选择任务类型" clearable filterable>
                <el-option
                  v-for="(item, index) in POINT_TASK_TYPE"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table
            ref="tableRef"
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            height="100%"
            border
          >
            <el-table-column width="50" type="index" label="顺序" align="center"></el-table-column>
            <el-table-column prop="code" label="点位编号" align="center" v-if="itemList[0].value"></el-table-column>
            <el-table-column
              prop="productionUnit"
              label="产废单位名称"
              align="center"
              v-if="itemList[1].value"
            ></el-table-column>
            <el-table-column
              prop="pickupPathName"
              label="所属路线"
              align="center"
              v-if="itemList[2].value"
            ></el-table-column>
            <el-table-column
              prop="firstCarrier"
              label="第一承运人"
              align="center"
              v-if="itemList[3].value"
            ></el-table-column>
            <el-table-column prop="operation" label="是否正常经营" align="center" v-if="itemList[4].value">
              <template #default="{ row }">{{ IS_NORMAL_OPERATION[row.operation] }}</template>
            </el-table-column>
            <el-table-column prop="detailType" label="任务类型" align="center" v-if="itemList[5].value">
              <template #default="{ row }">{{ POINT_TASK_TYPE[row.detailType] }}</template>
            </el-table-column>
            <el-table-column prop="overType" label="加班类型" align="center" v-if="itemList[6].value">
              <template #default="{ row }">{{
                row.overType || row.overType === 0 ? OVERTIME_TYPE[row.overType] : ""
              }}</template>
            </el-table-column>
            <el-table-column
              prop="rubbishTotal"
              label="废物总量（kg）"
              align="center"
              v-if="itemList[7].value"
            ></el-table-column>
            <el-table-column prop="waybillStatus" label="收运状态" align="center" v-if="itemList[8].value">
              <template #default="{ row }">{{ RECEIVING_CONDITION[row.waybillStatus] }}</template>
            </el-table-column>
            <el-table-column
              prop="waybillTime"
              label="收运时间"
              align="center"
              v-if="itemList[9].value"
            ></el-table-column>
            <el-table-column
              prop="effectiveDate"
              label="收运生效日期"
              align="center"
              v-if="itemList[10].value"
            ></el-table-column>
            <el-table-column
              prop="endDate"
              label="收运截止日期"
              align="center"
              v-if="itemList[11].value"
            ></el-table-column>
            <el-table-column prop="isClear" label="是否清空" align="center" v-if="itemList[12].value">
              <template #default="{ row }">{{ IS_NORMAL_OPERATION[row.isClear] }}</template>
            </el-table-column>
            <el-table-column prop="baggingMethod" label="桶装/袋装" align="center" v-if="itemList[13].value">
              <template #default="{ row }">{{ BARRELS_BAGS[row.baggingMethod] }}</template>
            </el-table-column>
            <el-table-column prop="verifyStatus" label="当前流程" align="center" v-if="itemList[14].value">
              <template #default="{ row }">{{ VERIFY_STATUS[row.verifyStatus] }}</template>
            </el-table-column>
            <el-table-column
              prop="verifyUserName"
              label="确认人"
              align="center"
              v-if="itemList[15].value"
            ></el-table-column>
            <el-table-column
              prop="verifyTime"
              label="确认时间"
              align="center"
              v-if="itemList[16].value"
            ></el-table-column>
            <el-table-column
              prop="userLongitude"
              label="经度"
              align="center"
              v-if="itemList[17].value"
            ></el-table-column>
            <el-table-column
              prop="userLatitude"
              label="纬度"
              align="center"
              v-if="itemList[18].value"
            ></el-table-column>
            <el-table-column prop="address" label="地址" align="center" v-if="itemList[19].value"></el-table-column>
            <el-table-column prop="image" label="图片" align="center">
              <template #default="{ row }">
                <el-badge
                  v-if="row.picture && row.picture.length > 0"
                  :value="row.picture.length > 1 ? row.picture.length : ''"
                  class="picture-badge"
                  type="success"
                >
                  <el-image
                    class="picture-img"
                    fit="cover"
                    :src="row.picture[0].url"
                    :preview-src-list="row.picture.map((i) => i.url)"
                    v-if="row.picture"
                  ></el-image>
                </el-badge>
              </template>
            </el-table-column>
            <el-table-column label="废物类型/重量（kg）" align="center" v-if="itemList[20].value">
              <el-table-column prop="infectiousWaste" label="感染性废物" align="center"></el-table-column>
              <el-table-column prop="damagingWaste" label="损伤性废物" align="center"></el-table-column>
              <el-table-column prop="pharmaceuticalWaste" label="药物性废物" align="center"></el-table-column>
              <el-table-column prop="pathologicalWaste" label="病理性废物" align="center"></el-table-column>
              <el-table-column prop="chemicalWaste" label="化学性废物" align="center"></el-table-column>
              <el-table-column prop="sludge" label="感染性废物一污泥" align="center" width="140"></el-table-column>
            </el-table-column>
            <el-table-column
              prop="productionUnitOperator"
              label="产废单位经办人"
              align="center"
              v-if="itemList[21].value"
            ></el-table-column>
            <el-table-column
              prop="residueRubbish"
              label="剩余垃圾"
              align="center"
              v-if="itemList[22].value"
            ></el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import {
    RECEIVING_CONDITION,
    POINT_TASK_TYPE,
    IS_NORMAL_OPERATION,
    BARRELS_BAGS,
    VERIFY_STATUS,
    OVERTIME_TYPE,
  } from "@/enums";
  import moment from "moment";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      waybillStatus: {
        type: Number,
        default: 0,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/waybill/waybillDetail/listPage",
        },
        RECEIVING_CONDITION,
        POINT_TASK_TYPE,
        IS_NORMAL_OPERATION,
        BARRELS_BAGS,
        VERIFY_STATUS,
        OVERTIME_TYPE,
        loading: false,
        showFilter: false,
        productionUnit: "",
        itemList: [
          { label: "点位编号", value: true },
          { label: "产废单位名称", value: true },
          { label: "所属路线", value: true },
          { label: "第一承运人", value: true },
          { label: "是否正常经营", value: true },
          { label: "任务类型", value: true },
          { label: "加班类型", value: true },
          { label: "废物总量（kg）", value: true },
          { label: "收运状态", value: true },
          { label: "收运时间", value: true },
          { label: "收运生效日期", value: true },
          { label: "收运截止日期", value: true },
          { label: "是否清空", value: true },
          { label: "桶装/袋装", value: true },
          { label: "当前流程", value: true },
          { label: "确认人", value: true },
          { label: "确认时间", value: true },
          { label: "经度", value: true },
          { label: "纬度", value: true },
          { label: "地址", value: true },
          { label: "废物类型/重量（kg）", value: false },
          { label: "产废单位经办人", value: false },
          { label: "剩余垃圾", value: false },
        ],
        allItemChecked: false,
      };
    },
    methods: {
      initData() {
        this.filterForm = {};
        this.productionUnit = "";
        this.page.pageNo = 1;
        this.page.pageSize = 10;
        this.getDataList();
      },
      async getDataList() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          productionUnit: this.productionUnit,
          effectiveEndBeginTime: moment().format("YYYY-MM-DD"),
          effectiveEndEndTime: moment().format("YYYY-MM-DD"),
          waybillStatus: this.waybillStatus,
          detailType: this.filterForm.detailType,
          channelId: this.channelId,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.tableData.forEach((item) => {
            try {
              item.picture = JSON.parse(item.picture);
            } catch (error) {
              item.picture = "";
            }
          });
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.getDataList();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.getDataList();
      },
      // 修改所有显示列
      changeAllReveal(value) {
        this.allItemChecked = value;
        this.itemList.slice(1, this.itemList.length).forEach((item) => (item.value = value));
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 列点击事件
      changeReveal(value, item) {
        item.value = value;
        let uncheckList = this.itemList.slice(1, this.itemList.length).filter((list) => !list.value);
        if (uncheckList.length === 0) {
          this.allItemChecked = true;
        } else {
          this.allItemChecked = false;
        }
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
      position: relative;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .w250 {
    width: 250px;
  }
  .picture-img {
    width: 60px;
    height: 60px;
  }
  ::v-deep .el-badge__content.is-fixed {
    top: 8px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
