# app-mirco-demo 子应用

## 使用说明

构建新的子应用时，需要配置 `config/index.js` 里面参数，需注意子应用名及端口名不能重复

```javascript
module.exports = {
  routerPrefix: "/demosub", //子应用路由前缀
  appName: "demosub", //子应用名称
  port: 7002, //子应用本地调试端口
};
```

### 安装依赖

```
npm install
```

### 启动开发服务器

```
npm run serve
```

### 打包

```
npm run test  测试
npm run pre   预生产
npm run prod  生产
```

### 打包后文件夹

```
web
```

### Customize configuration

See [Configuration Reference](https://cli.vuejs.org/config/).
