<template>
  <div class="container">
    <div class="title">收运点位类型统计</div>
    <div v-for="item in summaryData" :key="item.name">
      <div class="flex-box">
        <div class="left-label">{{ item.name }}</div>
        <div class="right-value">{{ item.value }}家</div>
      </div>
      <el-progress
        define-back-color="#3F3F3D"
        color="#11E48A"
        :show-text="false"
        :stroke-width="16"
        :percentage="calculatePercentage(item.value)"
      ></el-progress>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        summaryData: [
          {
            name: "大医院",
            value: 127,
          },
          {
            name: "小诊所",
            value: 3500,
          },
          {
            name: "智能收集柜",
            value: 4,
          },
          {
            name: "小型床位",
            value: 203,
          },
        ],
        max: 3000,
      };
    },
    methods: {
      calculatePercentage(value) {
        // 计算并限制 percentage 的值
        const percentage = (value * 100) / this.max;
        return Math.min(100, Math.max(0, percentage));
      },
    },
  };
</script>

<style lang="scss" scoped>
  .container {
    width: 500px;
    position: absolute;
    z-index: 1;
    top: 700px;
    left: 40px;
    background-color: #000;
    padding: 20px;
  }
  .title {
    color: #fff;
    font-size: 16px;
    margin-bottom: 20px;
  }
  .flex-box {
    margin-top: 20px;

    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #fff;
    font-size: 16px;
    margin-bottom: 10px;
  }
  .right-value {
    color: #11e48a;
  }
</style>
