<template>
  <div class="container" :class="'positon' + positon">
    <div class="content-wrapper">
      <img src="../images/location.png" alt="" />
      <div class="content">
        <div class="title">{{ title }}</div>
        <div class="flex-wrapper">
          <div>
            <div class="blue lable">总车位</div>
            <div class="blue value">8</div>
          </div>
          <div>
            <div class="blue lable">空闲车位</div>
            <div class="blue value">6</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    components: {},
    props: ["positon", "title"],
    data() {
      return {};
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {},
    watch: {},
  };
</script>
<style scoped lang="scss">
  .flex-wrapper {
    display: flex;
    width: 180px;
    margin-top: 14px;
    justify-content: space-between;
  }
  .container {
    position: absolute;
    z-index: 1;
  }
  .positon1 {
    top: 530px;
    left: 600px;
  }
  .positon2 {
    top: 480px;
    left: 980px;
  }
  .positon3 {
    top: 860px;
    left: 800px;
  }
  .content {
    position: absolute;
    top: 20px;
    left: 20px;
  }
  .title {
    font-size: 16px;
    color: #fff;
  }
  .blue {
    margin-bottom: 4px;
    font-size: 16px;
    color: #1df9fc;
  }
  .content-wrapper {
    position: relative;
  }
</style>
