<template>
  <div v-if="completed">
    <v-scale-screen width="1920" height="1080" class="dp-first-content">
      <div class="bg-img" :style="{ 'background-image': 'url(' + iconData.bg + ')' }"></div>
      <Header
        :channelList="channelList"
        :channel="channel"
        :value.sync="channelId"
        @toggleChannel="toggleChannel"
      ></Header>

      <div class="left">
        <CertificateManage :getCardManager="formData.getCardManager"></CertificateManage>
        <Maintenance :getVehicleMaintenance="formData.getVehicleMaintenance"></Maintenance>
        <Compliance :getVehicleCompliance="formData.getVehicleCompliance"></Compliance>
        <Refuel :getVehicleFuel="formData.getVehicleFuel"></Refuel>
      </div>
      <div class="right">
        <Abnormal :getVehicleAbnormal="formData.getVehicleAbnormal"></Abnormal>
        <Secure :getVehicleSafety="formData.getVehicleSafety"></Secure>
      </div>
      <mapChartsVue
        ref="mapChartsRef"
        :additiveLabel="additiveLabel"
        :externalEvent="true"
        :channelList="channelList"
        :channelId="channelId"
        @openRouteDialog="openRouteDialog"
      ></mapChartsVue>
      <!-- <management v-bind="formData"></management> -->
      <!-- <backlog v-bind="formData" :currIndex="currIndex" @handleSwitch="handleSwitch"></backlog> -->
      <!-- <abnormal-condition v-bind="formData"></abnormal-condition> -->
      <!-- <traffic-statistic v-bind="formData"></traffic-statistic> -->
    </v-scale-screen>
    <MaintenanceDialog
      :value.sync="showMaintenanceDialog"
      :maintenanceType="maintenanceType"
      :channelId="channelId"
    ></MaintenanceDialog>
    <MaintenanceListDialog
      :value.sync="showMaintenanceListDialog"
      :listType="listType"
      :channelId="channelId"
    ></MaintenanceListDialog>
    <RefuelDialog :value.sync="showRefuelDialog" :channelId="channelId"></RefuelDialog>
    <RefuelRankDialog :value.sync="showRefuelRankDialog" :channelId="channelId"></RefuelRankDialog>
    <SecureDialog :value.sync="showSecureDialog" :secureSize="secureSize" :channelId="channelId"></SecureDialog>
    <EvaluateDialog :value.sync="showEvaluateDialog" :secureSize="secureSize" :channelId="channelId"></EvaluateDialog>
    <SupervisoryDialog
      :value.sync="showSupervisoryDialog"
      :secureSize="secureSize"
      :channelId="channelId"
    ></SupervisoryDialog>
    <AbnormalDialog :value.sync="showAbnormalDialog" :channelId="channelId"></AbnormalDialog>
    <RouteDialog
      :value.sync="showRouteDialog"
      :districtName="districtName"
      :channelList="channelList"
      :channelId="channelId"
    ></RouteDialog>
    <CertificateDialog
      :value.sync="showCertificateDialog"
      :certType="certType"
      :channelId="channelId"
    ></CertificateDialog>
  </div>
</template>

<script>
  import VScaleScreen from "v-scale-screen";
  import Header from "./components/Header.vue";
  // import Backlog from "./components/Backlog.vue";
  // import Management from "./components/Management.vue";
  // import AbnormalCondition from "./components/AbnormalCondition.vue";
  // import TrafficStatistic from "./components/TrafficStatistic.vue";
  import mapChartsVue from "@/components/mapCharts/mapCharts.vue";
  import CertificateManage from "./components/CertificateManage.vue";
  import Maintenance from "./components/Maintenance.vue";
  import Compliance from "./components/Compliance.vue";
  import Refuel from "./components/Refuel.vue";
  import Abnormal from "./components/Abnormal.vue";
  import Secure from "./components/Secure.vue";
  import { getInfoApiFun, getInfoApiFunByParams } from "@/api/base";
  import { vehicleAreaStaticsApi, vehicleEventStaticsApi } from "@/api/vehicles";
  import websocket from "@/utils/websocket";
  import emitter from "@/utils/mitt";
  import MaintenanceDialog from "./components/MaintenanceDialog.vue";
  import MaintenanceListDialog from "./components/MaintenanceListDialog.vue";
  import RefuelDialog from "./components/RefuelDialog.vue";
  import SecureDialog from "./components/SecureDialog.vue";
  import EvaluateDialog from "./components/EvaluateDialog.vue";
  import SupervisoryDialog from "./components/SupervisoryDialog.vue";
  import AbnormalDialog from "../realTimeMonitor/components/ExceptReportDialog.vue";
  import RouteDialog from "./components/RouteDialog.vue";
  import RefuelRankDialog from "./components/RefuelRankDialog.vue";
  import CertificateDialog from "./components/CertificateDialog.vue";
  import { getLocalStorage } from "@/utils/storage";
  import { INTERVIEW_CHANNEL } from "@/utils/commonValue"; //访问渠道
  export default {
    mixins: [websocket],
    name: "VehiclesData",
    components: {
      VScaleScreen,
      Header,
      // Backlog,
      // Management,
      // AbnormalCondition,
      // TrafficStatistic,
      mapChartsVue,
      CertificateManage,
      Maintenance,
      Compliance,
      Refuel,
      Abnormal,
      Secure,
      MaintenanceDialog,
      MaintenanceListDialog,
      RefuelDialog,
      SecureDialog,
      EvaluateDialog,
      SupervisoryDialog,
      AbnormalDialog,
      RouteDialog,
      RefuelRankDialog,
      CertificateDialog,
    },
    data() {
      return {
        iconData: {
          bg: require("@/assets/images/<EMAIL>"),
        },
        areaData: [],
        additiveLabel: {
          show: true,
          padding: [10, 30, 10, 10],
          color: "#fff",
          borderColor: "#44FFF5",
          borderWidth: 0,
          borderRadius: 6,
          textStyle: {
            align: "left",
            lineHeight: 26,
            fontSize: 16,
          },
          width: 100,
          backgroundColor: {
            image: require("@/assets/images/labelBg2.png"),
            // 这里可以是图片的 URL，
            // 或者图片的 dataURI，
            // 或者 HTMLImageElement 对象，
            // 或者 HTMLCanvasElement 对象。
          },
          formatter(params) {
            let arr = [params.name, "分配车辆      " + params.value[0], ""];
            return arr.join("\n");
          },
        },
        formData: {
          getCardManager: {},
          getVehicleMaintenance: {},
          getVehicleCompliance: {},
          getVehicleFuel: {},
          getVehicleAbnormal: {},
          getVehicleSafety: {},
          areaStatics: {},
        },
        currIndex: 0,
        websocketUrl: `${process.env.VUE_APP_WSS_URL}/sctmpbase/api/screen/real-time?location=3`,
        showMaintenanceDialog: false, //待保养/待二级维护车辆
        maintenanceType: 0, //待保养/待二级维护类型
        showMaintenanceListDialog: false,
        listType: 0, //待保养/待二级维护/维修类型
        showRefuelDialog: false,
        showSecureDialog: false,
        secureSize: 0,
        showEvaluateDialog: false,
        showSupervisoryDialog: false,
        showAbnormalDialog: false,
        showRouteDialog: false,
        districtName: "",
        showRefuelRankDialog: false,
        showCertificateDialog: false,
        certType: [],
        apis: { channelList: "/api/base/dna/listByDna/" },
        channelId: "",
        channelList: [],
        channel: "",
        completed: false,
      };
    },
    async created() {
      this.channel = getLocalStorage(INTERVIEW_CHANNEL);
      await this.getChannelList();
      this.initData();
      let token = await window.LOGAN.getToken();
      this.initWebSocket(this.websocketUrl + `&token=${token}`);
      let socketData = [
        {
          propName: "getVehicleSafety",
          params: [this.channelId],
        },
        {
          propName: "getVehicleAbnormal",
          params: [this.channelId],
        },
        {
          propName: "getVehicleFuel",
          params: [this.channelId],
        },
        {
          propName: "getVehicleCompliance",
          params: [this.channelId],
        },
        {
          propName: "getCardManager",
          params: [this.channelId],
        },
        {
          propName: "getVehicleMaintenance",
          params: [this.channelId],
        },
      ];
      this.sendSocketMessage(socketData);
    },
    mounted() {
      emitter.on("open-maintenance-dialog", (value) => {
        this.showMaintenanceDialog = true;
        this.maintenanceType = value;
      });
      emitter.on("open-maintenanceList-dialog", (value) => {
        this.showMaintenanceListDialog = true;
        this.listType = value;
      });
      emitter.on("open-refuel-dialog", () => {
        this.showRefuelDialog = true;
      });
      emitter.on("open-secure-dialog", (obj) => {
        this[obj.key] = true;
        this.secureSize = obj.size;
      });
      emitter.on("open-abnormal-dialog", (value) => {
        this[value] = true;
      });
      emitter.on("open-refuelRank-dialog", () => {
        this.showRefuelRankDialog = true;
      });
      emitter.on("open-certificate-dialog", (value) => {
        this.showCertificateDialog = true;
        this.certType = value;
      });
    },
    beforeDestroy() {
      emitter.off("open-maintenance-dialog");
      emitter.off("open-maintenanceList-dialog");
      emitter.off("open-refuel-dialog");
      emitter.off("open-secure-dialog");
      emitter.off("open-abnormal-dialog");
      emitter.off("open-refuelRank-dialog");
      emitter.off("open-certificate-dialog");
      this.setOncloseMessage();
    },
    methods: {
      // 获取渠道列表
      async getChannelList() {
        let res = await getInfoApiFun(this.$DNACONFIG.dna, this.apis.channelList);
        this.channelList = this.channelList.concat(res.data);
        if (this.channel) {
          this.channelId = this.channel;
        } else {
          this.channelId = this.channelList[0].id;
        }
      },
      toggleChannel() {
        this.initData();
        this.$nextTick(() => {
          this.$refs.mapChartsRef.initCharts();
        });
        let socketData = [
          {
            propName: "getVehicleSafety",
            params: [this.channelId],
          },
          {
            propName: "getVehicleAbnormal",
            params: [this.channelId],
          },
          {
            propName: "getVehicleFuel",
            params: [this.channelId],
          },
          {
            propName: "getVehicleCompliance",
            params: [this.channelId],
          },
          {
            propName: "getCardManager",
            params: [this.channelId],
          },
          {
            propName: "getVehicleMaintenance",
            params: [this.channelId],
          },
        ];
        this.sendSocketMessage(socketData);
      },
      async initData() {
        try {
          const res = await Promise.allSettled([
            getInfoApiFunByParams({ channelId: this.channelId }, "/api/query/vehicle/getCardManager"),
            getInfoApiFunByParams({ channelId: this.channelId }, "/api/query/vehicle/getVehicleMaintenance"),
            getInfoApiFunByParams({ channelId: this.channelId }, "/api/query/vehicle/getVehicleCompliance"),
            getInfoApiFunByParams({ channelId: this.channelId }, "/api/query/vehicle/getVehicleFuel"),
            getInfoApiFunByParams({ channelId: this.channelId }, "/api/query/vehicle/getVehicleAbnormal"),
            getInfoApiFunByParams({ channelId: this.channelId }, "/api/query/vehicle/getVehicleSafety"),
            vehicleAreaStaticsApi({ channelId: this.channelId }),
          ]);
          if (res[0].status == "fulfilled") {
            this.formData.getCardManager = res[0]?.value?.data;
          }
          if (res[1].status == "fulfilled") {
            this.formData.getVehicleMaintenance = res[1]?.value?.data;
          }
          if (res[2].status == "fulfilled") {
            this.formData.getVehicleCompliance = res[2]?.value?.data;
          }
          if (res[3].status == "fulfilled") {
            this.formData.getVehicleFuel = res[3]?.value?.data;
          }
          if (res[4].status == "fulfilled") {
            this.formData.getVehicleAbnormal = res[4]?.value?.data;
          }
          if (res[5].status == "fulfilled") {
            this.formData.getVehicleSafety = res[5]?.value?.data;
          }
          if (res[6].status == "fulfilled") {
            this.formData.areaStatics = res[6]?.value?.data;
            this.additiveLabel.formatter = this.mapAddFormatter;
          }
          this.completed = true;
        } catch (error) {
          this.completed = true;
        }
      },
      mapAddFormatter(params) {
        let info = this.formData.areaStatics.filter((i) => i.areaName == params.name);
        let arr = [params.name, "分配车辆     " + (info && info.length ? info[0].vehicleQuantity : 0)];
        return arr.join("\n");
      },
      handleSwitch(flag) {
        this.currIndex = flag;
        this.getVehicleEventStatics();
      },
      async getVehicleEventStatics() {
        try {
          let res = await vehicleEventStaticsApi({ type: this.currIndex, channelId: this.channelId });
          this.formData.eventStatics = res.data;
        } catch (error) {
          console.warn(error);
        }
      },
      // 打开路线弹窗
      openRouteDialog(params) {
        if (this.showRouteDialog) return;
        this.districtName = params.name;
        this.showRouteDialog = true;
      },
      // websocket接收消息
      async setOnmessageMessage(res) {
        if (res.data === "token过期") {
          this.setOncloseMessage();
          await this.$store.dispatch("refreshAuthToken");
          let token = await window.LOGAN.getToken();
          this.initWebSocket(this.websocketUrl + `&token=${token}`);
          let socketData = [
            {
              propName: "getVehicleSafety",
              params: [this.channelId],
            },
            {
              propName: "getVehicleAbnormal",
              params: [this.channelId],
            },
            {
              propName: "getVehicleFuel",
              params: [this.channelId],
            },
            {
              propName: "getVehicleCompliance",
              params: [this.channelId],
            },
            {
              propName: "getCardManager",
              params: [this.channelId],
            },
            {
              propName: "getVehicleMaintenance",
              params: [this.channelId],
            },
          ];
          this.sendSocketMessage(socketData);
          return;
        }
        let rsp = "";
        try {
          rsp = JSON.parse(res.data);
        } catch (error) {
          rsp = "";
        }
        if (rsp) {
          for (let key in rsp) {
            switch (key) {
              case "areaStatics":
                this.formData[key] = rsp[key];
                this.additiveLabel.formatter = this.mapAddFormatter;
                this.additiveLabel.nowTime = Date.now();
                break;
              default:
                this.formData[key] = rsp[key];
                break;
            }
          }
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .bg-img {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-repeat: no-repeat;
    position: absolute;
    z-index: 0;
  }
  .map-chart {
    position: absolute;
    top: 200px;
    left: 450px;
    z-index: 1;
  }
  .left {
    position: absolute;
    top: 130px;
    left: 40px;
    z-index: 1;
  }
  .right {
    position: absolute;
    top: 130px;
    right: 40px;
    z-index: 1;
  }
</style>
