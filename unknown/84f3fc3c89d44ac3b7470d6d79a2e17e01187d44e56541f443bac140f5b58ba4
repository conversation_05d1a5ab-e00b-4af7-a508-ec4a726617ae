<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">查看对话</div>
      <baseTitle title="对话记录"></baseTitle>
      <el-table :data="dialogueList" :header-cell-style="{ background: '#F5F7F9' }" border>
        <el-table-column prop="status" label="状态" align="center">
          <template #default="{ row }">
            <el-tag :type="statusType[row.status]" effect="dark">{{ DIALOGUE_STATUS[row.status] }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="dialogueDate" label="日期" align="center"></el-table-column>
        <el-table-column prop="dialogueContent" label="对话内容" align="center"></el-table-column>
        <el-table-column prop="userName" label="对话人" align="center"></el-table-column>
        <el-table-column prop="contactNumber" label="对话人联系方式" align="center"></el-table-column>
        <el-table-column min-width="80" label="操作" align="center">
          <template #default="{ row }">
            <el-link type="primary" @click="openReply(row, 0)" v-if="row.status == 0">回复</el-link>
            <el-link type="primary" @click="openReply(row, 1)" v-else>查看</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
    </div>
    <ReplyDialog
      :value.sync="showReplyDialog"
      :replyRecord="replyRecord"
      :replyType="replyType"
      @refreshList="getRecord"
    ></ReplyDialog>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";
  import { getInfoApiFun } from "@/api/base";
  import ReplyDialog from "./replyDialog";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      baseTitle,
      ReplyDialog,
    },
    data() {
      return {
        dialogueList: [],
        rules: {},
        apis: {
          info: "/api/serviceEvaluationRecord/get/",
        },
        loading: false,
        showReplyDialog: false,
        replyRecord: {},
        DIALOGUE_STATUS: ["待回复", "已回复"],
        statusType: ["warning", ""],
        replyType: 0,
      };
    },
    async created() {
      if (this.recordId) {
        await this.getRecord();
      }
    },
    methods: {
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.dialogueList = res.data.dialogueList;
        }
      },
      // 打开回复弹窗
      openReply(row, type) {
        this.showReplyDialog = true;
        this.replyRecord = row;
        this.replyType = type;
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .type-card {
    margin-bottom: 20px;
  }
  .w-300 {
    width: 300px;
  }
  ::v-deep .question-item .el-form-item__label {
    text-align: left;
  }
  // 测试反馈 字数限制提示挡住内容
  ::v-deep .el-textarea__inner {
    padding-right: 40px !important;
  }
  ::v-deep .el-rate {
    height: 36px;
    display: flex;
    align-items: center;
  }
</style>
