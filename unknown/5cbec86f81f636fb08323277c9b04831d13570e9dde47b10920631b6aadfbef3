<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="210px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="用户名" prop="userName">
              <el-input
                :value="ruleForm.phone"
                placeholder="请输入用户名"
                clearable
                :maxlength="13"
                show-word-limit
                disabled
                v-if="recordId && !ruleForm.userName"
              ></el-input>
              <el-input
                v-model="ruleForm.userName"
                placeholder="请输入用户名"
                clearable
                :maxlength="13"
                show-word-limit
                :disabled="recordId ? true : false"
                v-else
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="真实姓名" prop="fullName">
              <el-input
                v-model="ruleForm.fullName"
                placeholder="请输入真实姓名"
                clearable
                :maxlength="14"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="ruleForm.email" placeholder="请输入邮箱" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="性别" prop="sex">
              <el-select v-model="ruleForm.sex" placeholder="请选择性别" clearable filterable>
                <el-option v-for="(item, index) in SEX_OPTIONS" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="用户身份" prop="userIdentity">
              <el-select
                class="w-300"
                v-model="ruleForm.userIdentity"
                placeholder="请选择用户身份"
                clearable
                filterable
                multiple
                @change="changeIdentity"
              >
                <el-option v-for="(item, index) in USER_IDENTITY" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input
                v-model="ruleForm.phone"
                placeholder="请输入联系电话"
                clearable
                :maxlength="11"
                show-word-limit
                :disabled="recordId ? true : false"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="工号" prop="jobNo">
              <el-input
                v-model="ruleForm.jobNo"
                placeholder="请输入工号"
                clearable
                maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="身份证号码" prop="idCard">
              <el-input
                v-model="ruleForm.idCard"
                placeholder="请输入身份证号码"
                clearable
                maxlength="18"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="用户类型" prop="userType">
              <el-select
                v-model="ruleForm.userType"
                placeholder="请选择用户类型"
                clearable
                filterable
                :disabled="ruleForm.userIdentity.includes(3) || ruleForm.userIdentity.includes(4)"
                @change="changeUserType"
              >
                <el-option v-for="(item, index) in USER_TYPE" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="所属公司" prop="company">
              <el-select
                class="w-300"
                v-model="ruleForm.company"
                placeholder="请选择所属公司"
                clearable
                filterable
                @change="changeCompany"
              >
                <el-option
                  v-for="item in companyOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <template v-if="recordId">
            <el-col :md="24" :lg="12">
              <el-form-item label="所属部门" prop="deptId">
                <el-cascader
                  ref="cascaderRef"
                  v-model="ruleForm.deptId"
                  placeholder="请选择所属部门"
                  filterable
                  clearable
                  :options="deptOptions"
                  :props="deptProps"
                  :show-all-levels="false"
                  :disabled="true"
                  @change="changeDept"
                ></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="部门负责人" prop="deptHeadName">
                <el-input
                  v-model="ruleForm.deptHeadName"
                  placeholder="请输入部门负责人"
                  clearable
                  readonly
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="直属上级" prop="directSuperior">
                <el-select
                  v-model="ruleForm.directSuperior"
                  placeholder="请选择直属上级"
                  clearable
                  filterable
                  :disabled="true"
                >
                  <el-option
                    v-for="item in userOptions"
                    :key="item.lgUnionId"
                    :label="item.fullName"
                    :value="item.lgUnionId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </template>
          <el-col :md="24" :lg="12">
            <el-form-item label="职位" prop="position">
              <el-select v-model="ruleForm.position" placeholder="请选择职位" clearable filterable>
                <el-option
                  v-for="item in positionOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="在职状态" prop="jobStatus">
              <el-select v-model="ruleForm.jobStatus" placeholder="请选择在职状态" clearable filterable>
                <el-option v-for="(item, index) in JOB_STATUS" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="用户头像" prop="avatar">
              <FileUpload @uploadChange="uploadChangeFileList" :imageList="imageList" :limit="1">
                <template #tips><el-tag type="warning">请上传用户头像</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="账号角色" prop="roleIds">
              <el-transfer
                :titles="['可选账号角色', '已选账号角色']"
                v-model="ruleForm.roleIds"
                :data="roleOptions"
                :button-texts="['删除角色', '添加角色']"
                :props="{
                  key: 'roleId',
                  label: 'roleName',
                }"
              >
              </el-transfer>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="ruleForm.userIdentity.includes(3) || ruleForm.userIdentity.includes(4)">
          <baseTitle title="司机/押运工职业信息"></baseTitle>
          <el-row v-if="ruleForm.userIdentity.includes(3)">
            <el-col :md="24" :lg="12">
              <el-form-item label="默认驾驶车辆" prop="plateNumber">
                <el-select v-model="ruleForm.plateNumber" placeholder="请选择车牌号" clearable filterable>
                  <el-option
                    v-for="item in carOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="准驾车型" prop="quasiDrivingType">
                <el-select v-model="ruleForm.quasiDrivingType" placeholder="请选择准驾车型" clearable filterable>
                  <el-option
                    v-for="(item, index) in QUASI_DRIVING_TYPE"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="驾驶证编号" prop="drivingCertificateNumber">
                <el-input
                  class="w-400"
                  v-model="ruleForm.drivingCertificateNumber"
                  placeholder="请输入驾驶证编号"
                  clearable
                  maxlength="100"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="驾驶证有效期开始日期" prop="drivingCertificateBeginDate">
                <el-date-picker
                  v-model="ruleForm.drivingCertificateBeginDate"
                  type="date"
                  placeholder="选择有效期开始日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="驾驶证有效期结束日期" prop="drivingCertificateEndDate">
                <el-date-picker
                  v-model="ruleForm.drivingCertificateEndDate"
                  type="date"
                  placeholder="选择有效期结束日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="驾驶证登记日期" prop="drivingCertificateRegistrationDate">
                <el-date-picker
                  v-model="ruleForm.drivingCertificateRegistrationDate"
                  type="date"
                  placeholder="选择登记日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="驾驶证主页照片" prop="drivingCertificatePhotoFront">
                <FileUpload
                  @uploadChange="handleUploadChange($event, 'drivingCertificatePhotoFront')"
                  :imageList="photoImageList['drivingCertificatePhotoFront']"
                ></FileUpload>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="驾驶证副页照片" prop="drivingCertificatePhotoBack">
                <FileUpload
                  @uploadChange="handleUploadChange($event, 'drivingCertificatePhotoBack')"
                  :imageList="photoImageList['drivingCertificatePhotoBack']"
                ></FileUpload>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="从业资格证编号" prop="qualificationCertificateNumber">
                <el-input
                  class="w-400"
                  v-model="ruleForm.qualificationCertificateNumber"
                  placeholder="请输入从业资格证编号"
                  clearable
                  maxlength="100"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="从业资格证有效期开始日期" prop="qualificationCertificateBeginDate">
                <el-date-picker
                  v-model="ruleForm.qualificationCertificateBeginDate"
                  type="date"
                  placeholder="选择有效期开始日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="从业资格证有效期结束日期" prop="qualificationCertificateEndDate">
                <el-date-picker
                  v-model="ruleForm.qualificationCertificateEndDate"
                  type="date"
                  placeholder="选择有效期结束日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="24">
              <el-form-item label="从业资格证登记日期" prop="qualificationCertificateRegistrationDate">
                <el-date-picker
                  v-model="ruleForm.qualificationCertificateRegistrationDate"
                  type="date"
                  placeholder="选择登记日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="从业资格证主页照片" prop="qualificationCertificatePhotoFront">
                <FileUpload
                  @uploadChange="handleUploadChange($event, 'qualificationCertificatePhotoFront')"
                  :imageList="photoImageList['qualificationCertificatePhotoFront']"
                ></FileUpload>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="从业资格证副页照片" prop="qualificationCertificatePhotoBack">
                <FileUpload
                  @uploadChange="handleUploadChange($event, 'qualificationCertificatePhotoBack')"
                  :imageList="photoImageList['qualificationCertificatePhotoBack']"
                ></FileUpload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-else>
            <el-col :md="24" :lg="12">
              <el-form-item label="默认押运车辆" prop="plateNumber">
                <el-select v-model="ruleForm.plateNumber" placeholder="请选择车牌号" clearable filterable>
                  <el-option
                    v-for="item in carOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <template v-if="ruleForm.userIdentity.includes(3)">
              <el-col :md="24" :lg="24">
                <el-form-item label="从业资格证编号" prop="qualificationCertificateNumber">
                  <el-input
                    class="w-400"
                    v-model="ruleForm.qualificationCertificateNumber"
                    placeholder="请输入从业资格证编号"
                    clearable
                    maxlength="100"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="12">
                <el-form-item label="从业资格证有效期开始日期" prop="qualificationCertificateBeginDate">
                  <el-date-picker
                    v-model="ruleForm.qualificationCertificateBeginDate"
                    type="date"
                    placeholder="选择有效期开始日期"
                    clearable
                    value-format="yyyy-MM-dd"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="12">
                <el-form-item label="从业资格证有效期结束日期" prop="qualificationCertificateEndDate">
                  <el-date-picker
                    v-model="ruleForm.qualificationCertificateEndDate"
                    type="date"
                    placeholder="选择有效期结束日期"
                    clearable
                    value-format="yyyy-MM-dd"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </template>
            <template v-else>
              <el-col :md="24" :lg="24">
                <el-form-item label="从业资格证编号">
                  <el-input
                    class="w-400"
                    v-model="ruleForm.qualificationCertificateNumber"
                    placeholder="请输入从业资格证编号"
                    clearable
                    maxlength="100"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="12">
                <el-form-item label="从业资格证有效期开始日期">
                  <el-date-picker
                    v-model="ruleForm.qualificationCertificateBeginDate"
                    type="date"
                    placeholder="选择有效期开始日期"
                    clearable
                    value-format="yyyy-MM-dd"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="12">
                <el-form-item label="从业资格证有效期结束日期">
                  <el-date-picker
                    v-model="ruleForm.qualificationCertificateEndDate"
                    type="date"
                    placeholder="选择有效期结束日期"
                    clearable
                    value-format="yyyy-MM-dd"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </template>
            <el-col :md="24" :lg="24">
              <el-form-item label="从业资格证登记日期" prop="qualificationCertificateRegistrationDate">
                <el-date-picker
                  v-model="ruleForm.qualificationCertificateRegistrationDate"
                  type="date"
                  placeholder="选择登记日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="从业资格证主页照片" prop="qualificationCertificatePhotoFront">
                <FileUpload
                  @uploadChange="handleUploadChange($event, 'qualificationCertificatePhotoFront')"
                  :imageList="photoImageList['qualificationCertificatePhotoFront']"
                ></FileUpload>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="从业资格证副页照片" prop="qualificationCertificatePhotoBack">
                <FileUpload
                  @uploadChange="handleUploadChange($event, 'qualificationCertificatePhotoBack')"
                  :imageList="photoImageList['qualificationCertificatePhotoBack']"
                ></FileUpload>
              </el-form-item>
            </el-col>
          </el-row>
        </template>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import { validPhone } from "@/utils/validate";
  import { SEX_OPTIONS, USER_TYPE, JOB_STATUS, USER_IDENTITY, QUASI_DRIVING_TYPE } from "@/enums";
  import { getInfoApiFun, createApiFun, updateApiFun, getListApiFun } from "@/api/base";
  import FileUpload from "@/components/FileUpload";
  import baseTitle from "@/components/baseTitle";
  import { deepClone } from "logan-common/utils";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
      positionOptions: {
        type: Array,
        default: () => [],
      },
      roleOptions: {
        type: Array,
        default: () => [],
      },
    },
    components: {
      FileUpload,
      baseTitle,
    },
    data() {
      return {
        ruleForm: {
          userName: "", //用户名
          fullName: "", //真实姓名
          phone: "", //手机号码
          email: "", //邮箱
          sex: "", //性别
          jobNo: "", //工号
          userIdentity: [], //用户身份
          deptId: "", //所在部门
          deptHeadName: "", //部门负责人
          directSuperior: "", //直属上级
          position: "", //职位
          company: "", //所属公司
          userType: "", //用户类型
          jobStatus: "", //在职状态
          idCard: "", //身份证号码
          avatar: "", //用户头像
          roleIds: [], //账号角色
          plateNumber: "", //车牌号
          // 司机/押运工表单项
          drivingCertificateNumber: "", //驾驶证编号
          quasiDrivingType: "", //准驾车型
          drivingCertificateBeginDate: "", //驾驶证有效期开始时间
          drivingCertificateEndDate: "", //驾驶证有效期结束时间
          drivingCertificateRegistrationDate: "", //驾驶证登记日期
          drivingCertificatePhotoFront: null, //驾驶证主页照片
          drivingCertificatePhotoBack: null, //驾驶证副页照片
          qualificationCertificateNumber: "", //从业资格证编号
          qualificationCertificateBeginDate: "", //从业资格证有效期开始时间
          qualificationCertificateEndDate: "", //从业资格证有效期结束时间
          qualificationCertificateRegistrationDate: "", //从业资格证登记日期
          qualificationCertificatePhotoFront: null, //从业资格证主页照片
          qualificationCertificatePhotoBack: null, //从业资格证副页照片
        },
        rules: {
          fullName: [
            { required: true, message: "请输入真实姓名", trigger: "blur" },
            { min: 2, max: 14, message: "长度在 2 到 14 个字符", trigger: "blur" },
          ],
          phone: [
            {
              required: true,
              validator: this.validatePhone,
              trigger: "blur",
            },
          ],
          jobNo: [{ required: true, message: "请输入工号", trigger: "blur" }],
          sex: [{ required: true, message: "请选择性别", trigger: "change" }],
          userIdentity: [{ required: true, message: "请选择用户身份", trigger: "change" }],
          userType: [{ required: true, message: "请选择用户类型", trigger: "change" }],
          jobStatus: [{ required: true, message: "请选择在职状态", trigger: "change" }],
          // 司机/押运工表单项
          plateNumber: [{ required: true, message: "请选择车牌号", trigger: "change" }],
          quasiDrivingType: [{ required: true, message: "请选择准驾车型", trigger: "change" }],
          drivingCertificateNumber: [{ required: true, message: "请输入驾驶证编号", trigger: "blur" }],
          drivingCertificateBeginDate: [{ required: true, message: "请选择驾驶证有效期开始日期", trigger: "blur" }],
          drivingCertificateEndDate: [{ required: true, message: "请选择驾驶证有效期结束日期", trigger: "blur" }],
          qualificationCertificateNumber: [{ required: true, message: "请输入从业资格证编号", trigger: "blur" }],
          qualificationCertificateBeginDate: [
            { required: true, message: "请选择从业资格证有效期开始日期", trigger: "change" },
          ],
          qualificationCertificateEndDate: [
            { required: true, message: "请选择从业资格证有效期结束日期", trigger: "change" },
          ],
          photoImageList: {
            drivingCertificatePhotoFront: [],
            drivingCertificatePhotoBack: [],
            qualificationCertificatePhotoFront: [],
            qualificationCertificatePhotoBack: [],
          },
        },
        SEX_OPTIONS,
        USER_TYPE,
        JOB_STATUS,
        USER_IDENTITY,
        QUASI_DRIVING_TYPE,
        userOptions: [], //用户列表
        apis: {
          userList: "/api/baseuser/list",
          create: "/api/baseuser/create",
          update: "/api/baseuser/update",
          info: "/api/baseuser/get/",
          companyList: "/api/company/list",
          enableDeptList: "/api/company/structure/findQy",
          carList: "/api/vehicle/dossier/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [],
        companyList: [],
        deptOptions: [],
        deptProps: {
          checkStrictly: true,
          value: "id",
          label: "name",
          emitPath: false,
        },
        carOptions: [],
        photoImageList: {
          drivingCertificatePhotoFront: [],
          drivingCertificatePhotoBack: [],
          qualificationCertificatePhotoFront: [],
          qualificationCertificatePhotoBack: [],
        },
      };
    },
    computed: {
      companyOptions() {
        let options = [];
        if (this.ruleForm.userType === 0) {
          options = this.companyList.filter((list) => list.isDefault == 1);
        } else if (this.ruleForm.userType === 1) {
          options = this.companyList.filter((list) => list.isDefault != 1);
        }
        return options;
      },
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptions();
      if (this.recordId) {
        this.getRecord();
      } else {
        this.$set(this.rules, "userName", [
          { required: true, message: "请输入用户名", trigger: "blur" },
          { min: 5, max: 13, message: "长度在 5 到 13 个字符", trigger: "blur" },
        ]);
        this.$nextTick(() => {
          this.$refs.ruleForm.clearValidate();
        });
      }
    },
    methods: {
      validatePhone(rule, value, callback) {
        if (!value) {
          callback(new Error("请输入联系电话"));
        } else if (!validPhone(value)) {
          callback(new Error("联系电话不正确"));
        } else {
          callback();
        }
      },
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          getListApiFun({}, this.apis.userList),
          getListApiFun({}, this.apis.companyList),
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
        ];
        let res = await Promise.all(promiseList);
        this.userOptions = res[0].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.companyList = res[1].data;
        this.carOptions = res[2].data;
      },
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.ruleForm.userIdentity = this.ruleForm.userIdentity.split(",").map(Number);
          let roleIds = this.ruleForm.roleLists.map((list) => list.roleId);
          this.$set(this.ruleForm, "roleIds", roleIds);
          let imageObj = "";
          try {
            imageObj = JSON.parse(this.ruleForm.avatar);
          } catch (error) {
            imageObj = "";
          }
          if (imageObj) {
            this.imageList = [{ url: imageObj.url, file: imageObj }];
          }
          if (this.ruleForm.company) {
            let rsp = await createApiFun({ companyId: this.ruleForm.company }, this.apis.enableDeptList);
            this.deptOptions = rsp.data;
          }
          this.ruleForm.phone = this.$sm2Decrypt(this.ruleForm.phone);
          if (this.ruleForm.idCard) {
            this.ruleForm.idCard = this.$sm2Decrypt(this.ruleForm.idCard);
          }
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 选择用户身份
      changeIdentity() {
        if (this.ruleForm.userIdentity.includes(3) || this.ruleForm.userIdentity.includes(4)) {
          this.ruleForm.userType = 0;
        }
      },
      // 选择用户类型
      changeUserType() {
        this.ruleForm.company = "";
        this.ruleForm.deptId = "";
        this.deptOptions = [];
      },
      // 选择所属公司
      async changeCompany(value) {
        this.ruleForm.deptId = "";
        this.deptOptions = [];
        if (!value) {
          return;
        }
        let res = await createApiFun({ companyId: value }, this.apis.enableDeptList);
        this.deptOptions = res.data;
      },
      // 选择部门
      changeDept(value) {
        if (value) {
          this.ruleForm.deptHeadName = this.$refs.cascaderRef.getCheckedNodes()[0].data.chargeName;
        } else {
          this.ruleForm.deptHeadName = "";
        }
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            let params = deepClone(this.ruleForm);
            params.userIdentity = params.userIdentity.join(",");
            try {
              let res = this.recordId
                ? await updateApiFun(this.$sm2Encrypt(JSON.stringify(params)), this.apis.update)
                : await createApiFun(this.$sm2Encrypt(JSON.stringify(params)), this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}用户成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      uploadChangeFileList(fileList) {
        if (fileList.length > 0) {
          this.ruleForm.avatar = JSON.stringify({
            name: fileList[0].name,
            size: fileList[0].size,
            ...fileList[0].file,
          });
        } else {
          this.ruleForm.avatar = "";
        }
      },
      // 司机附件change事件
      handleUploadChange(fileList, field) {
        if (fileList.length && fileList[0].file) {
          this.ruleForm[field] = { name: fileList[0].name, size: fileList[0].size, ...fileList[0].file };
        } else {
          this.ruleForm[field] = null;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .w-300 {
    width: 300px;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
    min-width: 240px;
  }
  ::v-deep .el-transfer-panel__body {
    height: 400px;
  }
  ::v-deep .el-transfer-panel__list {
    height: 100%;
  }
</style>
