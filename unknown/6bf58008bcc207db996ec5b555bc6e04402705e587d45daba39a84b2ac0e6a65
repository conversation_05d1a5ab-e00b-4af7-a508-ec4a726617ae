<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <record
        v-if="showRecord"
        :pageFlag="pageFlag"
        :recordId="recordId"
        @closeRecord="closeRecord"
        @refreshList="initData"
      ></record>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="customerName" placeholder="请输入客商名称" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" @click="exportFileThrottling">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
            :colNum="12"
          ></InterviewChannel>
          <el-col :span="12">
            <el-form-item label="投诉日期范围">
              <el-date-picker
                v-model="filterForm.complaintDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理情况">
              <el-select v-model="filterForm.handlingStatus" placeholder="请选择处理情况" clearable filterable>
                <el-option
                  v-for="(item, index) in HANDLING_STATUS"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="投诉类型">
              <el-select v-model="filterForm.type" placeholder="请选择投诉类型" clearable filterable>
                <el-option
                  v-for="(item, index) in COMPLAINT_TYPE"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column prop="customerName" label="客商名称" align="center"></el-table-column>
            <el-table-column prop="complaintDate" label="投诉日期" align="center"></el-table-column>
            <el-table-column prop="type" label="投诉类型" align="center">
              <template #default="{ row }">{{ COMPLAINT_TYPE[row.type] }}</template>
            </el-table-column>
            <el-table-column prop="handlingStatus" label="处理情况" align="center">
              <template #default="{ row }">{{ HANDLING_STATUS[row.handlingStatus] }}</template>
            </el-table-column>
            <el-table-column prop="handlingDate" label="处理日期" align="center"></el-table-column>
            <el-table-column prop="handlingPerson" label="处理人" align="center"></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="80" label="操作" align="center">
              <template #default="{ row }">
                <el-link v-if="row.handlingStatus == 0" class="mr-10" type="primary" @click="editRecord(row)"
                  >去处理</el-link
                >
                <el-link type="primary" @click="checkRecord(row)">详情</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, deleteApiFun, BASE_API_URL } from "@/api/base";
  import record from "./components/record.vue";
  import { HANDLING_STATUS, COMPLAINT_TYPE } from "@/enums";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
    },
    data() {
      return {
        pageFlag: "list",
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/complaintRecord/listPage",
          delete: "/api/complaintRecord/delete/",
          export: "/api/complaintRecord/export",
        },
        showRecord: false,
        recordId: "",
        HANDLING_STATUS,
        COMPLAINT_TYPE,
        loading: false,
        exportFileThrottling: null,
        showFilter: false,
        customerName: "",
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    created() {
      this.exportFileThrottling = this.$throttling(this.handleExport, 500);
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        this.loading = true;
        try {
          let params = {
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            customerName: this.customerName,
            complaintBeginTime: this.filterForm.complaintDate ? this.filterForm.complaintDate[0] : "",
            complaintEndTime: this.filterForm.complaintDate ? this.filterForm.complaintDate[1] : "",
            handlingStatus: this.filterForm.handlingStatus,
            type: this.filterForm.type,
            channelId: this.filterForm.channelId || "",
          };
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 编辑
      editRecord(row) {
        this.pageFlag = "edit";
        this.showRecord = true;
        this.recordId = row.id;
      },
      // 详情
      checkRecord(row) {
        this.pageFlag = "check";
        this.showRecord = true;
        this.recordId = row.id;
      },
      closeRecord() {
        this.pageFlag = "list";
        this.showRecord = false;
      },
      // 删除
      async deleteRecord(row) {
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            this.page.pageNo = this.pageDropProcess(this.page.pageNo, this.page.pageSize, this.page.total);
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.customerName = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 导出
      handleExport() {
        this.$confirm(`确认是否以当前筛选条件导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, {
                customerName: this.customerName,
                complaintBeginTime: this.filterForm.complaintDate ? this.filterForm.complaintDate[0] : "",
                complaintEndTime: this.filterForm.complaintDate ? this.filterForm.complaintDate[1] : "",
                handlingStatus: this.filterForm.handlingStatus,
                type: this.filterForm.type,
                channelId: this.filterForm.channelId || "",
              });
              if (res.success) {
                createDownloadEvent(`投诉记录${Date.now()}.xlsx`, [res.data]);
                window.ELEMENT.Message.success("导出成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
