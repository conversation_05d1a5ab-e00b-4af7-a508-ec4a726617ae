<template>
  <div class="count-container">
    <div class="count-box">
      <div class="count-value">0</div>
    </div>
    <div class="count-box">
      <div class="count-value">0</div>
    </div>
    <div class="count-box">
      <div class="count-value">0</div>
    </div>
    <div class="count-comma">,</div>
    <div class="count-box">
      <div class="count-value">0</div>
    </div>
    <div class="count-box">
      <div class="count-value">4</div>
    </div>
    <div class="count-box">
      <div class="count-value">9</div>
    </div>
    <div class="count-comma">,</div>
    <div class="count-box">
      <div class="count-value">3</div>
    </div>
    <div class="count-box">
      <div class="count-value">9</div>
    </div>
    <div class="count-box">
      <div class="count-value">0</div>
    </div>
    <div class="count-unit">（kg）</div>
  </div>
</template>

<script>
  export default {};
</script>

<style lang="scss" scoped>
  .count-container {
    display: flex;
    align-items: flex-end;
    margin: 10px 0;
  }
  .count-box {
    border: 1px solid #11e48a;
    background-color: rgba(17, 228, 138, 0.2);
    margin-right: 4px;
    border-radius: 4px;
    .count-value {
      font-size: 50px;
      font-weight: bold;
      color: #fff;
      padding: 0 4px;
      line-height: 54px;
    }
  }
  .count-comma {
    font-size: 30px;
    font-weight: bold;
    color: #dfdfdf;
    margin-right: 4px;
  }
  .count-unit {
    font-style: 20px;
    font-weight: bold;
    color: #fff;
  }
</style>
