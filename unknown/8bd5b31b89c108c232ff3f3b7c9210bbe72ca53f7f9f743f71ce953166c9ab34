<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">混合绩效</div>
      <baseTitle title="基础信息"></baseTitle>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="150px" label-suffix="：">
        <el-row>
          <el-col>
            <el-form-item label="绩效方案名称" required>
              <el-input :value="PERFORMANCE_RULE[ruleIndex]" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <baseTitle title="绩效规则信息"></baseTitle>
          <el-col :md="12">
            <el-form-item label="绩效计费类型" required>
              <el-input value="点位计费" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="12">
            <el-form-item label="收运方式" required>
              <el-input value="桶装/袋装" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="规则类型">
              <el-input value="混合规则" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="绩效计算规则">
              <div class="item-box">
                <el-input value="小型床位医院" placeholder="" reanonly></el-input
                >&nbsp;&nbsp;&nbsp;&nbsp;+&nbsp;&nbsp;&nbsp;&nbsp;
                <el-input :value="ruleObj[ruleIndex]" placeholder="" reanonly></el-input>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
    </div>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";
  import { PERFORMANCE_RULE } from "@/enums";
  export default {
    components: {
      baseTitle,
    },
    props: {
      ruleCode: {
        type: String,
        default: "",
      },
      ruleIndex: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        loading: false,
        ruleForm: {},
        rules: {},
        apis: {},
        ruleObj: {
          5: "诊所组绩效规则",
          6: "诊所组（南沙）绩效规则",
          7: "特殊诊所组绩效规则",
        },
        PERFORMANCE_RULE,
      };
    },
    mounted() {},
    methods: {
      // 取消
      closeRecord() {
        this.$emit("closeRecord");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-data {
    margin-bottom: 20px;
  }
  .item-box {
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
</style>
