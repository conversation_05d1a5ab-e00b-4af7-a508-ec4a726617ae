<template>
  <div>
    <el-dialog title="移动到" :visible.sync="dialogVisible" width="40%" :close-on-click-modal="false">
      <div v-loading="loading">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item v-for="(item, index) in breadList" :key="index">
            <template v-if="index < breadList.length - 1">
              <a @click="jumpAnotherFolder(item, index)">{{ item.name }}</a>
            </template>
            <template v-else>
              {{ item.name }}
            </template>
          </el-breadcrumb-item>
        </el-breadcrumb>
        <ul class="scroll-box small-scroll">
          <li
            class="folder-item flex-center-start"
            v-for="(item, index) in folderList"
            :key="index"
            @click="handleClick(item)"
          >
            <img class="icon-img" :src="require('@/assets/icons/folder.png')" alt="" />
            <div>{{ item.name }}</div>
          </li>
        </ul>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="moveFolderThrottling">移动到此</el-button>
          <el-button @click="dialogVisible = false">取 消</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { createApiFun, updateApiFun } from "@/api/base";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      folderItem: {
        type: Object,
        default: () => {},
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    watch: {
      value(newV) {
        if (newV) {
          this.getFolderList();
        } else {
          this.parentId = "";
        }
      },
    },
    data() {
      return {
        parentId: "",
        loading: false,
        moveFolderThrottling: () => {},
        apis: {
          list: "/api/document/folder/list",
          update: "/api/document/folder/update",
        },
        folderList: [],
        breadList: [{ id: "", name: "全部文件" }],
      };
    },
    created() {
      this.moveFolderThrottling = this.$throttling(this.moveFolder, 500);
    },
    methods: {
      // 获取所有文件夹列表
      async getFolderList() {
        this.loading = true;
        try {
          let res = await createApiFun({ parentId: this.parentId, type: 0 }, this.apis.list);
          if (res.success) {
            this.folderList = res.data;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      // 面包屑跳转回调事件
      jumpAnotherFolder(item, index) {
        this.parentId = item.id;
        this.breadList.splice(index + 1);
        this.getFolderList();
      },
      handleClick(item) {
        this.parentId = item.id;
        this.breadList.push({ id: item.id, name: item.name });
        this.getFolderList();
      },
      // 移动文件/文件夹
      async moveFolder() {
        this.loading = true;
        let params = this.folderItem;
        params.parentId = this.parentId;
        try {
          let res = await updateApiFun(params, this.apis.update);
          if (res.success) {
            this.dialogVisible = false;
            this.$message.success("移动成功");
            this.$emit("refreshList");
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .scroll-box {
    margin-top: 20px;
    height: 400px;
  }
  .folder-item {
    padding: 10px;
    padding-left: 0;
    cursor: pointer;
    &:hover {
      background-color: #f7f9fc;
    }
  }
  .icon-img {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    overflow: hidden;
  }
</style>
