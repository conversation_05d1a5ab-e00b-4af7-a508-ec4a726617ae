<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <createRecord v-if="showCreate" @closeRecord="closeRecord" @refreshList="initData"></createRecord>
      <updateRecord
        v-else-if="showUpdate"
        :recordItem="recordItem"
        @closeRecord="closeRecord"
        @refreshList="initData"
      ></updateRecord>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="keyword" placeholder="请输入车牌号/司机姓名" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
            <el-dropdown
              size="small"
              trigger="click"
              @command="handleDataImport"
              class="dropdown-button"
              placement="bottom-start"
            >
              <el-button size="small" type="primary">
                <span class="sctmp-iconfont icon-ic_daoru"></span>
                <span class="ml-5">导入</span>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">
                  <div class="has-padding-sm has-border-bottom">导入基础信息</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button size="small" type="primary" @click="handleExport">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
            :colNum="12"
          ></InterviewChannel>
          <el-col :span="12">
            <el-form-item label="自检日期范围">
              <el-date-picker
                v-model="filterForm.inspectTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column prop="plateNumber" label="车牌号" align="center"></el-table-column>
            <el-table-column prop="driverName" label="驾驶司机" align="center"></el-table-column>
            <el-table-column prop="inspectDate" label="自检日期" align="center"></el-table-column>
            <el-table-column label="安全自检信息" align="center">
              <el-table-column prop="beforeTime" label="出车前检时间" align="center"></el-table-column>
              <el-table-column prop="beforeStatus" label="出车前检状态" align="center">
                <template #default="{ row }">{{ EVALUATE_STATUS[row.beforeStatus] }}</template>
              </el-table-column>
              <el-table-column prop="centerTime" label="出车中检时间" align="center"></el-table-column>
              <el-table-column prop="centerStatus" label="出车中检状态" align="center">
                <template #default="{ row }">{{ EVALUATE_STATUS[row.centerStatus] }}</template>
              </el-table-column>
              <el-table-column prop="afterTime" label="出车后检时间" align="center"></el-table-column>
              <el-table-column prop="afterStatus" label="出车后检状态" align="center">
                <template #default="{ row }">{{ EVALUATE_STATUS[row.afterStatus] }}</template>
              </el-table-column>
            </el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="80" label="操作" align="center">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row)">编辑</el-link>
                <el-popconfirm title="确认删除当前安全自检记录？" @confirm="deleteRecord(row)">
                  <el-link type="danger" slot="reference">删除</el-link>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
    <importDialog
      :value.sync="importDialogShow"
      title="安全自检记录"
      :importApi="apis.import"
      :templateApi="apis.template"
      :importDialogType="importDialogType"
      :isTemplateApi="true"
      @importSuccess="searchFilter"
    ></importDialog>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, delApiFun, BASE_API_URL } from "@/api/base";
  import createRecord from "./components/create.vue";
  import updateRecord from "./components/update.vue";
  import { EVALUATE_STATUS, SELFTEST_STATUS } from "@/enums";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import importDialog from "@/components/importDialog";
  export default {
    components: {
      defaultPage,
      Pagination,
      createRecord,
      updateRecord,
      importDialog,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/vehicle/inspect/findGroupListPage",
          delete: "/api/vehicle/inspect/deleteByInspectTime",
          export: "/api/vehicle/inspect/export",
          template: "/api/vehicle/inspect/template",
          import: "/api/vehicle/inspect/import",
        },
        showCreate: false,
        showUpdate: false,
        EVALUATE_STATUS,
        SELFTEST_STATUS,
        importDialogShow: false,
        importDialogType: "",
        recordItem: {},
        showFilter: false,
        keyword: "",
        channelRecord: {},
        loading: false,
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        this.loading = true;
        try {
          let params = {
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            keyword: this.keyword,
            inspectBeginTime: this.filterForm.inspectTime ? this.filterForm.inspectTime[0] : "",
            inspectEndTime: this.filterForm.inspectTime ? this.filterForm.inspectTime[1] : "",
            channelId: this.filterForm.channelId || "",
          };
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showCreate = true;
      },
      // 编辑
      editRecord(row) {
        this.showUpdate = true;
        this.recordItem = row;
      },
      closeRecord() {
        this.showCreate = false;
        this.showUpdate = false;
      },
      // 删除
      async deleteRecord(row) {
        try {
          let res = await delApiFun(
            { plateNumber: row.plateNumber, inspectTime: row.inspectDate },
            this.apis.delete,
            true,
          );
          if (res.success) {
            this.page.pageNo = this.pageDropProcess(this.page.pageNo, this.page.pageSize, this.page.total);
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 导出
      handleExport() {
        this.$confirm(`确认是否以当前筛选条件导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, {
                keyword: this.keyword,
                inspectBeginTime: this.filterForm.inspectTime ? this.filterForm.inspectTime[0] : "",
                inspectEndTime: this.filterForm.inspectTime ? this.filterForm.inspectTime[1] : "",
                channelId: this.filterForm.channelId || "",
              });
              if (res.success) {
                createDownloadEvent(`安全自检记录${Date.now()}.xlsx`, [res.data]);
                window.ELEMENT.Message.success("导出成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
      // 导入
      handleDataImport(command) {
        this.importDialogType = command;
        this.importDialogShow = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
