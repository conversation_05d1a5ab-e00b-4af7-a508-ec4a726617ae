<template>
  <div class="certificate">
    <main-title title="证件管理"></main-title>
    <ul class="certificate-list">
      <li class="certificate-item" v-for="(item, index) in countList" :key="index" @click="itemClick(item)">
        <div class="certificate-item-label">{{ item.label }}</div>
        <div class="certificate-item-num">{{ item.num }}</div>
      </li>
    </ul>
  </div>
</template>

<script>
  import MainTitle from "./MainTitle.vue";
  import emitter from "@/utils/mitt";
  export default {
    components: {
      MainTitle,
    },
    props: {
      getCardManager: {
        type: Object,
        default: () => {},
      },
    },
    watch: {
      getCardManager: {
        handler(newV) {
          this.countList[0].num = newV.driversLicenseCount;
          this.countList[1].num = newV.qualificationCertificateCount;
          this.countList[2].num = newV.drivingLicenseCount;
          this.countList[3].num = newV.operationCertificate;
        },
        deep: true,
        immediate: true,
      },
    },
    data() {
      return {
        countList: [
          {
            label: "驾驶证过期预警",
            num: 0,
            certType: [12],
          },
          {
            label: "从业资格证过期预警",
            num: 0,
            certType: [13, 14],
          },
          {
            label: "行驶证过期预警",
            num: 0,
            certType: [10],
          },
          {
            label: "运营证过期预警",
            num: 0,
            certType: [11],
          },
        ],
      };
    },
    methods: {
      itemClick(item) {
        emitter.emit("open-certificate-dialog", item.certType);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .certificate-list {
    display: grid;
    grid-gap: 4px;
    grid-template-columns: repeat(4, 1fr);
    margin-top: 10px;
    transform: translateX(-16px);
    .certificate-item {
      color: #fff;
      text-align: center;
      cursor: pointer;
      .certificate-item-label {
        font-size: 14px;
      }
      .certificate-item-num {
        font-size: 32px;
        margin-top: 10px;
      }
    }
  }
</style>
