<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="配置扣分项" :visible.sync="dialogVisible" width="60%" destroy-on-close @open="initData">
      <div v-loading="loading">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
          <el-form-item label="" prop="demeritPoints">
            <el-table
              class="table-bottom"
              :data="ruleForm.demeritPoints"
              :header-cell-style="{ background: '#F5F7F9' }"
              border
              max-height="400px"
            >
              <el-table-column prop="type" label="扣分项" align="center">
                <template #default="{ row, $index }">
                  <el-form-item :prop="`demeritPoints.${$index}.type`" :rules="demeritPointsRules.type">
                    <el-select v-model="row.type" placeholder="请选择扣分项" clearable filterable>
                      <el-option
                        v-for="(item, index) in DEDUCTION_ITEM"
                        :key="index"
                        :label="item"
                        :value="index"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="deductSalary" label="扣除绩效工资金额" align="center">
                <template #default="{ row, $index }">
                  <el-form-item :prop="`demeritPoints.${$index}.deductSalary`" :rules="demeritPointsRules.deductSalary">
                    <el-input-number
                      v-model="row.deductSalary"
                      step-strictly
                      :min="0"
                      :max="99999999"
                    ></el-input-number>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="deductScore" label="扣除总分" align="center">
                <template #default="{ row, $index }">
                  <el-form-item :prop="`demeritPoints.${$index}.deductScore`" :rules="demeritPointsRules.deductScore">
                    <el-input-number v-model="row.deductScore" :precision="2" :min="0" :max="100"></el-input-number>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column prop="memo" label="备注" align="center">
                <template #default="{ row }">
                  <el-input
                    v-model="row.memo"
                    placeholder="请输入备注"
                    clearable
                    maxlength="50"
                    show-word-limit
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center">
                <template #default="{ $index }">
                  <el-link type="danger" @click="deleteDeductionItem($index)">删除</el-link>
                </template>
              </el-table-column>
            </el-table>
            <el-button type="primary" icon="el-icon-plus" @click="addDeductionItem">添加扣分项</el-button>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">返回</el-button>
          <el-button type="primary" @click="saveThrottling">完成</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { createApiFun } from "@/api/base";
  import { DEDUCTION_ITEM } from "@/enums";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      assessFormId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        ruleForm: {
          demeritPoints: [],
        },
        rules: {},
        demeritPointsRules: {
          type: [{ required: true, message: "请选择扣分项", trigger: "change" }],
          deductSalary: [{ required: true, message: "请输入扣除绩效工资金额", trigger: "blur" }],
          deductScore: [{ required: true, message: "请输入扣除总分", trigger: "blur" }],
        },
        saveThrottling: () => {},
        loading: false,
        apis: {
          save: "/api/assess/demeritPoint/create",
          info: "/api/assess/demeritPoint/list",
        },
        DEDUCTION_ITEM,
      };
    },
    created() {
      this.saveThrottling = this.$throttling(this.saveRecord, 500);
    },
    methods: {
      async initData() {
        let res = await createApiFun({ assessFormId: this.assessFormId }, this.apis.info);
        if (res.success) {
          this.ruleForm.demeritPoints = res.data;
          this.$nextTick(() => {
            this.$refs.ruleForm.clearValidate();
          });
        }
      },
      // 添加扣分项
      addDeductionItem() {
        this.ruleForm.demeritPoints.push({
          type: "",
          deductSalary: "",
          deductScore: "",
          memo: "",
        });
      },
      // 删除扣分项
      deleteDeductionItem(index) {
        this.ruleForm.demeritPoints.splice(index, 1);
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun({ assessFormId: this.assessFormId, ...this.ruleForm }, this.apis.save);
              if (res.success) {
                this.$message.success(`配置扣分项成功`);
                this.dialogVisible = false;
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .table-bottom {
    margin-bottom: 20px;
  }
</style>
