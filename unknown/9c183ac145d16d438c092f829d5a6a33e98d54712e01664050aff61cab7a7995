<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="8">
            <el-form-item label="车牌号" required>
              <el-select :value="recordItem.plateNumber" placeholder="请选择车牌号" clearable filterable disabled>
                <el-option v-for="item in carOptions" :key="item.id" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="8">
            <el-form-item label="驾驶司机" required>
              <el-select :value="recordItem.driverDossierId" placeholder="请选择驾驶司机" clearable filterable disabled>
                <el-option
                  v-for="item in driverOptions"
                  :key="item.id"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="8">
            <el-form-item label="自检日期" required>
              <el-date-picker
                :value="recordItem.inspectDate"
                type="date"
                placeholder="请选择自检日期"
                clearable
                value-format="yyyy-MM-dd"
                disabled
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-card class="type-card" v-for="(child, childIndex) in ruleForm.dataList" :key="child.id">
            <div slot="header" class="card-header">{{ SELFTEST_TITLE[child.type] }}</div>
            <el-col :md="24" :lg="12">
              <el-form-item label="自检时间" :prop="`dataList[${childIndex}][inspectTime]`" :rules="rules.inspectTime">
                <el-date-picker
                  v-model="child.inspectTime"
                  type="datetime"
                  placeholder="请选择自检时间"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                  :picker-options="pickerOptions"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item
                :label="`${SELFTEST_STATUS[child.type]}明细`"
                :prop="`dataList[${childIndex}][detailList]`"
                :rules="rules.detailList"
              >
                <el-table
                  :data="child.detailList"
                  :header-cell-style="{ background: '#F5F7F9' }"
                  style="width: 100%"
                  border
                >
                  <el-table-column type="index" align="center" label="序号" width="55"></el-table-column>
                  <el-table-column prop="configName" label="评价项目"></el-table-column>
                  <el-table-column prop="status" label="项目状态">
                    <template #default="{ row, $index }">
                      <el-form-item
                        :prop="`dataList[${childIndex}][detailList].${$index}.status`"
                        :rules="detailRules.status"
                      >
                        <el-switch
                          v-model="row.status"
                          active-text="正常"
                          inactive-text="异常"
                          :active-value="0"
                          :inactive-value="1"
                        ></el-switch>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="检查状态" required>
                <el-select v-model="child.status" placeholder="请选择检查状态" clearable filterable disabled>
                  <el-option
                    v-for="(item, index) in EVALUATE_STATUS"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="车辆照片" prop="fileList">
                <FileUpload
                  @uploadChange="uploadChangeFileList($event, childIndex)"
                  :imageList="imageList[childIndex]"
                  :limit="5"
                >
                  <template #tips
                    ><el-tag type="warning">请上传{{ SELFTEST_STATUS[child.type] }}状态时的车辆照片</el-tag></template
                  >
                </FileUpload>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item
                label="是否可以正常收运"
                :prop="`dataList[${childIndex}][influencePickup]`"
                :rules="rules.influencePickup"
                v-if="influenceOptions[childIndex]"
              >
                <el-select
                  v-model="child.influencePickup"
                  placeholder="请选择是否可以正常收运"
                  filterable
                  clearable
                  disabled
                >
                  <el-option
                    v-for="(item, index) in IS_TEMPORARY"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-card>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import FileUpload from "@/components/FileUpload";
  import { SELFTEST_STATUS, SELFTEST_TITLE, EVALUATE_STATUS, IS_TEMPORARY } from "@/enums";
  import { createApiFun, updateApiFun, getListApiFun } from "@/api/base";
  export default {
    props: {
      recordItem: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      FileUpload,
    },
    computed: {
      influenceOptions() {
        let options = [];
        this.ruleForm.dataList.forEach((item) => {
          if (item.detailList?.length > 0) {
            let filterList = item.detailList.filter((list) => list.status === 1);
            options.push(filterList.length > 0 ? true : false);
          }
        });
        return options;
      },
    },
    data() {
      return {
        ruleForm: {
          dataList: [],
        },
        rules: {
          inspectTime: [{ required: true, message: "请选择自检时间", trigger: "change" }],
          detailList: [{ required: true, message: "请填写明细", trigger: "change" }],
          influencePickup: [{ required: true, message: "请选择是否可以正常收运", trigger: "change" }],
        },
        detailRules: {
          status: [{ required: true, message: "请选择项目状态", trigger: "change" }],
        },
        apis: {
          create: "/api/vehicle/inspect/create",
          update: "/api/vehicle/inspect/update",
          info: "/api/vehicle/inspect/listByInspectTime",
          carList: "/api/vehicle/dossier/list",
          userList: "/api/baseuser/list",
          configList: "/api/vehicle/evaluationConfig/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [],
        SELFTEST_STATUS,
        SELFTEST_TITLE,
        EVALUATE_STATUS,
        IS_TEMPORARY,
        carOptions: [],
        driverOptions: [],
        pickerOptions: {
          disabledDate: (time) => time.getTime() > new Date(new Date().setHours(23, 59, 59)),
        },
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    async mounted() {
      this.getOptions();
      this.getRecord();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },
      // 获取用户详情
      async getRecord() {
        let res = await createApiFun(
          { plateNumber: this.recordItem.plateNumber, inspectTime: this.recordItem.inspectDate },
          this.apis.info,
        );
        if (res.success) {
          res.data.forEach((item) => {
            if (item.fileList?.length > 0) {
              this.imageList.push(
                item.fileList.map((list) => {
                  return {
                    url: list.url,
                    file: list,
                  };
                }),
              );
            }
          });
          this.ruleForm.dataList = res.data;
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            let paramsList = this.ruleForm.dataList.map((list, index) => {
              if (!this.influenceOptions[index]) {
                delete list.influencePickup;
              }
              return list;
            });
            let promiseList = paramsList.map((params) => {
              return updateApiFun(params, this.apis.update);
            });
            try {
              let res = await Promise.all(promiseList);
              let successList = res.filter((item) => item.success);
              if (successList.length === promiseList.length) {
                this.$message.success(`修改成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      uploadChangeFileList(fileList, index) {
        if (fileList.length > 0) {
          this.ruleForm.dataList[index].fileList = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
        } else {
          this.ruleForm.dataList[index].fileList = [];
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 16px;
  }
  .type-card {
    width: 100%;
    margin-bottom: 20px;
  }
  .w-300 {
    width: 300px;
  }
</style>
