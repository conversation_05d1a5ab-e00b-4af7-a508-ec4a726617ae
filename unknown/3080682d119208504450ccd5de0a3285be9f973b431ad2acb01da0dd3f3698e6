<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">{{ recordId ? "回访登记表编辑" : "新增回访登记表" }}</div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
        <el-row>
          <el-col :md="8" :lg="8">
            <el-form-item label="登记表名称" prop="name">
              <el-input
                v-model="ruleForm.name"
                placeholder="请输入登记表名称"
                clearable
                maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="回访登记表内容"></baseTitle>
        <el-card>
          <div class="card-box">
            <div class="card-left">1</div>
            <div class="card-right">
              <el-form-item label="题目类型" required>
                <el-select v-model="firstQuestionForm.type" placeholder="请选择题目类型" clearable filterable disabled>
                  <el-option v-for="item in typeOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="题目标题" required>
                <el-input
                  v-model="firstQuestionForm.name"
                  placeholder="请输入题目标题"
                  clearable
                  maxlength="255"
                  show-word-limit
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="选项值" required>
                <div class="option-list">
                  <el-tag
                    :key="tag"
                    v-for="tag in firstQuestionForm.optionList"
                    :disable-transitions="false"
                    size="large"
                    effect="dark"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </el-form-item>
            </div>
          </div>
        </el-card>
        <draggable
          v-model="ruleForm.questions"
          chosenClass="chosen"
          forceFallback="true"
          group="questions"
          animation="1000"
          draggable=".question-card"
          :delay="100"
          handle=".question-card"
        >
          <transition-group>
            <el-card class="question-card" v-for="(item, index) in ruleForm.questions" :key="item.id">
              <div class="card-box">
                <div class="card-left">{{ index + 2 }}</div>
                <div class="card-right">
                  <el-form-item label="题目类型" :prop="`questions.${index}.type`" :rules="questionRules.type">
                    <el-select
                      v-model="ruleForm.questions[index].type"
                      placeholder="请选择题目类型"
                      clearable
                      filterable
                    >
                      <el-option
                        v-for="item in typeOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="题目标题" :prop="`questions.${index}.name`" :rules="questionRules.name">
                    <el-input
                      v-model="ruleForm.questions[index].name"
                      placeholder="请输入题目标题"
                      clearable
                      maxlength="255"
                      show-word-limit
                    ></el-input>
                  </el-form-item>
                  <template v-if="[2, 3].includes(ruleForm.questions[index].type)">
                    <el-form-item
                      label="选项值"
                      :prop="`questions.${index}.optionList`"
                      :rules="questionRules.optionList"
                    >
                      <div class="option-list">
                        <draggable
                          v-model="ruleForm.questions[index].optionList"
                          chosenClass="chosen"
                          forceFallback="true"
                          group="option"
                          animation="1000"
                          draggable=".type-tag"
                        >
                          <transition-group>
                            <el-tag
                              :key="tag"
                              v-for="(tag, tagIndex) in ruleForm.questions[index].optionList"
                              closable
                              :disable-transitions="false"
                              @close="handleClose(index, tagIndex)"
                              size="large"
                              effect="dark"
                              class="type-tag"
                            >
                              {{ tag }}
                            </el-tag>
                          </transition-group>
                        </draggable>
                        <el-input
                          :class="{
                            'input-new-tag': true,
                            'tag-btn-left': ruleForm.questions[index].optionList.length,
                          }"
                          v-if="ruleForm.questions[index].inputVisible"
                          v-model="ruleForm.questions[index].inputValue"
                          :ref="'saveTagInput' + index"
                          size="small"
                          @keyup.enter.native="handleInputConfirm(index)"
                          @blur="handleInputConfirm(index)"
                          placeholder="请输入选项值"
                          clearable
                        >
                        </el-input>
                        <el-button
                          v-else
                          :class="{
                            'button-new-tag': true,
                            'tag-btn-left': ruleForm.questions[index].optionList.length,
                          }"
                          size="small"
                          @click="showInput(index)"
                          type="primary"
                          >+ 新增选项值</el-button
                        >
                      </div>
                    </el-form-item>
                  </template>
                  <el-form-item label="必须回答" :prop="`questions.${index}.required`" :rules="questionRules.required">
                    <el-switch
                      v-model="ruleForm.questions[index].required"
                      active-text="是"
                      inactive-text="否"
                    ></el-switch>
                  </el-form-item>
                </div>
              </div>
              <div class="card-footer" @click="deleteQuestion(index)">删除题目</div>
            </el-card>
          </transition-group>
        </draggable>
        <el-button @click="addQuestion" type="text">+ 增加题目</el-button>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";
  import draggable from "vuedraggable";
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  import { QUESTION_TYPE } from "@/enums";
  import { nanoid } from "nanoid";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      baseTitle,
      draggable,
    },
    data() {
      return {
        QUESTION_TYPE,
        typeOptions: [
          { id: 2, name: "单选题" },
          { id: 3, name: "多选题" },
          { id: 1, name: "主观题" },
        ],
        firstQuestionForm: {
          type: 2,
          name: "您对无害化公司的医疗废物收运处置服务是否满意？",
          optionList: ["非常满意", "满意", "一般", "不满意", "非常不满意"],
          inputVisible: false,
          inputValue: "",
          required: true,
        },
        ruleForm: {
          name: "", //登记表名称
          questions: [],
        },
        rules: {
          name: [{ required: true, message: "请选择登记表名称", trigger: "change" }],
          questions: [{ required: true, message: "请填写回访登记表内容", trigger: "change" }],
        },
        questionRules: {
          type: [{ required: true, message: "请选择题目类型", trigger: "change" }],
          name: [{ required: true, message: "请输入题目标题", trigger: "blur" }],
          optionList: [{ required: true, message: "请创建选项值", trigger: "blur" }],
          required: [{ required: true, message: "请选择是否必须回答", trigger: "change" }],
        },
        apis: {
          create: "/api/regTemplate/save",
          update: "/api/regTemplate/update",
          info: "/api/regTemplate/get/",
        },
        saveRecordThrottling: () => {},
        loading: false,
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    async mounted() {
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.ruleForm.questions = this.ruleForm.questions.slice(1, this.ruleForm.questions.length);
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            let params = {
              name: this.ruleForm.name,
            };
            let questions = JSON.parse(JSON.stringify(this.ruleForm.questions));
            if (!this.recordId) {
              questions = questions.map((item) => {
                return {
                  type: item.type,
                  name: item.name,
                  optionList: item.optionList,
                  required: item.required,
                };
              });
            } else {
              params.id = this.recordId;
            }
            params.questions = questions;
            this.loading = true;
            try {
              let res = this.recordId
                ? await updateApiFun(params, this.apis.update)
                : await createApiFun(params, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      // 删除选项
      handleClose(index, tagIndex) {
        this.ruleForm.questions[index].optionList.splice(tagIndex, 1);
      },
      // 添加选项值
      handleInputConfirm(index) {
        let inputValue = this.ruleForm.questions[index].inputValue.trim();
        if (inputValue) {
          if (this.ruleForm.questions[index].optionList.includes(inputValue)) {
            this.$message.warning("请勿创建相同的选项值");
            return;
          }
          this.ruleForm.questions[index].optionList.push(inputValue);
        }
        this.ruleForm.questions[index].inputVisible = false;
        this.ruleForm.questions[index].inputValue = "";
      },
      // 显示输入框
      showInput(index) {
        this.ruleForm.questions[index].inputVisible = true;
        this.$nextTick(() => {
          this.$refs[`saveTagInput${index}`][0].$refs.input.focus();
        });
      },
      // 增加题目
      addQuestion() {
        let obj = {
          id: nanoid(),
          type: "",
          name: "",
          optionList: [],
          inputVisible: false,
          inputValue: "",
          required: true,
        };
        this.ruleForm.questions.push(obj);
      },
      // 删除题目
      deleteQuestion(index) {
        this.ruleForm.questions.splice(index, 1);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .card-box {
    display: flex;
    align-items: flex-start;
    padding: 16px 20px;
    .card-right {
      flex: 1;
      overflow: hidden;
    }
  }
  .el-tag + .el-tag {
    margin-left: 10px;
  }
  .button-new-tag {
    height: 32px;
    line-height: 30px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 130px;
  }
  .tag-btn-left {
    margin-left: 10px;
  }
  .type-tag {
    height: auto;
    max-width: 100%;
    white-space: normal;
    word-break: break-all;
    margin-bottom: 4px;
    cursor: move;
  }
  .option-list {
    display: flex;
    align-items: center;
  }
  .card-footer {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    color: #909399;
    cursor: pointer;
  }
  .question-card {
    cursor: move;
  }
</style>
