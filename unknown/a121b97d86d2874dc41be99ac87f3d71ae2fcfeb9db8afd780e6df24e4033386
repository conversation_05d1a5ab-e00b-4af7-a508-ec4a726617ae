<template>
  <div v-if="isDone">
    <v-scale-screen width="1920" height="1080" class="monitor-container">
      <mapContainer class="mapContainer" @initMap="initMap"></mapContainer>
      <main class="main-container">
        <div class="header-container">医疗废物智慧收运管理平台实时监控大屏</div>
        <StatisticTotal
          :carList="carList"
          :channelList="channelList"
          :channel="channel"
          :value.sync="channelId"
          @openAIDrawer="openAIDrawer"
          @toggleChannel="toggleChannel"
        ></StatisticTotal>
      </main>
    </v-scale-screen>
    <DriverDialog :value.sync="showDriverShip" :channelId="channelId"></DriverDialog>
    <VehicleDialog :value.sync="showVehicleDialog" :channelId="channelId"></VehicleDialog>
    <UncollectPointDialog :value.sync="showPointDialog" :channelId="channelId"></UncollectPointDialog>
    <TrafficDetailsDialog :value.sync="showTrafficDetailDialog" :channelId="channelId"></TrafficDetailsDialog>
    <ExceptReportDialog :value.sync="showExceptReportDialog" :channelId="channelId"></ExceptReportDialog>
    <ElectronicWaybillDialog
      :value.sync="showElectronicWaybillDialog"
      :completed="completed"
      :channelId="channelId"
    ></ElectronicWaybillDialog>
    <MonitorDialog :value.sync="showMonitorDialog" :plateNumber="$store.state.plateNumber"></MonitorDialog>
    <AIDrawer :value.sync="showAIDrawer"></AIDrawer>
  </div>
</template>

<script>
  import VScaleScreen from "v-scale-screen";
  import mapContainer from "@/components/mapContainer";
  import StatisticTotal from "./components/statisticTotal";
  import { getInfoApiFunByParams, getInfoApiFun } from "@/api/base";
  import DriverDialog from "./components/DriverDialog";
  import VehicleDialog from "./components/VehicleDialog";
  import UncollectPointDialog from "./components/UncollectPointDialog";
  import TrafficDetailsDialog from "./components/TrafficDetailsDialog";
  import ExceptReportDialog from "./components/ExceptReportDialog";
  import ElectronicWaybillDialog from "./components/ElectronicWaybillDialog";
  import MonitorDialog from "./components/MonitorDialog";
  import AIDrawer from "./components/AIDrawer.vue";
  import emitter from "@/utils/mitt";
  import websocket from "@/utils/websocket";
  import { wgs84togcj02 } from "@/utils/coordinateTransform";
  import { getLocalStorage } from "@/utils/storage";
  import { INTERVIEW_CHANNEL } from "@/utils/commonValue"; //访问渠道
  let map = "";
  export default {
    mixins: [websocket],
    components: {
      VScaleScreen,
      mapContainer,
      StatisticTotal,
      DriverDialog,
      VehicleDialog,
      UncollectPointDialog,
      TrafficDetailsDialog,
      ExceptReportDialog,
      ElectronicWaybillDialog,
      MonitorDialog,
      AIDrawer,
    },
    data() {
      return {
        apis: {
          location: "/api/monitor/location",
          channelList: "/api/base/dna/listByDna/",
        },
        showDriverShip: false,
        showVehicleDialog: false,
        showPointDialog: false,
        showTrafficDetailDialog: false,
        showExceptReportDialog: false,
        showElectronicWaybillDialog: false,
        showMonitorDialog: false,
        websocketUrl: `${process.env.VUE_APP_WSS_URL}/sctmpbase/api/screen/real-time?location=0`,
        completed: 0,
        markerMap: {},
        textMap: {},
        carList: [],
        showAIDrawer: false,
        channelId: "",
        channelList: [],
        channel: "",
        centerObj: {
          gz: [113.2744826, 23.1820811],
          cz: [116.622225, 23.657976],
          sw: [115.375478, 22.787237],
        },
        isDone: false,
      };
    },
    computed: {
      chartKey() {
        let key = "gz";
        if (this.channelId) {
          let filterItem = this.channelList.filter((list) => list.id === this.channelId)[0];
          if (filterItem.name.includes("潮州")) {
            key = "cz";
          }
          if (filterItem.name.includes("汕尾")) {
            key = "sw";
          }
        }
        return key;
      },
    },
    async created() {
      this.channel = getLocalStorage(INTERVIEW_CHANNEL);
      await this.getChannelList();
      this.isDone = true;
      this.$store.dispatch("getFormData", this.channelId);
      let token = await window.LOGAN.getToken();
      this.initWebSocket(this.websocketUrl + `&token=${token}`);
      let socketData = [
        {
          propName: "waybillInfo",
          params: [this.channelId],
        },
        {
          propName: "statistic",
          params: [this.channelId],
        },
        {
          propName: "collectInfo",
          params: [this.channelId],
        },
        {
          propName: "abnormalInfo",
          params: [this.channelId],
        },
        {
          propName: "rubbishTotal",
          params: [this.channelId],
        },
      ];
      this.sendSocketMessage(socketData);
    },
    mounted() {
      emitter.on("open-dialog", (val) => {
        this[val] = true;
      });
      emitter.on("open-waybill-dialog", (obj) => {
        this[obj.key] = true;
        this.completed = obj.completed;
      });
    },
    beforeDestroy() {
      map.clearInfoWindow();
      map.clearMap();
      map.destroy();
      map = "";
      emitter.off("open-dialog");
      emitter.off("open-waybill-dialog");
      this.setOncloseMessage();
    },
    methods: {
      // 获取渠道列表
      async getChannelList() {
        let res = await getInfoApiFun(this.$DNACONFIG.dna, this.apis.channelList);
        this.channelList = this.channelList.concat(res.data);
        if (this.channel) {
          this.channelId = this.channel;
        } else {
          this.channelId = this.channelList[0].id;
        }
      },
      //打开AI弹窗
      openAIDrawer() {
        this.showAIDrawer = true;
      },
      // 初始化地图
      initMap(mapCom) {
        if (mapCom) {
          map = mapCom;
          this.getCarLocation();
          map.setCenter(this.centerObj[this.chartKey]);
        }
      },
      // 获取车辆实时位置
      async getCarLocation() {
        try {
          let res = await getInfoApiFunByParams({ channelId: this.channelId }, this.apis.location);
          this.carList = res.data.map((item) => {
            return {
              ...item,
              accStatus: Number(item.status) & (1 == 1) ? (item.speed ? 0 : 1) : 2,
              driverName: item.driverName ? item.driverName : "",
            };
          });
          map.clearInfoWindow();
          map.clearMap();
          this.markerMap = {};
          this.dealWithLocation(res.data, true);
        } catch (error) {
          console.log(error);
        }
      },
      toggleChannel() {
        this.getCarLocation();
        if (map) {
          map.setCenter(this.centerObj[this.chartKey]);
        }
        let socketData = [
          {
            propName: "waybillInfo",
            params: [this.channelId],
          },
          {
            propName: "statistic",
            params: [this.channelId],
          },
          {
            propName: "collectInfo",
            params: [this.channelId],
          },
          {
            propName: "abnormalInfo",
            params: [this.channelId],
          },
          {
            propName: "rubbishTotal",
            params: [this.channelId],
          },
        ];
        this.sendSocketMessage(socketData);
      },
      // 车辆定位数据处理
      dealWithLocation(locationList, first) {
        if (locationList.length > 0) {
          locationList.forEach((item) => {
            item.position = wgs84togcj02(item.longitude, item.latitude);
            if (Object.prototype.hasOwnProperty.call(this.markerMap, item.id)) {
              this.updateMarker(item);
            } else {
              this.addMarker(item);
            }
          });
          if (first) {
            map.setFitView();
          }
        }
      },
      // 增加点位
      addMarker(item) {
        let accStatus = Number(item.status) & (1 == 1);
        let iconImage = "";
        if (item.operationalNature === 0) {
          iconImage = accStatus
            ? item.speed
              ? require("@/assets/images/cart_drive.png")
              : require("@/assets/images/cart_static.png")
            : require("@/assets/images/cart_offline.png");
        } else {
          iconImage = accStatus
            ? item.speed
              ? require("@/assets/images/trolley_drive.png")
              : require("@/assets/images/trolley_static.png")
            : require("@/assets/images/trolley_offline.png");
        }
        const icon = new window.AMap.Icon({
          image: iconImage, //Icon 的图像
          imageSize: item.operationalNature === 0 ? new window.AMap.Size(20, 58) : new window.AMap.Size(20, 40),
        });
        this.markerMap[item.id] = new window.AMap.Marker({
          map: map,
          position: item.position,
          icon: icon,
          offset: new window.AMap.Pixel(0, 0),
          extData: { item },
          angle: item.direction,
          anchor: "middle-right",
        });
        // let acc = accStatus ? (item.speed ? "行驶中" : "静止") : "离线";
        this.textMap[item.id] = new window.AMap.Text({
          text: accStatus && item.speed ? `${item.plateNumber} - ${item.speed || 0}km/h` : `${item.plateNumber}`,
          anchor: "middle-left", // 设置文本标记锚点
          cursor: "pointer",
          style: {
            "background-color": "rgba(255,255,255,0.8)",
            "text-align": "center",
            "font-size": "12px",
          },
          offset: new window.AMap.Pixel(4, 0),
          position: item.position,
          extData: { item },
        });
        this.textMap[item.id].setMap(map);
        this.markerMap[item.id].on("click", this.markerClick);
        this.textMap[item.id].on("click", this.markerClick);
      },
      // 修改点位
      updateMarker(item) {
        let accStatus = Number(item.status) & (1 == 1);
        let iconImage = "";
        if (item.operationalNature === 0) {
          iconImage = accStatus
            ? item.speed
              ? require("@/assets/images/cart_drive.png")
              : require("@/assets/images/cart_static.png")
            : require("@/assets/images/cart_offline.png");
        } else {
          iconImage = accStatus
            ? item.speed
              ? require("@/assets/images/trolley_drive.png")
              : require("@/assets/images/trolley_static.png")
            : require("@/assets/images/trolley_offline.png");
        }
        const icon = new window.AMap.Icon({
          image: iconImage, //Icon 的图像
          imageSize: item.operationalNature === 0 ? new window.AMap.Size(20, 58) : new window.AMap.Size(20, 40),
        });
        this.markerMap[item.id].setIcon(icon);

        this.markerMap[item.id].setAngle(item.direction);
        this.textMap[item.id].setText(
          accStatus && item.speed ? `${item.plateNumber} - ${item.speed || 0}km/h` : `${item.plateNumber}`,
        );
        // 加载动画插件
        window.AMap.plugin("AMap.MoveAnimation", () => {
          this.markerMap[item.id].moveTo(item.position, {
            duration: 10000,
          });
          this.textMap[item.id].moveTo(item.position, {
            duration: 10000,
          });
        });
      },
      markerClick(e) {
        let { item } = e.target.getExtData();
        this.$router.push(
          `/sctmp_base/realTimeDriving?plateNumber=${item.plateNumber}&operationalNature=${item.operationalNature}`,
        );
      },
      handleCarList(item) {
        let index = this.carList.findIndex((list) => list.plateNumber === item.plateNumber);
        if (index >= 0) {
          this.carList[index].accStatus = Number(item.status) & (1 == 1) ? (item.speed ? 0 : 1) : 2;
          this.carList[index].speed = item.speed;
          this.carList.sort((a, b) => a.accStatus - b.accStatus || b.speed - a.speed);
        }
      },
      // websocket接收消息
      async setOnmessageMessage(res) {
        if (res.data === "token过期") {
          this.setOncloseMessage();
          await this.$store.dispatch("refreshAuthToken");
          let token = await window.LOGAN.getToken();
          this.initWebSocket(this.websocketUrl + `&token=${token}`);
          this.toggleChannel();
          return;
        }
        let rsp = "";
        try {
          rsp = JSON.parse(res.data);
        } catch (error) {
          rsp = "";
        }
        if (rsp) {
          for (let key in rsp) {
            if (key == "location") {
              if (rsp[key].length > 0) {
                this.dealWithLocation(rsp[key], false);
                this.handleCarList(rsp[key][0]);
              }
            } else {
              if (key == "statistic") {
                this.$store.commit("setStatisticData", rsp[key]);
              } else if (key == "rubbishTotal") {
                this.$store.commit("setRubbishTotal", rsp[key]);
              } else {
                this.$store.commit("setFormOtherData", { key, data: rsp[key] });
              }
            }
          }
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .monitor-container {
    position: relative;
  }
  .mapContainer {
    width: 100%;
    height: 100%;
  }
  .main-container {
    width: 100%;
    height: 100%;
    padding: 24px;
    padding-bottom: 10px;
    position: absolute;
    top: 0;
    left: 0;
    background-image: radial-gradient(rgba(0, 0, 0, 0.02) 80%, #0d6d6e 100%);
    pointer-events: none;
    display: flex;
    flex-direction: column;
  }
  .header-container {
    font-size: 32px;
    font-weight: bold;
    color: #2e6929;
    padding-bottom: 20px;
  }
  .label-title {
    font-size: 12px;
    font-weight: bold;
  }
  .label-text {
    font-size: 12px;
  }
</style>
