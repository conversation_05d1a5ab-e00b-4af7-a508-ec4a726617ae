<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage @createRecord="createRecord" @closeRecord="closeRecord">
      <record v-if="showRecord" :recordId="recordId" @closeRecord="closeRecord" @refreshList="initData"></record>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="keyword" placeholder="请输入客商名称/回访人名称" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
            <el-dropdown
              size="small"
              trigger="click"
              @command="handleDataImport"
              class="dropdown-button"
              placement="bottom-start"
            >
              <el-button size="small" type="primary">
                <span class="sctmp-iconfont icon-ic_daoru"></span>
                <span class="ml-5">导入</span>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">
                  <div class="has-padding-sm has-border-bottom">导入基础信息</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button size="small" type="primary" @click="handleExport">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
            :colNum="12"
          ></InterviewChannel>
          <el-col :span="12">
            <el-form-item label="回访日期范围">
              <el-date-picker
                v-model="filterForm.followUpDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处理情况">
              <el-select v-model="filterForm.handlingStatus" placeholder="请选择处理情况" clearable filterable>
                <el-option
                  v-for="(item, index) in VISIT_HANDLE_STATUS"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="回访方式">
              <el-select v-model="filterForm.method" placeholder="请选择回访方式" clearable filterable>
                <el-option
                  v-for="(item, index) in VISIT_RECORD_TYPE"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column prop="customerName" label="客商名称" align="center"></el-table-column>
            <el-table-column prop="followUpDate" label="回访日期" align="center"></el-table-column>
            <el-table-column prop="method" label="回访方式" align="center">
              <template #default="{ row }">{{ VISIT_RECORD_TYPE[row.method] }}</template>
            </el-table-column>
            <el-table-column prop="personName" label="回访人" align="center"></el-table-column>
            <el-table-column prop="content" label="主要回访内容" align="center" min-width="200">
              <template #default="{ row }">
                <el-tooltip effect="dark" :content="row.content" placement="top" :open-delay="100">
                  <div class="line-clamp line-clamp-2">{{ row.content }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="opinion" label="客户反馈、投诉意见" align="center" min-width="200">
              <template #default="{ row }">
                <el-tooltip effect="dark" :content="row.opinion" placement="top" :open-delay="100">
                  <div class="line-clamp line-clamp-2">{{ row.opinion }}</div>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="handlingStatus" label="处理情况" align="center">
              <template #default="{ row }">{{ VISIT_HANDLE_STATUS[row.handlingStatus] }}</template>
            </el-table-column>
            <el-table-column prop="handlingDate" label="处理日期" align="center"></el-table-column>
            <el-table-column prop="handlingPerson" label="处理人" align="center"></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="80" label="操作" align="center">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row)">编辑</el-link>
                <el-popconfirm title="确认删除当前回访记录？" @confirm="deleteRecord(row)">
                  <el-link type="danger" slot="reference">删除</el-link>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
    <importDialog
      :value.sync="importDialogShow"
      title="回访记录"
      :importApi="apis.import"
      :templateApi="apis.template"
      :importDialogType="importDialogType"
      :isTemplateApi="true"
      @importSuccess="searchFilter"
    ></importDialog>
    <el-dialog title="回访记录导出" :visible.sync="dialogVisible" width="30%" class="import-dialog" top="10%">
      <div v-loading="loading">
        <el-form :model="exportForm" :rules="exportRules" ref="exportForm" label-width="140px" label-suffix="：">
          <el-form-item label="回访登记表" prop="registrationId">
            <el-select
              class="w-300"
              v-model="exportForm.registrationId"
              placeholder="请选择回访登记表"
              clearable
              filterable
            >
              <el-option v-for="item in formOptions" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="exportFileThrottling">确 定</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, deleteApiFun, BASE_API_URL, getInfoApiFun } from "@/api/base";
  import record from "./components/record.vue";
  import { VISIT_RECORD_TYPE, VISIT_HANDLE_STATUS } from "@/enums";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import importDialog from "@/components/importDialog";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
      importDialog,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/followUpRecord/listPage",
          delete: "/api/followUpRecord/delete/",
          export: "/api/followUpRecord/export",
          template: "/api/followUpRecord/excelModel",
          import: "/api/followUpRecord/import",
          visitList: "/api/followUpRecord/getAllRegistration",
        },
        showRecord: false,
        recordId: "",
        VISIT_RECORD_TYPE,
        VISIT_HANDLE_STATUS,
        importDialogShow: false,
        importDialogType: "",
        dialogVisible: false,
        exportForm: {
          registrationId: "",
        },
        exportRules: {
          registrationId: [{ required: true, message: "请选择回访登记表", trigger: "change" }],
        },
        formOptions: [],
        loading: false,
        showFilter: false,
        keyword: "",
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    created() {
      this.exportFileThrottling = this.$throttling(this.handleexportFile, 500);
    },
    mounted() {
      this.getOptions();
      this.initData();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let res = await getInfoApiFun("", this.apis.visitList);
        this.formOptions = res.data;
      },
      async initData() {
        this.loading = true;
        try {
          let params = {
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            keyword: this.keyword,
            followUpBeginDate: this.filterForm.followUpDate ? this.filterForm.followUpDate[0] : "",
            followUpEndDate: this.filterForm.followUpDate ? this.filterForm.followUpDate[1] : "",
            handlingStatus: this.filterForm.handlingStatus,
            method: this.filterForm.method,
            channelId: this.filterForm.channelId || "",
          };
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showRecord = true;
        this.recordId = "";
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.recordId = row.id;
      },
      closeRecord() {
        this.showRecord = false;
      },
      // 删除
      async deleteRecord(row) {
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            this.page.pageNo = this.pageDropProcess(this.page.pageNo, this.page.pageSize, this.page.total);
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 打开导出弹框
      handleExport() {
        this.dialogVisible = true;
        this.exportForm.registrationId = "";
        this.$nextTick(() => {
          this.$refs.exportForm.clearValidate();
        });
      },
      // 导出
      handleexportFile() {
        this.$refs.exportForm.validate(async (valid) => {
          if (valid) {
            this.$confirm(`确认是否以当前筛选条件及所选回访登记表导出数据`, "提示", {
              confirmButtonText: "确认",
              cancelButtonText: "取消",
              type: "warning",
            })
              .then(async () => {
                this.loading = true;
                try {
                  let res = await exportFile(BASE_API_URL + this.apis.export, {
                    keyword: this.keyword,
                    followUpBeginDate: this.filterForm.followUpDate ? this.filterForm.followUpDate[0] : "",
                    followUpEndDate: this.filterForm.followUpDate ? this.filterForm.followUpDate[1] : "",
                    handlingStatus: this.filterForm.handlingStatus,
                    method: this.filterForm.method,
                    channelId: this.filterForm.channelId || "",
                    ...this.exportForm,
                  });
                  if (res.success) {
                    createDownloadEvent(`回访记录${Date.now()}.xlsx`, [res.data]);
                    window.ELEMENT.Message.success("导出成功");
                    this.dialogVisible = false;
                  }
                  this.loading = false;
                } catch (error) {
                  this.loading = false;
                }
              })
              .catch(() => {});
          }
        });
      },
      // 导入
      handleDataImport(command) {
        this.importDialogType = command;
        this.importDialogShow = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .w250 {
    width: 250px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
