<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      :title="`${formItem ? '编辑' : '新增'}考核维度`"
      :visible.sync="dialogVisible"
      width="40%"
      destroy-on-close
      @open="initData"
    >
      <div v-loading="loading">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px">
          <el-form-item label="维度名称" prop="name">
            <el-input
              v-model="ruleForm.name"
              placeholder="请输入维度名称"
              clearable
              maxlength="32"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="维度权重（%）" prop="weight">
            <el-input-number v-model="ruleForm.weight" step-strictly :min="1" :max="100"></el-input-number>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRoleThrottling">确定</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      formItem: {
        type: [String, Object],
        default: "",
      },
      formList: {
        type: Array,
        default: () => [],
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        originalForm: {
          name: "", //维度名称
          weight: "", //维度权重
        },
        ruleForm: {},
        rules: {
          name: [{ required: true, message: "请输入维度名称", trigger: "blur" }],
          weight: [{ required: true, message: "请输入维度权重", trigger: "blur" }],
        },
        saveRoleThrottling: () => {},
        loading: false,
      };
    },
    created() {
      this.saveRoleThrottling = this.$throttling(this.saveRecord, 500);
      this.ruleForm = this.originalForm;
    },
    methods: {
      async initData() {
        this.ruleForm = Object.assign({}, this.formItem ? this.formItem : this.originalForm);
        if (!this.formItem) {
          this.$nextTick(() => {
            this.$refs.ruleForm.clearValidate();
          });
        }
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            if (this.formList.length > 0) {
              let totalWeight = this.formList.reduce((total, currentValue) => {
                return total + currentValue.weight;
              }, 0);
              totalWeight = totalWeight + this.ruleForm.weight - (this.formItem?.weight || 0);
              if (totalWeight > 100) {
                this.$message.warning("考核维度权重大于100%，请重新设置");
                return;
              }
            }
            if (this.formItem) {
              let index = this.formList.findIndex((list) => list === this.formItem);
              this.$emit("editList", {
                item: this.ruleForm,
                index,
                showfield: "showDimension",
                listField: "assessDimensions",
              });
            } else {
              this.$emit("addList", { item: this.ruleForm, showfield: "showDimension", listField: "assessDimensions" });
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
