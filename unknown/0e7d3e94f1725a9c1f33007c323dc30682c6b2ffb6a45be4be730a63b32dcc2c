<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="企业名称" prop="name">
              <el-input
                v-model="ruleForm.name"
                placeholder="请输入企业名称"
                clearable
                maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="是否默认企业" prop="isDefault">
              <el-switch
                v-model="ruleForm.isDefault"
                active-text="是"
                inactive-text="否"
                :active-value="1"
                :inactive-value="0"
              ></el-switch>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="行业" prop="industry">
              <el-input
                v-model="ruleForm.industry"
                placeholder="请输入行业"
                clearable
                maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="企业层级" prop="level">
              <el-select v-model="ruleForm.level" placeholder="请选择企业层级" clearable filterable>
                <el-option
                  v-for="(item, index) in ENTERPRISE_LEVEL"
                  :key="index"
                  :label="item"
                  :value="String(index)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="法定代表人" prop="representativeName">
              <el-input
                v-model="ruleForm.representativeName"
                placeholder="请输入法定代表人"
                clearable
                maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="联系人名称" prop="personInCharge">
              <el-input
                v-model="ruleForm.personInCharge"
                placeholder="请输入联系人名称"
                clearable
                maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="联系人电话" prop="phoneNumber">
              <el-input
                v-model="ruleForm.phoneNumber"
                placeholder="请输入联系人电话"
                clearable
                maxlength="11"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="统一信用代码" prop="creditCode">
              <el-input
                v-model="ruleForm.creditCode"
                placeholder="请输入统一信用代码"
                clearable
                maxlength="50"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="详细地址" prop="address">
              <el-input
                v-model="ruleForm.address"
                placeholder="请输入详细地址"
                clearable
                maxlength="200"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="企业营业执照" prop="businessLicense">
              <FileUpload @uploadChange="uploadChangeFileList" :imageList="imageList" :limit="1">
                <template #tips><el-tag type="warning">请上传企业营业执照</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import FileUpload from "@/components/FileUpload";
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  import { ENTERPRISE_LEVEL } from "@/enums";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      FileUpload,
    },
    data() {
      return {
        ruleForm: {
          name: "", //企业名称
          industry: "", //行业
          level: "", //企业层级
          representativeName: "", //法定代表人
          personInCharge: "", //联系人名称
          phoneNumber: "", //联系人电话
          creditCode: "", //统一信用代码
          address: "", //详细地址
          businessLicense: [], //年审凭证
          isDefault: 0, //是否默认企业
        },
        rules: {
          name: [{ required: true, message: "请输入企业名称", trigger: "blur" }],
          industry: [{ required: true, message: "请输入行业", trigger: "blur" }],
          personInCharge: [{ required: true, message: "请输入联系人名称", trigger: "blur" }],
          phoneNumber: [{ required: true, message: "请输入联系人电话", trigger: "blur" }],
          isDefault: [{ required: true, message: "请选择是否默认企业", trigger: "change" }],
        },
        apis: {
          create: "/api/company/create",
          update: "/api/company/update",
          info: "/api/company/get/",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [],
        ENTERPRISE_LEVEL,
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          let imageObj = "";
          try {
            imageObj = JSON.parse(this.ruleForm.businessLicense);
          } catch (error) {
            imageObj = "";
          }
          if (imageObj) {
            this.imageList = [{ url: imageObj.url, file: imageObj }];
          }
          this.ruleForm.phoneNumber = this.ruleForm.phoneNumber ? this.$sm2Decrypt(this.ruleForm.phoneNumber) : "";
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = this.recordId
                ? await updateApiFun(this.$sm2Encrypt(JSON.stringify(this.ruleForm)), this.apis.update)
                : await createApiFun(this.$sm2Encrypt(JSON.stringify(this.ruleForm)), this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      uploadChangeFileList(fileList) {
        if (fileList.length > 0) {
          this.ruleForm.businessLicense = JSON.stringify({
            name: fileList[0].name,
            size: fileList[0].size,
            ...fileList[0].file,
          });
        } else {
          this.ruleForm.businessLicense = "";
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
  }
  ::v-deep .el-transfer-panel__body {
    height: 400px;
  }
</style>
