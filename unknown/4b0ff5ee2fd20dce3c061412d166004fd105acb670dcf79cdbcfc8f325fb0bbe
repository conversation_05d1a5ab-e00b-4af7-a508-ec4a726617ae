<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="人员编辑" :visible.sync="dialogVisible" width="60%" destroy-on-close @open="handleOpen">
      <div v-loading="loading">
        <header class="header">
          <div class="header-left">
            <InterviewChannel :value.sync="filterForm.channelId" :record.sync="channelRecord"></InterviewChannel>
            <el-form ref="filterForm" inline :model="filterForm" label-suffix=":" label-width="80px">
              <el-form-item label="人员名称">
                <el-input
                  v-model="filterForm.fullName"
                  placeholder="请输入人员名称"
                  clearable
                  @change="searchFilter"
                ></el-input>
              </el-form-item>
              <el-form-item label="联系电话">
                <el-input
                  v-model="filterForm.phone"
                  placeholder="请输入联系电话"
                  clearable
                  @change="searchFilter"
                ></el-input>
              </el-form-item>
              <el-form-item label="用户身份">
                <el-select
                  v-model="filterForm.userIdentity"
                  placeholder="请选择用户身份"
                  clearable
                  filterable
                  @change="searchFilter"
                >
                  <el-option v-for="(item, index) in USER_IDENTITY" :key="index" :label="item" :value="index">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
        </header>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-suffix="：">
          <el-form-item prop="personList" :rules="personRules">
            <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" border>
              <el-table-column type="index" align="center"></el-table-column>
              <el-table-column prop="fullName" label="真实姓名" align="center"></el-table-column>
              <el-table-column prop="userIdentity" label="用户身份" align="center">
                <template #default="{ row }">{{ USER_IDENTITY[row.userIdentity] }}</template>
              </el-table-column>
              <el-table-column prop="phone" label="联系电话" align="center"></el-table-column>
              <el-table-column prop="userName" label="用户名" align="center"></el-table-column>
              <el-table-column prop="assessSchemeName" label="考核方案信息" align="center">
                <template #default="{ row }">{{
                  row.assessSchemeName || row.assessSchemeName === 0 ? row.assessSchemeName : "-"
                }}</template>
              </el-table-column>
              <el-table-column prop="channelId" label="渠道名称" align="center">
                <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
              </el-table-column>
              <el-table-column min-width="80" label="操作" align="center">
                <template #default="{ row }">
                  <el-link type="danger" @click="deletePerson(row)" v-if="personIds.includes(row.lgUnionId)"
                    >删除</el-link
                  >
                  <el-link type="primary" @click="selectPerson(row)" v-else>选择</el-link>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
          <Pagination :page="page" @pageChange="pageChange"></Pagination>
          <el-form-item label="被考核部门">
            <el-cascader
              class="w500"
              v-model="ruleForm.deptList"
              placeholder="请选择被考核部门"
              filterable
              clearable
              :options="deptOptions"
              :props="deptProps"
              :show-all-levels="false"
            ></el-cascader>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveThrottling">确定</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun, getInfoApiFun, createApiFun } from "@/api/base";
  import { USER_IDENTITY } from "@/enums";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      personDialogRow: {
        type: Object,
        default: () => {},
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
      personIds() {
        return this.ruleForm.personList.map((list) => list.lgUnionId);
      },
      personRules() {
        let rules = [{ required: true, message: "请选择被考核人员", trigger: "change" }];
        if (this.ruleForm.deptList.length > 0) {
          rules[0].required = false;
        }
        return rules;
      },
    },
    watch: {
      "filterForm.channelId"() {
        this.searchFilter();
      },
    },
    data() {
      return {
        loading: false,
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        apis: {
          listPage: "/api/assess/scheme/getAssessSchemeSelectUserListPage",
          deptList: "/api/company/structure/findIn",
          addUser: "/api/assess/scheme/assessSchemeAddUser",
        },
        USER_IDENTITY,
        tableData: [],
        deptList: [],
        deptOptions: [],
        deptProps: {
          checkStrictly: true,
          value: "id",
          label: "name",
          emitPath: false,
          multiple: true,
        },
        ruleForm: {
          personList: [], //被考核人员
          deptList: [], //被考核部门
        },
        rules: {},
        saveThrottling: () => {},
        channelRecord: {},
      };
    },
    created() {
      this.saveThrottling = this.$throttling(this.saveRecord, 500);
    },
    methods: {
      // 打开弹窗事件
      handleOpen() {
        this.ruleForm.personList = [];
        this.ruleForm.deptList = [];
        this.getOptions();
        this.initData();
        this.personDialogRow.assessUsers.forEach((list) => {
          if (list.type === 1) {
            this.ruleForm.deptList.push(list.businessId);
          } else {
            this.ruleForm.personList.push({
              lgUnionId: list.businessId,
              fullName: list.fullName,
              phone: list.phone,
              userName: list.userName,
            });
          }
        });
      },
      // 获取数据列表
      async getOptions() {
        let promiseList = [getInfoApiFun("", this.apis.deptList)];
        let res = await Promise.all(promiseList);
        this.deptOptions = res[0].data;
      },
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          period: this.personDialogRow.period,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 选择人员
      selectPerson(row) {
        this.ruleForm.personList.push(row);
      },
      // 删除人员
      deletePerson(row) {
        let index = this.ruleForm.personList.findIndex((list) => list.lgUnionId === row.lgUnionId);
        if (index >= 0) {
          this.ruleForm.personList.splice(index, 1);
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            let assessUsers = [];
            this.ruleForm.personList.forEach((list) => {
              assessUsers.push({
                type: 0,
                businessId: list.lgUnionId,
              });
            });
            if (this.ruleForm.deptList.length > 0) {
              this.ruleForm.deptList.forEach((list) => {
                assessUsers.push({
                  type: 1,
                  businessId: list,
                });
              });
            }
            this.loading = true;
            try {
              let res = await createApiFun({ id: this.personDialogRow.id, assessUsers }, this.apis.addUser);
              if (res.success) {
                this.$message.success("人员编辑成功");
                this.dialogVisible = false;
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .header {
    margin-bottom: 20px;
  }
  .header-left {
    display: flex;
  }
  ::v-deep .header-left .el-form-item {
    margin-bottom: 0;
    margin-top: 20px;
  }
</style>
