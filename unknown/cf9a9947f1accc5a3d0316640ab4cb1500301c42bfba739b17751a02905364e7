<template>
  <div class="refuel">
    <main-title title="车辆加油与油耗"></main-title>
    <ul class="refuel-list">
      <li class="refuel-item" v-for="(item, index) in countList" :key="index" @click="itemClick(index)">
        <div class="refuel-item-label">{{ item.label }}</div>
        <div class="refuel-item-box">
          <div class="refuel-item-num">{{ item.num }}</div>
          <div class="refuel-item-unit">（{{ item.unit }}）</div>
        </div>
      </li>
    </ul>
    <div class="tab">
      <div class="tab-left">车辆加油与油耗分析图</div>
      <!-- <div class="tab-right">
        <ul class="tab-list">
          <li
            class="tab-item"
            :class="{ active: activeTab == index }"
            v-for="(item, index) in tabList"
            :key="index"
            @click="toggleTab(index)"
            >{{ item }}</li
          >
        </ul>
      </div> -->
    </div>
    <div class="chart-box">
      <div class="chart" id="refuel-chart"></div>
    </div>
  </div>
</template>

<script>
  import MainTitle from "./MainTitle.vue";
  import * as echarts from "echarts";
  import { getLastSevenDaysMonthDay } from "@/utils";
  import emitter from "@/utils/mitt";
  export default {
    components: {
      MainTitle,
    },
    props: {
      getVehicleFuel: {
        type: Object,
        default: () => {},
      },
    },
    watch: {
      getVehicleFuel: {
        handler(newV) {
          this.countList[0].num = newV.fuelAmount || 0;
          // this.countList[1].num = newV.fuelQuantity;
          if (newV.fuelList && newV?.fuelList.length > 0) {
            // this.option.xAxis[0].data = newV?.fuelList.map((list) => {
            //   let dateList = list.date.split("-");
            //   return `${dateList[1]}-${dateList[2]}`;
            // });
            newV.fuelList.forEach((list) => {
              let dateList = list.date.split("-");
              let index = this.option.xAxis[0].data.findIndex((item) => item == `${dateList[1]}-${dateList[2]}`);
              this.option.series[0].data[index] = list.amount;
            });
            // this.option.series[0].data = newV.fuelList.map((list) => list.amount);
            // this.option.series[1].data = newV.fuelList.map((list) => list.fuelQuantity);
            this.$nextTick(() => {
              this.init();
            });
          }
        },
        deep: true,
        immediate: true,
      },
    },
    data() {
      return {
        countList: [
          {
            label: "当月车辆加油费用",
            num: 0,
            unit: "元",
          },
          {
            label: "当月车辆平均油耗",
            num: 17.87,
            unit: "L/100km",
          },
        ],
        activeTab: 0,
        tabList: ["近七日", "近四周", "近12月"],
        option: {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
              crossStyle: {
                color: "#fff",
              },
            },
          },
          grid: {
            left: "8%",
            bottom: "8%",
          },
          legend: {
            // data: ["日期", "金额(元)", "油耗(L/100km)"],
            data: ["日期", "金额(元)"],
            textStyle: {
              color: "#fff",
            },
          },
          xAxis: [
            {
              type: "category",
              data: getLastSevenDaysMonthDay(),
              axisPointer: {
                type: "shadow",
              },
              // axisLine: {
              //   lineStyle: {
              //     color: "#fff",
              //   },
              // },
            },
          ],
          yAxis: [
            {
              type: "value",
              name: "金额(元)",
              min: 0,
              axisLabel: {
                color: "#fff",
              },
              nameTextStyle: {
                color: "#fff",
              },
              alignTicks: true,
            },
            // {
            //   type: "value",
            //   name: "油耗(L/100km)",
            //   min: 0,
            //   axisLabel: {
            //     color: "#fff",
            //   },
            //   nameTextStyle: {
            //     color: "#fff",
            //   },
            //   alignTicks: true,
            // },
          ],
          series: [
            {
              name: "金额(元)",
              type: "bar",
              data: [0, 0, 0, 0, 0, 0, 0],
              barWidth: "50%",
            },
            // {
            //   name: "油耗(L/100km)",
            //   type: "line",
            //   yAxisIndex: 1,
            //   data: [],
            // },
          ],
        },
      };
    },
    mounted() {
      // this.init();
    },
    methods: {
      toggleTab() {},
      init() {
        let chartInstance = echarts.init(document.getElementById("refuel-chart"));
        chartInstance.setOption(this.option);
      },
      itemClick(index) {
        switch (index) {
          case 0:
            emitter.emit("open-refuel-dialog");
            break;
          case 1:
            emitter.emit("open-refuelRank-dialog");
            break;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .refuel {
    margin-top: 16px;
  }
  .refuel-list {
    display: flex;
    justify-content: space-between;
    padding-right: 80px;
    margin-top: 16px;
    .refuel-item {
      color: #fff;
      text-align: center;
      cursor: pointer;
      .refuel-item-label {
        font-size: 14px;
      }
      .refuel-item-box {
        display: flex;
        align-items: flex-end;
        justify-content: center;
        margin-top: 10px;
        .refuel-item-num {
          font-size: 32px;
          line-height: 24px;
        }
        .refuel-item-unit {
          font-size: 12px;
        }
      }
    }
  }
  .tab {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 10px 0;
    .tab-left {
      font-size: 14px;
      color: #fff;
    }
  }
  .tab-list {
    background-color: rgba(76, 167, 134, 0.3);
    display: inline-flex;
    align-items: center;
    border-radius: 16px;
    color: #fff;
    .tab-item {
      width: 60px;
      text-align: center;
      padding: 4px 0;
      font-size: 12px;
      cursor: pointer;
      border-radius: 16px;
      &.active {
        background-color: var(--color-primary);
      }
    }
  }
  .chart-box {
    width: 500px;
    height: 256px;
    margin-bottom: 10px;
    .chart {
      width: 100%;
      height: 100%;
    }
  }
</style>
