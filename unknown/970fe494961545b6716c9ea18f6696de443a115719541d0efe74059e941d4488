<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="里程系数" :visible.sync="dialogVisible" width="40%" destroy-on-close @open="initData">
      <div v-loading="loading">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px">
          <el-form-item label="行驶公里范围" prop="min">
            <el-input-number v-model="ruleForm.min" step-strictly :min="minValue" :max="9999"></el-input-number>
            &nbsp;&nbsp;{{ "＜" }}&nbsp;&nbsp;—&nbsp;&nbsp;{{ "≤" }}&nbsp;&nbsp;
            <el-input-number v-model="ruleForm.max" step-strictly :min="minValue + 1" :max="9999"></el-input-number>
          </el-form-item>
          <el-form-item label="里程系数" prop="factor">
            <el-input-number v-model="ruleForm.factor" :min="0" :precision="2"></el-input-number>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRoleThrottling">确定</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      formItem: {
        type: [String, Object],
        default: "",
      },
      formList: {
        type: Array,
        default: () => [],
      },
      formIndex: {
        type: Number,
        default: 0,
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        originalForm: {
          min: "", //最小值
          max: 9999, //最大值
          factor: "", //里程系数
        },
        ruleForm: {},
        rules: {
          min: [{ required: true, trigger: "change", validator: this.validateMinAndMax }],
          factor: [{ required: true, message: "请输入里程系数", trigger: "blur" }],
        },
        saveRoleThrottling: () => {},
        loading: false,
        minValue: 0,
      };
    },
    created() {
      this.saveRoleThrottling = this.$throttling(this.saveRecord, 500);
      this.ruleForm = this.originalForm;
    },
    methods: {
      validateMinAndMax(_, value, callback) {
        if ((!value && value !== 0) || (!this.ruleForm.max && this.ruleForm.max !== 0)) {
          callback(new Error("请填写行驶公里范围"));
        } else {
          if (this.ruleForm.max <= this.ruleForm.min) {
            callback(new Error("请确保最大范围数值大于最小范围数值"));
          }
          callback();
        }
      },
      async initData() {
        this.ruleForm = Object.assign({}, this.formItem ? this.formItem : this.originalForm);
        if (!this.formItem) {
          this.minValue = this.formList[this.formList.length - 1]?.max || 0;
          this.$nextTick(() => {
            this.$refs.ruleForm.clearValidate();
          });
        } else {
          this.minValue = this.formIndex === 0 ? 0 : this.formList[this.formIndex - 1]?.max || 0;
        }
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            if (this.formItem) {
              let index = this.formList.findIndex((list) => list === this.formItem);
              this.$emit("editList", {
                item: this.ruleForm,
                index,
                showfield: "showMileageFactors",
                listField: "mileageFactors",
              });
            } else {
              this.$emit("addList", {
                item: this.ruleForm,
                showfield: "showMileageFactors",
                listField: "mileageFactors",
              });
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
