<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title="重置密码"
      :visible.sync="dialogVisible"
      width="20%"
      :before-close="handleClose"
      destroy-on-close
      :close-on-click-modal="false"
      @open="initForm"
    >
      <div v-if="showSuccess" class="success-box">
        <img class="success-img" src="@/assets/images/reset_success.png" alt="" />
        <div class="success-content">
          <div class="success-title">密码重置成功</div>
          <div class="success-text">用户密码已重置请提醒用户尽快修改密码</div>
        </div>
      </div>
      <div v-else v-loading="loading">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-position="top">
          <el-form-item label="请输入新密码" prop="newPassword">
            <el-input v-model.trim="ruleForm.newPassword" type="password" clearable placeholder="请输入新密码" />
          </el-form-item>
          <el-form-item label="请确认新密码" prop="confirmPassword">
            <el-input v-model.trim="ruleForm.confirmPassword" type="password" clearable placeholder="请确认新密码" />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="resetThrottling">下一步</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFun } from "@/api/base";
  import { encryptSm2 } from "logan-common/utils";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      lgUnionId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        ruleForm: {
          newPassword: "",
          confirmPassword: "",
        },
        rules: {
          newPassword: [
            {
              required: true,
              validator: this.validatePasswordComplexity,
              trigger: "blur",
            },
          ],
          confirmPassword: [
            {
              required: true,
              validator: this.validateConfirmPassword,
              trigger: "blur",
            },
          ],
        },
        saveRoleThrottling: () => {},
        showSuccess: false,
        apis: {
          resetword: "/api/user/manageResetPassWord",
          publicKey: "/api/user/secret/findPublicKey",
        },
        loading: false,
      };
    },
    created() {
      this.resetThrottling = this.$throttling(this.reset, 500);
    },
    methods: {
      validatePasswordComplexity(rule, value, callback) {
        if (!value) {
          callback(new Error("请输入新密码"));
          return;
        }

        const length = value.length >= 8;
        const hasLetter = /[a-zA-Z]/.test(value);
        const hasNumber = /[0-9]/.test(value);
        const hasSpecial = /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(value);

        if (!length) {
          callback(new Error("密码长度不能少于8位"));
        } else if (!hasLetter) {
          callback(new Error("密码必须包含字母"));
        } else if (!hasNumber) {
          callback(new Error("密码必须包含数字"));
        } else if (!hasSpecial) {
          callback(new Error("密码必须包含特殊符号"));
        } else {
          callback();
        }
      },
      validateConfirmPassword(rule, value, callback) {
        if (!value.trim()) {
          callback(new Error("请输入确认密码"));
        } else if (value.trim() != this.ruleForm.newPassword) {
          callback(new Error("密码不一致"));
        } else {
          callback();
        }
      },
      // 初始化
      initForm() {
        this.ruleForm.newPassword = "";
        this.ruleForm.confirmPassword = "";
        this.$nextTick(() => {
          this.$refs.ruleForm.clearValidate();
        });
      },
      // 下一步
      reset() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let { data } = await getInfoApiFun("", this.apis.publicKey);
              let res = await createApiFun(
                {
                  lgUnionId: this.lgUnionId,
                  newPassword: encryptSm2(this.ruleForm.newPassword, data),
                },
                this.apis.resetword,
              );
              if (res.success) {
                this.showSuccess = true;
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      // 关闭弹窗
      handleClose(done) {
        this.showSuccess = false;
        done();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .success-box {
    padding: 80px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .success-img {
      display: block;
      width: 64px;
      height: 64px;
      overflow: hidden;
    }
    .success-content {
      margin-left: 20px;
      color: #1d2925;
      .success-title {
        font-weight: 600;
        font-size: 20px;
        line-height: 33px;
      }
      .success-text {
        font-weight: 400;
        font-size: 16px;
        line-height: 28px;
      }
    }
  }
</style>
