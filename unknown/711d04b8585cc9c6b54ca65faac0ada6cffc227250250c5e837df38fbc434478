<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="对话记录" :visible.sync="dialogVisible" width="1400px" top="0" destroy-on-close @open="initForm">
      <div class="main-record" v-loading="loading">
        <div class="record-content">
          <div class="card-header">对话记录</div>
          <baseTitle title="基本信息"></baseTitle>
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px">
            <el-row>
              <el-col :md="24" :lg="8">
                <el-form-item label="日期">
                  <el-input :value="replyRecord.dialogueDate" readonly></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="8">
                <el-form-item label="对话人">
                  <el-input :value="replyRecord.userName" readonly></el-input>
                </el-form-item>
              </el-col>
              <el-col :md="24" :lg="8">
                <el-form-item label="对话人联系方式">
                  <el-input :value="replyRecord.contactNumber" readonly></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <baseTitle title="对话内容"></baseTitle>
            <el-row>
              <el-col>
                <el-form-item label="" label-width="0">
                  <el-input
                    readonly
                    :value="replyRecord.dialogueContent"
                    placeholder=""
                    clearable
                    type="textarea"
                    rows="6"
                    maxlength="500"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <baseTitle title="回复内容"></baseTitle>
            <el-row>
              <el-col>
                <el-form-item label="" prop="replyContent" label-width="0">
                  <el-input
                    :readonly="replyType == 1"
                    v-model="ruleForm.replyContent"
                    placeholder="请输入回复内容"
                    clearable
                    type="textarea"
                    rows="6"
                    maxlength="500"
                    show-word-limit
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="record-footer">
          <el-button @click="dialogVisible = false">{{ replyType == 0 ? "取消" : "返回" }}</el-button>
          <el-button type="primary" @click="saveReplyThrottling" v-if="replyType == 0">完成</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";
  import { createApiFun } from "@/api/base";
  export default {
    props: {
      replyType: {
        type: Number,
        default: 0,
      },
      replyRecord: {
        type: Object,
        default: () => {},
      },
      value: {
        type: Boolean,
        default: false,
      },
    },
    components: {
      baseTitle,
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        ruleForm: {
          replyContent: "",
        },
        rules: {
          replyContent: [{ required: true, message: "请输入回复内容", trigger: "blur" }],
        },
        apis: {
          reply: "/api/serviceEvaluationRecord/dialogue/reply",
        },
        loading: false,
        saveReplyThrottling: () => {},
      };
    },
    created() {
      this.saveReplyThrottling = this.$throttling(this.saveReply, 500);
    },
    methods: {
      initForm() {
        const { replyContent } = this.replyRecord;
        this.ruleForm.replyContent = replyContent;
      },
      // 保存回复
      async saveReply() {
        // this.loading = true;
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun(
                { id: this.replyRecord.id, replyContent: this.ruleForm.replyContent },
                this.apis.reply,
              );
              if (res.success) {
                this.$message.success("回复成功");
                this.dialogVisible = false;
                this.$emit("refreshList");
              }
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .w-400 {
    width: 400px;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .w-300 {
    width: 300px;
  }
  // 测试反馈 字数限制提示挡住内容
  ::v-deep .el-textarea__inner {
    padding-right: 40px !important;
  }
</style>
