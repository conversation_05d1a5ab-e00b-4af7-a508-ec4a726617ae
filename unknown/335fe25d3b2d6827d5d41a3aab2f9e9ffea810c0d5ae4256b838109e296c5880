<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="" :visible.sync="dialogVisible" width="70%" top="0" destroy-on-close :show-close="false">
      <div class="main-index">
        <header class="header">
          <div class="header-right">
            <span class="el-icon-info info-icon" v-if="recordForm?.memo" @click="dialogVisible = true"></span>
            <el-popover class="ml-10" placement="bottom" width="240" trigger="click">
              <div class="reveal-box">
                <div class="reveal-header">
                  <div>展示全部</div>
                  <el-switch
                    :value="allItemChecked"
                    active-text="是"
                    inactive-text="否"
                    @change="changeAllReveal"
                  ></el-switch>
                </div>
                <ul class="reveal-list">
                  <li class="reveal-item" v-for="(item, index) in itemList" :key="index">
                    <el-checkbox :value="item.value" :disabled="index === 0" @change="changeReveal($event, item)">{{
                      item.label
                    }}</el-checkbox>
                  </li>
                </ul>
              </div>
              <el-button slot="reference">
                <div class="btn-inner">
                  <span class="el-icon-s-grid"></span>
                  <span>显示列</span>
                  <span class="el-icon-caret-bottom"></span>
                </div>
              </el-button>
            </el-popover>
          </div>
        </header>
        <main class="main">
          <el-table
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            border
            height="100%"
            ref="tableRef"
          >
            <el-table-column type="index" align="center" label="顺序" width="60"></el-table-column>
            <el-table-column prop="code" label="点位编号" align="center" v-if="itemList[0].value"></el-table-column>
            <el-table-column
              prop="productionUnit"
              label="产废单位名称"
              align="center"
              width="120"
              v-if="itemList[1].value"
            ></el-table-column>
            <el-table-column
              prop="productionUnitOperator"
              label="产废单位经办人"
              align="center"
              width="120"
              v-if="itemList[2].value"
            ></el-table-column>
            <el-table-column prop="detailType" label="任务类型" align="center" v-if="itemList[3].value">
              <template #default="{ row }">{{ POINT_TASK_TYPE[row.detailType] }}</template>
            </el-table-column>
            <el-table-column prop="overType" label="加班类型" align="center" v-if="itemList[4].value">
              <template #default="{ row }">{{
                row.overType || row.overType === 0 ? OVERTIME_TYPE[row.overType] : ""
              }}</template>
            </el-table-column>
            <el-table-column
              prop="rubbishTotal"
              label="废物总量（kg）"
              align="center"
              width="140"
              v-if="itemList[5].value"
            ></el-table-column>
            <el-table-column prop="waybillStatus" label="收运状态" align="center" v-if="itemList[6].value">
              <template #default="{ row }">{{ RECEIVING_CONDITION[row.waybillStatus] }}</template>
            </el-table-column>
            <el-table-column
              prop="waybillTime"
              label="收运时间"
              align="center"
              v-if="itemList[7].value"
            ></el-table-column>
            <el-table-column
              prop="endDate"
              label="收运截止日期"
              align="center"
              width="130"
              v-if="itemList[8].value"
            ></el-table-column>
            <el-table-column prop="isClear" label="是否清空" align="center" v-if="itemList[9].value">
              <template #default="{ row }">{{ IS_NORMAL_OPERATION[row.isClear] }}</template>
            </el-table-column>
            <el-table-column prop="baggingMethod" label="桶装/袋装" align="center" v-if="itemList[10].value">
              <template #default="{ row }">{{ BARRELS_BAGS[row.baggingMethod] }}</template>
            </el-table-column>
            <el-table-column prop="verifyStatus" label="当前流程" align="center" v-if="itemList[11].value">
              <template #default="{ row }">{{ VERIFY_STATUS[row.verifyStatus] }}</template>
            </el-table-column>
            <el-table-column
              prop="verifyUserName"
              label="确认人"
              align="center"
              v-if="itemList[12].value"
            ></el-table-column>
            <el-table-column
              prop="verifyTime"
              label="确认时间"
              align="center"
              v-if="itemList[13].value"
            ></el-table-column>
            <el-table-column prop="longitude" label="经度" align="center" v-if="itemList[14].value"></el-table-column>
            <el-table-column prop="latitude" label="纬度" align="center" v-if="itemList[15].value"></el-table-column>
            <el-table-column
              prop="userLongitude"
              label="上报经度"
              align="center"
              v-if="itemList[16].value"
            ></el-table-column>
            <el-table-column
              prop="userLatitude"
              label="上报纬度"
              align="center"
              v-if="itemList[17].value"
            ></el-table-column>
            <el-table-column prop="address" label="地址" align="center" v-if="itemList[18].value"></el-table-column>
            <el-table-column prop="image" label="图片" align="center">
              <template #default="{ row }">
                <el-badge
                  v-if="row.picture && row.picture.length > 0"
                  :value="row.picture.length > 1 ? row.picture.length : ''"
                  class="picture-badge"
                  type="success"
                >
                  <el-image
                    class="picture-img"
                    fit="cover"
                    :src="row.picture[0].url"
                    :preview-src-list="row.picture.map((i) => i.url)"
                    v-if="row.picture"
                  ></el-image>
                </el-badge>
              </template>
            </el-table-column>
            <!-- <el-table-column
            prop="pictureTime"
            label="拍照时间"
            align="center"
            v-if="itemList[19].value"
          ></el-table-column>
          <el-table-column
            prop="pictureAddress"
            label="拍照地点"
            align="center"
            v-if="itemList[20].value"
          ></el-table-column> -->
            <el-table-column label="废物类型/重量（kg）" align="center" v-if="itemList[19].value">
              <el-table-column prop="infectiousWaste" label="感染性废物" align="center"></el-table-column>
              <el-table-column prop="damagingWaste" label="损伤性废物" align="center"></el-table-column>
              <el-table-column prop="pharmaceuticalWaste" label="药物性废物" align="center"></el-table-column>
              <el-table-column prop="pathologicalWaste" label="病理性废物" align="center"></el-table-column>
              <el-table-column prop="chemicalWaste" label="化学性废物" align="center"></el-table-column>
              <el-table-column prop="sludge" label="感染性废物一污泥" align="center" width="140"></el-table-column>
            </el-table-column>
            <el-table-column
              prop="firstCarrier"
              label="第一承运人"
              align="center"
              v-if="itemList[20].value"
            ></el-table-column>
            <el-table-column
              prop="residueRubbish"
              label="剩余垃圾"
              align="center"
              v-if="itemList[21].value"
            ></el-table-column>
            <el-table-column prop="operation" label="正常经营" align="center" v-if="itemList[22].value">
              <template #default="{ row }">{{ IS_NORMAL_OPERATION[row.operation] }}</template>
            </el-table-column>
          </el-table>
        </main>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    TRASH_TYPE,
    IS_NORMAL_OPERATION,
    RECEIVING_CONDITION,
    BARRELS_BAGS,
    VERIFY_STATUS,
    POINT_TASK_TYPE,
    OVERTIME_TYPE,
  } from "@/enums";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      tableData: {
        type: Array,
        default: () => [],
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        recordType: 0,
        recordId: "",
        loading: false,
        recordForm: {},
        showRecord: false,
        TRASH_TYPE,
        IS_NORMAL_OPERATION,
        RECEIVING_CONDITION,
        BARRELS_BAGS,
        VERIFY_STATUS,
        POINT_TASK_TYPE,
        OVERTIME_TYPE,
        itemList: [
          { label: "点位编号", value: true },
          { label: "产废单位名称", value: true },
          { label: "产废单位经办人", value: true },
          { label: "任务类型", value: true },
          { label: "加班类型", value: true },
          { label: "废物总量", value: true },
          { label: "收运状态", value: true },
          { label: "收运时间", value: true },
          { label: "收运截止日期", value: true },
          { label: "是否清空", value: true },
          { label: "桶装/袋装", value: true },
          { label: "当前流程", value: true },
          { label: "确认人", value: true },
          { label: "确认时间", value: true },
          { label: "经度", value: true },
          { label: "纬度", value: true },
          { label: "上报经度", value: true },
          { label: "上报纬度", value: true },
          { label: "地址", value: true },
          // { label: "拍照时间", value: true },
          // { label: "拍照地点", value: true },
          { label: "废物类型/重量", value: false },
          { label: "第一承运人", value: false },
          { label: "剩余垃圾", value: false },
          { label: "正常经营", value: false },
        ],
        allItemChecked: false,
      };
    },
    methods: {
      // 修改所有显示列
      changeAllReveal(value) {
        this.allItemChecked = value;
        this.itemList.slice(1, this.itemList.length).forEach((item) => (item.value = value));
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 列点击事件
      changeReveal(value, item) {
        item.value = value;
        let uncheckList = this.itemList.slice(1, this.itemList.length).filter((list) => !list.value);
        if (uncheckList.length === 0) {
          this.allItemChecked = true;
        } else {
          this.allItemChecked = false;
        }
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    justify-content: flex-end;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w400 {
    width: 400px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .picture-img {
    width: 60px;
    height: 60px;
  }
  .w250 {
    width: 250px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
