<template>
  <div class="micro-app-sctmp_base">
    <el-dialog :title="`${deptType == 1 ? '修改' : '新建'}部门`" :visible.sync="dialogVisible" width="520px">
      <div v-loading="loading">
        <el-form class="dept-form" :model="form" :rules="rules" ref="form" label-width="140px" label-suffix="：">
          <el-form-item label="部门名称" prop="name">
            <el-input
              v-model="form.name"
              placeholder="请输入部门名称"
              clearable
              maxlength="20"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="部门固定电话" prop="contactNumber">
            <el-input
              v-model="form.contactNumber"
              placeholder="请输入部门固定电话"
              clearable
              maxlength="20"
              show-word-limit
            ></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirmThrottling">确 定</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { createApiFun, updateApiFun } from "@/api/base";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      deptType: {
        type: Number,
        default: 0,
      },
      enterpriseInfo: {
        type: Object,
        default: () => {},
      },
      nodeInfo: {
        type: Object,
        default: () => {},
      },
      parentId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$refs.form.resetFields();
          this.$emit("update:value", e);
        },
      },
    },
    watch: {
      dialogVisible() {
        if (!this.nodeInfo.isRoot && this.deptType == 1) {
          this.form = JSON.parse(JSON.stringify(this.nodeInfo));
        } else {
          this.form = {
            name: "",
            contactNumber: "",
          };
        }
      },
    },
    data() {
      return {
        apis: {
          create: "/api/company/structure/create",
          update: "/api/company/structure/update",
        },
        form: {
          name: "",
          contactNumber: "",
        },
        rules: {
          name: [{ required: true, message: "请输入部门名称", trigger: "blur" }],
        },
        loading: false,
      };
    },
    created() {
      this.confirmThrottling = this.$throttling(this.confirm, 500);
    },
    mounted() {},
    methods: {
      confirm() {
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            let params = {
              companyId: this.enterpriseInfo.id,
              ...this.form,
            };
            if (!this.nodeInfo.isRoot && this.deptType == 0) {
              params.parentId = this.nodeInfo.id;
            }
            try {
              let res =
                this.deptType == 1
                  ? await updateApiFun(params, this.apis.update)
                  : await createApiFun(params, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.deptType == 1 ? "修改" : "新增"}成功`);
                this.dialogVisible = false;
                this.$emit("refreshTree");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    margin-top: 40px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  ::v-deep .dept-form .el-form-item {
    margin-bottom: 22px;
  }
</style>
