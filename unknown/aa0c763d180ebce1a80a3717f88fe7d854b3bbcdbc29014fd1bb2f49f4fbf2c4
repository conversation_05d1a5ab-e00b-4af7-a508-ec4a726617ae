<template>
  <div class="container container-position">
    <div class="title">本年各月收运垃圾重量</div>
    <div id="main" style="width: 400px; height: 200px"></div>
  </div>
</template>
<script>
  import * as echarts from "echarts";
  export default {
    components: {},
    props: {},
    data() {
      return {
        xAxisData: [],
        seriesData: [],
        summaryData: {
          safetySituation: [
            { month: 1, safetyNum: 0 },
            { month: 2, safetyNum: 0 },
            { month: 3, safetyNum: 0 },
            { month: 4, safetyNum: 0 },
            { month: 5, safetyNum: 0 },
            { month: 6, safetyNum: 0 },
            { month: 7, safetyNum: 113.73 },
            { month: 8, safetyNum: 508.61 },
            { month: 9, safetyNum: 707.77 },
          ],
        },
      };
    },
    computed: {},
    created() {},
    mounted() {
      this.dataCb();
      this.init();
    },
    methods: {
      dataCb() {
        this.getXAxisData();
        this.getSeriesData();
      },
      getSeriesData() {
        let length = this.xAxisData.length;
        const mappedUndefinedArray = new Array(length).fill(0);
        this.summaryData.safetySituation.forEach((item) => {
          mappedUndefinedArray[item.month - 1] = item.safetyNum;
        });
        this.seriesData = mappedUndefinedArray;
      },
      getXAxisData() {
        // 获取当前日期
        const now = new Date();

        // 获取当前年份的当前月份（注意：月份是从0开始的，所以8月是7）
        const currentMonth = now.getMonth() + 1;

        // 使用Array.from()方法生成从1到当前月份的数组
        // 第二个参数是一个函数，它接受两个参数：index（当前索引）和array（当前数组，但在这个场景下我们不需要它）
        // 我们通过index+1来确保数组从1开始
        const monthsArray = Array.from({ length: currentMonth }, (_, index) => index + 1);
        this.xAxisData = monthsArray.map((item) => {
          return `${item}月`;
        });
      },
      init() {
        var myChart = echarts.init(document.getElementById("main"));
        myChart.setOption({
          color: ["#11E48A"],
          barWidth: 8,
          tooltip: {
            trigger: "axis", // 触发类型，默认为数据项触发，可选为：'item' | 'axis'
            axisPointer: {
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
            formatter: function (params) {
              let res = params[0].name + "<br/>"; // x轴名称
              // 遍历params中的每一项数据
              params.forEach((item) => {
                let seriesName = item.seriesName; // 系列名称
                let value = item.value; // 数值
                let unit = ""; // 初始单位为空
                if (seriesName === "当月收运量") {
                  unit = "吨";
                }
                res += `${seriesName}: ${value}${unit}<br/>`; // 拼接显示内容
              });

              return res;
            },
          },
          legend: {
            itemWidth: 8,
            itemHeight: 8,
            top: "3%",
            data: ["当月收运量"],
            textStyle: {
              color: "#fff",
            },
            selectedMode: false, // 设置为不可点击
          },
          xAxis: {
            type: "category",
            data: this.xAxisData,
            axisLabel: {
              color: "#fff",
            },
          },
          grid: {
            top: "18%",
            left: "3%",
            right: "4%",
            bottom: "0%",
            containLabel: true,
          },
          yAxis: [
            {
              type: "value",
              name: "单位：吨",
              nameTextStyle: {
                color: "#fff",
              },
              axisLabel: {
                color: "#fff",
                formatter: "{value}",
              },
            },
          ],
          series: [
            {
              name: "当月收运量",
              type: "bar",
              data: this.seriesData,
            },
          ],
        });
      },
    },
    watch: {},
  };
</script>
<style scoped lang="scss">
  .container-position {
    width: 440px;
    height: 260px;
    position: absolute;
    z-index: 1;
    top: 450px;
    left: 1440px;
    background-color: #000;
    padding: 20px;
  }
  .title {
    font-size: 14px;
    color: #fff;
  }
</style>
