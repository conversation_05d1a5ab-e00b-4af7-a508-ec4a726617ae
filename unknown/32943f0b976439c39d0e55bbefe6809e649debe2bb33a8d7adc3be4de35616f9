<template>
  <div class="micro-app-sctmp_base" v-loading="loading">
    <el-dialog
      :title="`${maintenanceType ? '待二级维护' : '待保养'}车辆`"
      :visible.sync="dialogVisible"
      width="1400px"
      top="0"
      destroy-on-close
      :modal-append-to-body="false"
      :before-close="closeDialog"
      @open="initData"
    >
      <div class="full-height" v-loading="loading">
        <div class="handle-wrapper" v-if="showRecord">
          <component
            :is="`record0${maintenanceType}`"
            :recordItem="recordItem"
            @returnIndex="closeDialog"
            @refreshList="getDataList"
          />
        </div>
        <div class="main-index" v-else>
          <header class="header">
            <div class="header-left">
              <el-input class="w400 mr-10" v-model="keyword" placeholder="请输入车牌号" clearable></el-input>
            </div>
            <el-button @click="initData">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
          </header>
          <main class="main">
            <el-table
              ref="tableRef"
              :data="tableData"
              :header-cell-style="{ background: '#F5F7F9' }"
              border
              height="100%"
            >
              <el-table-column type="index" label="序号" align="center" width="50"></el-table-column>
              <el-table-column
                prop="todoTask"
                :label="`${maintenanceType ? '待二级维护' : '待保养'}车辆`"
                align="center"
              ></el-table-column>
              <el-table-column prop="createTime" label="上报时间" align="center"></el-table-column>
              <el-table-column prop="deadline" label="处理截止日期" align="center"></el-table-column>
              <el-table-column label="操作" align="center">
                <template #default="{ row }">
                  <el-link type="primary" @click="handleRecord(row)">去处理</el-link>
                </template>
              </el-table-column>
            </el-table>
          </main>
          <Pagination :page="page" @pageChange="pageChange"></Pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import record00 from "@/views/eventWatch/backlog/components/record00.vue";
  import record01 from "@/views/eventWatch/backlog/components/record01.vue";
  export default {
    components: {
      record00,
      record01,
    },
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      maintenanceType: {
        type: Number,
        default: 0,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/task/listPage",
        },
        loading: false,
        keyword: "",
        showRecord: false,
        recordId: "",
        recordItem: {},
      };
    },
    methods: {
      initData() {
        this.showRecord = false;
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.page.pageSize = 10;
        this.getDataList();
      },
      async getDataList() {
        try {
          this.loading = true;
          let params = {
            type: 0,
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            keyword: this.keyword,
            codeList: this.maintenanceType ? ["01"] : ["00"],
            statusList: [0, 2],
            channelId: this.channelId,
          };
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
          this.loading = false;
        } finally {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.getDataList();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.getDataList();
      },
      closeDialog() {
        if (this.showRecord) {
          this.showRecord = false;
          return;
        }
        this.dialogVisible = false;
      },
      // 去处理
      handleRecord(row) {
        this.recordId = row.id;
        this.recordItem = row;
        this.showRecord = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header-title {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
  }
  .header {
    display: flex;
    align-items: center;
    .header-right {
      margin-top: 20px;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w400 {
    width: 400px;
  }
  .handle-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
