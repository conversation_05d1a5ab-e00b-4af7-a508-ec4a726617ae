<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">{{ titleMap[pageFlag] }}</div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px">
        <baseTitle title="基础信息"></baseTitle>
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="客商名称" required>
              <el-input
                disabled
                v-model="ruleForm.customerName"
                placeholder="请输入客商名称"
                clearable
                maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="客商联系电话" required>
              <el-input
                disabled
                v-model="ruleForm.customerContactNumber"
                placeholder="请输入客商联系电话"
                clearable
                :maxlength="11"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12">
            <el-form-item label="客商电子邮件">
              <el-input
                disabled
                v-model="ruleForm.customerEmail"
                placeholder="无"
                clearable
                :maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="投诉日期" required>
              <el-date-picker
                disabled
                v-model="ruleForm.complaintDate"
                type="date"
                placeholder="请选择投诉日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="投诉详情"></baseTitle>
        <el-row>
          <el-col>
            <el-form-item label="投诉类型" required>
              <el-select v-model="ruleForm.type" placeholder="请选择投诉类型" clearable filterable disabled>
                <el-option
                  v-for="(item, index) in COMPLAINT_TYPE"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="投诉内容" required>
              <el-input
                disabled
                v-model="ruleForm.content"
                placeholder="无"
                clearable
                type="textarea"
                rows="6"
                maxlength="500"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="附件信息"></baseTitle>
        <el-row>
          <el-col :md="8" :lg="8">
            <el-form-item label="附件">
              <template v-if="this.ruleForm?.fileList?.length">
                <div class="flex-h" v-for="(item, index) in this.ruleForm.fileList" :key="index">
                  <span class="el-icon-circle-check file-icon"></span>
                  <el-link @click="previewFile(item)">{{ item.name }}</el-link>
                </div>
              </template>
              <template v-else>暂无数据</template>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="处理信息"></baseTitle>
        <el-row>
          <template>
            <el-col :md="24" :lg="12">
              <el-form-item label="处理日期" prop="handlingDate">
                <el-date-picker
                  :disabled="pageFlag == 'check'"
                  v-model="ruleForm.handlingDate"
                  type="date"
                  placeholder="请选择处理日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12" v-if="pageFlag == 'check'">
              <el-form-item label="处理状态" prop="handlingStatus" required>
                <el-select disabled v-model="ruleForm.handlingStatus" placeholder="请选择处理情况" clearable filterable>
                  <el-option v-for="(item, index) in HANDLING_STATUS" :key="index" :label="item" :value="index">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="处理备注" prop="remarks">
                <el-input
                  :disabled="pageFlag == 'check'"
                  v-model="ruleForm.remarks"
                  placeholder="请输入处理备注"
                  clearable
                  type="textarea"
                  rows="6"
                  maxlength="500"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <template v-if="pageFlag == 'edit'">
        <el-button @click="closeRecord">取消</el-button>
        <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
      </template>
      <template v-if="pageFlag == 'check'">
        <el-button @click="closeRecord">返回</el-button>
      </template>
    </div>
    <el-image
      v-show="false"
      :src="dialogImageUrl"
      fit="contain"
      :preview-src-list="[dialogImageUrl]"
      ref="image__preview"
    ></el-image>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";
  import { getInfoApiFun, createApiFun } from "@/api/base";
  import { HANDLING_STATUS, COMPLAINT_TYPE } from "@/enums";
  import { downloadFileBlob } from "@/utils";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
      pageFlag: {
        type: String,
        default: "edit",
      },
      recordItem: {
        type: [String, Object],
        default: "",
      },
    },
    components: {
      baseTitle,
    },
    data() {
      return {
        titleMap: {
          check: "投诉记录详情",
          edit: "处理投诉记录",
        },
        ruleForm: {
          customerName: "", //客商名称
          customerContactNumber: "", //客商联系电话
          customerEmail: "", //客商电子邮件
          complaintDate: "", // 投诉日期
          content: "", //投诉内容
          handlingDate: "", //处理日期
          remarks: "", //处理备注
          type: "", //投诉类型
        },
        rules: {
          handlingDate: [{ required: true, message: "请选择处理日期", trigger: "blur" }],
          remarks: [{ required: true, message: "请输入处理备注", trigger: "blur" }],
        },
        apis: {
          create: "/api/complaintRecord/complete",
          info: "/api/complaintRecord/get/",
        },
        HANDLING_STATUS,
        COMPLAINT_TYPE,
        saveRecordThrottling: () => {},
        loading: false,
        suffix: ".png,.jpg,.jpeg,.gif,.doc,.docx,.pdf,.xls,.xlsx",
        imageList: [],
        dialogImageUrl: "", //图片预览路径
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    async mounted() {
      if (this.recordId || this.recordItem?.businessId) {
        await this.getRecord();
        if (this.pageFlag == "edit") {
          // 处理时间默认为当前
          this.ruleForm.handlingDate = new Date();
        }
      }
    },
    methods: {
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordItem ? this.recordItem.businessId : this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
        }
      },
      // 关闭弹窗
      closeRecord() {
        if (this.recordItem) {
          this.$emit("returnIndex");
        } else {
          this.$emit("closeRecord");
        }
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            let params = {
              id: this.ruleForm.id,
              handlingDate: this.ruleForm.handlingDate,
              remarks: this.ruleForm.remarks,
            };
            this.loading = true;
            try {
              let res = await createApiFun(params, this.apis.create);
              if (res.success) {
                this.$message.success(`保存成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      uploadChangeFileList(fileList) {
        if (fileList.length > 0) {
          this.ruleForm.fileList = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
        } else {
          this.ruleForm.fileList = [];
        }
      },
      // 预览附件
      previewFile(item) {
        let suffix = item.url.slice(item.url.lastIndexOf("."));
        if ([".png", ".jpg", ".jpeg", ".gif"].includes(suffix)) {
          this.dialogImageUrl = item.url;
          this.$refs.image__preview.clickHandler();
        } else {
          this.downloadFile(item);
        }
      },
      // 下载文件
      downloadFile(item) {
        fetch(item.url)
          .then((res) => res.blob())
          .then((blob) => {
            // 将链接地址字符内容转变成blob地址
            downloadFileBlob(blob, item.name);
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .type-card {
    margin-bottom: 20px;
  }
  .w-300 {
    width: 300px;
  }
  .file-icon {
    color: #67c23a;
    padding-right: 4px;
  }
  ::v-deep .question-item .el-form-item__label {
    text-align: left;
  }
  // 测试反馈 字数限制提示挡住内容
  ::v-deep .el-textarea__inner {
    padding-right: 40px !important;
  }
</style>
