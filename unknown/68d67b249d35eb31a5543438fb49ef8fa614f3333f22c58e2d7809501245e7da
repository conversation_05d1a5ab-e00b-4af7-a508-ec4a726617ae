<template>
  <div class="backlog">
    <main-title title="车辆管理待办/告警事项"></main-title>
    <div class="backlog-num-list">
      <div class="backlog-num-box" v-for="(item, index) in countList" :key="index">
        <div class="num-box" :style="{ 'background-image': 'url(' + iconData.numbg + ')' }">{{ item.num }}</div>
        <div class="num-name">{{ item.label }}</div>
      </div>
    </div>
    <div class="switch-box flex-start">
      <div class="switch-item" :class="{ active: currIndex == 0 }" @click="handleSwitch(0)">待办事项</div>
      <div class="switch-item" :class="{ active: currIndex == 1 }" @click="handleSwitch(1)">告警通知</div>
    </div>
    <vue-seamless-scroll class="seaml-scroll" :data="listData" :class-option="seamlessScrollOption">
      <ul class="data-list">
        <li class="data-item" v-for="(item, index) in listData" :key="index">
          <div class="title">{{ item.title }}</div>
          <div class="num">{{ item.num }}</div>
        </li>
      </ul>
    </vue-seamless-scroll>
  </div>
</template>

<script>
  import MainTitle from "./MainTitle.vue";
  export default {
    components: {
      MainTitle,
    },
    props: {
      currIndex: {
        type: Number,
        default: 0,
      },
      eventStatics: {
        type: Array,
        default: () => [],
      },
      totalStatics: {
        type: Object,
        default: () => {},
      },
    },
    watch: {
      totalStatics: {
        handler(newV) {
          this.countList[0].num = newV?.eventQuantity;
          this.countList[1].num = newV?.todoQuantity;
          this.countList[2].num = newV?.abnormalQuantity;
        },
        immediate: true,
        deep: true,
      },
      eventStatics: {
        handler(newV) {
          this.listData = newV.map((item) => {
            return {
              title: item.title,
              num: item.quantity,
            };
          });
        },
        immediate: true,
        deep: true,
      },
    },
    data() {
      return {
        iconData: {
          numbg: require("@/assets/images/numbg.png"),
        },
        countList: [
          {
            label: "事件数量",
            num: 0,
          },
          {
            label: "待办事项",
            num: 0,
          },
          {
            label: "告警通知",
            num: 0,
          },
        ],
        listData: [],
        seamlessScrollOption: {
          step: 2, // 数值越大速度滚动越快
          limitMoveNum: 1, // 开始无缝滚动的数据量 this.dataList.length
          hoverStop: true, // 是否开启鼠标悬停stop
          direction: 1, // 0向下 1向上 2向左 3向右
          openWatch: true, // 开启数据实时监控刷新dom
          singleHeight: 50, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
          singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
          waitTime: 2000, // 单步运动停止的时间(默认值1000ms)
        },
      };
    },
    async created() {},
    methods: {
      handleSwitch(flag) {
        this.$emit("handleSwitch", flag);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .backlog {
    width: 480px;
    position: absolute;
    top: 130px;
    left: 40px;
    z-index: 1;
  }
  .backlog-num-list {
    display: flex;
    justify-content: space-around;
    padding: 47px 0 24px 0;
  }
  .backlog-num-box {
    width: 104px;
    .num-box {
      width: 100%;
      height: 64px;
      margin-bottom: 12px;
      font-weight: bold;
      font-size: 20px;
      color: #80f2c6;
      line-height: 26px;
      text-align: center;
    }
    .num-name {
      font-weight: 400;
      font-size: 16px;
      color: #ffffff;
      line-height: 22px;
      text-align: center;
    }
  }
  .switch-box {
    width: 180px;
    background: #093629;
    border-radius: 22px;
    cursor: pointer;
    .switch-item {
      width: 88px;
      border-radius: 22px;
      font-weight: 400;
      font-size: 16px;
      color: #c7f1e2;
      line-height: 40px;
      text-align: center;
    }
    .active {
      background: rgba(91, 249, 210, 0.2);
    }
  }
  .dataList {
    width: 100%;
    height: 300px;
    margin-top: 10px;
  }
  .seaml-scroll {
    height: 300px;
    overflow: hidden;
    width: 100%;
    font-size: 16px;
    line-height: 22px;
    font-weight: 400;
    color: #ffffff;
    margin-top: 10px;
    .data-list {
      .data-item {
        display: flex;
        padding: 9px 16px;
        margin-bottom: 10px;
        background: linear-gradient(270deg, rgba(93, 255, 203, 0) 0%, rgba(93, 255, 203, 0.1) 100%);
        .title {
          flex: 1;
          display: -webkit-box;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
        }
        .num {
          width: 80px;
          text-align: right;
        }
      }
    }
  }
</style>
