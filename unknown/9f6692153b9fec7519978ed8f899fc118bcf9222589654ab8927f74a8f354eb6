<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="收运单价" :visible.sync="dialogVisible" width="40%" destroy-on-close @open="initData">
      <div v-loading="loading">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px">
          <el-form-item label="收运点数区间" prop="min">
            <el-input-number v-model="ruleForm.min" step-strictly :min="minValue" :max="99"></el-input-number>
            &nbsp;&nbsp;{{ "＜" }}&nbsp;&nbsp;—&nbsp;&nbsp;{{ "≤" }}&nbsp;&nbsp;
            <el-input-number v-model="ruleForm.max" step-strictly :min="minValue + 1" :max="99"></el-input-number>
          </el-form-item>
          <el-form-item label="司机收运单价" prop="driverPrice">
            <el-input-number v-model="ruleForm.driverPrice" step-strictly :min="0"></el-input-number>
          </el-form-item>
          <el-form-item label="押运工收运单价" prop="supercargoPrice">
            <el-input-number v-model="ruleForm.supercargoPrice" step-strictly :min="0"></el-input-number>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveRoleThrottling">确定</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      formItem: {
        type: [String, Object],
        default: "",
      },
      formList: {
        type: Array,
        default: () => [],
      },
      formIndex: {
        type: Number,
        default: 0,
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        originalForm: {
          min: "", //最小值
          max: 99, //最大值
          driverPrice: "", //司机收运单价
          supercargoPrice: "", //押运工收运单价
        },
        ruleForm: {},
        rules: {
          min: [{ required: true, trigger: "change", validator: this.validateMinAndMax }],
          driverPrice: [{ required: true, message: "请输入司机收运单价", trigger: "blur" }],
          supercargoPrice: [{ required: true, message: "请输入押运工收运单价", trigger: "blur" }],
        },
        saveRoleThrottling: () => {},
        loading: false,
        minValue: 0,
      };
    },
    created() {
      this.saveRoleThrottling = this.$throttling(this.saveRecord, 500);
      this.ruleForm = this.originalForm;
    },
    methods: {
      validateMinAndMax(_, value, callback) {
        if ((!value && value !== 0) || (!this.ruleForm.max && this.ruleForm.max !== 0)) {
          callback(new Error("请填写收运点数区间"));
        } else {
          if (this.ruleForm.max <= this.ruleForm.min) {
            callback(new Error("请确保最大范围数值大于最小范围数值"));
          }
          callback();
        }
      },
      async initData() {
        this.ruleForm = Object.assign({}, this.formItem ? this.formItem : this.originalForm);
        if (!this.formItem) {
          this.minValue = this.formList[this.formList.length - 1]?.max || 0;
          this.$nextTick(() => {
            this.$refs.ruleForm.clearValidate();
          });
        } else {
          this.minValue = this.formIndex === 0 ? 0 : this.formList[this.formIndex - 1]?.max || 0;
        }
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            if (this.formItem) {
              let index = this.formList.findIndex((list) => list === this.formItem);
              this.$emit("editList", {
                item: this.ruleForm,
                index,
                showfield: "showDrumPrices",
                listField: "drumPrices",
              });
            } else {
              this.$emit("addList", { item: this.ruleForm, showfield: "showDrumPrices", listField: "drumPrices" });
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
