<template>
  <div class="main-record" v-loading="loading">
    <div class="record-content">
      <el-form
        :model="ruleForm"
        :rules="rules"
        ref="ruleForm"
        label-suffix="："
        label-width="220px"
        v-if="ruleForm && ruleForm.id"
      >
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="车牌号" prop="plateNumber">{{ ruleForm.plateNumber }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆型号" prop="vehicleModel">{{ ruleForm.vehicleModelName }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="品牌型号" prop="brandModel">{{ ruleForm.brandModel }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="能源类型" prop="vehicleEnergyType">{{
              VEHICLE_ENERGY_TYPE[ruleForm.vehicleEnergyType]
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="总质量(Kg)" prop="grossWeight">{{ ruleForm.grossWeight }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="加油/充电类型" prop="refuelType">{{ ruleForm.refuelTypeName }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="油箱(L)/电池容量(Kwh)" prop="fuelTankCapacity">{{
              ruleForm.fuelTankCapacity
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="排放标准" prop="emissionStandard">{{ ruleForm.emissionStandardName }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="24">
            <el-form-item label="车架号" prop="vin">{{ ruleForm.vin }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="24">
            <el-form-item label="发动机号" prop="engineNumber">{{ ruleForm.engineNumber }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="装载体积(m³)" prop="loadVolume">{{ ruleForm.loadVolume }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="装载重量(Kg)" prop="loadCapacity">{{ ruleForm.loadCapacity }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="购买日期" prop="purchaseDate">{{ ruleForm.purchaseDate }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="所属部门" prop="departmentId">{{ ruleForm.departmentName }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆状态" prop="status">{{ CAR_STATUS[ruleForm.status] }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="工作状态" prop="workingStatus">{{
              WORKING_STATUS[ruleForm.workingStatus]
            }}</el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="营运性质" prop="operationalNature">{{
              OPERATIONAL_NATURE[ruleForm.operationalNature]
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="最近年审日期" prop="recentAnnualReviewTime">{{
              ruleForm.recentAnnualReviewTime
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="下次年审日期" prop="nextAnnualReviewTime">{{
              ruleForm.nextAnnualReviewTime
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="最近保养日期" prop="recentMaintenanceTime">{{
              ruleForm.recentMaintenanceTime
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="下次保养日期" prop="nextMaintenanceTime">{{
              ruleForm.nextMaintenanceTime
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="最近交强险开始日期" prop="lastMotcStartTime">{{
              ruleForm.lastMotcStartTime
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="交强险终保日期" prop="lastMotcEndTime">{{ ruleForm.lastMotcEndTime }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="最近商业险开始日期" prop="lastCommercialStartTime">{{
              ruleForm.lastCommercialStartTime
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="商业险终保日期" prop="lastCommercialEndTime">{{
              ruleForm.lastCommercialEndTime
            }}</el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="车辆行驶证编号" prop="drivingCertNumber">{{
              ruleForm.drivingCertNumber
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆行驶证有效期开始日期" prop="drivingCertBeginDate">{{
              ruleForm.drivingCertBeginDate
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆行驶证有效期结束日期" prop="drivingCertEndDate">{{
              ruleForm.drivingCertEndDate
            }}</el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="车辆行驶证登记日期" prop="drivingCertRegDate">{{
              ruleForm.drivingCertRegDate
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="两客一危设备ID号" prop="deviceId">{{ ruleForm.deviceId }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="两客一危设备SIM卡号" prop="deviceSimNumber">{{
              ruleForm.deviceSimNumber
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="尾门设备ID号" prop="sternDoorDeviceId">{{ ruleForm.sternDoorDeviceId }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="尾门设备SIM卡号" prop="sternDoorDeviceSimNumber">{{
              ruleForm.sternDoorDeviceSimNumber
            }}</el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆行驶证件主页照片" prop="drivingCertPhotoFront">
              <el-image
                v-if="ruleForm.drivingCertPhotoFront && ruleForm.drivingCertPhotoFront.url"
                class="image-box"
                :src="ruleForm.drivingCertPhotoFront.url"
                :preview-src-list="[ruleForm.drivingCertPhotoFront.url]"
              >
              </el-image>
              <span v-else>-</span>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="车辆行驶证件副页照片" prop="drivingCertPhotoBack">
              <el-image
                v-if="ruleForm.drivingCertPhotoBack && ruleForm.drivingCertPhotoBack.url"
                class="image-box"
                :src="ruleForm.drivingCertPhotoBack.url"
                :preview-src-list="[ruleForm.drivingCertPhotoBack.url]"
              >
              </el-image>
              <span v-else>-</span>
            </el-form-item>
          </el-col>
          <template v-if="ruleForm.operationalNature === 0">
            <el-col :md="24" :lg="12">
              <el-form-item label="最近二级维护日期" prop="lastSecMaintTime">{{
                ruleForm.lastSecMaintTime
              }}</el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="下次二级维护日期" prop="nextSecMaintTime">{{
                ruleForm.nextSecMaintTime
              }}</el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="最近承运险开始日期" prop="lastTransInsuranceTime">{{
                ruleForm.lastTransInsuranceTime
              }}</el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="承运险终保日期" prop="finalTransInsuranceTime">{{
                ruleForm.finalTransInsuranceTime
              }}</el-form-item>
            </el-col>
            <!-- 营运证 -->
            <el-col>
              <el-form-item label="营运证编号" prop="operationCertNumber">{{
                ruleForm.operationCertNumber
              }}</el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="营运证有效期开始日期" prop="operationCertBeginDate">{{
                ruleForm.operationCertBeginDate
              }}</el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="营运证有效期结束日期" prop="operationCertEndDate">{{
                ruleForm.operationCertEndDate
              }}</el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="营运证登记日期" prop="operationCertRegDate">{{
                ruleForm.operationCertRegDate
              }}</el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="营运证证件主页照片" prop="operationCertPhotoFront">
                <el-image
                  v-if="ruleForm.operationCertPhotoFront && ruleForm.operationCertPhotoFront.url"
                  class="image-box"
                  :src="ruleForm.operationCertPhotoFront.url"
                  :preview-src-list="[ruleForm.operationCertPhotoFront.url]"
                >
                </el-image>
                <span v-else>-</span>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="营运证证件副页照片" prop="operationCertPhotoBack">
                <el-image
                  v-if="ruleForm.operationCertPhotoBack && ruleForm.operationCertPhotoBack.url"
                  class="image-box"
                  :src="ruleForm.operationCertPhotoBack.url"
                  :preview-src-list="[ruleForm.operationCertPhotoBack.url]"
                >
                </el-image>
                <span v-else>-</span>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun } from "@/api/base";
  import { CERT_TYPES, OPERATIONAL_NATURE, WORKING_STATUS, CAR_STATUS, VEHICLE_ENERGY_TYPE } from "@/enums";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        apis: {
          info: "/api/vehicle/dossier/get/",
        },
        ruleForm: {},
        rules: {
          plateNumber: [{ required: true, message: "请输入车牌号", trigger: "blur" }],
          grossWeight: [{ required: true, message: "请输入总质量", trigger: "blur" }],
          vehicleModel: [{ required: true, message: "请选择车辆型号", trigger: "change" }],
          emissionStandard: [{ required: true, message: "请选择排放标准", trigger: "change" }],
          purchaseDate: [{ required: true, message: "请选择购买日期", trigger: "change" }],
          workingStatus: [{ required: true, message: "请选择工作状态", trigger: "change" }],
          status: [{ required: true, message: "请选择车辆状态", trigger: "change" }],
          vehicleEnergyType: [{ required: true, message: "请选择能源类型", trigger: "change" }],
          operationalNature: [{ required: true, message: "请选择营运性质", trigger: "change" }],
          recentAnnualReviewTime: [{ required: true, message: "请选择最近年审日期", trigger: "change" }],
          nextAnnualReviewTime: [{ required: true, message: "请选择下次年审日期", trigger: "change" }],
          recentMaintenanceTime: [{ required: true, message: "请选择最近保养日期", trigger: "change" }],
          lastMotcStartTime: [{ required: true, message: "请选择最近交强险开始日期", trigger: "change" }],
          lastMotcEndTime: [{ required: true, message: "请选择交强险终保日期", trigger: "change" }],
          lastCommercialStartTime: [{ required: true, message: "请选择最近商业险开始日期", trigger: "change" }],
          lastCommercialEndTime: [{ required: true, message: "请选择商业险终保日期", trigger: "change" }],
          vin: [{ required: true, message: "请输入车架号", trigger: "blur" }],
          loadCapacity: [{ required: true, message: "请输入装载重量", trigger: "blur" }],
          engineNumber: [{ required: true, message: "请输入发动机号", trigger: "blur" }],
          fuelTankCapacity: [{ required: true, message: "请选择油箱容量", trigger: "change" }],

          operationCertNumber: [{ required: true, message: "请输入营运证编号", trigger: "blur" }],
          operationCertBeginDate: [{ required: true, message: "请选择营运证有效期开始日期", trigger: "change" }],
          operationCertEndDate: [{ required: true, message: "请选择营运证有效期结束日期", trigger: "change" }],
          operationCertRegDate: [{ required: true, message: "请选择营运证登记日期", trigger: "change" }],
          drivingCertNumber: [{ required: true, message: "请输入车辆行驶证编号", trigger: "blur" }],
          drivingCertBeginDate: [{ required: true, message: "请选择车辆行驶证有效期开始日期", trigger: "change" }],
          drivingCertEndDate: [{ required: true, message: "请选择车辆行驶证有效期结束日期", trigger: "change" }],
          drivingCertRegDate: [{ required: true, message: "请选择车辆行驶证登记日期", trigger: "change" }],
          lastSecMaintTime: [{ required: true, message: "请选择最近二级维护日期", trigger: "change" }],
          nextSecMaintTime: [{ required: true, message: "请选择下次二级维护日期", trigger: "change" }],
          lastTransInsuranceTime: [{ required: true, message: "请选择最近承运险开始日期", trigger: "change" }],
          finalTransInsuranceTime: [{ required: true, message: "请选择承运险终保日期", trigger: "change" }],
        },
        CERT_TYPES,
        OPERATIONAL_NATURE,
        WORKING_STATUS,
        CAR_STATUS,
        VEHICLE_ENERGY_TYPE,
      };
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取详情
      async getRecord() {
        this.loading = true;
        try {
          let res = await getInfoApiFun(this.recordId, this.apis.info);
          if (res.success) {
            this.ruleForm = res.data;
            for (let key in this.ruleForm) {
              this.ruleForm[key] =
                this.ruleForm[key] || this.ruleForm[key] === 0 || this.ruleForm[key] === false
                  ? this.ruleForm[key]
                  : "-";
            }
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      closeRecord() {
        this.$emit("closeRecord");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .record-content {
    flex: 1;
    overflow-y: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .image-box {
    width: 148px;
    height: 148px;
    overflow: hidden;
    border: 1px solid #c0ccda;
    border-radius: 6px;
  }
</style>
