<template>
  <div class="micro-app-sctmp_base" v-loading="loading">
    <el-dialog
      title="车辆加油记录"
      :visible.sync="dialogVisible"
      width="1400px"
      top="0"
      destroy-on-close
      :modal-append-to-body="false"
      :before-close="closeDialog"
      @open="initData"
    >
      <div class="full-height" v-loading="loading">
        <div class="main-index">
          <header class="header">
            <div class="header-left mr-10">
              <el-input class="w400" v-model="keyword" placeholder="请输入车牌号/司机名称" clearable></el-input>
            </div>
            <div class="filter-box">
              <el-badge :value="filterNumber" class="filter-badge">
                <el-button type="primary" @click="showFilter = !showFilter">
                  <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                  筛选
                  <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
                </el-button>
              </el-badge>
            </div>
            <el-button @click="initData">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
          </header>
          <FilterContent label-width="140px" v-show="showFilter">
            <el-col :span="12">
              <el-form-item label="加油类型">
                <el-select v-model="filterForm.refuelType" placeholder="请选择加油类型" clearable filterable>
                  <el-option v-for="(item, index) in options" :key="index" :label="item.name" :value="item.id">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </FilterContent>
          <main class="main">
            <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
              <el-table-column type="index" align="center"></el-table-column>
              <el-table-column prop="plateNumber" label="车牌号" align="center"> </el-table-column>
              <el-table-column prop="driverName" label="驾驶司机" align="center"></el-table-column>
              <el-table-column prop="refuelTypeName" label="加油类型" align="center"></el-table-column>
              <el-table-column prop="fuelQuantity" label="加油/充电量（L/kwh）" align="center"> </el-table-column>
              <el-table-column prop="amount" label="加油/充电金额(元)" align="center"></el-table-column>
              <el-table-column prop="fuelTime" label="加油/充电日期" align="center"></el-table-column>
              <el-table-column prop="totalMileage" label="行驶总里程（Km）" align="center"></el-table-column>
            </el-table>
          </main>
          <Pagination :page="page" @pageChange="pageChange"></Pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun, getInfoApiFun } from "@/api/base";
  import moment from "moment";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    data() {
      return {
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/vehicleFuel/listPage",
          vehicleFuelList: "/api/dict/refuelType/list",
        },
        loading: false,
        keyword: "",
        options: [], //用户列表
        showFilter: false,
        filterForm: {},
      };
    },
    mounted() {
      this.getVehicleFuelList();
    },
    methods: {
      async getVehicleFuelList() {
        let res = await getInfoApiFun("", this.apis.vehicleFuelList);
        if (res.success) {
          this.options = res.data;
        }
      },
      initData() {
        this.keyword = "";
        this.filterForm = {};
        this.page.pageNo = 1;
        this.page.pageSize = 10;
        this.getDataList();
      },
      async getDataList() {
        this.loading = true;
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          fuelBeginTime: moment().startOf("month").format("YYYY-MM-DD"),
          fuelEndTime: moment().endOf("month").format("YYYY-MM-DD"),
          refuelType: this.filterForm.refuelType,
          channelId: this.channelId,
        };
        try {
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
          this.loading = false;
        } catch (_) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.getDataList();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.getDataList();
      },
      closeDialog() {
        this.dialogVisible = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w400 {
    width: 400px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
