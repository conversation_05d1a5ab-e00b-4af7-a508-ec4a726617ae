<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">{{ detailType == 1 ? "月度绩效明细" : "月度基础绩效明细" }}</div>
      <baseTitle title="基础信息"></baseTitle>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="180px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="人员姓名">
              <el-input :value="detailItem.fullName" placeholder="请输入人员姓名" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="人员身份">
              <el-input
                :value="detailItem?.performanceDetailJson?.userIdentityName"
                placeholder="请输入人员身份"
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="绩效明细"></baseTitle>
        <el-form-item label="出勤天数">{{ performanceDetailJson?.dataDetail?.length }}（天）</el-form-item>
        <el-form-item label="点位收运绩效明细"></el-form-item>
        <el-table
          class="table-box"
          :data="performanceDetailJson.dataDetail"
          :header-cell-style="{ background: '#F5F7F9' }"
          border
        >
          <el-table-column prop="date" label="日期" align="center"></el-table-column>
          <!-- 大型床位点位收运情况 -->
          <el-table-column
            label="大型床位点位收运情况"
            align="center"
            v-if="dataDetailFields?.includes('drumOrBagDetail')"
          >
            <template #default="{ row }">
              <template v-if="row?.drumOrBagDetail">
                <el-link type="primary" @click="openTransportDialog(row, '', [0])">
                  <div
                    >收运点位总数：{{ row.drumOrBagDetail.pointTotal }}&nbsp;&nbsp;&nbsp;&nbsp;总重量：{{
                      row.drumOrBagDetail.rubbishTotal
                    }}（kg）</div
                  >
                  <div v-for="(item, index) in row.drumOrBagDetail.list" :key="index"
                    >{{ item?.type?.includes("t") ? "桶" : "袋" }}装：{{ item.count }}&nbsp;&nbsp;&nbsp;&nbsp;重量：{{
                      item.rubbish
                    }}（吨）&nbsp;&nbsp;&nbsp;&nbsp;单价：{{ item.price }}/吨</div
                  >
                </el-link>
              </template>
            </template>
          </el-table-column>
          <!-- 小型床位点位收运情况 -->
          <el-table-column
            label="小型床位点位收运情况"
            align="center"
            v-if="dataDetailFields?.includes('collectionPointDetail')"
          >
            <template #default="{ row }">
              <template v-if="row?.collectionPointDetail">
                <el-link type="primary" @click="openTransportDialog(row, '', [2, 3])">
                  <div>收运点位总数：{{ row.collectionPointDetail.pointTotal }}</div>
                  <div v-for="(item, index) in row.collectionPointDetail.list" :key="index"
                    >{{ item?.type?.includes("t") ? "桶" : "袋" }}装：{{ item.count }}&nbsp;&nbsp;&nbsp;&nbsp;单价：{{
                      item.price
                    }}/点</div
                  >
                </el-link>
              </template>
            </template>
          </el-table-column>
          <!-- 常规诊所点位收运情况 -->
          <el-table-column label="常规诊所点位" align="center" v-if="dataDetailFields?.includes('clinicDetail')">
            <template #default="{ row }">
              <template v-if="row?.clinicDetail">
                <el-link type="primary" @click="openTransportDialog(row, '', [1])">
                  <div>收运点位总数：{{ row.clinicDetail.collect }}</div>
                  <div>当月累计：{{ row.clinicDetail.accumulate }}/{{ row.clinicDetail.baseNumber }}点</div>
                </el-link>
              </template>
            </template>
          </el-table-column>
          <!-- 南沙诊所点位收运情况 -->
          <el-table-column label="南沙诊所点位" align="center" v-if="dataDetailFields?.includes('nsClinicDetail')">
            <template #default="{ row }">
              <template v-if="row?.nsClinicDetail">
                <el-link type="primary" @click="openTransportDialog(row, '440115000000', [1])">
                  <div>收运点位总数：{{ row.nsClinicDetail.collect }}</div>
                  <div>当月累计：{{ row.nsClinicDetail.accumulate }}/{{ row.nsClinicDetail.baseNumber }}点</div>
                </el-link>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="加班情况" align="center">
            <template #default="{ row }">
              <template v-if="row?.overtimePay">
                <div>
                  <el-link type="primary"
                    >{{ row?.overtimePay?.type }}加班：{{ row?.overtimePay?.amount }}（元）</el-link
                  >
                </div>
              </template>
              <template v-if="row?.overtime?.length > 0">
                <el-link
                  type="primary"
                  v-for="(item, index) in row?.overtime"
                  :key="index"
                  @click="openOvertimeWaybillDialog(item)"
                  >{{ item?.overtimeType ? "申请" : "下发" }}加班任务<span v-if="row?.overtime?.length > 1">{{
                    index + 1
                  }}</span
                  >：{{ item?.overtimePrice }}（元）</el-link
                >
              </template>
            </template>
          </el-table-column>
          <!-- 备注 -->
          <el-table-column label="备注" align="center">
            <template #default="{ row }">
              <template v-if="row?.districtSubsidy">
                <div
                  ><el-link type="primary"
                    >{{ row?.districtSubsidy?.districtName }}区域补贴：{{ row?.districtSubsidy?.amount }}（元）</el-link
                  ></div
                >
              </template>
              <div v-if="row.waybillChanges?.length > 0">
                <div><el-link type="primary" @click="openWaybillDialog(row, 'waybillChanges')">顶班</el-link></div>
              </div>
              <div v-if="row.driverWaybills?.length > 0">
                <div
                  >司机单人收运：<el-link type="primary" @click="openPointTaskDialog(row, 'driverWaybills')"
                    >单人收运点位数量&nbsp;&nbsp;{{ row?.driverWaybills?.length }}</el-link
                  ></div
                >
              </div>
              <div v-if="row.supercargoWaybills?.length > 0">
                <div
                  >单押运收运：<el-link type="primary" @click="openPointTaskDialog(row, 'supercargoWaybills')"
                    >单押运收运点位数量&nbsp;&nbsp;{{ row?.supercargoWaybills.length }}</el-link
                  ></div
                >
              </div>
            </template>
          </el-table-column>
          <!-- 当日绩效 -->
          <el-table-column prop="dayPerformance" label="当日绩效" align="center">
            <template #default="{ row }">
              <div v-if="row.dayPerformance > 0">
                <el-link type="primary">{{ row.dayPerformance }}（元）</el-link>
              </div>
              <div v-if="row.clinicDetail || row.nsClinicDetail">
                <el-link type="primary">收运诊所点位：{{ row.clinicTotal }}</el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <template v-if="dataDetailFields.length > 0 || detailType == 1">
          <baseTitle title="绩效计算"></baseTitle>
          <el-table class="table-box" :data="[{}]" :header-cell-style="{ background: '#F5F7F9' }" border>
            <!-- 大型床位绩效汇总 -->
            <el-table-column
              label="大型床位绩效汇总"
              align="center"
              v-if="dataDetailFields?.includes('drumOrBagDetail')"
            >
              <template>
                <div>{{ performanceDetailJson.drumOrBagPerformance }}（元）</div>
                <div>桶装点位数量：{{ performanceDetailJson.bucketCount }}</div>
                <div>桶装点位总重量：{{ performanceDetailJson.bucketWeight }}（kg）</div>
                <div>桶装点位：{{ performanceDetailJson.drumOrBagBucketPerformance }}（元）</div>
                <div>袋装点位数量：{{ performanceDetailJson.bagCount }}</div>
                <div>袋装点位总重量：{{ performanceDetailJson.bagWeight }}（kg）</div>
                <div>袋装点位：{{ performanceDetailJson.drumOrBagBagPerformance }}（元）</div>
              </template>
            </el-table-column>
            <!-- 小型床位绩效汇总 -->
            <el-table-column
              label="小型床位绩效汇总"
              align="center"
              v-if="dataDetailFields?.includes('collectionPointDetail')"
            >
              <template>
                <div>{{ performanceDetailJson.collectionPointPerformance }}（元）</div>
                <div>桶装点位：{{ performanceDetailJson.collectionPointBucketPerformance }}（元）</div>
                <div>袋装点位：{{ performanceDetailJson.collectionPointBagPerformance }}（元）</div>
              </template>
            </el-table-column>
            <!-- 诊所绩效汇总 -->
            <el-table-column label="诊所绩效汇总" align="center" v-if="detailItem?.hasClinicGroup">
              <template>
                <!-- 绩效计算公式展示区域 -->
                <div v-if="(detailItem?.lessType || detailItem?.lessType === 0) && detailItem?.pathFactor">
                  <!-- lessType为0时的计算公式: (基础工资 × 系数 + 超额工资 + 单干 = 绩效工资) -->
                  <span v-if="detailItem?.lessType === 0"
                    >{{
                      detailItem?.clinicGroupParam?.clinicBaseSalary || detailItem?.clinicGroupParam?.nsClinicBaseSalary
                    }}
                    × {{ detailItem?.pathFactor
                    }}{{
                      detailItem?.clinicGroupParam?.excessSalary
                        ? " + " + detailItem?.clinicGroupParam?.excessSalary
                        : ""
                    }}{{
                      detailItem?.clinicGroupParam?.singletonSalary
                        ? " + " + detailItem?.clinicGroupParam?.singletonSalary
                        : ""
                    }}
                    = {{ detailItem?.clinicGroupParam?.displaySalary }}（元）</span
                  >
                  <!-- lessType为1时的计算公式: (基础工资1 × 系数 + 基础工资2 × 系数 - 扣款1 - 扣款2 + 单干 = 绩效工资) -->
                  <span v-else-if="detailItem?.lessType === 1">
                    {{
                      detailItem?.clinicGroupParam?.clinicBaseSalary
                        ? detailItem?.clinicGroupParam?.clinicBaseSalary + " × " + detailItem?.pathFactor
                        : ""
                    }}
                    {{
                      detailItem?.clinicGroupParam?.clinicBaseSalary && detailItem?.clinicGroupParam?.nsClinicBaseSalary
                        ? " + "
                        : ""
                    }}
                    {{
                      detailItem?.clinicGroupParam?.nsClinicBaseSalary
                        ? detailItem?.clinicGroupParam?.nsClinicBaseSalary + " × " + detailItem?.pathFactor
                        : ""
                    }}{{
                      detailItem?.clinicGroupParam?.clinicDeduction
                        ? " - " + detailItem?.clinicGroupParam?.clinicDeduction
                        : ""
                    }}{{
                      detailItem?.clinicGroupParam?.nsClinicDeduction
                        ? " - " + detailItem?.clinicGroupParam?.nsClinicDeduction
                        : ""
                    }}{{
                      detailItem?.clinicGroupParam?.singletonSalary
                        ? " + " + detailItem?.clinicGroupParam?.singletonSalary
                        : ""
                    }}
                    = {{ detailItem?.clinicGroupParam?.displaySalary }}（元）
                  </span>
                  <!-- 其他情况的计算公式: (点数 × 4 × 线路系数 + 单干 = 绩效工资) -->
                  <span v-else
                    >{{ detailItem?.clinicGroupParam?.pointNum }} × 4 × {{ detailItem?.pathFactor
                    }}{{
                      detailItem?.clinicGroupParam?.singletonSalary
                        ? " + " + detailItem?.clinicGroupParam?.singletonSalary
                        : ""
                    }}
                    = {{ detailItem?.clinicGroupParam?.displaySalary }}（元）</span
                  >
                </div>
                <div class="select-box">
                  <div class="select-left">
                    <span class="el-icon-warning"></span>
                  </div>
                  <div class="select-right">
                    <el-form-item
                      label="线路系数"
                      label-width="150px"
                      prop="pathFactor"
                      v-if="canScore && performanceDetailJson?.clinicGroupParam?.factorList?.length"
                    >
                      <el-select v-model="ruleForm.pathFactor" placeholder="请选择线路系数" clearable filterable>
                        <el-option
                          v-for="(item, index) in performanceDetailJson?.clinicGroupParam?.factorList"
                          :key="index"
                          :label="item.factor + '（' + item.area + '）'"
                          :value="item.factor"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-link style="line-height: 34px" type="primary" :underline="false" v-else
                      >线路系数：{{ detailItem?.pathFactor ? detailItem?.pathFactor : "待选择线路系数" }}</el-link
                    >
                    <template v-if="detailItem.lessType !== 0">
                      <el-form-item label="不足基准点位计算" label-width="150px" prop="lessType" v-if="canScore">
                        <el-select
                          v-model="ruleForm.lessType"
                          placeholder="请选择不足基准时的规则"
                          clearable
                          filterable
                        >
                          <el-option
                            v-for="item in lessTypeList"
                            :key="item.id"
                            :label="item.name"
                            :value="item.id"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                      <div v-else>
                        <el-link style="line-height: 34px; margin-bottom: 4px" type="primary" :underline="false"
                          >不足基准点位计算：{{
                            detailItem.lessType ? lessTypeObj[detailItem.lessType] : "待选择不足基准时的规则"
                          }}</el-link
                        >
                      </div>
                    </template>
                  </div>
                </div>
                <div v-if="dataDetailFields?.includes('clinicDetail')"
                  >收运常规诊所点位数量：{{ performanceDetailJson?.clinicCollectNum }}/{{
                    performanceDetailJson?.clinicTotal
                  }}&nbsp;&nbsp;&nbsp;&nbsp;单人收运数量 = {{ performanceDetailJson?.clinicSingletonNum }}</div
                >
                <div v-if="dataDetailFields?.includes('nsClinicDetail')"
                  >收运南沙诊所点位数量：{{ performanceDetailJson?.nsClinicCollectNum }}/{{
                    performanceDetailJson?.nsClinicTotal
                  }}&nbsp;&nbsp;&nbsp;&nbsp;单人收运数量 = {{ performanceDetailJson?.nsClinicSingletonNum }}</div
                >
              </template>
            </el-table-column>
            <el-table-column
              label="区域补贴"
              align="center"
              v-if="
                performanceDetailJson?.totalDistrictSubsidyTotal ||
                performanceDetailJson?.totalDistrictSubsidyTotal === 0
              "
            >
              <template>{{ performanceDetailJson?.totalDistrictSubsidyTotal }}（元）</template>
            </el-table-column>
            <el-table-column
              label="加班费"
              align="center"
              v-if="
                performanceDetailJson?.totalOvertimePerformance || performanceDetailJson?.totalOvertimePerformance === 0
              "
            >
              <template>{{ performanceDetailJson?.totalOvertimePerformance }}（元）</template>
            </el-table-column>
            <el-table-column label="班组长补贴" align="center" v-if="performanceDetailJson?.teamLeaderSubsidy">
              <template>{{ performanceDetailJson?.teamLeaderSubsidy }}（元）</template>
            </el-table-column>
            <template v-if="detailType == 1">
              <el-table-column label="系数" align="center">
                <template>
                  <div class="table-title">考核系数：{{ detailItem?.integrate }}</div>
                </template>
              </el-table-column>
              <el-table-column label="扣款" align="center">
                <template>{{ deductSalary }}（元）</template>
              </el-table-column>
              <el-table-column label="绩效汇总" align="center">
                <template>{{ detailItem.payIncentives }}（元）</template>
              </el-table-column>
            </template>
          </el-table>
        </template>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeDetail">返回</el-button>
      <el-button type="primary" @click="savedDetailThrottling" v-if="canScore">保存</el-button>
    </div>
    <waybillDialog :value.sync="showWaybillDialog" :tableData="tableData"></waybillDialog>
    <pointTaskDialog :value.sync="showPointTaskDialog" :tableData="pointTaskTableData"></pointTaskDialog>
    <transportDialog
      :value.sync="showTransportDialog"
      :transportRow="transportRow"
      :transportType="transportType"
      :detailItem="detailItem"
    ></transportDialog>
  </div>
</template>

<script>
  import { createApiFun, getInfoApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import { USER_IDENTITY, OVER_TYPE } from "@/enums";
  import waybillDialog from "./waybillDialog";
  import pointTaskDialog from "./pointTaskDialog";
  import transportDialog from "./transportDialog";
  import { deepClone } from "logan-common/utils";
  export default {
    props: {
      // 详情id
      detailId: {
        type: String,
        default: "",
      },
      // 绩效明细类型 0-基础绩效 1-绩效明细
      detailType: {
        type: Number,
        default: 0,
      },
      // 扣款
      deductSalary: {
        type: Number,
        default: 0,
      },
      // 是否可评分
      canScore: {
        type: Boolean,
        default: false,
      },
    },
    components: {
      baseTitle,
      waybillDialog,
      pointTaskDialog,
      transportDialog,
    },
    data() {
      return {
        ruleForm: {
          pathFactor: "",
          lessType: "",
        },
        rules: {
          pathFactor: [{ required: true, message: "请选择线路系数", trigger: "change" }],
          lessType: [{ required: true, message: "请选择不足基准时的规则", trigger: "change" }],
        },
        apis: {
          create: "/api/waybill/sponsorWaybills",
          info: "/api/assess/form/get/",
          save: "/api/assess/form/clinicParam",
        },
        savedDetailThrottling: () => {},
        loading: false,
        detailItem: {},
        performanceDetailJson: {},
        dataDetailFields: [],
        USER_IDENTITY,
        OVER_TYPE,
        // 不足基准点位计算规则 0-按规则扣费 1-单个点位计费（x4）
        lessTypeList: [
          { id: 1, name: "按规则扣费" },
          { id: 2, name: "单个点位计费（x4）" },
        ],
        lessTypeObj: { 1: "按规则扣费", 2: "单个点位计费（x4）" },
        tableData: [],
        showWaybillDialog: false,
        pointTaskTableData: [],
        showPointTaskDialog: false,
        showTransportDialog: false,
        transportRow: {},
        transportDistrictId: "",
        transportType: [],
      };
    },
    async created() {
      this.savedDetailThrottling = this.$throttling(this.saveDetail, 500);
    },
    mounted() {
      this.getDetail();
    },
    methods: {
      // 获取详情
      async getDetail() {
        try {
          let res = await getInfoApiFun(this.detailId, this.apis.info);
          if (res.success) {
            try {
              res.data.performanceDetailJson = JSON.parse(res.data.performanceDetailJson);
              res.data.clinicGroupParam = JSON.parse(res.data.clinicGroupParam);
            } catch (error) {
              res.data.performanceDetailJson = {};
              res.data.clinicGroupParam = {};
            }
            this.detailItem = res.data;
            this.performanceDetailJson = res.data.performanceDetailJson;
            let userIdentityName = "";
            this.performanceDetailJson.userIdentity.split(",").forEach((i) => {
              userIdentityName += USER_IDENTITY[i] + ",";
            });
            this.performanceDetailJson.userIdentityName = userIdentityName.slice(0, -1);
            this.performanceDetailJson.dataDetail.forEach((list) => {
              for (let key in list) {
                this.dataDetailFields.push(key);
              }
            });
            this.dataDetailFields = [...new Set(this.dataDetailFields)];
            this.ruleForm.pathFactor = this.detailItem.pathFactor;
            this.ruleForm.lessType = this.detailItem.lessType;
            // console.log("this.detailItem ==> ", this.detailItem);
            // console.log("this.performanceDetailJson ==> ", this.performanceDetailJson);
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 关闭弹窗
      closeDetail() {
        this.$emit("closeDetail");
      },
      // 打开收运单弹窗
      openWaybillDialog(row, field) {
        this.showWaybillDialog = true;
        this.tableData = row[field];
      },
      // 打开加班弹窗
      openOvertimeWaybillDialog(item) {
        this.showWaybillDialog = true;
        this.tableData = [];
        this.tableData.push(item);
      },
      // 打开收运任务明细弹窗
      openPointTaskDialog(row, field) {
        this.showPointTaskDialog = true;
        this.pointTaskTableData = deepClone(row[field]);
        this.pointTaskTableData.forEach((data) => {
          try {
            data.picture = JSON.parse(data.picture);
          } catch (error) {
            data.picture = "";
          }
        });
      },
      // 打开点位收运情况任务明细
      openTransportDialog(row, districtId, type = []) {
        this.transportRow = row; // 点位收运情况
        this.transportDistrictId = districtId;
        this.transportType = type;
        this.showTransportDialog = true;
      },
      // 保存
      saveDetail() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun({ assessFormId: this.detailId, ...this.ruleForm }, this.apis.save);
              if (res.success) {
                this.$message.success(`保存成功`);
                this.closeDetail();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .w250 {
    width: 250px;
  }
  .table-box {
    margin-bottom: 20px;
  }
  .full-width {
    width: 100%;
  }
  .select-box {
    display: flex;
    justify-content: center;
    margin-top: 10px;
    .select-left {
      font-size: 20px;
      line-height: 36px;
      color: #e6a23c;
      margin-right: 10px;
    }
  }
  .overtime-box {
    display: flex;
    justify-content: center;
  }
  ::v-deep .task-form-item .el-form-item__content {
    margin-left: 0 !important;
  }
</style>
