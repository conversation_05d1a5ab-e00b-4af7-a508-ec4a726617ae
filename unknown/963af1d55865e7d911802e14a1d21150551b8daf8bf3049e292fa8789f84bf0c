<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">{{ recordId ? "回访记录编辑" : "新增回访记录" }}</div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px">
        <baseTitle title="基础信息"></baseTitle>
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="客商名称" prop="customerName">
              <el-input
                v-model="ruleForm.customerName"
                placeholder="请输入客商名称"
                clearable
                maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="客商联系电话" prop="customerContactNumber">
              <el-input
                v-model="ruleForm.customerContactNumber"
                placeholder="请输入客商联系电话"
                clearable
                :maxlength="11"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="12" :lg="12">
            <el-form-item label="客商电子邮件" prop="customerEmail">
              <el-input
                v-model="ruleForm.customerEmail"
                placeholder="请输入客商电子邮件"
                clearable
                :maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="回访方式" prop="method">
              <el-select v-model="ruleForm.method" placeholder="请选择回访方式" clearable filterable>
                <el-option v-for="(item, index) in VISIT_RECORD_TYPE" :key="index" :label="item" :value="index">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="回访人" prop="personName">
              <el-input
                v-model="ruleForm.personName"
                placeholder="请输入回访人名称"
                clearable
                maxlength="15"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="回访日期" prop="followUpDate">
              <el-date-picker
                v-model="ruleForm.followUpDate"
                type="date"
                placeholder="请选择回访日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="概要信息"></baseTitle>
        <el-row>
          <el-col>
            <el-form-item label="主要回访内容" prop="content">
              <el-input
                v-model="ruleForm.content"
                placeholder="请输入主要回访内容"
                clearable
                type="textarea"
                rows="6"
                maxlength="500"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="客户反馈意见" prop="opinion">
              <el-input
                v-model="ruleForm.opinion"
                placeholder="请输入客户反馈意见"
                clearable
                type="textarea"
                rows="6"
                maxlength="500"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="处理信息"></baseTitle>
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="处理情况" prop="handlingStatus">
              <el-select v-model="ruleForm.handlingStatus" placeholder="请选择处理情况" clearable filterable>
                <el-option v-for="(item, index) in VISIT_HANDLE_STATUS" :key="index" :label="item" :value="index">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <template v-if="ruleForm.handlingStatus === 1">
            <el-col :md="24" :lg="12">
              <el-form-item label="处理日期" prop="handlingDate">
                <el-date-picker
                  v-model="ruleForm.handlingDate"
                  type="date"
                  placeholder="请选择处理日期"
                  clearable
                  value-format="yyyy-MM-dd"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="处理备注" prop="remarks">
                <el-input
                  v-model="ruleForm.remarks"
                  placeholder="请输入处理备注"
                  clearable
                  type="textarea"
                  rows="6"
                  maxlength="500"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
        <baseTitle title="回访登记表详情"></baseTitle>
        <div v-for="(item, index) in ruleForm.questions" :key="item.id">
          <el-form-item
            class="question-item"
            :label="`${index + 1}.${item.name}`"
            label-width="100%"
            :required="item.required"
          ></el-form-item>
          <el-form-item
            :prop="`questions.${index}.value`"
            label=""
            label-width="0"
            :rules="item.required ? questionRules.textarea : []"
            v-if="item.type === 1"
          >
            <el-input
              v-model="item.value"
              placeholder="请输入内容"
              clearable
              type="textarea"
              rows="6"
              maxlength="500"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item
            :prop="`questions.${index}.value`"
            label=""
            label-width="0"
            :rules="item.required ? questionRules.radio : []"
            v-else-if="item.type === 2"
          >
            <el-radio-group v-model="item.value">
              <el-radio :label="option" v-for="(option, optionIndex) in item.optionList" :key="optionIndex"></el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            :prop="`questions.${index}.value`"
            label=""
            label-width="0"
            :rules="item.required ? questionRules.checkbox : []"
            v-else-if="item.type === 3"
          >
            <el-checkbox-group v-model="item.value">
              <el-checkbox
                :label="option"
                v-for="(option, optionIndex) in item.optionList"
                :key="optionIndex"
              ></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
        <baseTitle title="附件"></baseTitle>
        <el-row>
          <el-col>
            <el-form-item label="附件">
              <FileUpload
                listType="text"
                @uploadChange="uploadChangeFileList"
                :imageList="imageList"
                :limit="5"
                :suffix="suffix"
                :accept="suffix"
              >
                <template #tips><el-tag type="warning">请上传回访登记表扫描件、照片。</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import FileUpload from "@/components/FileUpload";
  import baseTitle from "@/components/baseTitle";
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  import { VISIT_RECORD_TYPE, VISIT_HANDLE_STATUS, QUESTION_TYPE } from "@/enums";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      FileUpload,
      baseTitle,
    },
    data() {
      return {
        ruleForm: {
          customerName: "", //客商名称
          customerContactNumber: "", //客商联系电话
          customerEmail: "", //客商电子邮件
          method: "", //回访方式
          personName: "", //回访人
          followUpDate: "", //回访日期
          content: "", //主要回访内容
          opinion: "", //客户反馈意见
          handlingStatus: "", //处理情况
          handlingDate: "", //处理日期
          remarks: "", //处理备注
          questions: [],
        },
        rules: {
          customerName: [{ required: true, message: "请输入客商名称", trigger: "blur" }],
          customerContactNumber: [{ required: true, message: "请输入客商联系电话", trigger: "blur" }],
          method: [{ required: true, message: "请选择回访方式", trigger: "change" }],
          personName: [{ required: true, message: "请输入回访人名称", trigger: "blur" }],
          followUpDate: [{ required: true, message: "请选择回访日期", trigger: "change" }],
          handlingStatus: [{ required: true, message: "请选择处理情况", trigger: "change" }],
          handlingDate: [{ required: true, message: "请选择处理日期", trigger: "change" }],
          remarks: [{ required: true, message: "请输入处理备注", trigger: "blur" }],
        },
        questionRules: {
          textarea: [{ required: true, message: "请输入内容", trigger: "blur" }],
          radio: [{ required: true, message: "请选择单选内容", trigger: "change" }],
          checkbox: [{ required: true, message: "请选择多选内容", trigger: "change" }],
        },
        apis: {
          create: "/api/followUpRecord/save",
          update: "/api/followUpRecord/update",
          info: "/api/followUpRecord/get/",
          form: "/api/followUpRecord/getPublished",
        },
        VISIT_RECORD_TYPE,
        VISIT_HANDLE_STATUS,
        QUESTION_TYPE,
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [],
        suffix: ".pdf,.png,.jpg,.jpeg,.gif",
        registrationId: "",
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    async mounted() {
      if (this.recordId) {
        await this.getRecord();
      } else {
        this.getVisitForm();
      }
    },
    methods: {
      // 获取已发布回访登记表
      async getVisitForm() {
        let res = await getInfoApiFun("", this.apis.form);
        if (res.success) {
          this.registrationId = res.data.id;
          let questions = res.data.questions;
          questions.forEach((item) => {
            item.value = item.type === 3 ? [] : "";
          });
          this.$set(this.ruleForm, "questions", questions);
        }
      },
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.registrationId = res.data.registrationId;
          let questions = this.ruleForm.infos.map((item) => {
            return {
              id: item.registrationFormId,
              name: item.textName,
              value: JSON.parse(item.textValue),
              type: item.type,
              optionList: item.options,
              required: item.required,
            };
          });
          this.$set(this.ruleForm, "questions", questions);
          if (this.ruleForm.fileList?.length > 0) {
            this.imageList = this.ruleForm.fileList.map((list) => {
              return {
                name: list.name,
                url: list.url,
                file: list,
              };
            });
          }
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            let params = JSON.parse(JSON.stringify(this.ruleForm));
            params.infos = params.questions.map((item) => {
              return {
                registrationFormId: item.id,
                textName: item.name,
                textValue: JSON.stringify(item.value),
                type: item.type,
                options: item.optionList,
                required: item.required,
              };
            });
            params.registrationId = this.registrationId;
            this.loading = true;
            try {
              let res = this.recordId
                ? await updateApiFun(params, this.apis.update)
                : await createApiFun(params, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      uploadChangeFileList(fileList) {
        if (fileList.length > 0) {
          this.ruleForm.fileList = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
        } else {
          this.ruleForm.fileList = [];
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .type-card {
    margin-bottom: 20px;
  }
  .w-300 {
    width: 300px;
  }
  ::v-deep .question-item .el-form-item__label {
    text-align: left;
  }
</style>
