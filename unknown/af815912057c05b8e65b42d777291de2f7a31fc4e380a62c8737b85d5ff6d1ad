<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">{{ recordId ? "服务评价记录编辑" : "新增服务评价记录" }}</div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px">
        <baseTitle title="基础信息"></baseTitle>
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="客商名称" prop="customerName">
              <el-input
                v-model="ruleForm.customerName"
                placeholder="请输入客商名称"
                clearable
                maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="客商联系电话" prop="customerContactNumber">
              <el-input
                v-model="ruleForm.customerContactNumber"
                placeholder="请输入客商联系电话"
                clearable
                :maxlength="11"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="客商电子邮件" prop="customerEmail">
              <el-input
                v-model="ruleForm.customerEmail"
                placeholder="请输入客商电子邮件"
                clearable
                :maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="评价日期" prop="evaluationDate">
              <el-date-picker
                v-model="ruleForm.evaluationDate"
                type="date"
                placeholder="请选择评价日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="回单据编号" prop="returnDocumentNumber">
              <el-input
                v-model="ruleForm.returnDocumentNumber"
                placeholder="请输入回单据编号"
                clearable
                maxlength="30"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="评价情况"></baseTitle>
        <el-row>
          <el-col>
            <el-form-item label="评价等级" prop="star">
              <el-rate v-model="ruleForm.star" show-text :texts="texts"></el-rate>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="评价内容" prop="content">
              <el-input
                v-model="ruleForm.content"
                placeholder="请输入评价内容"
                clearable
                type="textarea"
                rows="6"
                maxlength="500"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="附件信息"></baseTitle>
        <el-row>
          <el-col :md="8" :lg="8">
            <el-form-item label="附件">
              <FileUpload
                listType="text"
                @uploadChange="uploadChangeFileList"
                :imageList="imageList"
                :limit="5"
                :suffix="suffix"
                :accept="suffix"
              >
                <template #tips><el-tag type="warning">请上传附件（支持图片、文档、PDF）</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  import FileUpload from "@/components/FileUpload";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      baseTitle,
      FileUpload,
    },
    data() {
      return {
        ruleForm: {
          customerName: "", //客商名称
          customerContactNumber: "", //客商联系电话
          customerEmail: "", //客商电子邮件
          evaluationDate: "", //评价日期
          returnDocumentNumber: "", //回单据编号
          content: "", //评价内容
          star: 0, //评价等级
          fileList: [], //附件列表
        },
        rules: {
          customerName: [{ required: true, message: "请输入客商名称", trigger: "blur" }],
          customerContactNumber: [{ required: true, message: "请输入客商联系电话", trigger: "blur" }],
          evaluationDate: [{ required: true, message: "请输入评价日期", trigger: "blur" }],
          returnDocumentNumber: [{ required: true, message: "请输入回单据编号", trigger: "change" }],
          content: [{ required: true, message: "请输入评价", trigger: "blur" }],
          star: [{ required: true, message: "请填写评价等级", trigger: "change" }],
        },
        apis: {
          create: "/api/serviceEvaluationRecord/save",
          update: "/api/serviceEvaluationRecord/update",
          info: "/api/serviceEvaluationRecord/get/",
        },
        saveRecordThrottling: () => {},
        loading: false,
        texts: ["很差", "差", "正常", "满意", "很满意"],
        suffix: ".png,.jpg,.jpeg,.gif,.doc,.docx,.pdf,.xls,.xlsx",
        imageList: [],
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    async mounted() {
      if (this.recordId) {
        await this.getRecord();
      }
    },
    methods: {
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.imageList = this.ruleForm.fileList.map((list) => {
            return {
              url: list.url,
              name: list.name,
              file: list,
            };
          });
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            let params = JSON.parse(JSON.stringify(this.ruleForm));
            this.loading = true;
            try {
              let res = this.recordId
                ? await updateApiFun(params, this.apis.update)
                : await createApiFun(params, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      uploadChangeFileList(fileList) {
        if (fileList.length > 0) {
          this.ruleForm.fileList = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
        } else {
          this.ruleForm.fileList = [];
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .type-card {
    margin-bottom: 20px;
  }
  .w-300 {
    width: 300px;
  }
  ::v-deep .question-item .el-form-item__label {
    text-align: left;
  }
  // 测试反馈 字数限制提示挡住内容
  ::v-deep .el-textarea__inner {
    padding-right: 40px !important;
  }
  ::v-deep .el-rate {
    height: 36px;
    display: flex;
    align-items: center;
  }
</style>
