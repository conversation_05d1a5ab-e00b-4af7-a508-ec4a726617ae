<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      width="70%"
      top="0"
      destroy-on-close
      :before-close="closeRecord"
      :show-close="false"
    >
      <record v-if="showRecord" :recordId="recordId" :recordForm="recordForm" @closeRecord="closeRecord"></record>
      <div class="main-index" v-else>
        <main class="main">
          <el-table
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            height="100%"
            border
            ref="tableRef"
          >
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column prop="waybillCode" label="收运单编号" align="center" min-width="100"></el-table-column>
            <el-table-column
              prop="effectiveDate"
              label="收运单生效日期"
              align="center"
              min-width="160"
            ></el-table-column>
            <el-table-column prop="isTemp" label="临时收运单" align="center" min-width="100">
              <template #default="{ row }">{{ IS_TEMPORARY[row.isTemp] }}</template>
            </el-table-column>
            <el-table-column prop="name" label="路线名称" align="center"></el-table-column>
            <el-table-column prop="districtName" label="所属区域" align="center"></el-table-column>
            <el-table-column prop="type" label="路线属性" align="center" min-width="120">
              <template #default="{ row }">{{ ROUTE_PROPERTY[row.type] }}</template>
            </el-table-column>
            <el-table-column
              prop="defaultVehiclePlateNumber"
              label="默认车辆"
              align="center"
              min-width="120"
            ></el-table-column>
            <el-table-column prop="defaultDriverDossierName" label="默认司机" align="center"></el-table-column>
            <el-table-column prop="supercargoDossierOneName" label="押运工" align="center"></el-table-column>
            <el-table-column prop="supercargoDossierTwoName" label="押运工2" align="center"></el-table-column>
            <el-table-column prop="waybillType" label="收运方式" align="center">
              <template #default="{ row }">{{ WAYBILL_TYPE[row.waybillType] }}</template>
            </el-table-column>
            <el-table-column prop="pointNumber" label="点位数量" align="center"></el-table-column>
            <el-table-column prop="completeCount" label="已收运点位数" align="center"></el-table-column>
            <el-table-column prop="code" label="路线编号" align="center" min-width="120"></el-table-column>
            <el-table-column
              prop="defaultDriverDossierPhone"
              label="司机联系方式"
              align="center"
              min-width="120"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierOnePhone"
              label="押运工联系方式"
              align="center"
              min-width="120"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierTwoPhone"
              label="押运工2联系方式"
              align="center"
              min-width="140"
            ></el-table-column>
            <el-table-column prop="versionNumber" label="路线版本号" align="center" min-width="100">
              <template #default="{ row }">v{{ row.versionNumber.toFixed(1) }}</template>
            </el-table-column>
            <el-table-column prop="updateFullname" label="最近修改人" align="center" min-width="140"></el-table-column>
            <el-table-column prop="updateTime" label="最近修改时间" align="center" min-width="160"></el-table-column>
            <el-table-column label="操作" align="center" fixed="right" min-width="200">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row)">详情</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { IS_TEMPORARY, ROUTE_PROPERTY, WAYBILL_TYPE } from "@/enums";
  import record from "@/views/collectTransportManage/electronicWaybill/components/record.vue";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      tableData: {
        type: Array,
        default: () => [],
      },
    },
    components: {
      record,
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        filterForm: {},
        IS_TEMPORARY,
        ROUTE_PROPERTY,
        WAYBILL_TYPE,
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        recordType: 0,
        recordId: "",
        loading: false,
        recordForm: {},
        showRecord: false,
      };
    },
    methods: {
      // 查看详情
      editRecord(row) {
        this.recordId = row.id;
        this.recordForm = row;
        this.showRecord = true;
      },
      closeRecord(done) {
        if (this.showRecord) {
          this.showRecord = false;
          return;
        }
        done();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w400 {
    width: 400px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
