<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">编辑绩效方案</div>
      <baseTitle title="基础信息"></baseTitle>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="150px" label-suffix="：">
        <el-row>
          <el-col>
            <el-form-item label="绩效方案名称" required>
              <el-input value="按收运点数计算月度绩效（小型床位）" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <baseTitle title="桶装绩效规则信息"></baseTitle>
          <el-col :md="12">
            <el-form-item label="绩效计费类型" required>
              <el-input value="点位计费" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="12">
            <el-form-item label="收运方式" required>
              <el-input value="桶装" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="绩效计算公式" required>
              <el-input value="总收运点数 × 当月各点提成标准 × 当月考核系数" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="点位提成标准信息" required>
              <el-table :data="[{}]" :header-cell-style="{ background: '#F5F7F9' }" border>
                <el-table-column label="司机提成标准（元/点）" align="center">
                  <template>
                    <el-input-number v-model="ruleForm.drumDriverPrice" step-strictly :min="0"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="押运工提成标准（元/点）" align="center">
                  <template>
                    <el-input-number v-model="ruleForm.drumSupercargoPrice" step-strictly :min="0"></el-input-number>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <baseTitle title="袋装绩效规则信息"></baseTitle>
          <el-col :md="12">
            <el-form-item label="绩效计费类型" required>
              <el-input value="点位计费" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="12">
            <el-form-item label="收运方式" required>
              <el-input value="袋装" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="绩效计算公式" required>
              <el-input value="总收运点数 × 当月各点提成标准 × 当月考核系数" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="点位提成标准信息" required>
              <el-table :data="[{}]" :header-cell-style="{ background: '#F5F7F9' }" border>
                <el-table-column prop="price" label="司机提成标准（元/点）" align="center">
                  <template>
                    <el-input-number v-model="ruleForm.bagDriverPrice" step-strictly :min="0"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column prop="factor" label="押运工提成标准（元/点）" align="center">
                  <template>
                    <el-input-number v-model="ruleForm.bagSupercargoPrice" step-strictly :min="0"></el-input-number>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";
  import { createApiFun, updateApiFun } from "@/api/base";
  export default {
    components: {
      baseTitle,
    },
    props: {
      ruleCode: {
        type: String,
        default: "",
      },
      ruleIndex: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        loading: false,
        ruleForm: {
          drumDriverPrice: "", //桶装司机提成标准
          drumSupercargoPrice: "", //桶装押运工提成标准
          bagDriverPrice: "", //袋装司机提成标准
          bagSupercargoPrice: "", //袋装押运工提成标准
        },
        rules: {},
        apis: {
          info: "/api/assess/scheme/set/findByRuleCode",
          save: "/api/assess/scheme/set/updateCollectionPointSet",
        },
        saveRecordThrottling: () => {},
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取详情
      async getRecord() {
        this.loading = true;
        try {
          let res = await createApiFun({ ruleCode: this.ruleCode }, this.apis.info);
          if (res.success) {
            this.ruleForm = res.data;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
          console.log(error);
        }
      },
      // 取消
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await updateApiFun(this.ruleForm, this.apis.save);
              if (res.success) {
                this.$message.success(`保存成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-data {
    margin-bottom: 20px;
  }
</style>
