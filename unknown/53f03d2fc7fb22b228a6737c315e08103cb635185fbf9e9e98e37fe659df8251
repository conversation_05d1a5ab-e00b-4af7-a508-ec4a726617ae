<template>
  <div class="abnormal">
    <main-title title="车辆异常监控"></main-title>
    <ul class="abnormal-list">
      <li class="abnormal-item" v-for="(item, index) in countList" :key="index" @click="itemClick(index)">
        <div class="abnormal-item-label">{{ item.label }}</div>
        <div class="abnormal-item-num">{{ item.num }}</div>
      </li>
    </ul>
    <div class="tab-header">异常数量趋势分析</div>
    <!-- <div class="tab-box">
      <ul class="type-list">
        <li
          class="type-item"
          :class="{ active: activeType == index }"
          v-for="(item, index) in typeList"
          :key="index"
          @click="toggleType(index)"
          >{{ item }}</li
        >
      </ul>
      <ul class="tab-list">
        <li
          class="tab-item"
          :class="{ active: activeDate == index }"
          v-for="(item, index) in dateList"
          :key="index"
          @click="toggleDate(index)"
          >{{ item }}</li
        >
      </ul>
    </div> -->
    <div class="chart-box">
      <div class="chart" id="abnormal-chart"></div>
    </div>
  </div>
</template>

<script>
  import MainTitle from "./MainTitle.vue";
  import * as echarts from "echarts";
  import { getLastSevenDaysMonthDay } from "@/utils";
  import emitter from "@/utils/mitt";
  export default {
    components: {
      MainTitle,
    },
    props: {
      getVehicleAbnormal: {
        type: Object,
        default: () => {},
      },
    },
    watch: {
      getVehicleAbnormal: {
        handler(newV) {
          this.countList[0].num = newV.abnormalNum;
          // this.countList[1].num = newV.driveAbnormalNum;
          let unExceed = false;
          if (newV.combinedResults && newV?.combinedResults.length > 0) {
            // this.option.xAxis[0].data = newV?.combinedResults.map((list) => {
            //   let dateList = list.date.split("-");
            //   return `${dateList[1]}-${dateList[2]}`;
            // });
            newV.combinedResults.forEach((list) => {
              if (list.abnormalCount < 5) {
                unExceed = true;
              }
              let dateList = list.date.split("-");
              let index = this.option.xAxis[0].data.findIndex((item) => item == `${dateList[1]}-${dateList[2]}`);
              this.option.series[0].data[index] = list.abnormalCount;
            });
            if (unExceed) {
              this.option.yAxis[0].max = 5;
            }
            // this.option.series[0].data = newV.combinedResults.map((list) => list.abnormalCount);
            // this.option.series[1].data = newV.combinedResults.map((list) => list.driveAbnormalCount);
            this.$nextTick(() => {
              this.init();
            });
          }
        },
        deep: true,
        immediate: true,
      },
    },
    data() {
      return {
        countList: [
          {
            label: "今日司机上报异常数量",
            num: 0,
          },
          {
            label: "今日驾驶状态提醒",
            num: 0,
          },
        ],
        typeList: ["总情况", "上报异常", "驾驶异常"],
        activeType: 0,
        dateList: ["近七日", "近四周", "近12月"],
        activeDate: 0,
        option: {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
          },
          legend: {
            data: ["司机上报异常", "驾驶状态提醒"],
            textStyle: {
              color: "#fff",
            },
          },
          grid: {
            left: "8%",
            bottom: "8%",
          },
          xAxis: [
            {
              type: "category",
              data: getLastSevenDaysMonthDay(),
              axisLine: {
                lineStyle: {
                  color: "#fff",
                },
              },
            },
          ],
          yAxis: [
            {
              type: "value",
              name: "数量(件)",
              nameTextStyle: {
                color: "#fff",
              },
              axisLabel: {
                color: "#fff",
              },
              interval: 1,
              max: "dataMax",
            },
          ],
          series: [
            {
              name: "司机上报异常",
              type: "bar",
              stack: "Ad",
              emphasis: {
                focus: "series",
              },
              data: [0, 0, 0, 0, 0, 0, 0],
              itemStyle: {
                color: "#91cc75",
              },
              barWidth: "50%",
            },
            {
              name: "驾驶状态提醒",
              type: "bar",
              stack: "Ad",
              emphasis: {
                focus: "series",
              },
              data: [0, 0, 0, 0, 0, 0, 0],
              itemStyle: {
                color: "#5470c6",
              },
              barWidth: "50%",
            },
          ],
        },
      };
    },
    mounted() {
      this.init();
    },
    methods: {
      toggleType() {},
      toggleDate() {},
      init() {
        let chartInstance = echarts.init(document.getElementById("abnormal-chart"));
        chartInstance.setOption(this.option);
      },
      itemClick(index) {
        switch (index) {
          case 0:
            emitter.emit("open-abnormal-dialog", "showAbnormalDialog");
            break;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .abnormal-list {
    display: flex;
    align-items: center;
    justify-content: space-around;
    margin-top: 10px;
    .abnormal-item {
      color: #fff;
      text-align: center;
      cursor: pointer;
      .abnormal-item-label {
        font-size: 14px;
      }
      .abnormal-item-num {
        font-size: 32px;
        margin-top: 10px;
      }
    }
  }
  .tab-header {
    margin: 10px 0;
    color: #fff;
    font-size: 14px;
  }
  .tab-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
  }
  .type-list {
    background-color: rgba(76, 167, 134, 0.3);
    display: inline-flex;
    align-items: center;
    border-radius: 16px;
    color: #fff;
    .type-item {
      width: 70px;
      text-align: center;
      padding: 4px 0;
      font-size: 12px;
      cursor: pointer;
      border-radius: 16px;
      &.active {
        background-color: var(--color-primary);
      }
    }
  }
  .tab-list {
    background-color: rgba(76, 167, 134, 0.3);
    display: inline-flex;
    align-items: center;
    border-radius: 16px;
    color: #fff;
    .tab-item {
      width: 60px;
      text-align: center;
      padding: 4px 0;
      font-size: 12px;
      cursor: pointer;
      border-radius: 16px;
      &.active {
        background-color: var(--color-primary);
      }
    }
  }
  .chart-box {
    width: 500px;
    height: 300px;
    margin-bottom: 10px;
    .chart {
      width: 100%;
      height: 100%;
    }
  }
</style>
