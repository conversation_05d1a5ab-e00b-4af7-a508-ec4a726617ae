<template>
  <div class="micro-app-sctmp_base" v-loading="loading">
    <el-dialog
      :title="`${recordType ? (recordType === 1 ? '路线详情' : '车辆详情') : '区域分配车辆信息'}`"
      :visible.sync="dialogVisible"
      width="1400px"
      top="0"
      destroy-on-close
      :modal-append-to-body="false"
      :before-close="closeDialog"
      @open="initData"
    >
      <div class="full-height" v-loading="loading">
        <VehicleDetail v-if="recordType === 2" :recordId="recordId" @closeRecord="closeDialog"></VehicleDetail>
        <RouteDetail v-else-if="recordType === 1" :recordId="recordId" @closeRecord="closeDialog"></RouteDetail>
        <div class="main-index" v-else>
          <el-tabs v-model="districtId" type="card" @tab-click="handleTabClick">
            <el-tab-pane
              :label="item.name"
              :name="item.id"
              v-for="item in districtOptions"
              :key="item.id"
            ></el-tab-pane>
          </el-tabs>
          <main class="main">
            <el-table
              :data="tableData"
              :header-cell-style="{ background: '#F5F7F9' }"
              height="100%"
              border
              ref="tableRef"
            >
              <el-table-column type="index" align="center"></el-table-column>
              <el-table-column prop="name" label="路线名称" align="center" min-width="120">
                <template #default="{ row }">
                  <el-link type="primary" @click="viewRecord(1, row)">{{ row.name }}</el-link>
                </template>
              </el-table-column>
              <el-table-column prop="districtName" label="所属区域" align="center"></el-table-column>
              <el-table-column prop="type" label="路线属性" align="center" min-width="120">
                <template #default="{ row }">{{ ROUTE_PROPERTY[row.type] }}</template>
              </el-table-column>
              <el-table-column prop="defaultVehiclePlateNumber" label="收运车辆" align="center">
                <template #default="{ row }">
                  <el-link type="primary" @click="viewRecord(2, row)">{{ row.defaultVehiclePlateNumber }}</el-link>
                </template>
              </el-table-column>
              <el-table-column prop="defaultDriverDossierName" label="司机" align="center"></el-table-column>
              <el-table-column prop="supercargoDossierOneName" label="押运工" align="center"></el-table-column>
              <el-table-column prop="supercargoDossierTwoName" label="押运工2" align="center"></el-table-column>
              <el-table-column prop="waybillType" label="收运方式" align="center">
                <template #default="{ row }">{{ WAYBILL_TYPE[row.waybillType] }}</template>
              </el-table-column>
              <el-table-column prop="pointNumber" label="点位数量" align="center"></el-table-column>
            </el-table>
          </main>
          <Pagination :page="page" @pageChange="pageChange"></Pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import { ROUTE_PROPERTY, WAYBILL_TYPE } from "@/enums";
  import RouteDetail from "./RouteDetail.vue";
  import VehicleDetail from "./VehicleDetail.vue";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      listType: {
        type: Number,
        default: 0,
      },
      districtName: {
        type: String,
        default: "",
      },
      channelList: {
        type: Array,
        default: () => [],
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    components: {
      RouteDetail,
      VehicleDetail,
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/pickup/pickupPath/listPage",
        },
        loading: false,
        districtOptions: [],
        districtId: "",
        ROUTE_PROPERTY,
        WAYBILL_TYPE,
        recordId: "",
        recordType: 0,
        gzOptions: [
          {
            id: "440104000000",
            name: "越秀区",
          },
          {
            id: "440111000000",
            name: "白云区",
          },
          {
            id: "440113000000",
            name: "番禺区",
          },
          {
            id: "440105000000",
            name: "海珠区",
          },
          {
            id: "440103000000",
            name: "荔湾区",
          },
          {
            id: "440114000000",
            name: "花都区",
          },
          {
            id: "440117000000",
            name: "从化区",
          },
          {
            id: "440115000000",
            name: "南沙区",
          },
        ],
        czOptions: [
          {
            id: "445102000000",
            name: "湘桥区",
          },
          {
            id: "445103000000",
            name: "潮安区",
          },
          {
            id: "445122000000",
            name: "饶平县",
          },
        ],
        swOptions: [
          {
            id: "441502000000",
            name: "城区",
          },
          {
            id: "441521000000",
            name: "海丰县",
          },
          {
            id: "441523000000",
            name: "陆河县",
          },
          {
            id: "441581000000",
            name: "陆丰市",
          },
        ],
        dialogKey: "gz",
      };
    },
    methods: {
      // tab栏切换事件
      handleTabClick(tab) {
        this.districtId = tab.name;
        this.page.pageNo = 1;
        this.page.pageSize = 10;
        this.getDataList();
      },
      initData() {
        if (this.channelId) {
          let filterItem = this.channelList.filter((list) => list.id === this.channelId)[0];
          if (filterItem.name.includes("潮州")) {
            this.dialogKey = "gz";
          }
          if (filterItem.name.includes("潮州")) {
            this.dialogKey = "cz";
          }
          if (filterItem.name.includes("汕尾")) {
            this.dialogKey = "sw";
          }
        }
        this.districtOptions = this[this.dialogKey + "Options"];
        let item = this.districtOptions.filter((o) => o.name === this.districtName)[0];
        this.districtId = item.id;
        this.page.pageNo = 1;
        this.page.pageSize = 10;
        this.getDataList();
      },
      async getDataList() {
        this.loading = true;
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          districtId: this.districtId,
          status: 0,
        };
        try {
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
          this.loading = false;
        } catch (_) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.getDataList();
      },
      // 查看详情
      viewRecord(type, row) {
        if (type === 1) {
          this.recordId = row.id;
        } else {
          this.recordId = row.defaultVehicleDossierId;
        }
        this.recordType = type;
      },
      closeDialog() {
        if (this.recordType !== 0) {
          this.recordType = 0;
          return;
        }
        this.dialogVisible = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w400 {
    width: 400px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
