<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <component
        v-if="showRecord"
        :is="componentName"
        :ruleCode="ruleCode"
        :ruleIndex="ruleIndex"
        @closeRecord="closeRecord"
        @refreshList="initData"
      />
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="schemeName" placeholder="请输入绩效考核方案名称" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button type="primary" icon="el-icon-plus" @click="initDisposition">初始化绩效配置</el-button>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
            :colNum="12"
          ></InterviewChannel>
          <el-col :span="12">
            <el-form-item label="计费类型">
              <el-select v-model="filterForm.priceType" placeholder="请选择计费类型" clearable filterable>
                <el-option v-for="(item, index) in BILL_TYPE" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column prop="schemeName" label="绩效方案名称" align="center"></el-table-column>
            <el-table-column prop="priceType" label="计费类型" align="center">
              <template #default="{ row }">{{ BILL_TYPE[row.priceType] }}</template>
            </el-table-column>
            <el-table-column prop="ruleType" label="规则类型" align="center">
              <template #default="{ row }">{{ RULE_TYPE[row.ruleType] }}</template>
            </el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ $index }">
                <el-link class="mr-10" type="primary" @click="editRecord($index)">编辑</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import { getListPageApiFun, createApiFun } from "@/api/base";
  import { BILL_TYPE, RULE_TYPE } from "@/enums";
  import recordComponents from "./componentImport";
  export default {
    components: {
      defaultPage,
      ...recordComponents,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          init: "/api/assess/scheme/set/initiateAssessSchemeSet",
          listPage: "/api/assess/scheme/set/listPage",
        },
        BILL_TYPE,
        RULE_TYPE,
        showRecord: false,
        loading: false,
        showFilter: false,
        componentName: "",
        schemeName: "",
        ruleCode: "",
        ruleIndex: 0,
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          schemeName: this.schemeName,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 初始化绩效配置
      initDisposition() {
        this.$confirm(`确认是否初始化绩效配置`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await createApiFun({}, this.apis.init);
              if (res.success) {
                this.$message.success(`初始化绩效配置成功`);
                this.resetFilter();
              }
              this.loading = false;
            } catch (error) {
              console.log(error);
              this.loading = false;
            }
          })
          .catch(() => {});
      },
      // 编辑
      editRecord(index) {
        switch (index) {
          case 0:
            this.componentName = `record0`;
            this.ruleCode = "drumOrBag";
            break;
          case 1:
            this.componentName = `record1`;
            this.ruleCode = "collectionPoint";
            break;
          case 2:
            this.componentName = `record2`;
            this.ruleCode = "clinicGroup";
            break;
          case 3:
            this.componentName = `record2`;
            this.ruleCode = "nsClinicGroup";
            break;
          // case 4:
          // case 5:
          // case 6:
          //   this.componentName = `record5`;
          //   this.ruleCode = "Mixed";
          //   break;
          // case 7:
          //   this.componentName = `record8`;
          //   break;
          case 4:
            this.componentName = `record9`;
            this.ruleCode = "overtime";
            break;
        }
        this.ruleIndex = index;
        this.showRecord = true;
      },
      closeRecord() {
        this.showRecord = false;
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.schemeName = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
