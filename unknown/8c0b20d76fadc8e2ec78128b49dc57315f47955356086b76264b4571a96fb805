<template>
  <el-dialog
    :title="'批量导入' + title"
    :visible.sync="dialogVisible"
    width="60%"
    class="import-dialog"
    @open="initData"
  >
    <div v-loading="loading">
      <el-form :model="importForm" :rules="importRules" ref="importForm" label-width="120px" label-suffix="：">
        <el-form-item label="月度考核方案" prop="scheme">
          <el-select class="w400" v-model="importForm.scheme" placeholder="请选择考核方案" clearable filterable>
            <el-option
              v-for="item in schemeOptions"
              :key="item.id"
              :label="item.schemeName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <template v-if="importForm.scheme">
          <div class="template-box">
            <el-button type="text" @click="downloadTemplate">下载月考核模板</el-button>
          </div>
          <el-form-item label="导入文件" prop="file">
            <el-upload
              drag
              action="custom"
              :http-request="httpRequest"
              :accept="suffix"
              :limit="limit"
              :file-list="importForm.file"
              :on-change="handleChange"
              :on-remove="handleRemove"
              :before-upload="beforeAvatarUpload"
              :on-exceed="handleExceed"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖拽至此区域或<em>选择文件</em></div>
              <div>支持格式：xls、xlsx</div>
            </el-upload>
          </el-form-item>
        </template>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="importFileThrottling">确 定</el-button>
      </span>
    </div>
  </el-dialog>
</template>

<script>
  import { uploadFile, getFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import { BASE_API_URL, createApiFun } from "@/api/base";
  export default {
    name: "importDialog",
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      title: {
        type: String,
        default: "",
      },
      // 导入接口
      importApi: {
        type: String,
        default: "",
      },
      // 导入类型
      importDialogType: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    created() {
      this.importFileThrottling = this.$throttling(this.importFile, 500);
    },
    data() {
      return {
        loading: false,
        importForm: {
          scheme: "",
          file: [],
        },
        importRules: {
          scheme: [{ required: true, message: "请选择月度考核方案", trigger: "change" }],
          file: [{ required: true, message: "请导入文件", trigger: "change" }],
        },
        limit: 1,
        maxSize: 10,
        suffix: ".xlsx,.xls",
        deptProps: {
          checkStrictly: true,
          value: "id",
          label: "name",
          emitPath: false,
        },
        importFileThrottling: () => {},
        apis: {
          monthTemplate: "/api/access/record/template/",
          monthImport: "/api/access/record/import",
          schemeList: "/api/assess/scheme/list",
        },
        schemeOptions: [],
      };
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({ period: 0, status: 1 }, this.apis.schemeList)];
        let res = await Promise.all(promiseList);
        this.schemeOptions = res[0].data;
      },
      initData() {
        this.getOptions();
        this.importForm.scheme = "";
        this.importForm.file = [];
        this.$nextTick(() => {
          this.$refs.importForm.clearValidate();
        });
      },
      handleExceed() {
        this.$message.warning(`请最多上传 ${this.limit} 个文件。`);
      },
      handleRemove(file, fileList) {
        this.importForm.file = fileList;
      },
      handleChange(file, fileList) {
        this.importForm.file = fileList;
        this.$refs.importForm.validateField("file");
      },
      //上传之前
      beforeAvatarUpload(file) {
        //文件大小判断
        const isLimit = file.size / 1024 / 1024 < this.maxSize;
        if (!isLimit) {
          this.$message.warning(`上传文件大小不能超过 ${this.maxSize}MB!`);
          return false;
        }
        //后缀名判断
        if (this.suffix) {
          if (this.suffix === "*") return true;
          let suffix = file.name.slice(file.name.lastIndexOf("."));
          let suffixList = this.suffix.replace(/\s/g, "").split(",");
          console.log(suffixList);
          if (!suffixList.includes(suffix)) {
            this.$message.warning(`上传文件允许格式为${this.suffix}`);
            return false;
          }
        }
        return true;
      },
      // 下载模板
      async downloadTemplate() {
        let schemeItem = this.schemeOptions.filter((item) => item.id === this.importForm.scheme)[0];
        this.loading = true;
        try {
          let res = await getFile(BASE_API_URL + this.apis.monthTemplate + this.importForm.scheme);
          if (res.success) {
            createDownloadEvent(`${schemeItem.schemeName}考核模板.xlsx`, [res.data]);
            window.ELEMENT.Message.success("下载成功");
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
          console.warn(error);
        }
      },
      // 导入文件
      importFile() {
        this.$refs.importForm.validate(async (valid) => {
          if (valid) {
            let formData = new FormData();
            formData.append("file", this.importForm.file[0].raw);
            this.loading = true;
            try {
              let res = await uploadFile(BASE_API_URL + this.apis.monthImport, formData);
              if (res.success) {
                window.ELEMENT.Message.success("导入成功");
                this.dialogVisible = false;
                this.$emit("importSuccess");
              }
              this.loading = false;
            } catch (error) {
              console.warn(error);
              this.loading = false;
            }
          }
        });
      },
      httpRequest() {},
    },
  };
</script>

<style lang="scss">
  .import-dialog {
    .el-upload {
      width: 100%;
    }
    .el-upload-dragger {
      width: 100%;
    }
  }
  .el-upload-style {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .dialog-footer {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .template-box {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
  }
  .w400 {
    width: 400px;
  }
</style>
