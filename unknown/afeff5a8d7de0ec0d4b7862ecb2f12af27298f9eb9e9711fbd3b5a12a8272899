<template>
  <div class="management">
    <main-title title="今日车辆出车情况统计"></main-title>
    <div class="chart-list">
      <div class="chart-data">
        <sub-title class="mb-10" title="车辆总数"></sub-title>
        <Echart :chartData="chartData1" chartType="pie" key="char939391"></Echart>
      </div>
      <div class="chart-data">
        <sub-title class="mb-10" title="今日出车数"></sub-title>
        <Echart :chartData="chartData2" chartType="pie" key="char939392"></Echart>
      </div>
      <div class="chart-data">
        <sub-title class="mb-10" title="今日出车司机总数"></sub-title>
        <Echart :chartData="chartData3" chartType="pie" key="char939393"></Echart>
      </div>
      <div class="chart-data">
        <sub-title class="mb-10" title="今日出车押运工总数"></sub-title>
        <Echart :chartData="chartData4" chartType="pie" key="char939394"></Echart>
      </div>
    </div>
  </div>
</template>

<script>
  import MainTitle from "./MainTitle.vue";
  import Echart from "@/components/echart";
  import SubTitle from "./SubTitle.vue";
  export default {
    components: {
      MainTitle,
      Echart,
      SubTitle,
    },
    props: {
      totalStatics: {
        type: Object,
        default: () => {},
      },
      vehicleDayStatics: {
        type: Object,
        default: () => {},
      },
    },
    watch: {
      totalStatics: {
        handler(newV) {
          this.chartData1.graphic[0].style.text = (newV?.vehicleQuantity || 0) + "(辆)";
          this.chartData1.series[0].data = [
            { value: newV?.operateQuantity || 0, name: "经营性车辆" },
            { value: newV?.noOperateQuantity || 0, name: "非经营性车辆" },
          ];
        },
        immediate: true,
        deep: true,
      },
      vehicleDayStatics: {
        handler(newV) {
          this.chartData2.graphic[0].style.text = (newV?.vehicleQuantity || 0) + "(辆)";
          this.chartData2.series[0].data = [
            { value: newV?.vehicleOpQuantity || 0, name: "经营性车辆" },
            { value: newV?.vehicleNoOpQuantity || 0, name: "非经营性车辆" },
          ];
          this.chartData3.graphic[0].style.text = (newV?.driverQuantity || 0) + "(人)";
          this.chartData3.series[0].data = [
            { value: newV?.driverWaitQuantity || 0, name: "机动司机" },
            { value: newV?.driverRunQuantity || 0, name: "出车司机" },
          ];
          this.chartData4.graphic[0].style.text = (newV?.supercargoQuantity || 0) + "(人)";
          this.chartData4.series[0].data = [
            { value: newV?.supercargoWaitQuantity || 0, name: "机动押运工" },
            { value: newV?.supercargoRunQuantity || 0, name: "随车押运工" },
          ];
        },
        immediate: true,
        deep: true,
      },
    },
    data() {
      return {
        iconData: {
          numbg: require("@/assets/images/numbg.png"),
        },
        chartData1: {
          tooltip: {
            show: true,
            trigger: "item",
          },
          legend: {
            show: true,
            bottom: "0%",
            left: "center",
            textStyle: {
              color: "#fff",
              fontWeight: 400,
              fontSize: 12,
            },
          },
          graphic: [
            {
              type: "text",
              left: "center",
              top: "40%", // 精确地将文字置于圆环中心
              style: {
                text: "0(辆)",
                textAlign: "center",
                fill: "#fff", // 文字颜色
                fontSize: 16, // 字号
              },
            },
          ],
          series: [
            {
              name: "车辆总数",
              type: "pie",
              center: ["50%", "40%"],
              radius: ["60%", "45%"],
              top: "5%",
              label: {
                show: true,
                position: "outside",
                fontSize: 14,
                formatter: "{c}",
                color: "inherit",
              },
              labelLine: {
                show: true,
              },
              data: [],
            },
          ],
          color: ["#FABB28", "#006BF2"],
        },
        chartData2: {
          tooltip: {
            show: true,
            trigger: "item",
          },
          legend: {
            show: true,
            bottom: "0%",
            left: "center",
            textStyle: {
              color: "#fff",
              fontWeight: 400,
              fontSize: 12,
            },
          },
          graphic: [
            {
              type: "text",
              left: "center",
              top: "40%", // 精确地将文字置于圆环中心
              style: {
                text: "0(辆)",
                textAlign: "center",
                fill: "#fff", // 文字颜色
                fontSize: 16, // 字号
              },
            },
          ],
          series: [
            {
              name: "今日出车数",
              type: "pie",
              center: ["50%", "40%"],
              radius: ["60%", "45%"],
              top: "5%",
              label: {
                show: true,
                position: "outside",
                fontSize: 14,
                formatter: "{c}",
                color: "inherit",
              },
              labelLine: {
                show: true,
              },
              data: [],
            },
          ],
          color: ["#FABB28", "#006BF2"],
        },
        chartData3: {
          tooltip: {
            show: true,
            trigger: "item",
          },
          legend: {
            show: true,
            bottom: "0%",
            left: "center",
            textStyle: {
              color: "#fff",
              fontWeight: 400,
              fontSize: 12,
            },
          },
          graphic: [
            {
              type: "text",
              left: "center",
              top: "40%", // 精确地将文字置于圆环中心
              style: {
                text: "0(辆)",
                textAlign: "center",
                fill: "#fff", // 文字颜色
                fontSize: 16, // 字号
              },
            },
          ],
          series: [
            {
              name: "今日出车司机总数",
              type: "pie",
              center: ["50%", "40%"],
              radius: ["60%", "45%"],
              top: "5%",
              label: {
                show: true,
                position: "outside",
                fontSize: 14,
                formatter: "{c}",
                color: "inherit",
              },
              labelLine: {
                show: true,
              },
              data: [],
            },
          ],
          color: ["#FABB28", "#006BF2"],
        },
        chartData4: {
          tooltip: {
            show: true,
            trigger: "item",
          },
          legend: {
            show: true,
            bottom: "0%",
            left: "center",
            textStyle: {
              color: "#fff",
              fontWeight: 400,
              fontSize: 12,
            },
          },
          graphic: [
            {
              type: "text",
              left: "center",
              top: "40%", // 精确地将文字置于圆环中心
              style: {
                text: "0(辆)",
                textAlign: "center",
                fill: "#fff", // 文字颜色
                fontSize: 16, // 字号
              },
            },
          ],
          series: [
            {
              name: "今日出车押运工总数",
              type: "pie",
              center: ["50%", "40%"],
              radius: ["60%", "45%"],
              top: "5%",
              label: {
                show: true,
                position: "outside",
                fontSize: 14,
                formatter: "{c}",
                color: "inherit",
              },
              labelLine: {
                show: true,
              },
              data: [],
            },
          ],
          color: ["#FABB28", "#006BF2"],
        },
      };
    },
    created() {},
    methods: {},
  };
</script>

<style lang="scss" scoped>
  .management {
    width: 480px;
    position: absolute;
    top: 130px;
    right: 40px;
    z-index: 1;
  }
  .chart-list {
    padding-top: 20px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
  }
  .chart-data {
    width: 200px;
    height: 180px;
    margin-bottom: 50px;
  }
  .mb-10 {
    margin-bottom: 10px;
  }
</style>
