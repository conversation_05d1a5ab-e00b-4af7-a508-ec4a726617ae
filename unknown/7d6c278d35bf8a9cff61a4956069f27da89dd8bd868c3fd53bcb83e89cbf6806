<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="新增人员" :visible.sync="dialogVisible" width="800px">
      <div v-loading="loading">
        <el-transfer
          v-model="selectedList"
          ref="transferEle"
          :data="userList"
          :props="{
            key: 'id',
            label: 'name',
          }"
          :titles="['员工列表', '已选列表']"
          filterable
        ></el-transfer>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="confirmThrottling">确 定</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getInfoApiFunByParams, createApiFun } from "@/api/base";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      enterpriseInfo: {
        type: Object,
        default: () => {},
      },
      currentNode: {
        type: Object,
        default: () => {},
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        userList: [],
        selectedList: [],
        loading: false,
        apis: {
          findUser: "/api/baseuser/findUser",
          findStructureUser: "/api/company/structureUser/findStructureUser",
          create: "/api/company/structureUser/create",
        },
      };
    },
    created() {
      this.confirmThrottling = this.$throttling(this.confirm, 500);
    },
    mounted() {},
    methods: {
      // 初始化数据
      async initData(companyId, structureId) {
        let promiseList = [
          getInfoApiFunByParams({ companyId }, this.apis.findUser),
          getInfoApiFunByParams({ structureId }, this.apis.findStructureUser),
        ];
        let res = await Promise.all(promiseList);
        this.userList = res[0].data;
        this.selectedList = res[1].data.map((item) => item.id);
      },
      async confirm() {
        let params = {
          structureId: this.currentNode.id,
          userIds: this.selectedList,
        };
        this.loading = true;
        try {
          let res = await createApiFun(params, this.apis.create);
          if (res.success) {
            this.$message.success("人员新增成功");
            this.dialogVisible = false;
            this.$emit("refreshMember");
          }

          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    margin-top: 40px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  ::v-deep .el-transfer {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
    min-width: 240px;
    height: 600px;
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-transfer-panel__body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-transfer-panel__list {
    flex: 1;
    overflow: auto;
  }
</style>
