<template>
  <div class="micro-app-sctmp_base" v-loading="loading">
    <el-dialog
      title="车辆维保记录"
      :visible.sync="dialogVisible"
      width="1400px"
      top="0"
      destroy-on-close
      :modal-append-to-body="false"
      :before-close="closeDialog"
      @open="initData"
    >
      <div class="full-height" v-loading="loading">
        <div class="main-index">
          <header class="header">
            <div class="header-left mr-10">
              <el-input class="w400" v-model="keyword" placeholder="请输入车牌号/经办人/维保单位" clearable></el-input>
            </div>
            <el-button @click="initData">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
          </header>
          <main class="main">
            <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
              <el-table-column type="index" align="center"></el-table-column>
              <el-table-column prop="plateNumber" label="车牌号" align="center"> </el-table-column>
              <el-table-column prop="operatorName" label="经办人" align="center"></el-table-column>
              <el-table-column prop="type" label="维保类型" align="center">
                <template #default="{ row }">{{ MAINTENANCE_TYPE[row.type] }}</template>
              </el-table-column>
              <el-table-column prop="organizationName" label="维保单位" align="center"></el-table-column>
              <el-table-column prop="costs" label="维保费用(元)" align="center"></el-table-column>
              <el-table-column prop="recentMaintenanceTime" label="最近维保日期" align="center"></el-table-column>
              <el-table-column prop="nextMaintenanceTime" label="下次维保日期" align="center"></el-table-column>
            </el-table>
          </main>
          <Pagination :page="page" @pageChange="pageChange"></Pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import { MAINTENANCE_TYPE } from "@/enums";
  import moment from "moment";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      listType: {
        type: Number,
        default: 0,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/vehicleMaintenance/listPage",
        },
        loading: false,
        keyword: "",
        MAINTENANCE_TYPE,
      };
    },
    methods: {
      initData() {
        this.keyword = "";
        this.page.pageNo = 1;
        this.page.pageSize = 10;
        this.getDataList();
      },
      async getDataList() {
        this.loading = true;
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          recentMaintenanceBeginTime: moment().startOf("month").format("YYYY-MM-DD"),
          recentMaintenanceEndTime: moment().endOf("month").format("YYYY-MM-DD"),
          type: this.listType,
          channelId: this.channelId,
        };
        try {
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
          this.loading = false;
        } catch (_) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.getDataList();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.getDataList();
      },
      closeDialog() {
        this.dialogVisible = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .w400 {
    width: 400px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
