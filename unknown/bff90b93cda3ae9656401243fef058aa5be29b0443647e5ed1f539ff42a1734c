<template>
  <div class="compliance">
    <main-title title="车辆合规管理"></main-title>
    <ul class="compliance-list">
      <li class="compliance-item" v-for="(item, index) in countList" :key="index">
        <div class="compliance-item-label">{{ item.label }}</div>
        <div class="compliance-item-box">
          <div class="compliance-item-num">{{ item.num }}</div>
          <div class="compliance-item-unit">（{{ item.unit }}）</div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
  import MainTitle from "./MainTitle.vue";
  export default {
    components: {
      MainTitle,
    },
    props: {
      getVehicleCompliance: {
        type: Object,
        default: () => {},
      },
    },
    watch: {
      getVehicleCompliance: {
        handler(newV) {
          this.countList[0].num = newV.annualAuditCount;
          this.countList[1].num = newV.insureCount;
          this.countList[2].num = newV.vehicleIllegalCount;
        },
        deep: true,
        immediate: true,
      },
    },
    data() {
      return {
        countList: [
          {
            label: "待年审车辆数",
            num: 0,
            unit: "辆",
          },
          {
            label: "待投保车辆数",
            num: 0,
            unit: "辆",
          },
          {
            label: "当月违章车辆数",
            num: 0,
            unit: "辆",
          },
        ],
      };
    },
    methods: {},
  };
</script>

<style lang="scss" scoped>
  .compliance {
    margin-top: 16px;
  }
  .compliance-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    margin-top: 16px;
    transform: translateX(-44px);
    .compliance-item {
      color: #fff;
      text-align: center;
      .compliance-item-label {
        font-size: 14px;
      }
      .compliance-item-box {
        display: flex;
        align-items: flex-end;
        justify-content: center;
        margin-top: 10px;
        .compliance-item-num {
          font-size: 32px;
          line-height: 24px;
        }
        .compliance-item-unit {
          font-size: 12px;
        }
      }
    }
  }
</style>
