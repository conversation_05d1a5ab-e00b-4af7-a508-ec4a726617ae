<template>
  <div class="main-title flex-start-between" :style="{ 'background-image': 'url(' + iconData.bg + ')' }">
    <div class="title">{{ title }}</div>
    <div class="engTitle" v-if="engTitle">{{ engTitle }}</div>
  </div>
</template>

<script>
  export default {
    props: {
      title: {
        type: [String, Number],
        default: "标题",
      },
      engTitle: {
        type: [String, Number],
        default: "",
      },
    },
    data() {
      return {
        iconData: {
          bg: require("@/assets/images/title-bg.png"),
        },
      };
    },
  };
</script>

<style lang="scss" scoped>
  .main-title {
    width: 480px;
    height: 44px;
    padding: 0 10px 0 24px;
    .title {
      background: -webkit-linear-gradient(#ffffff, #9dfad8);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-size: 18px;
      padding-top: 10px;
      font-weight: bold;
    }
    .engTitle {
      color: #a5d3c2;
      padding-top: 8px;
    }
  }
</style>
