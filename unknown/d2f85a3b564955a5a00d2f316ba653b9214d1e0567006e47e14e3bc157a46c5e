<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      width="70%"
      top="0"
      destroy-on-close
      :show-close="false"
      @open="openDialog"
    >
      <div class="main-index" v-loading="loading">
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="productionUnit" placeholder="请输入产废单位名称" clearable></el-input>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
        </header>
        <div class="record-content">
          <el-table
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            border
            height="100%"
            ref="tableRef"
          >
            <el-table-column type="index" align="center" label="顺序" width="60"></el-table-column>
            <el-table-column prop="code" label="点位编号" align="center" v-if="itemList[0].value"></el-table-column>
            <el-table-column
              prop="productionUnit"
              label="产废单位名称"
              align="center"
              width="120"
              v-if="itemList[1].value"
            ></el-table-column>
            <el-table-column
              prop="productionUnitOperator"
              label="产废单位经办人"
              align="center"
              width="120"
              v-if="itemList[2].value"
            ></el-table-column>
            <el-table-column prop="detailType" label="任务类型" align="center" v-if="itemList[3].value">
              <template #default="{ row }">{{ POINT_TASK_TYPE[row.detailType] }}</template>
            </el-table-column>
            <el-table-column prop="overType" label="加班类型" align="center" v-if="itemList[4].value">
              <template #default="{ row }">{{
                row.overType || row.overType === 0 ? OVERTIME_TYPE[row.overType] : ""
              }}</template>
            </el-table-column>
            <el-table-column
              prop="rubbishTotal"
              label="废物总量（kg）"
              align="center"
              width="140"
              v-if="itemList[5].value"
            ></el-table-column>
            <el-table-column prop="waybillStatus" label="收运状态" align="center" v-if="itemList[6].value">
              <template #default="{ row }">{{ RECEIVING_CONDITION[row.waybillStatus] }}</template>
            </el-table-column>
            <el-table-column
              prop="waybillTime"
              label="收运时间"
              align="center"
              v-if="itemList[7].value"
            ></el-table-column>
            <el-table-column
              prop="endDate"
              label="收运截止日期"
              align="center"
              width="130"
              v-if="itemList[8].value"
            ></el-table-column>
            <el-table-column prop="isClear" label="是否清空" align="center" v-if="itemList[9].value">
              <template #default="{ row }">{{ IS_NORMAL_OPERATION[row.isClear] }}</template>
            </el-table-column>
            <el-table-column prop="baggingMethod" label="桶装/袋装" align="center" v-if="itemList[10].value">
              <template #default="{ row }">{{ BARRELS_BAGS[row.baggingMethod] }}</template>
            </el-table-column>
            <el-table-column prop="verifyStatus" label="当前流程" align="center" v-if="itemList[11].value">
              <template #default="{ row }">{{ VERIFY_STATUS[row.verifyStatus] }}</template>
            </el-table-column>
            <el-table-column
              prop="verifyUserName"
              label="确认人"
              align="center"
              v-if="itemList[12].value"
            ></el-table-column>
            <el-table-column
              prop="verifyTime"
              label="确认时间"
              align="center"
              v-if="itemList[13].value"
            ></el-table-column>
            <el-table-column prop="longitude" label="经度" align="center" v-if="itemList[14].value"></el-table-column>
            <el-table-column prop="latitude" label="纬度" align="center" v-if="itemList[15].value"></el-table-column>
            <el-table-column
              prop="userLongitude"
              label="上报经度"
              align="center"
              v-if="itemList[16].value"
            ></el-table-column>
            <el-table-column
              prop="userLatitude"
              label="上报纬度"
              align="center"
              v-if="itemList[17].value"
            ></el-table-column>
            <el-table-column prop="address" label="地址" align="center" v-if="itemList[18].value"></el-table-column>
            <el-table-column prop="image" label="图片" align="center">
              <template #default="{ row }">
                <el-badge
                  v-if="row.picture && row.picture.length > 0"
                  :value="row.picture.length > 1 ? row.picture.length : ''"
                  class="picture-badge"
                  type="success"
                >
                  <el-image
                    class="picture-img"
                    fit="cover"
                    :src="row.picture[0].url"
                    :preview-src-list="row.picture.map((i) => i.url)"
                    v-if="row.picture"
                  ></el-image>
                </el-badge>
              </template>
            </el-table-column>
            <el-table-column label="废物类型/重量（kg）" align="center" v-if="itemList[19].value">
              <el-table-column prop="infectiousWaste" label="感染性废物" align="center"></el-table-column>
              <el-table-column prop="damagingWaste" label="损伤性废物" align="center"></el-table-column>
              <el-table-column prop="pharmaceuticalWaste" label="药物性废物" align="center"></el-table-column>
              <el-table-column prop="pathologicalWaste" label="病理性废物" align="center"></el-table-column>
              <el-table-column prop="chemicalWaste" label="化学性废物" align="center"></el-table-column>
              <el-table-column prop="sludge" label="感染性废物一污泥" align="center" width="140"></el-table-column>
            </el-table-column>
            <el-table-column
              prop="firstCarrier"
              label="第一承运人"
              align="center"
              v-if="itemList[20].value"
            ></el-table-column>
            <el-table-column
              prop="residueRubbish"
              label="剩余垃圾"
              align="center"
              v-if="itemList[21].value"
            ></el-table-column>
            <el-table-column prop="operation" label="正常经营" align="center" v-if="itemList[22].value">
              <template #default="{ row }">{{ IS_NORMAL_OPERATION[row.operation] }}</template>
            </el-table-column>
          </el-table>
        </div>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun } from "@/api/base";
  import {
    TRASH_TYPE,
    IS_NORMAL_OPERATION,
    RECEIVING_CONDITION,
    BARRELS_BAGS,
    VERIFY_STATUS,
    POINT_TASK_TYPE,
    OVERTIME_TYPE,
  } from "@/enums";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      detailItem: {
        type: Object,
        default: () => {},
      },
      transportRow: {
        type: Object,
        default: () => {},
      },
      transportType: {
        type: Array,
        default: () => [],
      },
      transportDistrictId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        filterForm: {},
        TRASH_TYPE,
        IS_NORMAL_OPERATION,
        RECEIVING_CONDITION,
        BARRELS_BAGS,
        VERIFY_STATUS,
        POINT_TASK_TYPE,
        OVERTIME_TYPE,
        apis: {
          listPage: "/api/waybill/waybillDetail/listPage",
        },
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        loading: false,
        tableData: [],
        showFilter: false,
        productionUnit: "",
        itemList: [
          { label: "点位编号", value: true },
          { label: "产废单位名称", value: true },
          { label: "产废单位经办人", value: true },
          { label: "任务类型", value: true },
          { label: "加班类型", value: true },
          { label: "废物总量", value: true },
          { label: "收运状态", value: true },
          { label: "收运时间", value: true },
          { label: "收运截止日期", value: true },
          { label: "是否清空", value: true },
          { label: "桶装/袋装", value: true },
          { label: "当前流程", value: true },
          { label: "确认人", value: true },
          { label: "确认时间", value: true },
          { label: "经度", value: true },
          { label: "纬度", value: true },
          { label: "上报经度", value: true },
          { label: "上报纬度", value: true },
          { label: "地址", value: true },
          // { label: "拍照时间", value: true },
          // { label: "拍照地点", value: true },
          { label: "废物类型/重量", value: false },
          { label: "第一承运人", value: false },
          { label: "剩余垃圾", value: false },
          { label: "正常经营", value: false },
        ],
        allItemChecked: false,
      };
    },
    methods: {
      openDialog() {
        this.page.pageNo = 1;
        this.page.pageSize = 10;
        this.productionUnit = "";
        this.initData();
      },
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          productionUnit: this.productionUnit,
          waybillStatus: 1,
          lgUnionId: this.detailItem.userId,
          districtId: this.transportDistrictId,
          startWaybillTime: this.transportRow.date,
          endWaybillTime: this.transportRow.date,
          types: this.transportType,
        };
        this.loading = true;
        try {
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.tableData.forEach((data) => {
              try {
                data.picture = JSON.parse(data.picture);
              } catch (error) {
                data.picture = "";
              }
            });
            this.page.total = res.data.total;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.productionUnit = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .detail-box {
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    padding-top: 20px;
  }
  .header {
    display: flex;
    align-items: center;
    .header-left {
      margin-right: 10px;
    }
  }
  .info-icon {
    font-size: 30px;
    cursor: pointer;
  }
  .record-content {
    margin-top: 20px;
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .picture-img {
    width: 60px;
    height: 60px;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .map-box {
    width: 1px;
    height: 1px;
    position: fixed;
    top: -9999px;
    left: -9999px;
    opacity: 0;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
