import Vue from "vue";
import App from "./App.vue";
import "./public-path";
import router from "./router";
import store from "./store";
import "@/styles/index.scss"; //引入公共样式
import vueSeamlessScroll from "vue-seamless-scroll";
import { resizeDirective } from "@/utils/directives.js";
Vue.use(vueSeamlessScroll);

import moment from "moment";
moment.locale("zh-cn"); // zh-cn

import Pagination from "@/components/pagination-v";
import FilterContent from "@/components/FilterContent";
import InterviewChannel from "@/components/interviewChannel";
Vue.component("Pagination", Pagination);
Vue.component("FilterContent", FilterContent);
Vue.component("InterviewChannel", InterviewChannel);

Vue.config.productionTip = false;

//vuex仓库实例
Vue.prototype.$store = store;

Vue.directive("resize", resizeDirective);

window._AMapSecurityConfig = {
  securityJsCode: "6ed22552ff60b164f41e8787e6ccf37b",
};

let instance = null;
/**
 * Vue实例挂载及渲染方法
 */
function render(props = {}) {
  const { container } = props;
  instance = new Vue({
    router,
    store,
    render: (h) => h(App),
  }).$mount(container ? container.querySelector("#app") : "#app");
}

// 独立运行时
if (!window.__POWERED_BY_QIANKUN__) {
  render();
}

/**
 * bootstrap 只会在微应用初始化的时候调用一次，下次微应用重新进入时会直接调用 mount 钩子，不会再重复触发 bootstrap。
 * 通常我们可以在这里做一些全局变量的初始化，比如不会在 unmount 阶段被销毁的应用级别的缓存等。
 */
export async function bootstrap() {
  console.log("[vue] vue app bootstraped");
}

/**
 * 应用每次进入都会调用 mount 方法，通常我们在这里触发应用的渲染方法
 */
export async function mount(props) {
  console.log("[vue] props from main framework", props);
  render(props);
}

/**
 * 应用每次 切出/卸载 会调用的方法，通常在这里我们会卸载微应用的应用实例
 */
export async function unmount() {
  instance.$destroy();
  instance.$el.innerHTML = "";
  instance = null;
}

/**
 * 可选生命周期钩子，仅使用 loadMicroApp 方式加载微应用时生效
 */
export async function update(props) {
  console.log("update props", props);
}
