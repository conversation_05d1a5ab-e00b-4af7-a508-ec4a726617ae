<template>
  <div class="micro-app-sctmp_base full-height">
    <defaultPage @createRecord="createRecord" @closeRecord="closeRecord">
      <record
        v-if="showRecord === 1"
        :recordId="recordId"
        :positionOptions="positionOptions"
        :roleOptions="roleOptions"
        @closeRecord="closeRecord"
        @refreshList="initData"
      ></record>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="keyword" placeholder="请输入姓名/联系电话/工号" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
          ></InterviewChannel>
          <el-col :span="8">
            <el-form-item label="在职状态">
              <el-select v-model="filterForm.jobStatus" placeholder="请选择在职状态" clearable filterable>
                <el-option v-for="(item, index) in JOB_STATUS" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="默认驾驶/押运车辆">
              <el-input v-model="filterForm.plateNumber" placeholder="请输入车牌号" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用户身份">
              <el-select v-model="filterForm.userIdentity" placeholder="请选择用户身份" clearable filterable>
                <el-option v-for="(item, index) in USER_IDENTITY" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属部门">
              <el-cascader
                v-model="filterForm.deptId"
                placeholder="请选择所属部门"
                filterable
                clearable
                :options="deptOptions"
                :props="deptProps"
                :show-all-levels="false"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别">
              <el-select v-model="filterForm.sex" placeholder="请选择性别" clearable filterable>
                <el-option v-for="(item, index) in SEX_OPTIONS" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="职位">
              <el-select v-model="filterForm.position" placeholder="请选择职位" clearable filterable>
                <el-option
                  v-for="item in positionOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column prop="jobNo" label="工号" align="center">
              <template #default="{ row }">{{ row.jobNo || row.jobNo === 0 ? row.jobNo : "-" }}</template>
            </el-table-column>
            <el-table-column prop="fullName" label="姓名" align="center"> </el-table-column>
            <el-table-column prop="sex" label="性别" align="center">
              <template #default="{ row }">{{ SEX_OPTIONS[row.sex] }}</template>
            </el-table-column>
            <el-table-column prop="positionName" label="职位" align="center"></el-table-column>
            <el-table-column prop="phone" label="联系电话" align="center">
              <template #default="{ row }">{{ row.phone.replace(row.phone.substring(3, 7), "****") }}</template>
            </el-table-column>
            <el-table-column prop="deptName" label="所属部门" align="center"></el-table-column>
            <el-table-column prop="userIdentity" label="用户身份" align="center">
              <template #default="{ row }">
                <span v-for="(item, index) in row.userIdentity.split(',')" :key="item">
                  <span>{{ USER_IDENTITY[item] }}</span>
                  <span v-if="index < row.userIdentity.split(',').length - 1">，</span>
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="plateNumber" label="默认驾驶/押运车辆" align="center"></el-table-column>
            <el-table-column prop="jobStatus" label="在职状态" align="center">
              <template #default="{ row }">{{ JOB_STATUS[row.jobStatus] }}</template>
            </el-table-column>
            <el-table-column prop="isTeamLeader" label="是否班组长" align="center" min-width="140px">
              <template #default="{ row }">
                <el-switch
                  :value="row.isTeamLeader"
                  active-text="是"
                  inactive-text="否"
                  :active-value="1"
                  :inactive-value="0"
                  @change="changeTeamLeader(row)"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row)">编辑</el-link>
                <el-link class="mr-10" type="primary" @click="changePassword(row)">重置密码</el-link>
                <el-link class="mr-10" type="primary" @click="changeRole(row)">权限设置</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
    <roleEdit :value.sync="showRoleEdit" :lgUnionId="lgUnionId"></roleEdit>
    <resetPassword :value.sync="showReset" :lgUnionId="lgUnionId"></resetPassword>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import { SEX_OPTIONS, USER_IDENTITY, JOB_STATUS } from "@/enums";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, getInfoApiFun, updateApiFun, createApiFun } from "@/api/base";
  import record from "./components/record.vue";
  import roleEdit from "./components/roleEdit.vue";
  import resetPassword from "./components/resetPassword.vue";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
      roleEdit,
      resetPassword,
    },
    data() {
      return {
        filterForm: {},
        SEX_OPTIONS,
        USER_IDENTITY,
        JOB_STATUS,
        positionOptions: [],
        deptOptions: [],
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/baseuser/listPage",
          positionList: "/api/dict/position/list",
          enable: "/api/baseuser/enable",
          roleList: "/api/userrole/roleList",
          deptList: "/api/company/structure/findIn",
          setTeamLeader: "/api/baseuser/setTeamLeader",
        },
        showRecord: 0,
        recordId: "",
        showRoleEdit: false,
        roleOptions: [], //角色列表
        showReset: false,
        lgUnionId: "",
        deptProps: {
          checkStrictly: true,
          value: "id",
          label: "name",
          emitPath: false,
        },
        identityOptions: [
          { id: 0, name: "其他" },
          { id: 1, name: "财务" },
          { id: 2, name: "运营" },
          { id: 5, name: "行政" },
          { id: 6, name: "客商" },
        ],
        userForm: {},
        showDriver: false,
        showShipWorker: false,
        recordForm: {},
        showFilter: false,
        keyword: "",
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
      this.getPositionList();
      this.getDeptList();
      this.getRoleList();
    },
    methods: {
      // 获取职位列表
      async getPositionList() {
        let res = await getInfoApiFun("", this.apis.positionList);
        if (res.success) {
          this.positionOptions = res.data;
        }
      },
      // 获取部门列表
      async getDeptList() {
        let res = await getInfoApiFun("", this.apis.deptList);
        if (res.success) {
          this.deptOptions = res.data;
        }
      },
      // 获取角色列表
      async getRoleList() {
        let res = await getInfoApiFun("", this.apis.roleList);
        if (res.success) {
          this.roleOptions = res.data.filter((item) => ![100, 168, 200].includes(item.level));
        }
      },
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas.map((item) => {
            return {
              ...item,
              phone: this.$sm2Decrypt(item.phone),
            };
          });
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showRecord = 1;
        this.recordId = "";
      },
      // 编辑
      editRecord(row) {
        this.showRecord = 1;
        this.recordId = row.id;
      },
      closeRecord() {
        this.showRecord = 0;
      },
      // 切换账号状态(启用/禁用)
      changeEnable(row) {
        this.$confirm(`是否确认${row.isEnable ? "禁用" : "启用"}账号`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            let res = await updateApiFun({ id: row.id }, this.apis.enable);
            if (res.success) {
              this.$message.success(`账号${row.isEnable ? "禁用" : "启用"}成功`);
              this.initData();
            }
          })
          .catch(() => {});
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 添加权限
      changeRole(row) {
        this.showRoleEdit = true;
        this.lgUnionId = row.lgUnionId;
      },
      // 重置密码
      changePassword(row) {
        this.$confirm(`是否确认重置密码`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            this.showReset = true;
            this.lgUnionId = row.lgUnionId;
          })
          .catch(() => {});
      },
      // 切换是否班组长
      changeTeamLeader(row) {
        this.$confirm(`是否确认切换是否班组长`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await createApiFun(
                { lgUnionId: row.lgUnionId, isTeamLeader: row.isTeamLeader === 1 ? 0 : 1 },
                this.apis.setTeamLeader,
              );
              if (res.success) {
                this.$message.success(`切换成功`);
                this.initData();
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          })
          .catch(() => {});
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .w250 {
    width: 250px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
</style>
