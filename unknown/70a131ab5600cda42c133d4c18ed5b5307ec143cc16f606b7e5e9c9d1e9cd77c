<template>
  <div class="detail-container">
    <waybillRecord
      v-if="showRecord"
      :recordId="recordId"
      :recordForm="recordForm"
      @closeRecord="closeRecord"
    ></waybillRecord>
    <div class="main-record micro-app-sctmp_base" v-else v-loading="loading">
      <div class="record-content">
        <div class="card-header">{{ detailType === 1 ? "月度绩效明细" : "基础绩效明细" }}</div>
        <baseTitle title="绩效明细"></baseTitle>
        <el-form :model="ruleForm" ref="ruleForm" label-width="210px" label-suffix="：">
          <el-row>
            <template v-if="detailType === 1">
              <el-col>
                <el-form-item label="绩效方案名称">
                  <el-input value="桶装/袋装收运月度绩效" placeholder="" reanonly></el-input>
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item label="桶装绩效计算公式">
                  <el-input
                    value="当月每日收运单价 × 当月内每日的总收运量 × 当月考核系数"
                    placeholder=""
                    reanonly
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item label="袋装绩效计算公式">
                  <el-input
                    value="收运单价 × 当月总收运量 × 当月总里程系数 × 当月考核系数"
                    placeholder=""
                    reanonly
                  ></el-input>
                </el-form-item>
              </el-col>
            </template>
            <el-col>
              <el-form-item label="点位收运绩效明细"></el-form-item>
              <el-table
                class="table-data"
                :data="ruleForm.detailInfo"
                :header-cell-style="{ background: '#F5F7F9' }"
                border
              >
                <el-table-column prop="date" label="日期" align="center"></el-table-column>
                <el-table-column prop="bucketCount" label="桶装点位收运数量" align="center"></el-table-column>
                <el-table-column prop="bucketStand" label="桶装垃圾收运单价（元/吨）" align="center"></el-table-column>
                <el-table-column
                  prop="bucketRubbishTotal"
                  label="桶装垃圾收运重量（kg）"
                  align="center"
                ></el-table-column>
                <el-table-column prop="bagCount" label="袋装点位收运数量" align="center"></el-table-column>
                <el-table-column prop="bagStand" label="袋装垃圾收运单价（元/吨）" align="center"></el-table-column>
                <el-table-column prop="bagRubbishTotal" label="袋装垃圾收运重量（kg）" align="center"></el-table-column>
                <el-table-column prop="performance" label="当日绩效（元）" align="center"></el-table-column>
              </el-table>
            </el-col>
            <el-col>
              <el-form-item label="桶装垃圾收运绩效总计（元）">
                <el-input :value="ruleForm.bucketTotal" placeholder="" reanonly></el-input>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="袋装垃圾收运绩效总计（元）">
                <el-table :data="bagData" :header-cell-style="{ background: '#F5F7F9' }" border>
                  <el-table-column prop="date" label="月份" align="center"></el-table-column>
                  <el-table-column prop="price" label="袋装垃圾收运单价（元/吨）" align="center"></el-table-column>
                  <el-table-column
                    prop="bagRubbishTotal"
                    label="当月袋装垃圾收运总量（kg）"
                    align="center"
                  ></el-table-column>
                  <el-table-column prop="factor" label="当月里程系数" align="center"></el-table-column>
                  <el-table-column prop="performance" label="本月袋装垃圾绩效（元）" align="center"></el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
            <template v-if="detailType === 1">
              <el-col>
                <el-form-item label="考核系数">
                  <el-input :value="ruleForm.ratio" placeholder="" reanonly></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="扣款金额">
                  <el-input :value="deductSalary" placeholder="" reanonly></el-input>
                </el-form-item>
              </el-col>
            </template>
            <el-col>
              <el-form-item label="绩效工资（元）" v-if="detailType === 1">
                <el-input
                  :value="`（${ruleForm.bucketTotal} + ${bagData[0]?.performance || 0}）x ${
                    ruleForm.ratio
                  } - ${deductSalary} = ${ruleForm.payIncentives}`"
                  placeholder=""
                  reanonly
                ></el-input>
              </el-form-item>
              <el-form-item label="基础绩效（元）" v-else>
                <el-input
                  :value="`${ruleForm.bucketTotal} + ${bagData[0]?.performance || 0} = ${ruleForm.basicPerformance}`"
                  placeholder=""
                  reanonly
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <baseTitle title="收运明细"></baseTitle>
        <waybillTable :waybillInfo="ruleForm.waybillInfo" @editRecord="editRecord"></waybillTable>
      </div>
      <div class="record-footer">
        <el-button @click="closeDetail">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";
  import waybillRecord from "@/views/collectTransportManage/electronicWaybill/components/record.vue";
  import waybillTable from "./waybillTable.vue";
  import { getInfoApiFun } from "@/api/base";
  export default {
    components: {
      baseTitle,
      waybillRecord,
      waybillTable,
    },
    props: {
      detailId: {
        type: String,
        default: "",
      },
      deductSalary: {
        type: Number,
        default: 0,
      },
      detailType: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        loading: false,
        ruleForm: {},
        bagData: [],
        showRecord: false,
        recordId: "",
        recordForm: {},
        apis: {
          info: "/api/access/record/detail/",
        },
      };
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取详情
      async getRecord() {
        try {
          let res = await getInfoApiFun(this.detailId, this.apis.info);
          if (res.success) {
            this.ruleForm = res.data;
            this.bagData = [this.ruleForm.bagTotal];
          }
        } catch (error) {
          console.log(error);
        }
      },
      // 返回
      closeDetail() {
        this.$emit("closeDetail");
      },
      editRecord(row) {
        this.recordId = row.id;
        this.recordForm = row;
        this.showRecord = true;
      },
      closeRecord() {
        this.showRecord = false;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .detail-container {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-data {
    margin-bottom: 20px;
  }
</style>
