<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="电子版考核表" :visible.sync="dialogVisible" width="1200px" top="0" destroy-on-close>
      <div v-loading="loading">
        <div class="output__wrap">
          <!-- 页头页眉 -->
          <!-- <div id="pdf-header" style="height: 3cm" class="pdf-hf"></div> -->
          <!-- <div id="pdf-footer" style="height: 3cm" class="pdf-hf"></div> -->

          <!-- 内容/导出部分 -->
          <div class="main" id="meet-pdf-id">
            <div class="page">
              <div class="text-title pdf-group-item">{{ examineForm.schemeName }}</div>
              <div class="tip-title pdf-group-item">考核维度</div>
              <div class="table-wrap">
                <el-table :data="examineForm.assessDimensions" border>
                  <el-table-column prop="name" label="维度名称" align="center"></el-table-column>
                  <el-table-column prop="weight" label="维度满分" align="center"></el-table-column>
                  <el-table-column label="打分" align="center"></el-table-column>
                </el-table>
              </div>
              <footer class="form-footer pdf-group-item">
                <div class="text">被考核人 ________________</div>
                <div class="text mt-12">考核人 ________________</div>
                <div class="text mt-12">考核时间 ________________</div>
              </footer>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">返回</el-button>
        <el-button type="primary" @click="saveThrottling">生成电子版考核表</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { PdfLoader } from "@/utils/pdfLoader";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      examineForm: {
        type: Object,
        default: () => {},
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        loading: false,
        saveThrottling: () => {},
        isShow: false,
        tableData: [],
        isPdf: false, // 是否导出中
        pdfName: "", // 生成pdf文件名称
        isLoadingText: true, // 生成pdf是否是否需要loading,如外部指定情况下就不需要
      };
    },
    created() {
      this.saveThrottling = this.$throttling(this.createPdf, 500);
    },
    methods: {
      // 生成pdf文件
      async createPdf() {
        let loading = null;
        loading = this.$loading({
          lock: true,
          text: "导出中...",
          spinner: "el-icon-loading",
          background: "rgba(255, 255, 255, 0.9)",
        });

        this.isPdf = true;
        await this.$nextTick();
        let pdfDom = document.getElementById("meet-pdf-id");
        const pdfFooter = document.getElementById("pdf-footer");
        const pdfHeader = document.getElementById("pdf-header");

        setTimeout(() => {
          let pdfObj = new PdfLoader(pdfDom, {
            fileName: `${this.examineForm.schemeName}.pdf`,
            footer: pdfFooter,
            header: pdfHeader,
            baseY: 0,
            contentWidth: 595,
          });

          pdfObj
            .getPdf()
            .then(async (res) => {
              this.$message.success("导出成功！");
              this.isPdf = false;
              loading.close();
              this.dialogVisible = false;
            })
            .catch((error) => {
              this.isPdf = false;
              loading.close();
              this.$message.warning("导出失败，请重试！");
            });
        }, 1000);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .output__wrap {
    .mt-20 {
      margin-top: 20px;
    }
    // 页面大小及边距
    .page {
      min-height: 297mm;
      min-width: 210mm;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      padding: 3.2cm 2.6cm 3.2cm; /* 国家标准公文页边距 GB/T 9704-2012 */
      padding: 3cm 2cm;
      .page-inner {
        min-height: calc(297mm - 73mm);
      }

      // 页眉页脚
      .pdf-hf {
        position: fixed;
        top: -100vh;
        width: 210mm;
      }

      .text-title {
        font-size: 24px;
        text-align: center;
        padding: 12px 0;
        padding-top: 0;
        font-weight: bold;
      }
      .tip-title {
        font-size: 20px;
        font-weight: bold;
        padding: 20px 0;
      }
      .form-footer {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        margin-top: 100px;
      }
      .text {
        font-size: 18px;
        line-height: 20px;
      }

      ::v-deep .el-table {
        .cell {
          font-family: "Times New Roman", "仿宋_GB2312", FangSong_GB2312, 仿宋, FangSong, STSong;
          font-size: 11pt;
          color: #000;
          padding-left: 5px;
          padding-right: 5px;
        }

        th .cell {
          font-size: 13px;
        }

        th,
        td {
          min-width: 100px;
          border-color: #000;
          color: #000;
          padding: 5px 0;
          background-color: #fff;
        }

        thead th.el-table__cell {
          background-color: #fff;
        }
      }

      /**改变边框颜色*/
      ::v-deep .el-table {
        &::after {
          background-color: transparent !important;
        }
        /* 改变表头字体颜色 */
        .el-table thead tr th {
          border-color: #000 !important;
        }
        .el-table--border:after,
        .el-table--group:after,
        &:before,
        &::after {
          background-color: #000 !important;
        }

        td,
        th.is-leaf {
          border-bottom: 1px solid #000 !important;
        }

        .el-table--border th,
        .el-table--border th.gutter:last-of-type {
          border-bottom: 1px solid #000 !important;
        }

        .el-table--border td,
        .el-table--border th {
          border-right: 1px solid #000 !important;
        }
        td,
        th.is-leaf {
          border-bottom: 1px solid #000 !important;
          border-color: #000 !important;
        }
        td,
        .building-top th.is-leaf {
          border-bottom: 1px solid #000 !important;
          border-color: #000 !important;
        }
      }

      ::v-deep .el-table--border {
        border-color: #000 !important;
      }
      ::v-deep .el-table--group {
        border-color: #000 !important;
      }
    }
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
  }
  ::v-deep .el-dialog__body {
    padding: 40px 30px;
    height: calc(100% - 134px);
    overflow-y: auto;
  }
</style>
