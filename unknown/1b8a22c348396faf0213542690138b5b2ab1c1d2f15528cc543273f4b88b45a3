<template>
  <div class="screen-header">
    <div class="bg-img" :style="{ 'background-image': 'url(' + iconData.bg + ')' }">
      <div class="currDate">{{ currDate }}</div>
      <div class="screen-title">医疗废物智慧收运管理平台车辆管理大屏</div>
      <div class="weather"></div>
    </div>
    <div class="left-channel" v-if="!channel">
      <el-radio-group v-model="channelId" @change="toggleChannel">
        <el-radio-button :label="label.id" v-for="label in channelList" :key="label.id">{{
          label.name
        }}</el-radio-button>
      </el-radio-group>
    </div>
  </div>
</template>

<script>
  import moment from "moment";
  export default {
    props: {
      channelList: {
        type: Array,
        default: () => [],
      },
      channel: {
        type: String,
        default: "",
      },
      value: {
        type: String,
        default: "",
      },
    },
    computed: {
      channelId: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        iconData: {
          bg: require("@/assets/images/header-bg.png"),
        },
        currDate: "",
      };
    },
    created() {
      moment.locale();
      this.getDate();
    },
    methods: {
      getDate() {
        let timer = setTimeout(() => {
          clearTimeout(timer);
          this.currDate = moment().format("YYYY年MM月DD日 | ") + moment().format("dddd");
          this.getDate();
        }, 1000);
      },
      toggleChannel() {
        this.$emit("toggleChannel");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .screen-header {
    width: 100%;
    position: absolute;
    z-index: 1;
  }
  .bg-img {
    position: relative;
    width: 100%;
    height: 120px;
    display: flex;
  }
  .currDate {
    flex: 1;
    font-size: 16px;
    color: #a5d3c2;
    line-height: 21px;
    font-style: normal;
    padding-top: 12px;
    text-align: left;
    padding-left: 40px;
  }
  .screen-title {
    flex: 1;
    font-size: 34px;
    line-height: 40px;
    font-weight: bold;
    background: -webkit-linear-gradient(#ffffff, #9dfad8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
    padding-top: 24px;
  }
  .weather {
    flex: 1;
    text-align: right;
    color: #a5d3c2;
    padding-right: 40px;
    padding-top: 10px;

    .temperature {
      font-weight: 500;
      font-size: 20px;
      color: #ffffff;
      line-height: 24px;
      margin-right: 5px;
    }
  }
  .left-channel {
    position: absolute;
    bottom: 10px;
    right: 58px;
  }
</style>
