<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <record
        v-if="showRecord"
        :recordId="recordId"
        :canEdit="canEdit"
        @closeRecord="closeRecord"
        @refreshList="initData"
      ></record>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="schemeName" placeholder="请输入考核方案名称" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
            <el-dropdown
              size="small"
              trigger="click"
              @command="handleDataImport"
              class="dropdown-button"
              placement="bottom-start"
            >
              <el-button size="small" type="primary">
                <span class="sctmp-iconfont icon-ic_daoru"></span>
                <span class="ml-5">导入</span>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">
                  <div class="has-padding-sm has-border-bottom">导入基础信息</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button class="mr-10" size="small" type="primary" @click="handleExport">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
          ></InterviewChannel>
          <el-col :span="8">
            <el-form-item label="考核周期">
              <el-select v-model="filterForm.period" placeholder="请选择考核周期" clearable filterable>
                <el-option v-for="(item, index) in EXAMINE_PERIOD" :key="index" :label="item" :value="index">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="方案生成日期">
              <el-date-picker
                v-model="filterForm.generateDate"
                type="date"
                placeholder="请选择方案生成日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" border height="100%">
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column prop="generateDate" label="方案生成日期" align="center"></el-table-column>
            <el-table-column prop="schemeName" label="考核方案" align="center"></el-table-column>
            <el-table-column prop="period" label="考核周期" align="center">
              <template #default="{ row }">{{ EXAMINE_PERIOD[row.period] }}</template>
            </el-table-column>
            <el-table-column prop="assessDimensions" label="考核维度数" align="center">
              <template #default="{ row }">{{ row.period === 1 ? "-" : row.assessDimensions.length }}</template>
            </el-table-column>
            <el-table-column prop="assessGrades" label="考核等级数" align="center">
              <template #default="{ row }">{{ row.assessGrades.length }}</template>
            </el-table-column>
            <el-table-column prop="assessFlows" label="流程数" align="center">
              <template #default="{ row }">{{
                row.period === 1 ? "-" : row.assessFlows.filter((list) => list.enable === 1).length
              }}</template>
            </el-table-column>
            <el-table-column prop="status" label="考核状态" align="center">
              <template #default="{ row }">{{ ASSESSMENT_STATUS[row.status] }}</template>
            </el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="openInit(row)" v-if="row.status === 0">发起考核</el-link>
                <el-link class="mr-10" type="primary" @click="openEvaluationForm(row)" v-if="row.period === 0"
                  >电子版考核表</el-link
                >
                <template v-if="row.status === 0">
                  <el-link class="mr-10" type="primary" @click="editRecord(row, false)">编辑</el-link>
                  <el-popconfirm class="mr-10" title="确认删除当前考核方案？" @confirm="deleteRecord(row)">
                    <el-link type="danger" slot="reference">删除</el-link>
                  </el-popconfirm>
                </template>
                <template v-else-if="row.status === 1">
                  <el-link class="mr-10" type="primary" @click="openPersonDialog(row)">人员编辑</el-link>
                  <el-popconfirm class="mr-10" title="确认终止当前考核方案？" @confirm="terminateRecord(row)">
                    <el-link type="danger" slot="reference">终止</el-link>
                  </el-popconfirm>
                </template>
                <el-link v-if="row.status != 0" class="mr-10" type="primary" @click="editRecord(row, true)"
                  >详情</el-link
                >
                <el-popconfirm title="确认拷贝当前考核方案？" @confirm="copyRecord(row)">
                  <el-link type="primary" slot="reference">拷贝方案</el-link>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
    <initAssessment :value.sync="showInit" :period="period" :initId="initId" @refreshList="initData"></initAssessment>
    <importDialog
      :value.sync="importDialogShow"
      title="考核方案"
      :importDialogType="importDialogType"
      @importSuccess="searchFilter"
    ></importDialog>
    <evaluationForm :value.sync="showForm" :examineForm="examineForm"></evaluationForm>
    <personDialog
      :value.sync="showPersonDialog"
      :personDialogRow="personDialogRow"
      @refreshList="initData"
    ></personDialog>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import { getListPageApiFun, deleteApiFun, BASE_API_URL, getInfoApiFun } from "@/api/base";
  import record from "./components/record.vue";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import { EXAMINE_PERIOD, PERFORMANCE_RULE, ASSESSMENT_STATUS } from "@/enums";
  import importDialog from "./components/importDialog";
  import initAssessment from "./components/initAssessment";
  import evaluationForm from "./components/evaluationForm";
  import personDialog from "./components/personDialog";
  export default {
    components: {
      defaultPage,
      record,
      importDialog,
      initAssessment,
      evaluationForm,
      personDialog,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/assess/scheme/listPage",
          delete: "/api/assess/scheme/delete/",
          export: "/api/assess/scheme/exportAssessScheme",
          initiateAssessment: "/api/assess/scheme/initiateAssessment",
          terminate: "/api/assess/scheme/terminationAssess/",
          copy: "/api/assess/scheme/copy/",
        },
        showRecord: false,
        recordId: "",
        loading: false,
        EXAMINE_PERIOD,
        PERFORMANCE_RULE,
        ASSESSMENT_STATUS,
        importDialogShow: false,
        importDialogType: "",
        showInit: false, //发起考核
        period: 0, //当前发起考核方案考核类型
        showForm: false, //电子版考核表弹窗
        examineForm: {},
        initId: "",
        canEdit: false,
        showFilter: false,
        schemeName: "",
        showPersonDialog: false,
        personDialogRow: {},
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          schemeName: this.schemeName,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showRecord = true;
        this.recordId = "";
        this.canEdit = false;
      },
      // 编辑
      editRecord(row, flag) {
        this.showRecord = true;
        this.recordId = row.id;
        this.canEdit = flag;
      },
      closeRecord() {
        this.showRecord = false;
      },
      // 删除
      async deleteRecord(row) {
        this.loading = true;
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            this.page.pageNo = this.pageDropProcess(this.page.pageNo, this.page.pageSize, this.page.total);
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
          console.warn(error);
        }
      },
      // 终止
      async terminateRecord(row) {
        this.loading = true;
        try {
          let res = await getInfoApiFun(row.id, this.apis.terminate);
          if (res.success) {
            this.$message.success("终止成功");
            this.initData();
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
          console.warn(error);
        }
      },
      // 拷贝
      async copyRecord(row) {
        this.loading = true;
        try {
          let res = await getInfoApiFun(row.id, this.apis.copy);
          if (res.success) {
            window.ELEMENT.Message.success("拷贝成功");
            this.initData();
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
          console.warn(error);
        }
      },
      // 打开考核弹窗
      openInit(row) {
        this.initId = row.id;
        this.period = row.period;
        this.showInit = true;
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.schemeName = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 导出
      handleExport() {
        this.$confirm(`确认是否以当前筛选条件导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, {
                schemeName: this.schemeName,
                ...this.filterForm,
              });
              if (res.success) {
                createDownloadEvent(`考核方案${Date.now()}.xlsx`, [res.data]);
                window.ELEMENT.Message.success("导出成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
      // 导入
      handleDataImport(command) {
        this.importDialogType = command;
        this.importDialogShow = true;
      },
      openEvaluationForm(row) {
        this.showForm = true;
        this.examineForm = row;
      },
      // 打开人员编辑弹窗
      openPersonDialog(row) {
        this.showPersonDialog = true;
        this.personDialogRow = row;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .w250 {
    width: 250px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
</style>
