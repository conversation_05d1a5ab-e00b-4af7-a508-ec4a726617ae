<template>
  <div class="micro-app-sctmp_base">
    <el-drawer :visible.sync="dialogVisible" destroy-on-close :withHeader="false" :size="1300">
      <AIComponent></AIComponent>
    </el-drawer>
  </div>
</template>

<script>
  import AIComponent from "@/views/AI/index.vue";
  export default {
    components: {
      AIComponent,
    },
    props: {
      value: {
        type: Boolean,
        default: false,
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
  };
</script>

<style lang="scss" scoped></style>
