<template>
  <div id="app" :class="className">
    <keep-alive :include="includeString">
      <router-view />
    </keep-alive>
  </div>
</template>
<script>
  import { appName } from "../config/index.js";
  import { getLocalStorage } from "@/utils/storage";
  import { TOP_MENU_KEY } from "@/utils/commonValue";
  export default {
    data() {
      return {
        className: `micro-app-${appName}`,
        includeString: [],
      };
    },
    watch: {
      $route: {
        handler() {
          let topMenus = getLocalStorage(TOP_MENU_KEY) || [];
          let routeIncludes = topMenus.map((item) => item.url);
          this.includeString = ["/sctmp_base/homePage"].concat(routeIncludes);
        },
        deep: true,
        immediate: true,
      },
    },
  };
</script>
<style lang="scss">
  #app {
    height: 100%;
  }
</style>
