<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">{{ recordId ? "编辑" : "新增" }}考核记录</div>
      <baseTitle title="基础信息"></baseTitle>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="考核方案" prop="assessSchemeId">
              <el-select
                class="w400"
                v-model="ruleForm.assessSchemeId"
                placeholder="请选择考核方案"
                clearable
                filterable
                :disabled="isDisabled"
                @change="changeScheme"
              >
                <el-option
                  v-for="item in schemeOptions"
                  :key="item.id"
                  :label="`${item.schemeName}（${item.period === 1 ? '年度' : '月度'}）`"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <template v-if="ruleForm.assessSchemeId">
            <el-col :md="24" :lg="12">
              <el-form-item label="考核周期" required>
                <el-select :value="recordForm.period" placeholder="请选择考核周期" clearable filterable disabled>
                  <el-option
                    v-for="(item, index) in EXAMINE_PERIOD"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item
                label="考核年份"
                prop="yearOrMonth"
                :rules="[{ required: true, message: '请选择考核年份', trigger: 'change' }]"
                v-if="recordForm.period === 1"
              >
                <el-date-picker
                  v-model="ruleForm.yearOrMonth"
                  type="year"
                  placeholder="请选择考核年份"
                  value-format="yyyy"
                  :disabled="isDisabled"
                  :picker-options="yearOptions"
                  @change="selectYear"
                ></el-date-picker>
              </el-form-item>
              <el-form-item
                label="考核月度"
                prop="yearOrMonth"
                :rules="[{ required: true, message: '请选择考核月度', trigger: 'change' }]"
                v-else
              >
                <el-date-picker
                  v-model="ruleForm.yearOrMonth"
                  type="month"
                  placeholder="请选择考核月度"
                  value-format="yyyy/MM"
                  :disabled="isDisabled"
                  :picker-options="monthOptions"
                  @change="changeMonth"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="被考核人员" prop="personList">
                <el-table
                  class="table-bottom"
                  :data="ruleForm.personList"
                  :header-cell-style="{ background: '#F5F7F9' }"
                  border
                >
                  <el-table-column type="index" align="center"></el-table-column>
                  <el-table-column prop="fullName" label="真实姓名" align="center"></el-table-column>
                  <el-table-column prop="phone" label="联系电话" align="center"></el-table-column>
                  <el-table-column prop="userName" label="用户名" align="center"></el-table-column>
                  <el-table-column label="操作" align="center" v-if="!isDisabled">
                    <template #default="{ row }">
                      <el-link type="danger" @click="deletePerson(row)">删除</el-link>
                    </template>
                  </el-table-column>
                </el-table>
                <el-button type="primary" icon="el-icon-plus" @click="showPerson = true" v-if="!isDisabled"
                  >选人员</el-button
                >
              </el-form-item>
            </el-col>
          </template>
          <person
            :value.sync="showPerson"
            :period="recordForm.period"
            :personList="ruleForm.personList"
            @selectPerson="selectPerson"
            @deletePerson="deletePerson"
          ></person>
        </el-row>
        <template v-if="ruleForm.assessSchemeId">
          <template v-if="recordForm.period === 0">
            <baseTitle title="考核评分"></baseTitle>
            <ul v-if="ruleForm.flowScoreList.length > 0">
              <el-card class="table-bottom" v-for="(item, index) in ruleForm.flowScoreList" :key="item.assessFlowId">
                <el-form-item label="评分项">
                  <el-table :data="item.dimensionScoreList" :header-cell-style="{ background: '#F5F7F9' }" border>
                    <el-table-column prop="name" label="维度名称" align="center"></el-table-column>
                    <el-table-column prop="weight" label="维度满分" align="center"></el-table-column>
                    <el-table-column prop="weight" label="权重" align="center">
                      <template #default="{ row }">{{ row.weight }}%</template>
                    </el-table-column>
                    <el-table-column prop="score" label="评分" align="center">
                      <template #default="{ row, $index }">
                        <el-form-item
                          :prop="`flowScoreList.${index}.dimensionScoreList.${$index}.score`"
                          :rules="flowScoreListRules.score"
                        >
                          <el-input-number
                            v-model="row.score"
                            :precision="2"
                            :min="0"
                            :max="row.weight"
                            :disabled="isDisabled"
                          ></el-input-number>
                        </el-form-item>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-card>
            </ul>
            <el-form-item label="扣分项">
              <el-table
                class="table-bottom"
                :data="ruleForm.demeritPoint"
                :header-cell-style="{ background: '#F5F7F9' }"
                border
              >
                <el-table-column label="扣分项" align="center">
                  <template #default="{ row, $index }">
                    <el-form-item :prop="`demeritPoint.${$index}.type`" :rules="demeritPointRules.type">
                      <el-select
                        v-model="row.type"
                        placeholder="请选择扣分项"
                        clearable
                        filterable
                        :disabled="isDisabled"
                      >
                        <el-option
                          v-for="(item, index) in DEDUCTION_ITEM"
                          :key="index"
                          :label="item"
                          :value="index"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="扣除绩效工资金额" align="center">
                  <template #default="{ row, $index }">
                    <el-form-item :prop="`demeritPoint.${$index}.deductSalary`" :rules="demeritPointRules.deductSalary">
                      <el-input-number
                        v-model="row.deductSalary"
                        step-strictly
                        :min="0"
                        :max="99999999"
                        :disabled="isDisabled"
                      ></el-input-number>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="扣除总分" align="center">
                  <template #default="{ row, $index }">
                    <el-form-item :prop="`demeritPoint.${$index}.deductScore`" :rules="demeritPointRules.deductScore">
                      <el-input-number
                        v-model="row.deductScore"
                        :precision="2"
                        :min="0"
                        :max="100"
                        :disabled="isDisabled"
                      ></el-input-number>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column prop="memo" label="备注" align="center">
                  <template #default="{ row }">
                    <el-input
                      v-model="row.memo"
                      placeholder="请输入备注"
                      clearable
                      maxlength="50"
                      show-word-limit
                      :disabled="isDisabled"
                    ></el-input>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" v-if="!isDisabled">
                  <template #default="{ $index }">
                    <el-link type="danger" @click="deleteDeductionItem($index)">删除</el-link>
                  </template>
                </el-table-column>
              </el-table>
              <el-button type="primary" icon="el-icon-plus" @click="addDeductionItem" v-if="!isDisabled"
                >添加扣分项</el-button
              >
            </el-form-item>
          </template>
          <template v-if="clinicParam?.hasClinicGroup">
            <el-form-item label="线路系数" label-width="150px" prop="pathFactor" v-if="!clinicParam?.pathFactor">
              <el-select v-model="ruleForm.pathFactor" placeholder="请选择线路系数" clearable filterable>
                <el-option
                  v-for="(item, index) in clinicParam?.clinicGroupParam?.factorList"
                  :key="index"
                  :label="item.factor + '（' + item.area + '）'"
                  :value="item.factor"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="不足基准点位计算"
              label-width="150px"
              prop="lessType"
              v-if="!clinicParam?.lessType && clinicParam?.lessType !== 0"
            >
              <el-select v-model="ruleForm.lessType" placeholder="请选择不足基准时的规则" clearable filterable>
                <el-option v-for="item in lessTypeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </template>
          <el-form-item label="考核总分" required>
            <el-input
              class="w500"
              :value="totalScore"
              placeholder="根据考核人打分数据，按照月度考核方案中流程权重自动计算总分"
              readonly
            ></el-input>
          </el-form-item>
          <el-form-item label="考核等级" required>
            <el-input
              class="w500"
              :value="level ? level : '无'"
              placeholder="根据所选的考核方案的考核流程、等级规则自动生成对应的考核结果"
              readonly
            ></el-input>
          </el-form-item>
          <el-form-item label="绩效积分" required v-if="recordForm.period === 1">
            <el-input class="w500" :value="integrate" placeholder="绩效积分" readonly></el-input>
          </el-form-item>
          <el-form-item label="被考核人签字" prop="signFile" v-if="recordForm.period === 0">
            <div v-if="ruleForm.signFile">
              <el-image
                class="sign-img"
                fit="fill"
                :src="ruleForm.signFile"
                :preview-src-list="[ruleForm.signFile]"
              ></el-image>
            </div>
            <el-button type="primary" @click="showSign = true" v-if="!isDisabled">电子签名</el-button>
          </el-form-item>
        </template>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">{{ isDisabled ? "返回" : "取消" }}</el-button>
      <el-button type="primary" @click="saveRecordThrottling" v-if="!isDisabled">保存</el-button>
    </div>
    <signature :value.sync="showSign" :baseUrl="baseUrl" @success="setSign"></signature>
  </div>
</template>

<script>
  import moment from "moment";
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  import { EXAMINE_PERIOD, PERFORMANCE_RULE, ASSESSMENT_FLOWS, DEDUCTION_ITEM } from "@/enums";
  import baseTitle from "@/components/baseTitle";
  import person from "./person.vue";
  import signature from "@/components/signature";
  import { OSSUpload } from "@/components/FileUpload/upload";
  import { floatAdd, floatSub, floatMul, floatDiv, base64ToFile, roundReserveDecimals } from "@/utils";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
      isDisabled: {
        type: Boolean,
        default: false,
      },
    },
    components: {
      baseTitle,
      person,
      signature,
    },
    computed: {
      flowOptions() {
        let options = [];
        if (this.recordForm?.assessFlows.length > 0) {
          options = this.recordForm.assessFlows.filter((item) => item.enable === 1);
        }
        return options;
      },
      // 考核总分
      totalScore() {
        let score = 0;
        if (this.recordForm.period === 1) {
          score = this.yearTotalScore;
        } else {
          if (this.ruleForm?.flowScoreList.length > 0) {
            let dimensionScore = 0;
            this.ruleForm.flowScoreList.forEach((list) => {
              dimensionScore = floatAdd(
                dimensionScore,
                floatMul(
                  list.dimensionScoreList.reduce((total, currentValue) => {
                    return floatAdd(total, currentValue.score);
                  }, 0),
                  floatDiv(list.assessFlowWeight, 100),
                ),
              );
            });
            let demeritScore = this.ruleForm.demeritPoint.reduce((total, currentValue) => {
              return floatAdd(total, currentValue.deductScore);
            }, 0);
            let scoreTotal = Number(floatSub(dimensionScore, demeritScore));
            score = scoreTotal > 0 ? scoreTotal : 0;
          }
        }
        return roundReserveDecimals(score, 2);
      },
      // 考核等级
      level() {
        let value = "";
        if (this.recordForm?.assessGrades.length > 0) {
          this.recordForm.assessGrades.forEach((list) => {
            if (list.min < this.totalScore && this.totalScore <= list.max) {
              value = list.name;
            }
          });
        }
        return value;
      },
      // 绩效积分
      integrate() {
        let value = 0;
        if (this.recordForm?.assessGrades.length > 0) {
          this.recordForm.assessGrades.forEach((list) => {
            if (list.min < this.totalScore && this.totalScore <= list.max) {
              value = list.integrate;
            }
          });
        }
        return value;
      },
      schemeOptions() {
        let options = this.schemeList.filter((list) => list.status === 1);
        return options;
      },
    },
    data() {
      return {
        EXAMINE_PERIOD,
        PERFORMANCE_RULE,
        ASSESSMENT_FLOWS,
        DEDUCTION_ITEM,
        recordForm: {},
        originalForm: {
          assessSchemeId: "", //考核方案
          yearOrMonth: "", //考核年度/月度
          flowScoreList: [], //考核评分列表
          personList: [], //被考核人员
          demeritPoint: [], //扣分项列表
          signFile: "", //签字
        },
        ruleForm: {},
        rules: {
          assessSchemeId: [{ required: true, message: "请选择考核方案", trigger: "change" }],
          personList: [{ required: true, message: "请选择被考核人员", trigger: "change" }],
          signFile: [{ required: true, message: "请填写电子签名", trigger: "change" }],
          pathFactor: [{ required: true, message: "请选择线路系数", trigger: "change" }],
          lessType: [{ required: true, message: "请选择不足基准时的规则", trigger: "change" }],
        },
        demeritPointRules: {
          type: [{ required: true, message: "请选择扣分项", trigger: "change" }],
          deductSalary: [{ required: true, message: "请输入扣除绩效工资金额", trigger: "blur" }],
          deductScore: [{ required: true, message: "请输入扣除总分", trigger: "blur" }],
        },
        flowScoreListRules: {
          score: [{ required: true, message: "请输入维度评分", trigger: "blur" }],
        },
        apis: {
          create: "/api/access/record/create",
          update: "/api/access/record/update",
          info: "/api/access/record/get/",
          calc: "/api/access/record/calc",
          schemeList: "/api/assess/scheme/list",
          calcClinic: "/api/access/record/calcClinic",
        },
        saveRecordThrottling: () => {},
        loading: false,
        monthOptions: {
          disabledDate: (time) => {
            let now = new Date();
            let last = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            return time > new Date(`${moment(last).format("YYYY/MM/DD")} 23:59:59`).getTime();
          },
        },
        yearOptions: {
          disabledDate: (time) => {
            let nowYear = new Date().getFullYear();
            return time > new Date(`${nowYear}/12/31 23:59:59`);
          },
        },
        showPerson: false,
        showSign: false,
        baseUrl: "",
        schemeList: [],
        yearTotalScore: 0,
        clinicParam: {},
        lessTypeList: [
          { id: 1, name: "按规则扣费" },
          { id: 2, name: "单个点位计费（x4）" },
        ],
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    async mounted() {
      await this.getOptions();
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({}, this.apis.schemeList)];
        let res = await Promise.all(promiseList);
        this.schemeList = res[0].data;
      },
      // 获取详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.ruleForm.personList = [res.data.user];
          this.ruleForm.demeritPoint = res.data.demeritPoint || [];
          this.processData(this.ruleForm.assessSchemeId, true);
        }
      },
      // 选择考核方案
      changeScheme(value) {
        if (value) {
          this.ruleForm = Object.assign({}, this.originalForm);
          this.ruleForm.assessSchemeId = value;
          this.ruleForm.personList = [];
          this.processData(value, false);
          this.$nextTick(() => {
            this.$refs.ruleForm.clearValidate();
          });
        } else {
          this.recordForm = {};
        }
      },
      processData(idValue, isRecord) {
        let item = this.schemeList.filter((o) => o.id === idValue)[0];
        item.assessDimensions.forEach((i) => {
          i.assessDimensionId = i.id;
        });
        this.recordForm = Object.assign({}, item);
        if (item.period === 0) {
          if (!isRecord) {
            let flowScoreList = [];
            let flowList = item.assessFlows.filter((list) => list.enable === 1);
            flowList.forEach((list) => {
              flowScoreList.push({
                assessFlowId: list.id,
                assessFlowWeight: list.weight,
                dimensionScoreList: item.assessDimensions.map((data) => {
                  return { ...data, score: "" };
                }),
              });
            });
            this.ruleForm.flowScoreList = flowScoreList;
            this.ruleForm.demeritPoint = [];
          } else {
            let flowList = item.assessFlows.filter((list) => list.enable === 1);
            let flowWeightObj = {};
            flowList.forEach((list) => {
              flowWeightObj[list.id] = list.weight;
            });
            this.ruleForm.flowScoreList.forEach((list) => {
              list.assessFlowWeight = flowWeightObj[list.assessFlowId];
            });
          }
        }
      },
      // 选择人员
      selectPerson(row) {
        if (this.ruleForm.personList.length > 0) {
          this.$message.warning("只能选择一个被考核人员");
          return;
        }
        this.ruleForm.personList.push(row);

        if (this.ruleForm.yearOrMonth && this.recordForm.period === 1) {
          this.getUserMonthTotalScore();
        }
        if (this.ruleForm.yearOrMonth && this.ruleForm.personList.length > 0) {
          this.getClinicParam();
        }
        this.$nextTick(() => {
          this.$refs.ruleForm.validateField("personList");
        });
      },
      // 选择年份
      selectYear(value) {
        if (value) {
          if (this.ruleForm.personList.length > 0) {
            this.getUserMonthTotalScore();
          }
        } else {
          this.yearTotalScore = 0;
        }
      },
      // 考核月度change事件
      changeMonth(value) {
        if (value && this.ruleForm.personList.length > 0) {
          this.getClinicParam();
        }
      },
      // 获取被考核人考核月份是否需要选择线路系数/扣款规则
      async getClinicParam() {
        let res = await createApiFun(
          {
            lgUnionId: this.ruleForm.personList[0].lgUnionId,
            year: Number(this.ruleForm.yearOrMonth.split("/")[0]),
            month: Number(this.ruleForm.yearOrMonth.split("/")[1]),
          },
          this.apis.calcClinic,
        );
        if (res.success) {
          this.clinicParam = res.data;
          if (this.clinicParam.hasClinicGroup) {
            this.$set(this.ruleForm, "pathFactor", "");
            this.$set(this.ruleForm, "lessType", "");
          }
        }
      },
      // 获取被考核人当前年份的月份考核总分
      async getUserMonthTotalScore() {
        let res = await createApiFun(
          { lgUnionId: this.ruleForm.personList[0].lgUnionId, year: this.ruleForm.yearOrMonth },
          this.apis.calc,
        );
        if (res.success) {
          if (res.data.month === 0) {
            this.yearTotalScore = 0;
          } else {
            this.yearTotalScore = roundReserveDecimals(floatDiv(res.data.totalScore, res.data.month), 2);
          }
        }
      },
      // 删除人员
      deletePerson(row) {
        let index = this.ruleForm.personList.findIndex((list) => list.lgUnionId === row.lgUnionId);
        if (index >= 0) {
          this.ruleForm.personList.splice(index, 1);
          this.yearTotalScore = 0;
        }
      },
      // 添加扣分项
      addDeductionItem() {
        this.ruleForm.demeritPoint.push({
          type: "",
          deductSalary: "",
          deductScore: 0,
          memo: "",
        });
      },
      // 删除扣分项
      deleteDeductionItem(index) {
        this.ruleForm.demeritPoint.splice(index, 1);
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 签名返回数据
      async setSign(url) {
        this.baseUrl = url;
        this.ruleForm.signFile = url;
        this.showSign = false;
      },
      async httpRequest(currFile) {
        await OSSUpload.getOssBase();
        return new Promise((resolve) => {
          let ossClient = new OSSUpload();
          ossClient.init();
          ossClient
            .upload(currFile, () => {})
            .then((data) => {
              console.log("上传成功", data);
              resolve(data.url);
            })
            .catch((err) => {
              console.log("上传失败", err);
              this.loading = false;
              resolve(false);
            });
        });
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            let params = Object.assign({}, this.ruleForm);
            params.lgUnionId = this.ruleForm.personList[0].lgUnionId;
            this.loading = true;
            try {
              if (this.recordForm.period === 0) {
                params.signFile = await this.httpRequest(base64ToFile(this.baseUrl, "signature"));
              }
              let res = this.recordId
                ? await updateApiFun(params, this.apis.update)
                : await createApiFun(params, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}考核记录成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .mr-10 {
    margin-right: 10px;
  }
  .w400 {
    width: 62%;
  }
  .w500 {
    width: 80%;
  }
  .table-bottom {
    margin-bottom: 20px;
  }
  .sign-img {
    width: 100px;
    height: 100px;
  }
</style>
