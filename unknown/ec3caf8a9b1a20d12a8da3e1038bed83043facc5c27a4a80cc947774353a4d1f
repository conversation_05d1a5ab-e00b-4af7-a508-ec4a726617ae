<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="8">
            <el-form-item label="车牌号" prop="plateNumber">
              <el-select v-model="ruleForm.plateNumber" placeholder="请选择车牌号" clearable filterable>
                <el-option v-for="item in carOptions" :key="item.id" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="8">
            <el-form-item label="驾驶司机" prop="driverDossierId">
              <el-select v-model="ruleForm.driverDossierId" placeholder="请选择驾驶司机" clearable filterable>
                <el-option
                  v-for="item in driverOptions"
                  :key="item.id"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="8">
            <el-form-item label="自检类型" prop="type">
              <el-select
                v-model="ruleForm.type"
                placeholder="请选择自检类型"
                filterable
                :disabled="!ruleForm.plateNumber || !ruleForm.driverDossierId"
                class="w-300"
              >
                <el-option
                  v-for="(item, index) in SELFTEST_STATUS"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-card class="type-card" v-if="ruleForm.type || ruleForm.type === 0">
            <div slot="header" class="card-header">{{ SELFTEST_TITLE[ruleForm.type] }}</div>
            <el-col :md="24" :lg="12">
              <el-form-item label="自检时间" prop="inspectTime">
                <el-date-picker
                  v-model="ruleForm.inspectTime"
                  type="datetime"
                  placeholder="请选择自检时间"
                  format="yyyy-MM-dd HH:mm"
                  value-format="yyyy-MM-dd HH:mm"
                  :picker-options="pickerOptions"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item :label="`${SELFTEST_STATUS[ruleForm.type]}明细`" prop="detailList" required>
                <el-table
                  :data="ruleForm.detailList"
                  :header-cell-style="{ background: '#F5F7F9' }"
                  style="width: 100%"
                  border
                >
                  <el-table-column type="index" align="center" label="序号" width="55"></el-table-column>
                  <el-table-column prop="configName" label="评价项目"></el-table-column>
                  <el-table-column prop="status" label="项目状态">
                    <template #default="{ row, $index }">
                      <el-form-item :prop="`detailList.${$index}.status`" :rules="detailRules.status">
                        <el-switch
                          v-model="row.status"
                          active-text="正常"
                          inactive-text="异常"
                          :active-value="0"
                          :inactive-value="1"
                        ></el-switch>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="车辆照片" prop="fileList">
                <FileUpload @uploadChange="uploadChangeFileList" :imageList="imageList" :limit="5">
                  <template #tips
                    ><el-tag type="warning"
                      >请上传{{ SELFTEST_STATUS[ruleForm.type] }}状态时的车辆照片</el-tag
                    ></template
                  >
                </FileUpload>
              </el-form-item>
            </el-col>
            <el-col>
              <el-form-item label="是否可以正常收运" prop="influencePickup" v-if="showinfluence">
                <el-select v-model="ruleForm.influencePickup" placeholder="请选择是否可以正常收运" filterable clearable>
                  <el-option
                    v-for="(item, index) in IS_TEMPORARY"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-card>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import FileUpload from "@/components/FileUpload";
  import { SELFTEST_STATUS, SELFTEST_TITLE, EVALUATE_STATUS, IS_TEMPORARY } from "@/enums";
  import { createApiFun, getListApiFun } from "@/api/base";
  export default {
    components: {
      FileUpload,
    },
    computed: {
      showinfluence() {
        let flag = false;
        if (this.ruleForm.detailList && this.ruleForm.detailList.length > 0) {
          let filterList = this.ruleForm.detailList.filter((list) => list.status === 1);
          if (filterList.length > 0) {
            flag = true;
          }
        }
        return flag;
      },
    },
    data() {
      return {
        ruleForm: {
          plateNumber: "", //车牌号
          driverDossierId: "", //司机
          type: "", //自检类型
          inspectTime: "", //车前检自检时间
          detailList: [],
          influencePickup: "", //是否可以正常收运
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号", trigger: "change" }],
          driverDossierId: [{ required: true, message: "请选择驾驶司机", trigger: "change" }],
          type: [{ required: true, message: "请选择自检类型", trigger: "change" }],
          inspectTime: [{ required: true, message: "请选择自检时间", trigger: "change" }],
          detailList: [{ required: true, message: "请填写明细", trigger: "change" }],
          influencePickup: [{ required: true, message: "请选择是否可以正常收运", trigger: "change" }],
        },
        detailRules: {
          status: [{ required: true, message: "请选择项目状态", trigger: "change" }],
        },
        apis: {
          create: "/api/vehicle/inspect/create",
          info: "/api/vehicle/inspect/get/",
          carList: "/api/vehicle/dossier/list",
          userList: "/api/baseuser/list",
          configList: "/api/vehicle/evaluationConfig/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [],
        SELFTEST_STATUS,
        SELFTEST_TITLE,
        EVALUATE_STATUS,
        IS_TEMPORARY,
        carOptions: [],
        driverOptions: [],
        pickerOptions: {
          disabledDate: (time) => time.getTime() > new Date(new Date().setHours(23, 59, 59)),
        },
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    async mounted() {
      this.getOptions();
      this.getConfigList();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },
      // 获取配置项列表
      async getConfigList() {
        let res = await createApiFun({ type: 1 }, this.apis.configList);
        let configList = res.data;
        this.ruleForm.detailList = configList.map((list) => {
          return {
            configId: list.id,
            configName: list.name,
            status: 0,
          };
        });
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            if (!this.showinfluence) {
              delete this.ruleForm.influencePickup;
            }
            try {
              let res = await createApiFun(this.ruleForm, this.apis.create);
              if (res.success) {
                this.$message.success(`新增成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      uploadChangeFileList(fileList) {
        if (fileList.length > 0) {
          this.ruleForm.fileList = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
        } else {
          this.ruleForm.fileList = [];
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 16px;
  }
  .type-card {
    width: 100%;
    margin-bottom: 20px;
  }
  .w-300 {
    width: 300px;
  }
</style>
