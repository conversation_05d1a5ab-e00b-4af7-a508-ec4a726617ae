<template>
  <div class="screen-TodayNum">
    <div class="subtitle">今日收运量</div>
    <NumBox></NumBox>
  </div>
</template>
<script>
  import NumBox from "./NumBox.vue";

  export default {
    components: {
      NumBox,
    },
    props: {},
    data() {
      return {};
    },
    computed: {},
    created() {},
    mounted() {},
    methods: {},
    watch: {},
  };
</script>
<style scoped lang="scss">
  .screen-TodayNum {
    position: absolute;
    z-index: 1;
    top: 120px;
    left: 40px;
  }
  .subtitle {
    width: 160px;
    padding: 8px 10px;
    background-color: #0c3925;
    font-size: 26px;
    color: #fff;
    line-height: 40px;
  }
</style>
