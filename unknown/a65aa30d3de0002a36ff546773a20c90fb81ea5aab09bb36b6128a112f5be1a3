<template>
  <div class="container">
    <div class="time-box">
      <el-progress
        :width="100"
        type="circle"
        :percentage="79.19"
        :stroke-width="10"
        define-back-color="#aaa"
        text-color="#fff"
        color="#CAF982"
      ></el-progress>
      <div class="right">
        <div class="right-title">今日24小时收运点位总数</div>
        <div class="right-count">48</div>
        <div class="right-title mt-16">今日已收24小时收运点位数量</div>
        <div class="right-count">38</div>
      </div>
    </div>
    <div class="time-box">
      <el-progress
        :width="100"
        type="circle"
        :percentage="73.68"
        :stroke-width="10"
        define-back-color="#aaa"
        text-color="#fff"
        color="#CAF982"
      ></el-progress>
      <div class="right">
        <div class="right-title">今日48小时收运点位总数</div>
        <div class="right-count">4107</div>
        <div class="right-title mt-16">今日累计48小时点位已收运数量</div>
        <div class="right-count">3026</div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {};
</script>

<style lang="scss" scoped>
  .container {
    position: absolute;
    z-index: 1;
    top: 260px;
    left: 40px;
  }
  .overtime-box {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    color: #fff;
    .overtime-total {
      font-size: 40px;
      font-weight: bold;
      line-height: 30px;
    }
    .overtime-unit {
      font-size: 12px;
    }
  }
  .overtime-text {
    margin-top: 16px;
    text-align: center;
    font-size: 16px;
    color: #fff;
  }
  .time-box {
    display: flex;
    align-items: center;
    color: #fff;
    margin-top: 40px;
  }
  .right {
    margin-left: 60px;
  }
  .right-title {
    font-size: 16px;
  }
  .right-count {
    font-size: 40px;
    font-weight: bold;
    margin-top: 4px;
  }
  .mt-16 {
    margin-top: 16px;
  }
</style>
