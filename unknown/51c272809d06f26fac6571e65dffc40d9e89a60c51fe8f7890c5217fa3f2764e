<template>
  <el-table :data="waybillInfo" :header-cell-style="{ background: '#F5F7F9' }" border>
    <el-table-column type="index" align="center"></el-table-column>
    <el-table-column prop="waybillCode" label="收运单编号" align="center" min-width="100"></el-table-column>
    <el-table-column prop="effectiveDate" label="收运单生效日期" align="center" min-width="160"></el-table-column>
    <el-table-column prop="isTemp" label="临时收运单" align="center" min-width="100">
      <template #default="{ row }">{{ IS_TEMPORARY[row.isTemp] }}</template>
    </el-table-column>
    <el-table-column prop="name" label="路线名称" align="center"></el-table-column>
    <el-table-column prop="districtName" label="所属区域" align="center"></el-table-column>
    <el-table-column prop="type" label="路线属性" align="center" min-width="120">
      <template #default="{ row }">{{ ROUTE_PROPERTY[row.type] }}</template>
    </el-table-column>
    <el-table-column prop="defaultVehiclePlateNumber" label="默认车辆" align="center" min-width="120"></el-table-column>
    <el-table-column prop="defaultDriverDossierName" label="默认司机" align="center"></el-table-column>
    <el-table-column prop="supercargoDossierOneName" label="押运工" align="center"></el-table-column>
    <el-table-column prop="supercargoDossierTwoName" label="押运工2" align="center"></el-table-column>
    <el-table-column prop="waybillType" label="收运方式" align="center">
      <template #default="{ row }">{{ WAYBILL_TYPE[row.waybillType] }}</template>
    </el-table-column>
    <el-table-column prop="pointNumber" label="点位数量" align="center"></el-table-column>
    <el-table-column prop="collectNum" label="已收运点位数" align="center"></el-table-column>
    <el-table-column label="操作" align="center" fixed="right" min-width="200">
      <template #default="{ row }">
        <el-link class="mr-10" type="primary" @click="editRecord(row)">详情</el-link>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
  import { IS_TEMPORARY, ROUTE_PROPERTY, WAYBILL_TYPE } from "@/enums";
  export default {
    props: {
      waybillInfo: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        IS_TEMPORARY,
        ROUTE_PROPERTY,
        WAYBILL_TYPE,
      };
    },
    methods: {
      editRecord(row) {
        this.$emit("editRecord", row);
      },
    },
  };
</script>

<style lang="scss" scoped></style>
