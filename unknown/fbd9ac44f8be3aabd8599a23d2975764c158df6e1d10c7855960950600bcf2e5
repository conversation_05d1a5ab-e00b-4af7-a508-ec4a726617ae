<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">{{ recordId ? "编辑" : "新增" }}考核方案</div>
      <baseTitle title="基础信息"></baseTitle>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="考核方案名称" prop="schemeName">
              <el-input
                v-model="ruleForm.schemeName"
                placeholder="请输入考核方案名称"
                clearable
                maxlength="30"
                show-word-limit
                :disabled="canEdit"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="考核周期" prop="period">
              <el-select
                v-model="ruleForm.period"
                placeholder="请选择考核周期"
                clearable
                filterable
                @change="changePeriod"
                :disabled="canEdit"
              >
                <el-option
                  v-for="(item, index) in EXAMINE_PERIOD"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <template v-if="ruleForm.period || ruleForm.period === 0">
            <el-col>
              <el-form-item label="被考核人员" prop="personList" :rules="personRules">
                <el-table
                  class="table-bottom"
                  :data="ruleForm.personList"
                  :header-cell-style="{ background: '#F5F7F9' }"
                  border
                >
                  <el-table-column type="index" align="center"></el-table-column>
                  <el-table-column prop="fullName" label="真实姓名" align="center"></el-table-column>
                  <el-table-column prop="phone" label="联系电话" align="center"></el-table-column>
                  <el-table-column prop="userName" label="用户名" align="center"></el-table-column>
                  <el-table-column label="操作" align="center" v-if="!canEdit">
                    <template #default="{ row }">
                      <el-link type="danger" @click="deletePerson(row)">删除</el-link>
                    </template>
                  </el-table-column>
                </el-table>
                <el-button type="primary" icon="el-icon-plus" @click="showPerson = true" v-if="!canEdit"
                  >选人员</el-button
                >
              </el-form-item>
            </el-col>
            <person
              :value.sync="showPerson"
              :period="ruleForm.period"
              :personList="ruleForm.personList"
              :assessSchemeId="assessSchemeId"
              @selectPerson="selectPerson"
              @deletePerson="deletePerson"
            ></person>
            <el-col>
              <el-form-item label="被考核部门" prop="deptList">
                <el-cascader
                  class="w500"
                  v-model="ruleForm.deptList"
                  placeholder="请选择被考核部门"
                  filterable
                  clearable
                  :options="deptOptions"
                  :props="deptProps"
                  :show-all-levels="false"
                  :disabled="canEdit"
                ></el-cascader>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
        <template v-if="ruleForm.period || ruleForm.period === 0">
          <baseTitle title="考核方案信息"></baseTitle>
          <el-row>
            <template v-if="ruleForm.period === 0">
              <el-col>
                <el-form-item label="满分分值" prop="perfectScore" required>
                  <el-input
                    v-model="ruleForm.perfectScore"
                    placeholder="请输入满分分值"
                    clearable
                    maxlength="10"
                    show-word-limit
                    readonly
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item label="考核维度" prop="assessDimensions">
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    @click="editItem('dimensionItem', 'showDimension', '')"
                    v-if="!canEdit"
                    >新增考核维度</el-button
                  >
                  <el-table
                    class="table-top"
                    :data="ruleForm.assessDimensions"
                    :header-cell-style="{ background: '#F5F7F9' }"
                    border
                  >
                    <el-table-column type="index" align="center"></el-table-column>
                    <el-table-column prop="name" label="维度名称" align="center"></el-table-column>
                    <el-table-column prop="weight" label="维度满分" align="center"></el-table-column>
                    <el-table-column prop="weight" label="权重" align="center">
                      <template #default="{ row }">{{ row.weight }}%</template>
                    </el-table-column>
                    <el-table-column min-width="140" label="操作" align="center" v-if="!canEdit">
                      <template #default="{ row, $index }">
                        <el-link class="mr-10" type="primary" @click="editItem('dimensionItem', 'showDimension', row)"
                          >编辑</el-link
                        >
                        <el-link type="danger" @click="deleteItem('assessDimensions', $index)">删除</el-link>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </template>
            <dimension
              :value.sync="showDimension"
              :formItem="dimensionItem"
              :formList="ruleForm.assessDimensions"
              @addList="addList"
              @editList="editList"
            ></dimension>
            <el-col>
              <el-form-item label="考核等级" prop="assessGrades">
                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  @click="editItem('gradeItem', 'showGrade', '')"
                  v-if="!canEdit"
                  >新增考核等级</el-button
                >
                <el-table
                  class="table-top"
                  :data="ruleForm.assessGrades"
                  :header-cell-style="{ background: '#F5F7F9' }"
                  border
                >
                  <el-table-column prop="sort" label="等级排名" align="center"></el-table-column>
                  <el-table-column prop="name" label="等级名称" align="center"></el-table-column>
                  <el-table-column prop="address" label="等级分数区间" align="center">
                    <template #default="{ row }">{{ row.min }}&lt;x&lt;={{ row.max }}</template>
                  </el-table-column>
                  <el-table-column prop="coefficient" label="考核系数" align="center"></el-table-column>
                  <el-table-column prop="personnelProportion" label="人员占比" align="center">
                    <template #default="{ row }">{{
                      row.personnelProportion === undefined ? "无限制" : `≤${row.personnelProportion}%`
                    }}</template>
                  </el-table-column>
                  <el-table-column prop="integrate" label="年度绩效积分" align="center" v-if="ruleForm.period === 1">
                    <template #default="{ row }">{{ row.integrate }}分</template>
                  </el-table-column>
                  <el-table-column min-width="140" label="操作" align="center" v-if="!canEdit">
                    <template #default="{ row, $index }">
                      <el-link class="mr-10" type="primary" @click="editItem('gradeItem', 'showGrade', row)"
                        >编辑</el-link
                      >
                      <el-link type="danger" @click="deleteItem('assessGrades', $index)">删除</el-link>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
            <grade
              :value.sync="showGrade"
              :formItem="gradeItem"
              :formList="ruleForm.assessGrades"
              :period="ruleForm.period"
              @addList="addList"
              @editList="editList"
            ></grade>
          </el-row>
          <template v-if="ruleForm.period === 0">
            <baseTitle title="流程信息"></baseTitle>
            <el-row>
              <el-col>
                <el-form-item label="考核流程" prop="assessFlows" required>
                  <el-table
                    class="table-bottom"
                    :data="ruleForm.assessFlows"
                    :header-cell-style="{ background: '#F5F7F9' }"
                    style="width: 100%"
                    border
                  >
                    <el-table-column type="index" label="流程顺序" align="center" width="100"></el-table-column>
                    <el-table-column label="流程内容" align="center">
                      <template #default="{ row }">{{ ASSESSMENT_FLOWS[row.flowCode] }}</template>
                    </el-table-column>
                    <el-table-column prop="weight" label="权重" align="center">
                      <template #default="{ row, $index }">
                        <el-form-item :prop="`assessFlows.${$index}.weight`" :rules="assessFlowsRules.weight">
                          <el-input-number
                            v-model="row.weight"
                            step-strictly
                            :min="0"
                            :max="100"
                            :disabled="canEdit"
                          ></el-input-number>
                        </el-form-item>
                      </template>
                    </el-table-column>
                    <el-table-column prop="enable" label="状态" align="center">
                      <template #default="{ row }">
                        <el-switch
                          v-model="row.enable"
                          active-text="启用"
                          inactive-text="禁用"
                          :active-value="1"
                          :inactive-value="0"
                          :disabled="canEdit"
                        ></el-switch>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </el-col>
            </el-row>
            <!-- <baseTitle title="绩效工资信息"></baseTitle>
            <el-row>
              <el-col>
                <el-form-item label="考核细则">
                  <el-link type="primary" @click="openFilePdf">点击下载</el-link>
                </el-form-item>
              </el-col>
            </el-row> -->
          </template>
        </template>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">{{ canEdit ? "返回" : "取消" }}</el-button>
      <el-button type="primary" @click="saveRecordThrottling" v-if="!canEdit">保存</el-button>
    </div>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  import { EXAMINE_PERIOD, ASSESSMENT_FLOWS } from "@/enums";
  import baseTitle from "@/components/baseTitle";
  import dimension from "./dimension.vue";
  import grade from "./grade.vue";
  import person from "./person.vue";
  import { downloadFileBlob } from "@/utils";
  import { deepClone } from "logan-common/utils";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
      canEdit: {
        type: Boolean,
        default: false,
      },
    },
    components: {
      baseTitle,
      dimension,
      grade,
      person,
    },
    computed: {
      personRules() {
        let rules = [{ required: true, message: "请选择被考核人员", trigger: "change" }];
        if (this.ruleForm.deptList.length > 0) {
          rules[0].required = false;
        }
        return rules;
      },
    },
    data() {
      return {
        ruleForm: {
          schemeName: "", //考核方案名称
          period: "", //考核周期
          personList: [], //被考核人员
          deptList: [], //被考核部门
          perfectScore: 100, //满分分值
          assessDimensions: [], //维度列表
          assessGrades: [], //等级列表
          assessFlows: [
            {
              flowCode: "SELF_EVALUATION", //考核内容
              weight: 0, //权重
              enable: 0,
            },
            {
              flowCode: "DIRECT_SUPERVISOR_EVALUATION", //考核内容
              weight: 50, //权重
              enable: 1,
            },
            {
              flowCode: "DEPARTMENT_HEAD_EVALUATION", //考核内容
              weight: 50, //权重
              enable: 1,
            },
          ], //流程列表
        },
        rules: {
          schemeName: [{ required: true, message: "请输入考核方案名称", trigger: "blur" }],
          period: [{ required: true, message: "请选择考核周期", trigger: "change" }],
          // personList: [{ required: true, message: "请选择被考核人员", trigger: "change" }],
          assessDimensions: [{ required: true, message: "请填写考核维度", trigger: "change" }],
          assessGrades: [{ required: true, message: "请填写考核等级", trigger: "change" }],
        },
        assessFlowsRules: {
          weight: [{ required: true, message: "请输入权重", trigger: "blur" }],
        },
        apis: {
          create: "/api/assess/scheme/create",
          update: "/api/assess/scheme/update",
          info: "/api/assess/scheme/get/",
          deptList: "/api/company/structure/findIn",
          fileUrl: `${process.env.VUE_APP_BASE_API_URL}/sctmpbase/static/医废平台绩效模块说明文件.pdf`,
        },
        saveRecordThrottling: () => {},
        loading: false,
        EXAMINE_PERIOD,
        ASSESSMENT_FLOWS,
        deptProps: {
          checkStrictly: true,
          value: "id",
          label: "name",
          emitPath: false,
          multiple: true,
        },
        deptOptions: [],
        showDimension: false, //考核维度弹窗
        dimensionItem: {},
        showGrade: false, //考核等级弹窗
        gradeItem: {},
        showPerson: false,
        assessSchemeId: "",
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptions();
      if (this.recordId) {
        this.getRecord();
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [getInfoApiFun("", this.apis.deptList)];
        let res = await Promise.all(promiseList);
        this.deptOptions = res[0].data;
      },
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = Object.assign(this.ruleForm, res.data);
          this.assessSchemeId = res.data.id;
          this.ruleForm.assessUsers.forEach((list) => {
            if (list.type === 1) {
              this.ruleForm.deptList.push(list.businessId);
            } else {
              this.ruleForm.personList.push({
                lgUnionId: list.businessId,
                fullName: list.fullName,
                phone: list.phone,
                userName: list.userName,
              });
            }
          });
          this.ruleForm.assessGrades.forEach((list) => {
            list.personnelProportion = !list.personnelProportion ? undefined : list.personnelProportion;
          });
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 考核周期修改
      changePeriod() {
        this.ruleForm.personList = [];
        this.ruleForm.assessGrades = [];
        this.ruleForm.assessDimensions = [];
        this.ruleForm.deptList = [];
        this.ruleForm.rule = "";
      },
      // 选择人员
      selectPerson(row) {
        this.ruleForm.personList.push(row);
      },
      // 删除人员
      deletePerson(row) {
        let index = this.ruleForm.personList.findIndex((list) => list.lgUnionId === row.lgUnionId);
        if (index >= 0) {
          this.ruleForm.personList.splice(index, 1);
        }
      },
      // 编辑考核维度 / 考核等级
      editItem(itemField, showField, row) {
        this[itemField] = row;
        this[showField] = true;
      },
      // 删除考核维度 / 考核等级
      deleteItem(listField, index) {
        this.ruleForm[listField].splice(index, 1);
      },
      // 添加列表数据
      addList(obj) {
        this.ruleForm[obj.listField].push(obj.item);
        this[obj.showfield] = false;
      },
      // 编辑列表数据
      editList(obj) {
        this.$set(this.ruleForm[obj.listField], obj.index, obj.item);
        this[obj.showfield] = false;
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            if (this.ruleForm.period === 0) {
              let totalWeight = this.ruleForm.assessDimensions.reduce((total, currentValue) => {
                return total + currentValue.weight;
              }, 0);
              if (totalWeight !== 100) {
                this.$message.warning("考核维度权重总值需满100%");
                return;
              }
              let flowWeight = this.ruleForm.assessFlows.reduce((total, currentValue) => {
                if (currentValue.enable === 1) {
                  return total + currentValue.weight;
                }
                return total;
              }, 0);
              if (flowWeight !== 100) {
                this.$message.warning("考核流程权重总值需满100%");
                return;
              }
            }
            let assessUsers = [];
            this.ruleForm.personList.forEach((list) => {
              assessUsers.push({
                type: 0,
                businessId: list.lgUnionId,
              });
            });
            if (this.ruleForm.deptList.length > 0) {
              this.ruleForm.deptList.forEach((list) => {
                assessUsers.push({
                  type: 1,
                  businessId: list,
                });
              });
            }
            let params = deepClone(this.ruleForm);
            params.assessGrades.forEach((list) => {
              list.personnelProportion = list.personnelProportion === undefined ? "" : list.personnelProportion;
            });
            this.loading = true;
            try {
              let res = this.recordId
                ? await updateApiFun({ ...params, assessUsers }, this.apis.update)
                : await createApiFun({ ...params, assessUsers }, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}考核方案成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      // 打开考核细则
      openFilePdf() {
        fetch(this.apis.fileUrl)
          .then((res) => res.blob())
          .then((blob) => {
            // 将链接地址字符内容转变成blob地址
            downloadFileBlob(blob, "医疗废物智慧收运管理平台绩效规则说明文件.pdf");
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-bottom {
    margin-bottom: 20px;
  }
  .table-top {
    margin-top: 20px;
  }
  .w500 {
    width: 500px;
  }
  .mr-10 {
    margin-right: 10px;
  }
</style>
