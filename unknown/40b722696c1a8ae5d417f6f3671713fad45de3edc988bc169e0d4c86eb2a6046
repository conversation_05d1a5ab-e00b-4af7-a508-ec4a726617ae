<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="评分" :visible.sync="dialogVisible" width="40%" destroy-on-close @open="initData">
      <div v-loading="loading">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
          <el-form-item label="" prop="assessDimensionScores">
            <el-table
              :data="ruleForm.assessDimensionScores"
              :header-cell-style="{ background: '#F5F7F9' }"
              border
              max-height="500px"
            >
              <el-table-column type="index" align="center"></el-table-column>
              <el-table-column prop="assessDimensionName" label="维度名称" align="center"></el-table-column>
              <el-table-column prop="assessDimensionWeight" label="维度满分" align="center"></el-table-column>
              <el-table-column prop="score" label="打分" align="center">
                <template #default="{ row, $index }">
                  <el-form-item :prop="`assessDimensionScores.${$index}.score`" :rules="dimensionRules.score">
                    <el-input-number
                      v-model="row.score"
                      :min="0"
                      :precision="2"
                      :max="row.assessDimensionWeight"
                    ></el-input-number>
                  </el-form-item>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">返回</el-button>
          <el-button type="primary" @click="saveThrottling">完成</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun } from "@/api/base";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      assessSchemeId: {
        type: String,
        default: "",
      },
      assessFormId: {
        type: String,
        default: "",
      },
      assessFormScoreId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        ruleForm: {
          assessDimensionScores: [],
        },
        rules: {},
        dimensionRules: {
          score: [{ required: true, message: "请输入评分", trigger: "blur" }],
        },
        saveThrottling: () => {},
        loading: false,
        apis: {
          save: "/api/assess/form/assessFormMark",
          info: "/api/assess/form/getAssessDimensionScoreList/",
          record: "/api/assess/scheme/get/",
        },
      };
    },
    created() {
      this.saveThrottling = this.$throttling(this.saveRecord, 500);
    },
    methods: {
      async initData() {
        let res = await getInfoApiFun(this.assessFormScoreId, this.apis.info);
        if (res.success) {
          if (res.data && res.data.length > 0) {
            this.ruleForm.assessDimensionScores = res.data;
          } else {
            let rsp = await getInfoApiFun(this.assessSchemeId, this.apis.record);
            if (rsp.success) {
              let assessDimensions = rsp.data.assessDimensions;
              this.ruleForm.assessDimensionScores = assessDimensions.map((item) => {
                return {
                  assessDimensionId: item.id,
                  assessDimensionName: item.name,
                  score: 0,
                  assessDimensionWeight: item.weight,
                };
              });
            }
          }
          this.$nextTick(() => {
            this.$refs.ruleForm.clearValidate();
          });
        }
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun({ assessFormId: this.assessFormId, ...this.ruleForm }, this.apis.save);
              if (res.success) {
                this.$message.success(`评分成功`);
                this.dialogVisible = false;
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
