<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <detail
        v-if="showDetail"
        :detailId="detailId"
        canScore
        @closeDetail="showDetail = false"
        @refreshList="initData"
      ></detail>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-input">
            <el-input class="w300" v-model="keyword" placeholder="请输入考核方案名称/被考核人名称" clearable></el-input>
          </div>
          <InterviewChannel :value.sync="filterForm.channelId" :record.sync="channelRecord"></InterviewChannel>
          <el-form class="mr-10" :model="filterForm" label-suffix=":" label-width="80px">
            <el-form-item label="考核月度">
              <el-date-picker v-model="filterForm.yearMonth" type="month" placeholder="请选择考核月度"></el-date-picker>
            </el-form-item>
          </el-form>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
        </header>
        <main class="main">
          <el-table
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            height="100%"
            border
            ref="tableRef"
          >
            <el-table-column prop="month" label="考核月度" align="center">
              <template #default="{ row }">{{ row.year }}-{{ row.month >= 10 ? row.month : "0" + row.month }}</template>
            </el-table-column>
            <el-table-column prop="assessSchemeName" label="考核方案" align="center"></el-table-column>
            <el-table-column prop="period" label="考核周期" align="center">
              <template #default>月度</template>
            </el-table-column>
            <el-table-column prop="fullName" label="被考核人" align="center"></el-table-column>
            <el-table-column label="上一级评分情况" align="center">
              <template #default="{ row }">
                <el-link type="primary" @click="viewBefore(row)">{{
                  row.isReturnAssessForm == 1 ? row.beforeScore + `(${row.beforeAssessGradeName})` : ""
                }}</el-link>
              </template>
            </el-table-column>
            <el-table-column prop="basicPerformance" label="基础绩效" align="center">
              <template #default="{ row }">
                <el-link type="primary" @click="viewPerformance(row, 0)">
                  <span v-if="row.hasClinicGroup && ((!row.lessType && row.lessType !== 0) || !row.pathFactor)">
                    <span class="el-icon-warning"></span>
                    <span>待选择</span>
                    <span v-if="!row.lessType && row.lessType !== 0">扣款方式</span>
                    <span v-if="!row.lessType && row.lessType !== 0 && !row.pathFactor">及</span>
                    <span v-if="!row.pathFactor">线路系数</span>
                  </span>
                  <span v-else>{{ row.basicPerformance }}</span>
                </el-link>
              </template>
            </el-table-column>
            <el-table-column label="扣分项" align="center">
              <template #default="{ row }">
                <el-link type="primary" @click="openDeduction(row)">配置扣分项</el-link>
              </template>
            </el-table-column>
            <el-table-column label="评分" align="center" width="360">
              <template #default="{ row }">
                <el-table :data="row.assessDimensions" border :show-header="false">
                  <el-table-column label="" align="center">
                    <template #default="scope">{{ scope.row.name }}（{{ scope.row.weight }}分）</template>
                  </el-table-column>
                  <el-table-column label="" align="center">
                    <template #default="scope">
                      <el-input-number
                        style="width: 140px"
                        v-model="scope.row.score"
                        :min="0"
                        :precision="2"
                        :max="scope.row.weight"
                        @blur="handleInputNumber(scope.row)"
                        @change="handleChange(row)"
                      ></el-input-number>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-table-column>
            <el-table-column prop="score" label="总分" align="center"></el-table-column>
            <el-table-column prop="assessGradeName" label="考核等级" align="center"></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="100" label="操作" align="center">
              <template #default="{ row }">
                <el-popconfirm
                  class="mr-12"
                  title="确认要退回上级吗？"
                  @confirm="returnSuperior(row)"
                  v-if="row.isReturnAssessForm == 1"
                >
                  <el-link type="primary" slot="reference">退回上级</el-link>
                </el-popconfirm>
                <el-popconfirm title="确认完成当前考核评分？" @confirm="confirmRecord(row)">
                  <el-link type="primary" slot="reference">完成</el-link>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
    <deduction :value.sync="showDeduction" :assessFormId="assessFormId" @refreshList="initData"></deduction>
    <Mark
      :value.sync="showMark"
      :assessSchemeId="assessSchemeId"
      :assessFormId="assessFormId"
      :assessFormScoreId="assessFormScoreId"
      @refreshList="initData"
    ></Mark>
    <beforeDimension :value.sync="showBefore" :beforeAssessDimensions="beforeAssessDimensions"></beforeDimension>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import { getListPageApiFun, createApiFun, postApiFunCancel } from "@/api/base";
  import Mark from "./components/mark.vue";
  import deduction from "./components/deduction.vue";
  import moment from "moment";
  import beforeDimension from "./components/beforeDimension.vue";
  import detail from "../exmaineLedger/components/detail";
  export default {
    components: {
      defaultPage,
      Mark,
      deduction,
      beforeDimension,
      detail,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/assess/form/commissionScoreListPage",
          complete: "/api/assess/form/completeAssessForm",
          return: "/api/assess/form/returnAssessForm",
          save: "/api/assess/form/assessFormMark",
        },
        loading: false,
        showMark: false,
        showDeduction: false,
        assessSchemeId: "", //考核表id
        assessFormId: "", //考核评分表id
        assessFormScoreId: "", //扣分id
        keyword: "",
        controller: "",
        componentName: "",
        detailId: "",
        ruleIndex: 0,
        showDetail: false,
        showBefore: false,
        beforeAssessDimensions: [],
        channelRecord: {},
      };
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          channelId: this.filterForm.channelId || "",
        };
        if (this.filterForm.yearMonth) {
          params.year = moment(this.filterForm.yearMonth).format("YYYY");
          params.month = moment(this.filterForm.yearMonth).format("M");
        }
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
          this.$nextTick(() => {
            this.$refs.tableRef.doLayout();
          });
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 打开扣分弹窗
      openDeduction(row) {
        this.showDeduction = true;
        this.assessFormId = row.assessFormId;
      },
      // 打开评分弹窗
      openMark(row) {
        this.showMark = true;
        this.assessSchemeId = row.assessSchemeId;
        this.assessFormId = row.assessFormId;
        this.assessFormScoreId = row.id;
      },
      // 退回上级
      async returnSuperior(row) {
        try {
          let res = await createApiFun({ assessFormId: row.assessFormId }, this.apis.return);
          if (res.success) {
            this.page.pageNo = this.pageDropProcess(this.page.pageNo, this.page.pageSize, this.page.total);
            this.$message.success("退回上级成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 完成考核评分
      async confirmRecord(row) {
        try {
          let res = await createApiFun({ assessFormId: row.assessFormId }, this.apis.complete);
          if (res.success) {
            this.page.pageNo = this.pageDropProcess(this.page.pageNo, this.page.pageSize, this.page.total);
            this.$message.success("完成考核评分成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      async handleInputNumber(item) {
        if (!item.score && item.score !== 0) {
          item.score = 0;
        }
      },
      async handleChange(row) {
        let assessDimensionScores = row.assessDimensions.map((item) => {
          return {
            assessDimensionId: item.assessDimensionId,
            score: item.score || 0,
          };
        });
        try {
          if (this.controller) {
            this.controller.abort();
          }
          // 创建一个新的 AbortController 实例
          this.controller = new AbortController();
          const signal = this.controller.signal;
          const res = await postApiFunCancel(
            {
              assessDimensionScores,
              assessFormId: row.assessFormId,
            },
            this.apis.save,
            { signal },
          );
          if (res.success) {
            this.initData();
          }
        } catch (error) {
          console.log(error);
        }
      },
      // 查看绩效详情
      viewPerformance(item) {
        // switch (item.rule) {
        //   case 0:
        //     this.componentName = `detail0`;
        //     break;
        //   case 1:
        //     this.componentName = `detail1`;
        //     break;
        //   case 2:
        //     this.componentName = `detail2`;
        //     break;
        //   case 3:
        //     this.componentName = `detail2`;
        //     break;
        //   case 4:
        //     this.componentName = `detail4`;
        //     break;
        //   case 5:
        //   case 6:
        //   case 7:
        //     this.componentName = `detail5`;
        //     break;
        //   case 8:
        //     this.componentName = `detail8`;
        //     break;
        // }
        // this.ruleIndex = item.rule;
        this.detailId = item.assessFormId;
        this.showDetail = true;
      },
      // 查看上一级评分详情
      viewBefore(row) {
        this.beforeAssessDimensions = row.beforeAssessDimensions;
        this.showBefore = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-input {
      margin-right: 10px;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w300 {
    width: 300px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .header .el-form-item {
    margin-bottom: 0;
  }
</style>
