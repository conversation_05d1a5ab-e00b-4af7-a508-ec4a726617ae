<template>
  <div class="secure">
    <main-title title="车辆安全管理"></main-title>
    <ul class="secure-list">
      <li class="secure-item" v-for="(item, index) in countList" :key="index" @click="itemClick(index, item)">
        <div class="secure-item-label">{{ item.label }}</div>
        <div class="secure-item-box">
          <div class="secure-item-num">{{ item.num }}</div>
          <div class="secure-item-unit">（{{ item.unit }}）</div>
        </div>
      </li>
    </ul>
    <div class="tab-header">安全自检情况统计图</div>
    <!-- <ul class="tab-list">
      <li
        class="tab-item"
        :class="{ active: activeTab == index }"
        v-for="(item, index) in tabList"
        :key="index"
        @click="toggleTab(index)"
        >{{ item }}</li
      >
    </ul> -->
    <div class="chart-box">
      <div class="chart" id="secure-chart"></div>
    </div>
  </div>
</template>

<script>
  import MainTitle from "./MainTitle.vue";
  import * as echarts from "echarts";
  import { getLastSevenDaysMonthDay } from "@/utils";
  import emitter from "@/utils/mitt";
  export default {
    components: {
      MainTitle,
    },
    props: {
      getVehicleSafety: {
        type: Object,
        default: () => {},
      },
    },
    watch: {
      getVehicleSafety: {
        handler(newV) {
          this.countList[0].num = newV.noInspectNum ? (newV.noInspectNum > 10 ? 10 : newV.noInspectNum) : 0;
          this.countList[1].num = newV.vehicleEvaluationNum
            ? newV.vehicleEvaluationNum > 10
              ? 10
              : newV.vehicleEvaluationNum
            : 0;
          this.countList[2].num = newV.vehicleCheckNum ? (newV.vehicleCheckNum > 10 ? 10 : newV.vehicleCheckNum) : 0;
          if (newV.inspectList && newV?.inspectList.length > 0) {
            // this.option.xAxis[0].data = newV?.inspectList.map((list) => {
            //   let dateList = list.date.split("-");
            //   return `${dateList[1]}-${dateList[2]}`;
            // });
            newV.inspectList.forEach((list) => {
              let dateList = list.date.split("-");
              let index = this.option.xAxis[0].data.findIndex((item) => item == `${dateList[1]}-${dateList[2]}`);
              this.option.series[0].data[index] = list.count;
              this.option.series[1].data[index] = list.errorPercentage;
            });
            // this.option.series[0].data = newV.inspectList.map((list) => list.count);
            // this.option.series[1].data = newV.inspectList.map((list) => list.errorPercentage);
            this.$nextTick(() => {
              this.init();
            });
          }
        },
        deep: true,
        immediate: true,
      },
    },
    data() {
      return {
        countList: [
          {
            label: "今日未完成自检车辆数",
            num: 0,
            unit: "辆",
          },
          {
            label: "今日车辆状态评价异常数",
            num: 0,
            unit: "辆",
          },
          {
            label: "今日抽查车辆异常数",
            num: 0,
            unit: "辆",
          },
        ],
        activeTab: 0,
        tabList: ["近七日", "近四周", "近12月"],
        option: {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross",
              crossStyle: {
                color: "#fff",
              },
            },
          },
          grid: {
            left: "8%",
            bottom: "8%",
          },
          legend: {
            data: ["日期", "自检数量(次)", "自检正常率(%)"],
            textStyle: {
              color: "#fff",
            },
          },
          xAxis: [
            {
              type: "category",
              data: getLastSevenDaysMonthDay(),
              axisPointer: {
                type: "shadow",
              },
              // axisLine: {
              //   lineStyle: {
              //     color: "#fff",
              //   },
              // },
            },
          ],
          yAxis: [
            {
              type: "value",
              name: "自检数量(次)",
              min: 0,
              axisLabel: {
                color: "#fff",
              },
              nameTextStyle: {
                color: "#fff",
              },
              alignTicks: true,
            },
            {
              type: "value",
              name: "自检正常率(%)",
              min: 0,
              axisLabel: {
                color: "#fff",
              },
              nameTextStyle: {
                color: "#fff",
              },
              alignTicks: true,
            },
          ],
          series: [
            {
              name: "自检数量(次)",
              type: "bar",
              barWidth: "50%",
              data: [0, 0, 0, 0, 0, 0, 0],
            },
            {
              name: "自检正常率(%)",
              type: "line",
              yAxisIndex: 1,
              barWidth: "50%",
              data: [0, 0, 0, 0, 0, 0, 0],
            },
          ],
        },
      };
    },
    mounted() {},
    methods: {
      toggleTab() {},
      init() {
        let chartInstance = echarts.init(document.getElementById("secure-chart"));
        chartInstance.setOption(this.option);
      },
      itemClick(index, item) {
        switch (index) {
          case 0:
            emitter.emit("open-secure-dialog", {
              key: "showSecureDialog",
              size: item.num,
            });
            break;
          case 1:
            emitter.emit("open-secure-dialog", {
              key: "showEvaluateDialog",
              size: item.num,
            });
            break;
          case 2:
            emitter.emit("open-secure-dialog", {
              key: "showSupervisoryDialog",
              size: item.num,
            });
            break;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .secure {
    margin-top: 20px;
  }
  .secure-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    margin-top: 16px;
    .secure-item {
      color: #fff;
      cursor: pointer;
      .secure-item-label {
        font-size: 14px;
      }
      .secure-item-box {
        display: flex;
        align-items: flex-end;
        justify-content: center;
        margin-top: 10px;
        .secure-item-num {
          font-size: 32px;
          line-height: 24px;
        }
        .secure-item-unit {
          font-size: 12px;
        }
      }
    }
  }
  .tab-header {
    margin: 16px 0;
    color: #fff;
    font-size: 14px;
  }
  .tab-list {
    background-color: rgba(76, 167, 134, 0.3);
    display: inline-flex;
    align-items: center;
    border-radius: 16px;
    color: #fff;
    margin-bottom: 16px;
    .tab-item {
      width: 60px;
      text-align: center;
      padding: 4px 0;
      font-size: 12px;
      cursor: pointer;
      border-radius: 16px;
      &.active {
        background-color: var(--color-primary);
      }
    }
  }
  .chart-box {
    width: 500px;
    height: 300px;
    margin-bottom: 10px;
    .chart {
      width: 100%;
      height: 100%;
    }
  }
</style>
