<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title="选择下发人员"
      :visible.sync="dialogVisible"
      width="1400px"
      top="0"
      destroy-on-close
      @open="resetFilter"
      v-loading="loading"
    >
      <div class="distribute-left">
        <ul class="select-list">
          <li class="select-item" v-for="item in selectList" :key="item.lgUnionId">
            <el-tag class="tag-item" closable size="medium" @close="toggleUser(item)">{{ item.fullName }}</el-tag>
          </li>
        </ul>
      </div>
      <div class="distribute-right">
        <header class="header">
          <div class="header-input">
            <el-input
              class="w250"
              v-model="keyword"
              placeholder="请输入人员名称/联系电话"
              clearable
              @change="searchFilter"
            ></el-input>
          </div>
        </header>
        <main class="distribute-main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" label="" align="center"></el-table-column>
            <el-table-column prop="fullName" label="姓名" align="center"></el-table-column>
            <el-table-column prop="sex" label="性别" align="center">
              <template #default="{ row }">{{ SEX_OPTIONS[row.sex] }}</template>
            </el-table-column>
            <el-table-column prop="phone" label="联系电话" align="center">
              <template #default="{ row }">{{ row.phone.replace(row.phone.substring(3, 7), "****") }}</template>
            </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <el-link
                  :type="distributeList.includes(row.lgUnionId) ? 'danger' : 'primary'"
                  @click="toggleUser(row)"
                  >{{ distributeList.includes(row.lgUnionId) ? "删除" : "选择" }}</el-link
                >
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="distributeThrottling" :disabled="!distributeList.length"
            >保存并下发</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getListPageApiFun, createApiFun } from "@/api/base";
  import { USER_IDENTITY, SEX_OPTIONS } from "@/enums";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      registrationId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
      distributeList() {
        return this.selectList.map((item) => item.lgUnionId);
      },
    },
    data() {
      return {
        apis: {
          listPage: "/api/baseuser/listPage",
          distribute: "/api/regTemplate/distribute",
        },
        distributeThrottling: () => {},
        loading: false,
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        keyword: "",
        USER_IDENTITY,
        SEX_OPTIONS,
        selectList: [],
      };
    },
    created() {
      this.distributeThrottling = this.$throttling(this.handleDistribute, 500);
    },
    methods: {
      // 获取客商用户分页列表
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          userIdentity: 1,
          jobStatus: 0,
        };
        this.loading = true;
        try {
          let res = await getListPageApiFun(params, this.apis.listPage);
          if (res.success) {
            this.tableData = res.data.datas;
            this.page.total = res.data.total;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      resetFilter() {
        this.keyword = "";
        this.selectList = [];
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 选择/删除人员
      toggleUser(row) {
        if (this.distributeList.includes(row.lgUnionId)) {
          //删除
          let index = this.selectList.findIndex((item) => item.lgUnionId === row.lgUnionId);
          if (index >= 0) {
            this.selectList.splice(index, 1);
          }
        } else {
          // 添加
          this.selectList.push(row);
        }
      },
      // 下发
      async handleDistribute() {
        this.loading = true;
        try {
          let res = await createApiFun(
            { registrationId: this.registrationId, distributeList: this.distributeList },
            this.apis.distribute,
          );
          if (res.success) {
            this.$message.success("下发成功");
            this.$emit("refreshList");
            this.dialogVisible = false;
          }
        } catch (error) {
          this.loading = false;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
  }
  .distribute-left {
    width: 300px;
    height: 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }
  .distribute-right {
    flex: 1;
    overflow: hidden;
    padding: 10px 16px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin: 0 12px;
    .distribute-main {
      flex: 1;
      margin-top: 20px;
      overflow: auto;
    }
  }
  .w250 {
    width: 250px;
  }
  .select-list {
    padding: 10px;
    height: 100%;
    overflow-y: auto;
    .select-item {
      margin-bottom: 10px;
      .tag-item {
        width: 100%;
        height: 40px;
        line-height: 40px;
        position: relative;
        font-size: 14px;
      }
    }
  }
  ::v-deep .el-tag .el-icon-close {
    position: absolute;
    top: 12px;
    right: 5px;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 60px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
    display: flex;
  }
</style>
