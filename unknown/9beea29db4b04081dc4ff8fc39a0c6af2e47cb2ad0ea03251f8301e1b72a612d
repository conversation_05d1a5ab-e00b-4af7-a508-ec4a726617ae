<template>
  <div class="maintenance">
    <main-title title="车辆维保管理"></main-title>
    <ul class="maintenance-list" v-for="(list, i) in countList" :key="i">
      <li class="maintenance-item" v-for="item in list" :key="item.id" @click="itemClick(item)">
        <div class="maintenance-item-label">{{ item.label }}</div>
        <div class="maintenance-item-box">
          <div class="maintenance-item-num">{{ item.num }}</div>
          <div class="maintenance-item-unit">（{{ item.unit }}）</div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
  import MainTitle from "./MainTitle.vue";
  import emitter from "@/utils/mitt";
  export default {
    components: {
      MainTitle,
    },
    props: {
      getVehicleMaintenance: {
        type: Object,
        default: () => {},
      },
    },
    watch: {
      getVehicleMaintenance: {
        handler(newV) {
          this.countList[0][0].num = newV.vehicleMaintenanceCount;
          this.countList[0][1].num = newV.vehicleMaintenanceCosts;
          this.countList[1][0].num = newV.vehicleTwoMaintenanceCount;
          this.countList[1][1].num = newV.vehicleTwoMaintenanceCosts;
          this.countList[2][0].num = newV.vehicleMaintainCount;
          this.countList[2][1].num = newV.vehicleMaintainCosts;
        },
        deep: true,
        immediate: true,
      },
    },
    data() {
      return {
        countList: [
          [
            {
              label: "待保养车辆数",
              num: 0,
              unit: "辆",
              id: 0,
            },
            {
              label: "当月保养总费用",
              num: 0,
              unit: "元",
              id: 1,
            },
          ],
          [
            {
              label: "待二级维护车辆数",
              num: 0,
              unit: "辆",
              id: 2,
            },
            {
              label: "当月二级维护总费用",
              num: 0,
              unit: "元",
              id: 3,
            },
          ],
          [
            {
              label: "当月维修车辆数",
              num: 0,
              unit: "辆",
              id: 4,
            },
            {
              label: "当月维修总费用",
              num: 0,
              unit: "元",
              id: 5,
            },
          ],
        ],
      };
    },
    methods: {
      //每一项点击事件
      itemClick(item) {
        switch (item.id) {
          case 0:
            emitter.emit("open-maintenance-dialog", 0);
            break;
          case 1:
            emitter.emit("open-maintenanceList-dialog", 0);
            break;
          case 2:
            emitter.emit("open-maintenance-dialog", 1);
            break;
          case 3:
            emitter.emit("open-maintenanceList-dialog", 1);
            break;
          case 4:
          case 5:
            emitter.emit("open-maintenanceList-dialog", 2);
            break;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .maintenance {
    margin-top: 16px;
  }
  .maintenance-list {
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 100px;
    .maintenance-item {
      color: #fff;
      text-align: center;
      cursor: pointer;
      .maintenance-item-label {
        font-size: 14px;
      }
      .maintenance-item-box {
        display: flex;
        align-items: flex-end;
        justify-content: center;
        margin-top: 10px;
        .maintenance-item-num {
          font-size: 32px;
          line-height: 24px;
        }
        .maintenance-item-unit {
          font-size: 12px;
        }
      }
    }
  }
</style>
