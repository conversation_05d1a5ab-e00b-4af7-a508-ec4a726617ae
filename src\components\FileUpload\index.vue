<template>
  <div>
    <el-upload
      :multiple="multiple"
      :limit="limit"
      :drag="drag"
      action="custom"
      :accept="accept"
      :http-request="httpRequest"
      :show-file-list="showFileList"
      :file-list="fileList"
      :list-type="listType"
      :on-change="handleChange"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleRemove"
      :before-upload="beforeAvatarUpload"
      :on-exceed="handleExceed"
      :class="{ hideUpload: hideUpload }"
    >
      <slot name="default">
        <el-button type="primary" size="small" v-if="listType === 'text'" icon="el-icon-upload2" plain
          >上传文件</el-button
        >
        <i v-else class="el-icon-plus"></i>
      </slot>
    </el-upload>
    <slot name="tips"></slot>
    <div class="up-tips" v-if="suffix && !drag">
      <i class="el-icon-warning-outline"></i>
      <span>允许格式：{{ suffix }}</span>
    </div>
    <div class="up-tips" v-if="!drag">
      <i class="el-icon-warning-outline"></i>
      <span> 限制文件最大上传数量为：{{ limit }}个，单文件大小最大{{ maxSize }}MB，</span>
      <span>当前已上传：{{ fileList.length }}个。</span>
    </div>
    <el-image
      v-show="false"
      :src="dialogImageUrl"
      fit="contain"
      :preview-src-list="[dialogImageUrl]"
      ref="image__preview"
    ></el-image>
  </div>
</template>

<script>
  import { OSSUpload } from "./upload";
  export default {
    props: {
      //是否支持拖拽
      drag: {
        type: Boolean,
        default: false,
      },
      //文件上传数量
      limit: {
        type: Number,
        default: 1,
      },
      //支持列表显示样式： text/picture-card
      listType: {
        type: String,
        default: "picture-card",
      },
      //展示已下载文件列表
      imageList: {
        type: Array,
        default: () => {
          return [];
        },
      },
      //允许上传的类型
      accept: {
        type: String,
        default: "image/*",
      },
      //允许上传的文件后缀
      suffix: {
        type: String,
        default: ".png,.jpg,.jpeg,.gif",
      },
      //开启多选
      multiple: {
        type: Boolean,
        default: false,
      },
      showFileList: {
        type: Boolean,
        default: true,
      },
      //最大size（单位：MB）
      maxSize: {
        type: [Number, String],
        default: 10,
      },
    },
    data() {
      return {
        fileList: [], //文件上传列表
        dialogImageUrl: "", //图片预览路径
        dialogVisible: false, //打开预览图片窗口
        hideUpload: false, //是否隐藏上传按钮
      };
    },
    watch: {
      imageList(val) {
        // console.log("展示文件列表", val);
        this.fileList = val;
      },
      fileList: {
        handler(val) {
          // console.log("文件列表改变", val);
          this.hideUpload = val.length >= this.limit; //文件数量超出限制后隐藏上传按钮
          //文件列表改变
          this.$emit("uploadChange", val);
        },
        deep: true,
      },
    },
    mounted() {
      this.fileList = this.imageList;
    },
    methods: {
      handleExceed() {
        // console.log(files, fileList);
        this.$message.warning(`请最多上传 ${this.limit} 个文件。`);
      },
      handleRemove(file, fileList) {
        // console.log("图片列表移除", file, fileList);
        this.fileList = fileList;
        this.hideUpload = fileList.length >= this.limit; //文件数量超出限制后隐藏上传按钮
      },
      handleChange(file, fileList) {
        // console.log("图片改变", file, fileList);
        this.fileList = fileList;
        this.hideUpload = fileList.length >= this.limit; //文件数量超出限制后隐藏上传按钮
        if (window.QCefClient) {
          //修正底座路径
          window.QCefClient.invokeMethod("correctWorkingPath", "");
        }
      },
      //图片预览
      handlePictureCardPreview(file) {
        if (this.listType === "text") return;
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
        this.$refs.image__preview.clickHandler();
      },
      //上传之前
      beforeAvatarUpload(file) {
        //文件大小判断
        const isLimit = file.size / 1024 / 1024 < this.maxSize;
        if (!isLimit) {
          this.$message.warning(`上传文件大小不能超过 ${this.maxSize}MB!`);
          return false;
        }
        //后缀名判断
        if (this.suffix) {
          if (this.suffix === "*") return true;
          let suffix = file.name.slice(file.name.lastIndexOf("."));
          let suffixList = this.suffix.replace(/\s/g, "").split(",");
          console.log(suffixList);
          if (!suffixList.includes(suffix)) {
            this.$message.warning(`上传文件允许格式为${this.suffix}`);
            return false;
          }
        }
        return true;
      },

      async httpRequest(obj) {
        //初始化OSSUpload
        await OSSUpload.getOssBase();
        let index = this.fileList.length - 1;
        let currFile = this.fileList[index]; //当前上传的文件
        let ossClient = new OSSUpload();
        ossClient.init();
        // console.log("已选择文件", currFile.name, currFile.name);
        ossClient
          .upload(currFile.raw, (percentage) => {
            currFile.status = "uploading";
            currFile.percentage = percentage;
          })
          .then((data) => {
            console.log("上传成功", obj);
            this.hideUpload = this.fileList.length >= this.limit; //上传完成后 判断是否超出文件上传限制 显示/隐藏上传按钮
            currFile.file = data;
            currFile.status = "success";
            this.$emit("uploadSuccess", obj.file);
          })
          .catch((err) => {
            console.warn("上传失败", err);
            currFile.status = "fail";
            this.fileList.splice(index, 1);
          });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .up-tips {
    font-size: 12px;
    line-height: 16px;
    color: #999999;
    font-weight: 400;
    margin-top: 5px;
  }
  .hideUpload ::v-deep .el-upload--picture-card {
    display: none;
  }
</style>
