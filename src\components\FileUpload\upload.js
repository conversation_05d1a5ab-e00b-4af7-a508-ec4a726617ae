let OSS = require("ali-oss"); //阿里云
import { ossBaseParameter } from "@/api/common";
import { sm2Decrypt } from "@/utils";

// OSS文件命名
export function timestamp() {
  let add = function (m) {
    return m < 10 ? "0" + m : m;
  };
  let time = new Date();
  let h = time.getHours();
  let mm = time.getMinutes();
  let s = time.getSeconds();
  let chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split("");
  // let charsIndex = Math.floor(Math.random() * 26);
  let idvalue = "";
  let n = 23; //这个值可以改变的，对应的生成多少个字母，根据自己需求所改
  for (let i = 0; i < n; i++) {
    idvalue += chars[getRandomIntInclusive(0, 51)];
  }
  // return "" + add(h) + add(mm) + add(s) + chars[charsIndex] + time.getTime();
  return "" + add(h) + add(mm) + add(s) + idvalue + time.getTime();
}

function getRandomIntInclusive(min, max) {
  const randomBuffer = new Uint32Array(1);
  window.crypto.getRandomValues(randomBuffer);
  let randomNumber = randomBuffer[0] / (0xffffffff + 1);
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(randomNumber * (max - min + 1)) + min;
}

//定义OSS上传类
export class OSSUpload {
  static ossBase = {};
  //获取OSS上传基础参数
  static getOssBase() {
    return new Promise((resolve, reject) => {
      ossBaseParameter()
        .then((res) => {
          if (res.success) {
            try {
              res = JSON.parse(sm2Decrypt(res.data));
            } catch (error) {
              res = {};
            }
            OSSUpload.ossBase = {
              ...res,
              region: res.region,
              accessKeyId: res.accessKeyId,
              accessKeySecret: res.accessKeySecret,
              bucket: res.bucketName,
              stsToken: res.stsSecurityToken,
            };
            resolve(res);
          }
        })
        .catch((err) => {
          reject(err);
        });
    });
  }
  constructor() {
    this.ossClient = null;
    this.fileName = null;
  }

  init() {
    // console.log("OSSUpload.ossBase", OSSUpload.ossBase);
    this.ossClient = new OSS({
      region: OSSUpload.ossBase.region,
      accessKeyId: OSSUpload.ossBase.accessKeyId,
      accessKeySecret: OSSUpload.ossBase.accessKeySecret,
      bucket: OSSUpload.ossBase.bucket,
      stsToken: OSSUpload.ossBase.stsToken,
      refreshSTSToken: async () => {
        let data = await OSSUpload.getOssBase();
        // console.log("getOssParams响应，请检查data.data.AccessKeyId", data);
        return {
          accessKeyId: data.accessKeyId,
          accessKeySecret: data.accessKeySecret,
          stsToken: data.stsToken,
        };
      },
      refreshSTSTokenInterval: 1000 * 60 * 59, //1小时刷新，后端跟我说有效期是一小时
    });
  }

  //上传视频
  upload(file, cb, cpt) {
    if (!file) {
      throw new Error("file is no found");
    }
    if (!OSSUpload.ossBase.objectName) {
      throw new Error("请先初始化上传参数");
    }
    return new Promise((resolve, reject) => {
      //文件名不存在则创建
      if (!this.fileName) {
        this.fileName = timestamp() + file.name.substring(file.name.lastIndexOf("."));
      }
      let ossFileName = OSSUpload.ossBase.objectName + this.fileName;
      // 定义中断点。
      let abortCheckpoint = null;
      //上传参数
      let options = {
        parallel: 4, // 设置并发上传的分片数量。
        partSize: 1024 * 1024, //设置分片大小1M
        timeout: 60 * 1000, //设置超时时间
      };
      if (cpt) {
        options.checkpoint = cpt;
      }
      this.ossClient
        .multipartUpload(ossFileName, file, {
          ...options,
          progress: function (p, checkpoint) {
            // checkpoint参数用于记录上传进度，断点续传上传时将记录的checkpoint参数传入即可。
            let percentage = Math.ceil(p * 100);
            // console.log("上传进度", percentage);
            abortCheckpoint = checkpoint; // 为中断点赋值。
            return typeof cb == "function" && cb(percentage);
          },
        })
        .then((result) => {
          // console.log("result: ", result);
          // console.log("上传成功1111", result.res);
          resolve({
            url: OSSUpload.ossBase.host + "/" + result.name,
            fileName: this.fileName,
            ossFileName: ossFileName,
          });
          this.fileName = null;
        })
        .catch((err) => {
          window.ELEMENT.MessageBox.confirm("文件上传超时，是否重新连接上传？", "提示", {
            confirmButtonText: "重新连接",
            cancelButtonText: "取消上传",
          })
            .then(async () => {
              //断点续传
              let data = await this.upload(file, cb, abortCheckpoint);
              resolve(data);
            })
            .catch(() => {
              reject(err);
            });
        });
    });
  }
}

//将base64转换为blob
export function base64ToBlob(base64) {
  var arr = base64.split(",");
  var mime = arr[0].match(/:(.*?);/)[1];
  var bstr = window.atob(arr[1]);
  var n = bstr.length;
  var u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new Blob([u8arr], { type: mime });
}

//将blob转换为file
export function blobToFile(blob, fileName) {
  blob.lastModifiedDate = new Date();
  blob.name = fileName;
  return blob;
}

/**
 * 图片压缩
 * @param {*} file
 * @returns
 */
export function compressImage(file) {
  return new Promise((resolve) => {
    // 创建一个reader
    let reader = new FileReader();
    // 将图片2将转成 base64 格式
    reader.readAsDataURL(file);
    // 读取成功后的回调
    reader.onloadend = function () {
      let result = this.result;
      let img = new Image();
      img.onload = function () {
        let canvas = document.createElement("canvas");
        let ctx = canvas.getContext("2d");
        //瓦片canvas
        let tCanvas = document.createElement("canvas");
        let tctx = tCanvas.getContext("2d");
        // let initSize = img.src.length;
        let width = img.width;
        let height = img.height;

        //如果图片大于四百万像素，计算压缩比并将大小压至400万以下
        let ratio; //像素比
        if ((ratio = (width * height) / 4000000) > 1) {
          ratio = Math.sqrt(ratio);
          width /= ratio;
          height /= ratio;
          // console.log("图片大于400w像素 ratio width=", width);
          // console.log("图片大于400w像素 ratio height=", height);
        } else {
          ratio = 1;
        }
        canvas.width = width;
        canvas.height = height;
        //铺底色
        ctx.fillStyle = "#fff";
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        //如果图片像素大于100万,则使用瓦片绘制
        let count;
        if ((count = (width * height) / 1000000) > 1) {
          count = Math.floor(Math.sqrt(count) + 1); //计算要分成多少块瓦片
          // 计算每块瓦片的宽和高
          let nw = Math.floor(width / count);
          let nh = Math.floor(height / count);
          tCanvas.width = nw;
          tCanvas.height = nh;
          for (let i = 0; i < count; i++) {
            for (let j = 0; j < count; j++) {
              tctx.drawImage(img, i * nw * ratio, j * nh * ratio, nw * ratio, nh * ratio, 0, 0, nw, nh);
              ctx.drawImage(tCanvas, i * nw, j * nh, nw, nh);
            }
          }
          console.warn("1-2-图片超过100W像素 瓦片绘制");
        } else {
          ctx.drawImage(img, 0, 0, width, height);
          console.warn("1-3-图片不超过100W像素");
        }
        setTimeout(() => {
          let ysImg = new Image(); //创建已经执行压缩的图片对象，再执行旋转操作
          ysImg.onload = function () {
            console.log("图片超过100W像素 1000*1000  ---- count", count);

            let ndata = canvas.toDataURL("image/jpeg", 0.8);
            tCanvas.width = tCanvas.height = canvas.width = canvas.height = 0;
            canvas = tCanvas = null;

            resolve(ndata);
          };
          ysImg.src = canvas.toDataURL("image/jpeg");
        }, 500);
      };
      img.src = result;
    };
  });
}
