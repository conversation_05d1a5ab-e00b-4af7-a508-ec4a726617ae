<template>
  <div class="all-management">
    <main-title title="今日路线收运量排名统计"></main-title>
    <div class="data-title">
      <div class="title">排名</div>
      <div class="title">路线名称</div>
      <div class="title">车牌号</div>
      <div class="nums">收运量（KG）</div>
    </div>

    <!-- 子级的展示 -->
    <div class="seaml-scroll chid-scroll can-scroll" v-if="areaId">
      <ul class="data-list">
        <li
          class="data-item"
          :class="['data-item' + index, item.pickupPathId === pickupPathId ? 'data-item-active' : '']"
          v-for="(item, index) in waybillCollectRankDataChid"
          :key="index"
          @click="handleItem(item)"
        >
          <div class="title">
            {{ index + 1 < 10 ? "0" + (index + 1) : index + 1 }}
          </div>
          <div class="title">{{ item.name }}</div>
          <div class="title">{{ item.plateNumber }}</div>
          <div class="nums">{{ item.rubbishTotal }}</div>
        </li>
      </ul>
    </div>

    <!-- 全部的，需要滚动 -->
    <vue-seamless-scroll
      class="seaml-scroll"
      :class="{ 'can-scroll': waybillCollectRankData.length < 6 }"
      :data="waybillCollectRankData"
      :class-option="seamlessScrollOption"
      v-else
    >
      <ul class="data-list">
        <li class="data-item" :class="'data-item' + index" v-for="(item, index) in waybillCollectRankData" :key="index">
          <div class="title">
            {{ index + 1 < 10 ? "0" + (index + 1) : index + 1 }}
          </div>
          <div class="title">{{ item.name }}</div>
          <div class="title">{{ item.plateNumber }}</div>
          <div class="nums">{{ item.rubbishTotal }}</div>
        </li>
      </ul>
    </vue-seamless-scroll>
  </div>
</template>

<script>
  import MainTitle from "@/components/mainTitle/index.vue";

  export default {
    components: {
      MainTitle,
    },
    props: {
      waybillCollectRankData: {
        type: Array,
        default: () => [],
      },

      waybillCollectRankDataChid: {
        type: Array,
        default: () => [],
      },

      areaId: {
        type: String,
        default: "",
      },
    },
    data() {
      return {
        listData: [],
        seamlessScrollOption: {
          step: 2, // 数值越大速度滚动越快
          limitMoveNum: 6, // 开始无缝滚动的数据量 this.dataList.length
          hoverStop: true, // 是否开启鼠标悬停stop
          direction: 1, // 0向下 1向上 2向左 3向右
          openWatch: true, // 开启数据实时监控刷新dom
          singleHeight: 50, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
          singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
          waitTime: 2000, // 单步运动停止的时间(默认值1000ms)
          openTouch: false, // 关闭移动端滑动,不然移动端点击自己动了
        },
        pickupPathId: "",
      };
    },
    created() {
      let list = [];
      for (let i = 0; i < 10; i++) {
        list.push({
          id: i + 199239239 + "",
          address: "越秀1线",
          carNo: "粤A.FD557",
          nums: 900,
        });
      }
      this.listData = list;
    },

    mounted() {},

    beforeDestroy() {},

    methods: {
      handleItem(item) {
        if (item.pickupPathId === this.pickupPathId) {
          this.pickupPathId = "";
        } else {
          this.pickupPathId = item.pickupPathId;
        }
        // 绘制路线通知
        this.$emit("drawLine", item);
      },

      setActiveId(id) {
        this.pickupPathId = id;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .all-management {
    width: 480px;
    position: absolute;
    top: 650px;
    left: 40px;
    z-index: 1;
  }
  .data-title {
    display: flex;
    padding: 9px 16px;
    margin-bottom: 10px;
    color: #ffffff;
    background: linear-gradient(270deg, rgba(93, 255, 203, 0) 0%, rgba(93, 255, 203, 0.1) 100%);
    font-size: 16px;
    line-height: 22px;
    font-weight: 400;
    width: 100%;
    .title {
      flex: 1;
      text-align: center;
    }
    .nums {
      flex: 1;
      text-align: center;
    }
  }
  .seaml-scroll {
    height: 280px;
    overflow: hidden;
    width: 100%;
    font-size: 16px;
    line-height: 22px;
    font-weight: 400;
    color: #ffffff;
    margin-top: 10px;

    .data-list {
      .data-item {
        display: flex;
        padding: 9px 16px;
        margin-bottom: 10px;
        min-height: 50px;
        align-items: center;
        background: linear-gradient(270deg, rgba(93, 255, 203, 0) 0%, rgba(93, 255, 203, 0.1) 100%);
        text-align: center;

        .title {
          flex: 1;
          margin-right: 6px;
        }
        .nums {
          flex: 1;
          text-align: center;
        }

        &-active {
          color: #00fffc;
          border: 1px solid #ffa600;
        }
      }
    }
  }

  .chid-scroll {
    overflow: auto;
    height: 300px;
    .data-item {
      cursor: pointer;
    }
  }

  .can-scroll {
    overflow: auto;
    &::-webkit-scrollbar {
      width: 0 !important; /* 隐藏滚动条 */
      height: 0 !important; /* 隐藏滚动条 */
    }
    /* 隐藏滚动条（WebKit） */
    &::-webkit-scrollbar {
      display: none;
    }

    /* 隐藏滚动条（Firefox） */
    @-moz-document url-prefix() {
      scrollbar-width: thin;
    }
    /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
    -ms-overflow-style: none;
    scrollbar-width: none; /* Firefox 64+ */
    scrollbar-color: transparent transparent;
  }
</style>
