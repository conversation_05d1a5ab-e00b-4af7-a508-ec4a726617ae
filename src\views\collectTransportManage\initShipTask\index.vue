<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <record
        v-if="showRecord"
        :recordItem="recordItem"
        :recordType="recordType"
        @closeRecord="closeRecord"
        @refreshList="initData"
      ></record>
      <handleRecord
        v-else-if="showHandle"
        :recordItem="recordItem"
        @returnIndex="closeRecord"
        @refreshList="initData"
      ></handleRecord>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="keyword" placeholder="请输入驾驶司机/押运工姓名" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
            :colNum="12"
          ></InterviewChannel>
          <el-col :span="12">
            <el-form-item label="收运车辆" prop="plateNumber">
              <el-select v-model="filterForm.plateNumber" placeholder="请选择收运车辆" clearable filterable>
                <el-option v-for="item in carOptions" :key="item.id" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务日期范围">
              <el-date-picker
                v-model="filterForm.taskTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column prop="expectTime" label="期望收运时间" align="center"> </el-table-column>
            <el-table-column prop="defaultVehiclePlateNumber" label="收运车辆" align="center"></el-table-column>
            <el-table-column prop="defaultDriverDossierName" label="驾驶司机" align="center"></el-table-column>
            <el-table-column prop="supercargoDossierOneName" label="押运工" align="center"></el-table-column>
            <el-table-column prop="supercargoDossierTwoName" label="押运工2" align="center"></el-table-column>
            <el-table-column prop="pointNumber" label="收运点数" align="center"></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <template v-if="row.isSponsor == 2">
                  <el-link
                    v-if="moment(row.expectTime).isSameOrBefore(moment().endOf('day'))"
                    class="mr-10"
                    type="primary"
                    @click="editRecord(row, 2)"
                    >详情</el-link
                  >
                  <template v-else>
                    <el-link class="mr-10" type="primary" @click="editRecord(row, 1)">编辑</el-link>
                    <el-popconfirm title="确认删除当前收运任务？" @confirm="deleteRecord(row)">
                      <el-link type="danger" slot="reference">删除</el-link>
                    </el-popconfirm>
                  </template>
                </template>
                <template v-else>
                  <el-link type="primary" @click="toHandle(row)" v-if="row.taskStatus == 1">详情</el-link>
                  <el-link type="primary" @click="toHandle(row)" v-else>去处理</el-link>
                </template>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, deleteApiFun, createApiFun } from "@/api/base";
  import record from "./components/record.vue";
  import moment from "moment";
  import handleRecord from "@/views/eventWatch/backlog/components/record21.vue";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
      handleRecord,
    },
    data() {
      return {
        moment,
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/waybill/sponsorListPage",
          delete: "/api/waybill/delete/",
          carList: "/api/vehicle/dossier/list",
        },
        showRecord: false,
        recordId: "",
        loading: false,
        showFilter: false,
        keyword: "",
        carOptions: [],
        recordItem: {}, //详情数据
        recordType: 0, //详情类型
        showHandle: false,
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.getOptions();
      this.initData();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [createApiFun({ statusList: [0, 1] }, this.apis.carList)];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
      },
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          effectiveBeginTime: this.filterForm.taskTime ? this.filterForm.taskTime[0] : "",
          effectiveEndTime: this.filterForm.taskTime ? this.filterForm.taskTime[1] : "",
          defaultVehiclePlateNumber: this.filterForm.plateNumber,
          channelId: this.filterForm.channelId || "",
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showRecord = true;
        this.recordItem = {};
        this.recordType = 0;
      },
      // 编辑
      editRecord(row, type) {
        this.showRecord = true;
        this.recordItem = row;
        this.recordType = type;
      },
      closeRecord() {
        this.showRecord = false;
        this.showHandle = false;
      },
      // 删除
      async deleteRecord(row) {
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            this.page.pageNo = this.pageDropProcess(this.page.pageNo, this.page.pageSize, this.page.total);
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      toHandle(row) {
        this.recordItem = { ...row, businessId: row.id };
        this.showHandle = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w250 {
    width: 250px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
