<template>
  <div class="car-person">
    <MainTitle title="车辆/人员基础信息" />
    <div
      class="car-person-inner"
      :class="{
        'car-person-inner2': vehicleInfo.monitorWaybillDetailPoint,
        'flex-around': vehicleInfo?.supercargoDossierTwoName && vehicleInfo.monitorWaybillDetailPoint,
      }"
    >
      <div class="car-person-item">
        <div class="box">
          <div class="label">收运车辆：</div>
          <div class="value">{{ vehicleInfo?.plateNumber }}</div>
        </div>
        <div class="box">
          <div class="label">收运司机：</div>
          <div class="value">
            <img class="value-img" v-if="vehicleInfo?.driverAvatar" :src="vehicleInfo.driverAvatar" />
            <img class="value-img" v-else src="@/assets/images/default_avatar.png" />
            <div class="value-box">
              <div class="name">{{ vehicleInfo?.defaultDriverDossierName }}</div>
              <div class="phone">{{ vehicleInfo?.defaultDriverDossierPhone }}</div>
            </div>
          </div>
        </div>
        <div class="box" v-if="vehicleInfo?.supercargoDossierOneName">
          <div class="label">收运押运工：</div>
          <div class="value">
            <img class="value-img" v-if="vehicleInfo?.cargoOneAvatar" :src="vehicleInfo.cargoOneAvatar" />
            <img class="value-img" v-else src="@/assets/images/default_avatar.png" />
            <div class="value-box">
              <div class="name">{{ vehicleInfo?.supercargoDossierOneName }}</div>
              <div class="phone">{{ vehicleInfo?.supercargoDossierOnePhone }}</div>
            </div>
          </div>
        </div>
        <div class="box" v-if="vehicleInfo?.supercargoDossierTwoName">
          <div class="label">收运押运工2：</div>
          <div class="value">
            <img class="value-img" v-if="vehicleInfo?.cargoTwoAvatar" :src="vehicleInfo.cargoTwoAvatar" />
            <img class="value-img" v-else src="@/assets/images/default_avatar.png" />
            <div class="value-box">
              <div class="name">{{ vehicleInfo?.supercargoDossierTwoName }}</div>
              <div class="phone">{{ vehicleInfo?.supercargoDossierTwoPhone }}</div>
            </div>
          </div>
        </div>
      </div>

      <div
        v-if="vehicleInfo?.monitorWaybillDetailPoint"
        class="car-person-item"
        :class="{ 'flex-around': vehicleInfo.monitorWaybillDetailPoint?.supercargoDossierTwoName }"
      >
        <div class="box">
          <div class="label">收运车辆：</div>
          <div class="value">{{ vehicleInfo?.monitorWaybillDetailPoint?.plateNumber }}</div>
        </div>
        <div class="box">
          <div class="label">收运司机：</div>
          <div class="value">
            <img
              class="value-img"
              v-if="vehicleInfo?.monitorWaybillDetailPoint?.driverAvatar"
              :src="vehicleInfo.monitorWaybillDetailPoint.driverAvatar"
            />
            <img class="value-img" v-else src="@/assets/images/default_avatar.png" />
            <div class="value-box">
              <div class="name">{{ vehicleInfo?.monitorWaybillDetailPoint?.defaultDriverDossierName }}</div>
              <div class="phone">{{ vehicleInfo?.monitorWaybillDetailPoint?.defaultDriverDossierPhone }}</div>
            </div>
          </div>
        </div>
        <div class="box" v-if="vehicleInfo?.monitorWaybillDetailPoint?.supercargoDossierOneName">
          <div class="label">收运押运工：</div>
          <div class="value">
            <img
              class="value-img"
              v-if="vehicleInfo?.monitorWaybillDetailPoint?.cargoOneAvatar"
              :src="vehicleInfo.monitorWaybillDetailPoint.cargoOneAvatar"
            />
            <img class="value-img" v-else src="@/assets/images/default_avatar.png" />
            <div class="value-box">
              <div class="name">{{ vehicleInfo.monitorWaybillDetailPoint?.supercargoDossierOneName }}</div>
              <div class="phone">{{ vehicleInfo.monitorWaybillDetailPoint?.supercargoDossierOnePhone }}</div>
            </div>
          </div>
        </div>
        <div class="box" v-if="vehicleInfo.monitorWaybillDetailPoint?.supercargoDossierTwoName">
          <div class="label">收运押运工2：</div>
          <div class="value">
            <img
              class="value-img"
              v-if="vehicleInfo.monitorWaybillDetailPoint?.cargoTwoAvatar"
              :src="vehicleInfo.monitorWaybillDetailPoint.cargoTwoAvatar"
            />
            <img class="value-img" v-else src="@/assets/images/default_avatar.png" />
            <div class="value-box">
              <div class="name">{{ vehicleInfo.monitorWaybillDetailPoint?.supercargoDossierTwoName }}</div>
              <div class="phone">{{ vehicleInfo.monitorWaybillDetailPoint?.supercargoDossierTwoPhone }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import MainTitle from "@/components/mainTitle/index.vue";

  export default {
    components: {
      // Title,
      MainTitle,
    },
    props: ["vehicleInfo"],
  };
</script>

<style lang="scss" scoped>
  .car-person {
    height: 325px;
    display: flex;
    flex-direction: column;

    .main-title {
      flex: none;
    }
  }

  .box {
    display: flex;
    align-items: center;
    margin: 20px 0;
    .label {
      flex: 1;
      font-size: 18px;
      color: #fff;
      line-height: 18px;
      width: 180px;
    }
    .value {
      flex: 2;
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
      color: #fff;
      line-height: 20px;
      .value-img {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
      }
      .value-box {
        margin-left: 30px;
      }

      .name {
        margin-bottom: 3px;
      }
      .phone {
        font-size: 14px;
        color: #fff;
      }
    }
  }

  .car-person-inner {
    display: flex;
    flex: 1;
    padding-top: 5px;
  }

  .car-person-inner2 {
    display: flex;
    flex: 1;
    margin-left: 15px;

    .car-person-item {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .box {
      margin: 9px 0;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      .label {
        font-size: 14px;
        margin-bottom: 5px;
      }
      .value {
        font-size: 18px;
        justify-content: flex-start;
      }
      .value-box {
        margin-left: 15px;
      }
      .value-img {
        width: 40px;
        height: 40px;
      }
    }
  }

  .flex-around {
    .box {
      margin: 6px;
    }
  }
</style>
