<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="160px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="车牌号" prop="plateNumber">
              <el-select v-model="ruleForm.plateNumber" placeholder="请选择车牌号" clearable filterable>
                <el-option v-for="item in carOptions" :key="item.id" :label="item.name" :value="item.name"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="经办人" prop="operatorName">
              <el-input
                v-model="ruleForm.operatorName"
                placeholder="请输入经办人"
                clearable
                maxlength="20"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="评价日期" prop="conditionTime">
              <el-date-picker
                v-model="ruleForm.conditionTime"
                type="date"
                placeholder="请选择评价日期"
                clearable
                value-format="yyyy-MM-dd"
                :picker-options="pickerOptions"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="车辆状况评价明细" prop="detailList">
              <el-table
                :data="ruleForm.detailList"
                :header-cell-style="{ background: '#F5F7F9' }"
                style="width: 100%"
                border
                class="detail-list"
              >
                <el-table-column type="index" align="center" label="序号" width="55"></el-table-column>
                <el-table-column prop="configName" label="评价项目"></el-table-column>
                <el-table-column prop="status" label="项目状态">
                  <template #default="{ row, $index }">
                    <el-form-item :prop="`detailList.${$index}.status`" :rules="detailRules.status">
                      <el-select v-model="row.status" placeholder="请选择项目状态" clearable filterable>
                        <el-option
                          v-for="(item, index) in EVALUATE_STATUS"
                          :key="index"
                          :label="item"
                          :value="index"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <!-- <el-table-column prop="exceptionDesc" label="异常描述">
                  <template #default="{ row, $index }">
                    <el-form-item :prop="`detailList.${$index}.exceptionDesc`" :rules="detailRules.exceptionDesc">
                      <el-input
                        v-model="row.exceptionDesc"
                        placeholder="请输入异常描述"
                        clearable
                        maxlength="500"
                        show-word-limit
                      ></el-input>
                    </el-form-item>
                  </template>
                </el-table-column> -->
              </el-table>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12" v-if="recordId">
            <el-form-item label="检查状态" prop="status">
              <el-select v-model="ruleForm.status" placeholder="请选择检查状态" clearable filterable disabled>
                <el-option
                  v-for="(item, index) in EVALUATE_STATUS"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24">
            <el-form-item label="附件" prop="fileList">
              <FileUpload @uploadChange="uploadChangeFileList" :imageList="imageList" :limit="5">
                <template #tips><el-tag type="warning">请上传异常状态项目照片</el-tag></template>
              </FileUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import FileUpload from "@/components/FileUpload";
  import { EVALUATE_STATUS } from "@/enums";
  import { getInfoApiFun, createApiFun, updateApiFun } from "@/api/base";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      FileUpload,
    },
    data() {
      return {
        ruleForm: {
          plateNumber: "", //车牌号
          operatorName: "", //经办人
          status: "", //检查状态
          conditionTime: "", //评价时间
          detailList: [], //车辆状况明细
          fileList: [], //附件
        },
        rules: {
          plateNumber: [{ required: true, message: "请选择车牌号", trigger: "change" }],
          operatorName: [{ required: true, message: "请输入经办人", trigger: "blur" }],
          status: [{ required: true, message: "请选择检查状态", trigger: "change" }],
          conditionTime: [{ required: true, message: "请选择评价日期", trigger: "change" }],
          detailList: [{ required: true, message: "请填写车辆状况明细", trigger: "change" }],
        },
        detailRules: {
          status: [{ required: true, message: "请选择项目状态", trigger: "change" }],
          exceptionDesc: [{ required: true, message: "请输入异常描述", trigger: "blur", validator: this.validateDesc }],
        },
        apis: {
          create: "/api/vehicle/evaluation/create",
          update: "/api/vehicle/evaluation/update",
          info: "/api/vehicle/evaluation/get/",
          configList: "/api/vehicle/evaluationConfig/list",
          carList: "/api/vehicle/dossier/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        imageList: [],
        EVALUATE_STATUS,
        carOptions: [],
        pickerOptions: {
          disabledDate: (time) => time.getTime() > new Date(new Date().setHours(23, 59, 59)),
        },
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    async mounted() {
      if (this.recordId) {
        await this.getRecord();
      } else {
        await this.getConfigList();
      }
      this.getOptions();
    },
    methods: {
      validateDesc(rule, value, callback) {
        let index = rule.field.split(".")[1];
        if (this.ruleForm.detailList[index].status === 0) {
          callback();
        } else {
          if (!value) {
            callback(new Error("请输入异常描述"));
          } else {
            callback();
          }
        }
      },
      // 获取配置项列表
      async getConfigList() {
        let res = await createApiFun({ type: 0 }, this.apis.configList);
        let configList = res.data;
        this.ruleForm.detailList = configList.map((list) => {
          return {
            configId: list.id,
            configName: list.name,
            status: "",
            exceptionDesc: "",
          };
        });
      },
      // 获取数据列表
      async getOptions() {
        let res = await createApiFun({ statusList: [0, 1] }, this.apis.carList);
        this.carOptions = res.data;
      },
      // 获取用户详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          if (this.ruleForm.fileList?.length > 0) {
            this.imageList = this.ruleForm.fileList.map((list) => {
              return {
                url: list.url,
                file: list,
              };
            });
          }
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = this.recordId
                ? await updateApiFun(this.ruleForm, this.apis.update)
                : await createApiFun(this.ruleForm, this.apis.create);
              if (res.success) {
                this.$message.success(`${this.recordId ? "修改" : "新增"}成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
      uploadChangeFileList(fileList) {
        if (fileList.length > 0) {
          this.ruleForm.fileList = fileList.map((list) => {
            return {
              name: list.name,
              size: list.size,
              ...list.file,
            };
          });
        } else {
          this.ruleForm.fileList = [];
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  ::v-deep .el-transfer-panel {
    width: 30%;
  }
  ::v-deep .el-transfer-panel__body {
    height: 400px;
  }
</style>
