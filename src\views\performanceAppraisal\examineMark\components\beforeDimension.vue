<template>
  <div class="micro-app-sctmp_base">
    <el-dialog title="上一级评分情况" :visible.sync="dialogVisible" width="40%" destroy-on-close>
      <el-table :data="beforeAssessDimensions" :header-cell-style="{ background: '#F5F7F9' }" border max-height="500px">
        <el-table-column prop="name" label="考核维度" align="center">
          <template #default="{ row }">{{ row.name }}（{{ row.weight }}分）</template>
        </el-table-column>
        <el-table-column prop="score" label="分数" align="center"></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">返回</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      beforeAssessDimensions: {
        type: Array,
        default: () => [],
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    methods: {},
  };
</script>

<style lang="scss" scoped>
  .dialog-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
