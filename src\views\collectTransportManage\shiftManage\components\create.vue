<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">新增换班</div>
      <baseTitle title="基础信息"></baseTitle>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="180px" label-suffix="：">
        <el-row>
          <el-col :md="24" :lg="12">
            <el-form-item label="申请人名称" prop="applyUserId">
              <el-select
                v-model="ruleForm.applyUserId"
                placeholder="请选择申请人"
                clearable
                filterable
                @change="changeApplyUser"
              >
                <el-option
                  v-for="item in userOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="申请人联系电话" prop="applyPhone">
              <el-input
                class="w250"
                :value="ruleForm.applyPhone"
                placeholder="请输入申请人联系电话"
                clearable
                maxlength="11"
                show-word-limit
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="换班开始日期" prop="changeStartDate">
              <el-date-picker
                v-model="ruleForm.changeStartDate"
                type="date"
                placeholder="请选择换班开始日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :md="24" :lg="12">
            <el-form-item label="换班结束日期" prop="changeEndDate">
              <el-date-picker
                v-model="ruleForm.changeEndDate"
                type="date"
                placeholder="请选择换班结束日期"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="详情备注" prop="remark">
              <el-input
                v-model="ruleForm.remark"
                placeholder="请输入详情备注"
                clearable
                type="textarea"
                rows="10"
                maxlength="800"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <baseTitle title="业务调整信息"></baseTitle>
        <el-row>
          <el-col>
            <el-form-item label="选择顶班人员" prop="changeUserId">
              <el-select v-model="ruleForm.changeUserId" placeholder="请选择顶班人员" clearable filterable>
                <el-option
                  v-for="item in changeUserOptions"
                  :key="item.lgUnionId"
                  :label="item.fullName"
                  :value="item.lgUnionId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">返回</el-button>
      <el-button type="primary" @click="saveRecordThrottling">完成</el-button>
    </div>
  </div>
</template>

<script>
  import { createApiFun, getListApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
    },
    components: {
      baseTitle,
    },
    data() {
      return {
        ruleForm: {
          applyUserId: "", //申请人id
          applyFullName: "", //申请人名称
          applyPhone: "", //申请人联系电话
          changeStartDate: "", //换班开始日期
          changeEndDate: "", //换班结束日期
          remark: "", //详情备注
          changeUserId: "", //选择顶班人员
        },
        rules: {
          applyUserId: [{ required: true, message: "请选择申请人", trigger: "change" }],
          applyPhone: [{ required: true, message: "请输入申请人联系电话", trigger: "blur" }],
          changeStartDate: [{ required: true, message: "请选择换班开始日期", trigger: "change" }],
          changeEndDate: [{ required: true, message: "请选择换班结束日期", trigger: "change" }],
          remark: [{ required: true, message: "请输入详情备注", trigger: "blur" }],
          changeUserId: [{ required: true, message: "请选择顶班人员", trigger: "change" }],
        },
        apis: {
          create: "/api/waybill/change/create",
          userList: "/api/baseuser/list",
        },
        saveRecordThrottling: () => {},
        loading: false,
        userOptions: [], //用户列表
        driverOptions: [], //司机列表
        shipOptions: [], //押运工列表
        changeUserOptions: [], //顶班人员列表
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getOptions();
    },
    methods: {
      // 获取基础数据
      async getOptions() {
        let promiseList = [
          getListApiFun({ userIdentitys: [3, 4] }, this.apis.userList),
          getListApiFun({ userIdentitys: [3] }, this.apis.userList),
          getListApiFun({ userIdentitys: [4] }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.userOptions = res[0].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.userOptions.forEach((item) => {
          item.identityArr = item.userIdentity.split(",").map(Number);
        });
      },
      // 申请人change事件回调
      changeApplyUser(value) {
        this.ruleForm.applyPhone = "";
        this.ruleForm.changeUserId = "";
        this.changeUserOptions = [];
        if (!value) {
          return;
        }
        let item = this.userOptions.filter((item) => item.lgUnionId === value)[0];
        this.ruleForm.applyPhone = item.phone;
        if (item.identityArr.includes(3) && item.identityArr.includes(4)) {
          this.changeUserOptions = this.userOptions;
        } else if (item.identityArr.includes(3)) {
          this.changeUserOptions = this.driverOptions;
        } else if (item.identityArr.includes(4)) {
          this.changeUserOptions = this.shipOptions;
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await createApiFun(this.ruleForm, this.apis.create);
              if (res.success) {
                this.$message.success(`新增换班成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .w250 {
    width: 250px;
  }
</style>
