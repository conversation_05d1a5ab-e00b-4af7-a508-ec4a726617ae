<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-content">
      <div class="card-header">编辑绩效方案</div>
      <baseTitle title="基础信息"></baseTitle>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="180px" label-suffix="：">
        <el-row>
          <el-col>
            <el-form-item label="绩效方案名称" required>
              <el-input :value="PERFORMANCE_RULE[ruleIndex]" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <baseTitle title="绩效规则信息"></baseTitle>
          <el-col :md="12">
            <el-form-item label="绩效计费类型" required>
              <el-input value="点位计费" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :md="12">
            <el-form-item label="收运方式" required>
              <el-input value="桶装/袋装" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="绩效计算公式" required>
              <el-input value="绩效工资标准 × 线路系数 × 考核系数" placeholder="" reanonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <baseTitle title="司机绩效工资标准"></baseTitle>
          <el-col>
            <el-form-item label="基准工资标准">
              <el-table :data="[{}]" :header-cell-style="{ background: '#F5F7F9' }" border>
                <el-table-column label="基准点位（点）" align="center">
                  <template>
                    <el-input-number v-model="ruleForm.standardPointNum" step-strictly :min="0"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="标准工资（元）" align="center">
                  <template>
                    <el-input-number v-model="ruleForm.standardSalary" step-strictly :min="0"></el-input-number>
                  </template>
                </el-table-column>
              </el-table>
              <el-link type="info" :underline="false">当司机达到该收运点位数量时即可获得的工资。</el-link>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="增加/扣除工资标准">
              <el-table :data="[{}]" :header-cell-style="{ background: '#F5F7F9' }" border>
                <el-table-column label="超出基准点位每点金额（元）" align="center">
                  <template>
                    <el-input-number v-model="ruleForm.afterPointPrice" step-strictly :min="0"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="低于基准点位每点金额（元）" align="center">
                  <template>
                    <el-input-number v-model="ruleForm.beforePointPrice" step-strictly :min="0"></el-input-number>
                  </template>
                </el-table-column>
              </el-table>
              <el-link type="info" :underline="false">当司机未达到/超过该收运点位数量时需要扣除/多发的金额。</el-link>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="无押运工时工资标准">
              <el-table class="table-data" :data="[{}]" :header-cell-style="{ background: '#F5F7F9' }" border>
                <el-table-column label="基准点位（点）" align="center">
                  <template>
                    <el-input-number
                      v-model="ruleForm.noSupercargoStandardPointNum"
                      step-strictly
                      :min="0"
                    ></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="标准工资（元）" align="center">
                  <template>
                    <el-input-number
                      v-model="ruleForm.noSupercargoStandardSalary"
                      step-strictly
                      :min="0"
                    ></el-input-number>
                  </template>
                </el-table-column>
              </el-table>
              <el-table :data="[{}]" :header-cell-style="{ background: '#F5F7F9' }" border>
                <el-table-column label="超出基准点位每点金额（元）" align="center">
                  <template>
                    <el-input-number
                      v-model="ruleForm.noSupercargoAfterPointPrice"
                      step-strictly
                      :min="0"
                    ></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="低于基准点位每点金额（元）" align="center">
                  <template>
                    <el-input-number
                      v-model="ruleForm.noSupercargoBeforePointPrice"
                      step-strictly
                      :min="0"
                    ></el-input-number>
                  </template>
                </el-table-column>
              </el-table>
              <el-link type="info" :underline="false">当司机整月都一个人收运一整条路线时所执行的工资标准。</el-link>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <baseTitle title="押运工绩效工资标准"></baseTitle>
          <el-col>
            <el-form-item label="基准工资标准">
              <el-table :data="[{}]" :header-cell-style="{ background: '#F5F7F9' }" border>
                <el-table-column label="基准点位（点）" align="center">
                  <template>
                    <el-input-number
                      v-model="ruleForm.supercargoStandardPointNum"
                      step-strictly
                      :min="0"
                    ></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="标准工资（元）" align="center">
                  <template>
                    <el-input-number
                      v-model="ruleForm.supercargoStandardSalary"
                      step-strictly
                      :min="0"
                    ></el-input-number>
                  </template>
                </el-table-column>
              </el-table>
              <el-link type="info" :underline="false">当押运工达到该收运点位数量时即可获得的工资。</el-link>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="增加/扣除工资标准">
              <el-table :data="[{}]" :header-cell-style="{ background: '#F5F7F9' }" border>
                <el-table-column label="超出基准点位每点金额（元）" align="center">
                  <template>
                    <el-input-number
                      v-model="ruleForm.supercargoAfterPointPrice"
                      step-strictly
                      :min="0"
                    ></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column label="低于基准点位每点金额（元）" align="center">
                  <template>
                    <el-input-number
                      v-model="ruleForm.supercargoBeforePointPrice"
                      step-strictly
                      :min="0"
                    ></el-input-number>
                  </template>
                </el-table-column>
              </el-table>
              <el-link type="info" :underline="false">当押运工未达到/超过该收运点位数量时需要扣除/增加的金额。</el-link>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <baseTitle title="线路系数标准"></baseTitle>
          <el-form-item label="" required>
            <el-table :data="ruleForm.pathFactors" :header-cell-style="{ background: '#F5F7F9' }" border>
              <el-table-column prop="name" label="线路区域" align="center"></el-table-column>
              <el-table-column prop="factor" label="系数" align="center">
                <template #default="{ row }">
                  <el-input-number v-model="row.factor" :min="0" :precision="2"></el-input-number>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-row>
        <el-row>
          <baseTitle title="未满出勤天数工资标准"></baseTitle>
          <el-form-item label="" required>
            <el-table :data="[{}]" :header-cell-style="{ background: '#F5F7F9' }" border>
              <el-table-column label="出勤天数低于（天）" align="center">
                <template>
                  <el-input-number v-model="ruleForm.beforeAttendanceDay" step-strictly :min="0"></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="工资标准（元/点）" align="center">
                <template>
                  <el-input-number
                    v-model="ruleForm.beforeAttendanceDaySalary"
                    step-strictly
                    :min="0"
                  ></el-input-number>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-row>
      </el-form>
    </div>
    <div class="record-footer">
      <el-button @click="closeRecord">取消</el-button>
      <el-button type="primary" @click="saveRecordThrottling">保存</el-button>
    </div>
  </div>
</template>

<script>
  import baseTitle from "@/components/baseTitle";
  import { createApiFun, updateApiFun } from "@/api/base";
  import { PERFORMANCE_RULE } from "@/enums";
  export default {
    components: {
      baseTitle,
    },
    props: {
      ruleCode: {
        type: String,
        default: "",
      },
      ruleIndex: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        loading: false,
        ruleForm: {
          standardPointNum: "", //司机基准点位
          standardSalary: "", //司机标准工资
          afterPointPrice: "", //司机超出基准点位每点金额
          beforePointPrice: "", //司机低于基准点位每点金额
          noSupercargoStandardPointNum: "", //司机无押运工时基准点位
          noSupercargoStandardSalary: "", //司机无押运工时标准工资
          noSupercargoAfterPointPrice: "", //司机无押运工时超出基准点位每点金额
          noSupercargoBeforePointPrice: "", //司机无押运工时低于基准点位每点金额
          supercargoStandardPointNum: "", //押运工基准点位
          supercargoStandardSalary: "", //押运工标准工资
          supercargoAfterPointPrice: "", //押运工超出基准点位每点金额
          supercargoBeforePointPrice: "", //押运工低于基准点位每点金额
          beforeAttendanceDay: "", //低于多少天出勤天数
          beforeAttendanceDaySalary: "", //低于出勤天数的工资标准
          pathFactors: [], //线路系数标准
        },
        rules: {
          pathFactors: [{ required: true, message: "请填写线路系数标准", trigger: "change" }],
        },
        apis: {
          info: "/api/assess/scheme/set/findByRuleCode",
          save: "/api/assess/scheme/set/",
        },
        saveRecordThrottling: () => {},
        PERFORMANCE_RULE,
        ruleApi: {
          2: "updateClinicGroupSet",
          3: "updateNsClinicGroupSet",
        },
      };
    },
    created() {
      this.saveRecordThrottling = this.$throttling(this.saveRecord, 500);
    },
    mounted() {
      this.getRecord();
    },
    methods: {
      // 获取详情
      async getRecord() {
        this.loading = true;
        try {
          let res = await createApiFun({ ruleCode: this.ruleCode }, this.apis.info);
          if (res.success) {
            this.ruleForm = res.data;
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
          console.log(error);
        }
      },
      // 取消
      closeRecord() {
        this.$emit("closeRecord");
      },
      // 保存
      saveRecord() {
        this.$refs.ruleForm.validate(async (valid) => {
          if (valid) {
            this.loading = true;
            try {
              let res = await updateApiFun(this.ruleForm, this.apis.save + this.ruleApi[this.ruleIndex]);
              if (res.success) {
                this.$message.success(`保存成功`);
                this.closeRecord();
                this.$emit("refreshList");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
            }
          }
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
  }
  .table-data {
    margin-bottom: 20px;
  }
</style>
