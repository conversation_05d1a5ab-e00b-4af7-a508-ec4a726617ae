<template>
  <div class="title-box">
    <div class="title-left"></div>
    <div class="title-text" :style="styleObject">{{ title }}</div>
  </div>
</template>

<script>
  export default {
    props: {
      title: {
        type: String,
        default: "",
      },
      styleObject: {
        type: Object,
        default: () => {},
      },
    },
  };
</script>

<style lang="scss" scoped>
  .title-box {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    .title-left {
      width: 4px;
      height: 16px;
      background: #4ca786;
      margin-right: 8px;
    }
    .title-text {
      font-size: 18px;
      font-weight: 600;
      color: #122131;
      line-height: 25px;
    }
  }
</style>
