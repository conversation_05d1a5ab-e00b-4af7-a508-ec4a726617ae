<template>
  <div class="card-container mr-10">
    <div class="card-title">{{ title }}</div>
    <div class="card-value">
      <div class="card-count">{{ count }}</div>
      <div class="card-unit" v-if="unit">{{ unit }}</div>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      title: {
        type: String,
        default: "",
      },
      count: {
        type: Number,
        default: 0,
      },
      unit: {
        type: String,
        default: "",
      },
    },
  };
</script>

<style lang="scss" scoped>
  .card-container {
    padding: 20px;
    border: 1px solid #000;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .card-title {
    font-size: 16px;
    white-space: nowrap;
  }
  .card-value {
    display: flex;
    align-items: baseline;
    margin-top: 16px;
  }
  .card-count {
    font-size: 60px;
    font-weight: bold;
  }
</style>
