<template>
  <div class="container">
    <div class="header">
      <div class="header-label">收运路线名称：</div>
      <div class="header-title">{{ carData?.routeName }}</div>
    </div>
    <div class="time-box">
      <div class="right">
        <div class="right-title">今日收运点位总数</div>
        <div class="right-count">{{ carData?.total }}</div>
        <div class="right-title mt-16">今日已收运点位总数</div>
        <div class="right-count">{{ carData?.collectNum }}</div>
      </div>
      <el-progress
        :width="100"
        type="circle"
        :percentage="roundReserveDecimals(carData?.collectPercentage * 100) || 0"
        :stroke-width="10"
        define-back-color="#aaa"
        text-color="#fff"
        color="#CAF982"
      ></el-progress>
    </div>
  </div>
</template>

<script>
  import { roundReserveDecimals } from "@/utils";
  export default {
    props: ["carData"],
    data() {
      return {
        roundReserveDecimals,
      };
    },
  };
</script>

<style lang="scss" scoped>
  .container {
    margin: 30px 0;
    padding-left: 60px;
  }
  .header {
    display: flex;
    align-items: center;
    font-weight: bold;
    color: #fff;
    .header-label {
      font-size: 18px;
    }
    .header-title {
      font-size: 24px;
    }
  }
  .time-box {
    display: flex;
    align-items: center;
    color: #fff;
    margin-top: 40px;
    justify-content: space-between;
    padding-right: 40px;
  }
  .right {
    font-weight: bold;
  }
  .right-title {
    font-size: 16px;
  }
  .right-count {
    font-size: 40px;
    font-weight: bold;
    margin-top: 4px;
  }
  .mt-16 {
    margin-top: 16px;
  }
</style>
