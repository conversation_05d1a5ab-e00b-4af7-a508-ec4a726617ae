<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <deptManage v-if="showDeptManage" :enterpriseInfo="enterpriseInfo" @closeRecord="closeRecord"></deptManage>
      <template v-else>
        <record v-if="showRecord" :recordId="recordId" @closeRecord="closeRecord" @refreshList="initData"></record>
        <div class="main-index" v-else>
          <header class="header">
            <div class="header-input">
              <el-input
                class="w400"
                v-model="filterForm.keyword"
                placeholder="请输入企业名称/法人名称/联系人名称/联系人电话信息"
                clearable
              ></el-input>
            </div>
            <InterviewChannel :value.sync="filterForm.channelId" :record.sync="channelRecord"></InterviewChannel>
            <el-button @click="resetFilter">重置</el-button>
            <el-button type="primary" @click="searchFilter">查询</el-button>
            <div class="header-right">
              <el-button size="small" type="primary" icon="el-icon-plus" @click="createRecord">新增</el-button>
            </div>
          </header>
          <main class="main">
            <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
              <el-table-column prop="name" label="企业名称" align="center"></el-table-column>
              <el-table-column prop="creditCode" label="统一社会信用代码" align="center">
                <template #default="{ row }">{{
                  row.creditCode || row.creditCode === 0 ? row.creditCode : "-"
                }}</template>
              </el-table-column>
              <el-table-column prop="industry" label="行业" align="center"></el-table-column>
              <el-table-column prop="level" label="企业层级" align="center">
                <template #default="{ row }">{{ row.level ? ENTERPRISE_LEVEL[Number(row.level)] : "-" }}</template>
              </el-table-column>
              <el-table-column prop="representativeName" label="法定代表人" align="center">
                <template #default="{ row }">{{
                  row.representativeName || row.representativeName === 0 ? row.representativeName : "-"
                }}</template>
              </el-table-column>
              <el-table-column prop="personInCharge" label="联系人" align="center"></el-table-column>
              <el-table-column prop="phoneNumber" label="联系电话" align="center">
                <template #default="{ row }">{{
                  row.phoneNumber.replace(row.phoneNumber.substring(3, 7), "****")
                }}</template>
              </el-table-column>
              <el-table-column prop="isDefault" label="是否默认企业" align="center">
                <template #default="{ row }">{{ row.isDefault === 1 ? "是" : "否" }}</template>
              </el-table-column>
              <el-table-column prop="channelId" label="渠道名称" align="center">
                <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
              </el-table-column>
              <el-table-column min-width="140" label="操作" align="center">
                <template #default="{ row }">
                  <el-link class="mr-10" type="primary" @click="editRecord(row)">编辑</el-link>
                  <el-link class="mr-10" type="primary" @click="openDeptManage(row)">部门管理</el-link>
                  <el-popconfirm title="确认导出当前部门？" @confirm="exportDept(row)">
                    <el-link class="mr-10" type="primary" slot="reference">导出部门</el-link>
                  </el-popconfirm>
                  <el-popconfirm title="确认删除当前企业？" @confirm="deleteRecord(row)">
                    <el-link type="danger" slot="reference">删除</el-link>
                  </el-popconfirm>
                </template>
              </el-table-column>
            </el-table>
          </main>
          <Pagination :page="page" @pageChange="pageChange"></Pagination>
        </div>
      </template>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, deleteApiFun, BASE_API_URL } from "@/api/base";
  import record from "./components/record.vue";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import { ENTERPRISE_LEVEL } from "@/enums";
  import deptManage from "./components/deptManage.vue";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
      deptManage,
    },
    data() {
      return {
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/company/listPage",
          delete: "/api/company/delete/",
          export: "/api/vehicle/annualReview/export",
          deptExport: "/api/company/structureUser/export",
        },
        showRecord: false,
        recordId: "",
        loading: false,
        ENTERPRISE_LEVEL,
        showDeptManage: false,
        enterpriseInfo: {},
        channelRecord: {},
      };
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas.map((item) => {
            return {
              ...item,
              phoneNumber: this.$sm2Decrypt(item.phoneNumber),
            };
          });
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 新增
      createRecord() {
        this.showRecord = true;
        this.recordId = "";
      },
      // 编辑
      editRecord(row) {
        this.showRecord = true;
        this.recordId = row.id;
      },
      closeRecord() {
        this.showRecord = false;
        this.showDeptManage = false;
      },
      // 删除
      async deleteRecord(row) {
        try {
          let res = await deleteApiFun("", this.apis.delete + row.id);
          if (res.success) {
            this.page.pageNo = this.pageDropProcess(this.page.pageNo, this.page.pageSize, this.page.total);
            window.ELEMENT.Message.success("删除成功");
            this.initData();
          }
        } catch (error) {
          console.warn(error);
        }
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 导出
      handleExport() {
        this.$confirm(`确认是否以当前筛选条件导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, this.filterForm);
              if (res.success) {
                createDownloadEvent(`人员管理导出记录${Date.now()}.xlsx`, [res.data]);
                window.ELEMENT.Message.success("导出成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
      // 打开部门管理
      openDeptManage(row) {
        this.enterpriseInfo = row;
        this.showDeptManage = true;
      },
      // 导出部门
      async exportDept(row) {
        this.loading = true;
        try {
          let res = await exportFile(BASE_API_URL + this.apis.deptExport, { id: row.id });
          if (res.success) {
            createDownloadEvent(`${row.name}_部门导出记录${Date.now()}.xlsx`, [res.data]);
            window.ELEMENT.Message.success("导出成功");
          }
          this.loading = false;
        } catch (error) {
          this.loading = false;
          console.warn(error);
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-input {
      margin-right: 10px;
    }
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w400 {
    width: 400px;
  }
</style>
