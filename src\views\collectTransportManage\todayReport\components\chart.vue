<template>
  <div class="chart-container" :id="chartId"></div>
</template>

<script>
  import * as echarts from "echarts";
  export default {
    props: {
      chartId: {
        type: String,
        default: "chart",
      },
      centerTitle: {
        type: String,
        default: "",
      },
      total: {
        type: Number,
        default: 0,
      },
      dataList: {
        type: Array,
        default: () => [],
      },
    },
    mounted() {
      this.init();
    },
    methods: {
      init() {
        let chartInstance = echarts.init(document.getElementById(this.chartId));
        let option = {
          title: {
            text: this.total,
            subtext: this.centerTitle,
            left: "center",
            top: "40%",
          },
          legend: {
            bottom: "0%",
            left: "center",
            itemWidth: 8,
            itemHeight: 8,
            icon: "rect",
            textStyle: {
              color: "#889098",
            },
          },
          tooltip: {
            trigger: "item",
            formatter: "{b}<br/>{c}",
          },
          series: [
            {
              type: "pie",
              radius: [60, 80],
              center: ["50%", "50%"],
              roseType: "radius",
              label: {
                show: true,
                color: "inherit",
                formatter: "{c}",
              },
              data: this.dataList,
            },
          ],
        };
        chartInstance.setOption(option);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .chart-container {
    width: 100%;
    height: 100%;
  }
</style>
