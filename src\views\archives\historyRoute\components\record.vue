<template>
  <div class="main-record micro-app-sctmp_base" v-loading="loading">
    <div class="record-left">
      <div class="record-content">
        <div class="card-header">
          <div>历史收运路线详情</div>
          <div class="version-number">版本号：v{{ ruleForm.versionNumber.toFixed(1) }}</div>
        </div>
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="140px" label-suffix="：">
          <baseTitle title="路线基础信息："></baseTitle>
          <el-row>
            <el-col :md="24" :lg="12">
              <el-form-item label="路线名称" prop="name">
                <el-input
                  v-model="ruleForm.name"
                  placeholder="请输入路线名称"
                  clearable
                  maxlength="32"
                  show-word-limit
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="路线编号" prop="code">
                <el-input
                  v-model="ruleForm.code"
                  placeholder="请输入路线编号"
                  clearable
                  maxlength="32"
                  show-word-limit
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="路线状态" prop="status">
                <el-select v-model="ruleForm.status" placeholder="请选择路线状态" clearable filterable disabled>
                  <el-option
                    v-for="(item, index) in ROUTE_STATUS"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="所属区域" prop="districtId">
                <el-cascader
                  v-model="ruleForm.districtId"
                  :options="districtOptions"
                  clearable
                  filterable
                  :props="cascaderProps"
                  disabled
                ></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="路线属性" prop="type">
                <el-select v-model="ruleForm.type" placeholder="请选择路线属性" clearable filterable disabled>
                  <el-option
                    v-for="(item, index) in ROUTE_PROPERTY"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="收运方式" prop="waybillType">
                <el-select v-model="ruleForm.waybillType" placeholder="请选择收运方式" clearable filterable disabled>
                  <el-option
                    v-for="(item, index) in WAYBILL_TYPE"
                    :key="index"
                    :label="item"
                    :value="index"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <baseTitle title="收运车辆、人员基础信息："></baseTitle>
          <el-row>
            <el-col>
              <el-form-item label="默认收运车辆" prop="defaultVehiclePlateNumber">
                <el-select
                  v-model="ruleForm.defaultVehiclePlateNumber"
                  placeholder="请选择默认收运车辆"
                  clearable
                  filterable
                  disabled
                >
                  <el-option
                    v-for="item in carOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="默认驾驶司机" prop="defaultDriverDossierId">
                <el-select
                  v-model="ruleForm.defaultDriverDossierId"
                  placeholder="请选择默认驾驶司机"
                  clearable
                  filterable
                  disabled
                >
                  <el-option
                    v-for="item in driverOptions"
                    :key="item.lgUnionId"
                    :label="item.fullName"
                    :value="item.lgUnionId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="司机联系方式" prop="defaultDriverDossierPhone">
                <el-input
                  v-model="ruleForm.defaultDriverDossierPhone"
                  placeholder="请输入司机联系方式"
                  clearable
                  :maxlength="11"
                  show-word-limit
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="默认押运工1" prop="supercargoDossierOneId">
                <el-select
                  v-model="ruleForm.supercargoDossierOneId"
                  placeholder="请选择默认押运工1"
                  clearable
                  filterable
                  disabled
                >
                  <el-option
                    v-for="item in shipWorkerOptions"
                    :key="item.lgUnionId"
                    :label="item.fullName"
                    :value="item.lgUnionId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="押运工联系方式" prop="supercargoDossierOnePhone">
                <el-input
                  v-model="ruleForm.supercargoDossierOnePhone"
                  placeholder="请输入押运工联系方式"
                  clearable
                  :maxlength="11"
                  show-word-limit
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="默认押运工2" prop="supercargoDossierTwoId">
                <el-select
                  v-model="ruleForm.supercargoDossierTwoId"
                  placeholder="请选择默认押运工2"
                  clearable
                  filterable
                  disabled
                >
                  <el-option
                    v-for="item in shipWorkerOptions"
                    :key="item.lgUnionId"
                    :label="item.fullName"
                    :value="item.lgUnionId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :md="24" :lg="12">
              <el-form-item label="押运工联系方式" prop="supercargoDossierTwoPhone">
                <el-input
                  v-model="ruleForm.supercargoDossierTwoPhone"
                  placeholder="请输入押运工联系方式"
                  clearable
                  :maxlength="11"
                  show-word-limit
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <baseTitle title="收运点位基础信息："></baseTitle>
          <el-form-item prop="tableData" label-width="0">
            <el-table
              :data="ruleForm.tableData"
              :header-cell-style="{ background: '#F5F7F9' }"
              style="width: 100%"
              border
              row-key="id"
              row-class-name="table-row"
            >
              <el-table-column type="index" label="收运顺序" align="center" width="80"></el-table-column>
              <el-table-column prop="name" label="点位名称" align="center" min-width="200">
                <template #default="{ row }">
                  <el-tooltip effect="dark" :content="row.name" placement="top" :open-delay="100">
                    <div class="line-clamp line-clamp-2">{{ row.name }}</div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="code" label="点位编号" align="center"></el-table-column>
              <el-table-column prop="address" label="点位地址" align="center" min-width="240">
                <template #default="{ row }">
                  <el-tooltip effect="dark" :content="row.address" placement="top" :open-delay="100">
                    <div class="line-clamp line-clamp-2">{{ row.address }}</div>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="contact" label="点位联系人" align="center"></el-table-column>
              <el-table-column prop="contactPhone" label="点位联系方式" align="center"></el-table-column>
              <el-table-column prop="type" label="点位类型" align="center">
                <template #default="{ row }">{{ POINT_TYPE[row.type] }}</template>
              </el-table-column>
              <el-table-column prop="period" label="收运周期" align="center">
                <template #default="{ row }">{{ COLLECTION_CYCLE[row.period] }}</template>
              </el-table-column>
              <el-table-column prop="frequency" label="收运频次" align="center"></el-table-column>
              <el-table-column prop="isUndock" label="经营状态" align="center">
                <template #default="{ row }">{{ REMOVE_STATUS[row.isUndock] }}</template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </el-form>
      </div>
      <div class="record-footer">
        <el-button @click="closeRecord">返回</el-button>
      </div>
    </div>
    <template v-if="recordId">
      <div class="vertical-line"></div>
      <div class="record-right">
        <ul class="history-list">
          <li class="history-item" v-for="item in historyList" :key="item.id">
            <div class="item-header">
              <div class="header-left"
                >{{ item.createFullname }}&nbsp;&nbsp;版本号v{{ item.versionNumber.toFixed(1) }}</div
              >
              <div class="header-right">{{ item.createTime }}</div>
            </div>
            <div class="item-content" v-html="item.content"></div>
            <div class="item-remark">备注：{{ item.remark }}</div>
            <el-link type="primary" @click="viewHistoryList(item.pickupPathHistoryId, item.columns)"
              >历史路线信息</el-link
            >
          </li>
        </ul>
      </div>
    </template>
    <historyRecord
      :value.sync="showHistoryRecord"
      :historyId="historyId"
      :historyColumns="historyColumns"
      :districtOptions="districtOptions"
      :cascaderProps="cascaderProps"
      :carOptions="carOptions"
      :driverOptions="driverOptions"
      :shipWorkerOptions="shipWorkerOptions"
    ></historyRecord>
  </div>
</template>

<script>
  import { getInfoApiFun, createApiFun, getListApiFun } from "@/api/base";
  import baseTitle from "@/components/baseTitle";
  import { ROUTE_PROPERTY, ROUTE_STATUS, POINT_TYPE, REMOVE_STATUS, WAYBILL_TYPE, COLLECTION_CYCLE } from "@/enums";
  import historyRecord from "@/views/archives/receiveRoute/components/historyRecord.vue";
  export default {
    props: {
      recordId: {
        type: String,
        default: "",
      },
      districtOptions: {
        type: Array,
        default: () => [],
      },
      cascaderProps: {
        type: Object,
        default: () => {},
      },
    },
    components: {
      baseTitle,
      historyRecord,
    },
    data() {
      return {
        ruleForm: {
          name: "", //路线名称
          code: "", //路线编号
          status: "", //路线状态
          districtId: "", //所属区域id
          type: "", //路线属性
          strategy: 0, //路线策略（默认为0-速度优先）
          defaultVehiclePlateNumber: "", //默认收运车辆
          defaultDriverDossierId: "", //默认驾驶司机
          defaultDriverDossierPhone: "", //司机联系方式
          supercargoDossierOneId: "", //默认押运工1
          supercargoDossierOnePhone: "", //押运工联系方式
          supercargoDossierTwoId: "", //默认押运工2
          supercargoDossierTwoPhone: "", //押运工联系方式
          tableData: [], //点位列表
          versionNumber: 1,
        },
        rules: {
          name: [{ required: true, message: "请输入路线名称", trigger: "blur" }],
          code: [{ required: true, message: "请输入路线编号", trigger: "blur" }],
          status: [{ required: true, message: "请选择路线状态", trigger: "change" }],
          districtId: [{ required: true, message: "请选择所属区域", trigger: "change" }],
          type: [{ required: true, message: "请选择路线属性", trigger: "change" }],
          defaultVehiclePlateNumber: [{ required: true, message: "请选择默认收运车辆", trigger: "change" }],
          defaultDriverDossierId: [{ required: true, message: "请选择默认驾驶司机", trigger: "change" }],
          defaultDriverDossierPhone: [{ required: true, message: "请输入司机联系方式", trigger: "blur" }],
          supercargoDossierOneId: [{ required: true, message: "请选择默认押运工", trigger: "change" }],
          supercargoDossierOnePhone: [{ required: true, message: "请输入押运工联系方式", trigger: "blur" }],
          tableData: [{ type: "array", required: true, message: "请添加收运点位信息", trigger: "change" }],
        },
        ROUTE_PROPERTY,
        ROUTE_STATUS,
        POINT_TYPE,
        REMOVE_STATUS,
        WAYBILL_TYPE,
        COLLECTION_CYCLE,
        carOptions: [], //车辆列表
        driverOptions: [], //司机列表
        shipWorkerOptions: [], //押运工列表
        apis: {
          info: "/api/pickup/pickupPath/backup/data/get/",
          carList: "/api/vehicle/dossier/list",
          userList: "/api/baseuser/list",
          historyList: "/api/pickup/pickupPath/backup/data/pickupPathHistoryRecordList",
        },
        loading: false,
        showPointList: false, //点位列表弹框
        showHistoryRecord: false,
        historyList: [],
        historyId: "",
        historyColumns: "",
      };
    },
    mounted() {
      this.getOptions();
      if (this.recordId) {
        this.getRecord();
        this.getHistoryList();
      }
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let promiseList = [
          createApiFun({ statusList: [0, 1] }, this.apis.carList),
          getListApiFun({ userIdentity: 3 }, this.apis.userList),
          getListApiFun({ userIdentity: 4 }, this.apis.userList),
        ];
        let res = await Promise.all(promiseList);
        this.carOptions = res[0].data;
        this.driverOptions = res[1].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
        this.shipWorkerOptions = res[2].data.map((item) => {
          return {
            ...item,
            phone: item.phone ? this.$sm2Decrypt(item.phone) : "",
            idCard: item.idCard ? this.$sm2Decrypt(item.idCard) : "",
          };
        });
      },
      // 获取收运路线档案详情
      async getRecord() {
        let res = await getInfoApiFun(this.recordId, this.apis.info);
        if (res.success) {
          this.ruleForm = res.data;
          this.ruleForm.tableData = res.data.pickupPointList.map((item) => {
            return {
              ...item,
              contactPhone: item.contactPhone ? this.$sm2Decrypt(item.contactPhone) : "",
            };
          });
          this.ruleForm.defaultDriverDossierPhone = this.$sm2Decrypt(this.ruleForm.defaultDriverDossierPhone);
          this.ruleForm.supercargoDossierOnePhone = this.ruleForm.supercargoDossierOnePhone
            ? this.$sm2Decrypt(this.ruleForm.supercargoDossierOnePhone)
            : "";
          this.ruleForm.supercargoDossierTwoPhone = this.ruleForm.supercargoDossierTwoPhone
            ? this.$sm2Decrypt(this.ruleForm.supercargoDossierTwoPhone)
            : "";
        }
      },
      // 获取历史记录列表
      async getHistoryList() {
        let res = await createApiFun({ pickupPathId: this.recordId }, this.apis.historyList);
        if (res.success) {
          this.historyList = res.data;
        }
      },
      // 关闭弹窗
      closeRecord() {
        this.$emit("closeDetail");
      },
      // 查看历史记录
      viewHistoryList(id, columns) {
        this.historyId = id;
        this.historyColumns = columns;
        this.showHistoryRecord = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    flex: 1;
    overflow: auto;
    display: flex;
    margin-top: 20px;
  }
  .record-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: auto;
  }
  .vertical-line {
    width: 0;
    height: 100%;
    border-right: 1px dashed #606266;
    margin: 0 20px;
  }
  .record-right {
    width: 400px;
    height: 100%;
    display: flex;
    flex-direction: column;
    .history-list {
      flex: 1;
      overflow: auto;
    }
  }
  .record-content {
    flex: 1;
    overflow: auto;
  }
  .card-header {
    text-align: center;
    font-size: 20px;
    margin-bottom: 20px;
    position: relative;
    .version-number {
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%);
      font-size: 14px;
      color: #909399;
    }
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .mt-10 {
    margin-top: 10px;
  }
  .history-item {
    font-size: 14px;
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
    .item-header {
      color: #909399;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
    }
  }
</style>
