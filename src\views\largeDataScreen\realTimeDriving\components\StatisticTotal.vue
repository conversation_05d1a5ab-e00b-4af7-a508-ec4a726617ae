<template>
  <div class="statistics-container">
    <div class="left">
      <Title title="路线信息"></Title>
      <div class="route">
        <div class="box">
          <div class="label">路线名称：</div>
          <div class="value">{{ formData?.name }}</div>
        </div>
        <div class="box">
          <div class="label">路线所属区域：</div>
          <div class="value">{{ formData?.districtName }}</div>
        </div>
        <div class="box">
          <div class="label">路线属性：</div>
          <div class="value">{{ ROUTE_PROPERTY[formData?.type] }}</div>
        </div>
        <div class="box">
          <div class="label">收运方式：</div>
          <div class="value">{{ WAYBILL_TYPE[formData?.waybillType] }}</div>
        </div>
      </div>
      <Title title="收运单信息"></Title>
      <div class="info">
        <div class="box">
          <div class="label">收运车辆：</div>
          <div class="value">{{ formData?.defaultVehiclePlateNumber }}</div>
        </div>
        <div class="box">
          <div class="label">收运司机：</div>
          <div class="value">
            <div>{{ formData?.defaultDriverDossierName }}</div>
            <div class="phone">{{ formData?.defaultDriverDossierPhone }}</div>
          </div>
        </div>
        <div class="box">
          <div class="label">收运押运工：</div>
          <div class="value">
            <div>{{ formData?.supercargoDossierOneName }}</div>
            <div class="phone">{{ formData?.supercargoDossierOnePhone }}</div>
          </div>
        </div>
        <div class="box">
          <div class="label">收运押运工2：</div>
          <div class="value">
            <div>{{ formData?.supercargoDossierTwoName }}</div>
            <div class="phone">{{ formData?.supercargoDossierTwoPhone }}</div>
          </div>
        </div>
      </div>
      <Title title="收运单收运信息"></Title>
      <div class="waybill">
        <div class="time-box">
          <el-progress
            :width="100"
            type="circle"
            :percentage="roundReserveDecimals(formData?.collectPercentage * 100) || 0"
            :stroke-width="10"
            define-back-color="#aaa"
            text-color="#fff"
            color="#CAF982"
          ></el-progress>
          <div class="time-right">
            <div class="right-title">收运单点位总数</div>
            <div class="right-count">{{ formData?.pointNumber }}</div>
            <div class="right-title mt-16">已收运点位总数</div>
            <div class="right-count">{{ formData?.collectNum }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="flex-1"></div>
    <div class="right">
      <Title title="路线收运量信息"></Title>
      <Count :rubbishTotalList="formData?.rubbishTotalList"></Count>
    </div>
  </div>
</template>

<script>
  import Title from "@/views/largeDataScreen/realTimeMonitor/components/Title";
  import { ROUTE_PROPERTY, WAYBILL_TYPE } from "@/enums";
  import Count from "./Count.vue";
  import { roundReserveDecimals } from "@/utils";
  export default {
    props: ["formData"],
    components: {
      Title,
      Count,
    },
    data() {
      return {
        ROUTE_PROPERTY,
        WAYBILL_TYPE,
        roundReserveDecimals,
      };
    },
  };
</script>

<style lang="scss" scoped>
  .statistics-container {
    display: flex;
    justify-content: space-between;
  }
  .left,
  .right {
    flex: 4;
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.4);
  }
  .flex-1 {
    flex: 6;
  }
  .route {
    margin: 30px 0;
  }
  .info {
    margin: 30px 0;
  }
  .box {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    margin-bottom: 40px;
  }
  .label {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    line-height: 18px;
  }
  .value {
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    line-height: 20px;
    .phone {
      font-size: 14px;
      color: #fff;
    }
  }
  .time-box {
    display: flex;
    align-items: center;
    color: #fff;
    margin-top: 30px;
  }
  .time-right {
    margin-left: 60px;
  }
  .right-title {
    font-size: 16px;
  }
  .right-count {
    font-size: 40px;
    font-weight: bold;
    margin-top: 4px;
  }
  .mt-16 {
    margin-top: 16px;
  }
</style>
