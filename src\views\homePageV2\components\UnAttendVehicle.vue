<template>
  <div class="backlog">
    <main-title title="今日空闲车辆"></main-title>
    <vue-seamless-scroll
      v-if="scrollList && scrollList.length > 0"
      class="seaml-scroll"
      :data="scrollList"
      :class-option="seamlessScrollOption"
    >
      <ul class="data-list">
        <li class="data-item" v-for="(car, index) in scrollList" :key="index">
          <div v-if="listData[index]" class="plateNumber omit1">{{ listData[index].plateNumber }}</div>
          <div v-if="listData[index + 1]" class="plateNumber omit1">{{ listData[index + 1].plateNumber }}</div>
          <div v-if="listData[index + 2]" class="plateNumber omit1">{{ listData[index + 2].plateNumber }}</div>
          <div v-if="listData[index + 3]" class="plateNumber omit1">{{ listData[index + 3].plateNumber }}</div>
        </li>
      </ul>
    </vue-seamless-scroll>
    <div v-else class="EmptyWrapper">
      <Empty></Empty>
    </div>
  </div>
</template>

<script>
  import Empty from "./Empty.vue";

  import MainTitle from "./MainTitle.vue";
  export default {
    props: ["listData"],
    components: {
      MainTitle,
      Empty,
    },
    data() {
      return {
        seamlessScrollOption: {
          step: 2, // 数值越大速度滚动越快
          limitMoveNum: 6, // 开始无缝滚动的数据量 this.dataList.length
          hoverStop: true, // 是否开启鼠标悬停stop
          direction: 1, // 0向下 1向上 2向左 3向右
          openWatch: true, // 开启数据实时监控刷新dom
          singleHeight: 50, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
          singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
          waitTime: 2000, // 单步运动停止的时间(默认值1000ms)
        },
        // numList: [
        //   {
        //     title: "车辆总数",
        //     icon: require("../images/ic_carNum.png"),
        //     num: 0,
        //     subtext: "(辆)",
        //   },
        //   {
        //     title: "今日收运车辆",
        //     icon: require("../images/ic_carNum.png"),
        //     num: 0,
        //     subtext: "(辆)",
        //   },
        //   {
        //     title: "异常上报数量",
        //     icon: require("../images/ic_warn.png"),
        //     num: 0,
        //   },
        //   {
        //     title: "异常驾驶状态",
        //     icon: require("../images/ic_warn.png"),
        //     num: 0,
        //   },
        // ],
      };
    },
    computed: {
      scrollList() {
        const result = [];
        for (let i = 0; i < this.listData.length; i += 4) {
          if (i + 3 < this.listData.length + 4) {
            // 确保不会越界
            result.push(this.listData[i]); // 每四项取第一个
          }
        }
        return result;
      },
    },
    async created() {},
    methods: {},
  };
</script>

<style lang="scss" scoped>
  .backlog {
    width: 480px;
    position: absolute;

    z-index: 1;

    top: 732px;
    left: 1400px;
  }
  .mr-top-36 {
    margin-top: 36px;
  }
  .mr-top-56 {
    margin-top: 56px;
  }
  .num-flex-wrapper {
    width: 480px;

    display: flex;
    flex-wrap: wrap;
    /* 其他样式 */
  }
  .seaml-scroll {
    height: 250px;
    overflow: hidden;
    width: 100%;
    font-size: 16px;
    line-height: 22px;
    font-weight: 400;
    color: #ffffff;
    margin-top: 20px;
    .data-list {
      .data-item {
        display: flex;
        .plateNumber {
          width: 120px;
          padding: 0px 16px;
          margin-bottom: 10px;
          line-height: 40px;
          color: #ffffff;
          font-size: 16px;
          background: linear-gradient(270deg, rgba(93, 255, 203, 0) 0%, rgba(93, 255, 203, 0.1) 100%);
        }
      }
    }
  }
  .omit1 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
  }
</style>
