<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage :topInfo="topInfo" @closeRecord="closeRecord">
      <contract v-if="showType === 1" ref="contract" :recordId="merchantFileId" @closeRecord="closeRecord"></contract>
      <record v-show="showType === 2" ref="record" @closeRecord="closeRecord"></record>
      <point v-show="showType === 3" ref="point" @closeRecord="closeRecord"></point>
      <div class="main-index" v-show="showType === 0">
        <header class="header">
          <div class="header-left">
            <el-input class="w250" v-model="keyword" placeholder="请输入客商编号/名称" clearable></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <el-button type="primary" size="small" icon="el-icon-folder-opened">同步</el-button>
            <el-dropdown
              size="small"
              trigger="click"
              @command="handleDataImport"
              class="dropdown-button"
              placement="bottom-start"
            >
              <el-button size="small" type="primary">
                <span class="sctmp-iconfont icon-ic_daoru"></span>
                <span class="ml-5">导入</span>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="1">
                  <div class="has-padding-sm has-border-bottom">导入基础信息</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
            <el-button size="small" type="primary" @click="handleExport">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button>
          </div>
        </header>
        <FilterContent label-width="140px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
          ></InterviewChannel>
          <el-col :span="8">
            <el-form-item label="客商状态">
              <el-select v-model="filterForm.status" placeholder="请选择客商状态" clearable filterable>
                <el-option
                  v-for="(item, index) in CUSTOMER_STATUS"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="信用状态">
              <el-select v-model="filterForm.creditStatus" placeholder="请选择信用状态" clearable filterable>
                <el-option v-for="(item, index) in CREDIT_STATUS" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="客户类型">
              <el-select v-model="filterForm.type" placeholder="请选择客户类型" clearable filterable>
                <el-option v-for="(item, index) in CUSTOMER_TYPE" :key="index" :label="item" :value="index"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在地址">
              <el-input v-model="filterForm.address" placeholder="请输入详细地址" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="filterForm.merchantFileCreateTime"
                type="date"
                placeholder="请选择创建时间"
                clearable
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收款员">
              <el-input v-model="filterForm.receivingTeller" placeholder="请输入收款员姓名" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="制单人">
              <el-input v-model="filterForm.single" placeholder="请输入制单人姓名" clearable></el-input>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table :data="tableData" :header-cell-style="{ background: '#F5F7F9' }" height="100%" border>
            <el-table-column type="index" width="55" align="center"></el-table-column>
            <el-table-column prop="code" label="客商编号" align="center"> </el-table-column>
            <el-table-column prop="name" label="客商名称" align="center"> </el-table-column>
            <el-table-column prop="type" label="客户类型" align="center">
              <template #default="{ row }">{{ CUSTOMER_TYPE[row.type] }}</template>
            </el-table-column>
            <el-table-column prop="status" label="客商状态" align="center">
              <template #default="{ row }">{{ CUSTOMER_STATUS[row.status] }}</template>
            </el-table-column>
            <el-table-column prop="address" label="详细地址" align="center" min-width="300"></el-table-column>
            <el-table-column prop="creditStatus" label="信用状态" align="center">
              <template #default="{ row }">{{ CREDIT_STATUS[row.creditStatus] }}</template>
            </el-table-column>
            <el-table-column prop="receivingTeller" label="收款员" align="center"></el-table-column>
            <el-table-column prop="single" label="制单人" align="center"></el-table-column>
            <el-table-column prop="merchantFileCreateTime" label="创建时间" align="center"></el-table-column>
            <el-table-column prop="oldCode" label="旧编号" align="center"></el-table-column>
            <el-table-column prop="oldName" label="曾用名" align="center"></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column min-width="140" label="操作" align="center">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row, 1)">合同</el-link>
                <el-link class="mr-10" type="primary" @click="editRecord(row, 2)">详情</el-link>
                <el-link class="mr-10" type="primary" @click="editRecord(row, 3)">收运点位</el-link>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
    <importDialog
      :value.sync="importDialogShow"
      title="客商档案"
      :importApi="apis.import"
      :templateApi="apis.template"
      :importDialogType="importDialogType"
      :isTemplateApi="true"
      @importSuccess="searchFilter"
    ></importDialog>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import Pagination from "@/components/pagination-v";
  import { getListPageApiFun, BASE_API_URL } from "@/api/base";
  import record from "./components/record.vue";
  import point from "./components/point.vue";
  import contract from "./components/contract.vue";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  import importDialog from "@/components/importDialog";
  import { CUSTOMER_TYPE, CREDIT_STATUS, CUSTOMER_STATUS } from "@/enums";
  export default {
    components: {
      defaultPage,
      Pagination,
      record,
      point,
      contract,
      importDialog,
    },
    data() {
      return {
        topInfo: {
          buttonName: "新增",
          subTitle: "列表",
          buttonShow: true,
          buttonPermission: "-1",
        },
        filterForm: {},
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/merchant/merchantFile/listPage",
          export: "/api/merchant/merchantFile/exportExcelMerchantFile",
          import: "/api/merchant/merchantFile/importMerchantFile",
          template: "/api/merchant/merchantFile/excleModel",
        },
        showType: 0,
        loading: false,
        importDialogShow: false,
        importDialogType: "",
        merchantFileId: "",
        CUSTOMER_TYPE,
        CREDIT_STATUS,
        CUSTOMER_STATUS,
        showFilter: false,
        keyword: "",
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    mounted() {
      this.initData();
    },
    methods: {
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          keyword: this.keyword,
          ...this.filterForm,
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas;
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 编辑
      editRecord(row, type) {
        this.showType = type;
        this.topInfo.buttonShow = false;
        switch (type) {
          case 1:
            this.topInfo.subTitle = "合同信息";
            this.merchantFileId = row.id;
            break;
          case 2:
            this.topInfo.subTitle = "详情";
            this.$refs.record.getRecord(row.id);
            break;
          case 3:
            this.topInfo.subTitle = "收运点位";
            this.$refs.point.getPointList(row.id);
            break;
        }
      },
      closeRecord() {
        this.showType = 0;
        this.topInfo.buttonShow = true;
        this.topInfo.subTitle = "列表";
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 导出
      handleExport() {
        this.$confirm(`确认是否以当前筛选条件导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, {
                keyword: this.keyword,
                ...this.filterForm,
              });
              if (res.success) {
                createDownloadEvent(`客商档案${Date.now()}.xlsx`, [res.data]);
                window.ELEMENT.Message.success("导出成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
      // 导入
      handleDataImport(command) {
        this.importDialogType = command;
        this.importDialogShow = true;
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .w250 {
    width: 250px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
</style>
