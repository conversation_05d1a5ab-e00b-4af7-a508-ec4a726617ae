<template>
  <div class="micro-app-sctmp_base">
    <el-dialog
      title=""
      :visible.sync="dialogVisible"
      width="40%"
      top="0"
      destroy-on-close
      @open="initData"
      :show-close="false"
    >
      <template #title>
        <header class="header">今日收运量详情</header>
      </template>
      <div class="main-record" v-loading="loading">
        <div class="record-content">
          <div id="run"></div>
        </div>
        <div class="record-footer">
          <el-button @click="dialogVisible = false">返回</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getInfoApiFunByParams } from "@/api/base";
  import * as echarts from "echarts";
  export default {
    props: {
      value: {
        type: Boolean,
        default: false,
      },
      channelId: {
        type: String,
        default: "",
      },
    },
    computed: {
      dialogVisible: {
        get() {
          return this.value;
        },
        set(e) {
          this.$emit("update:value", e);
        },
      },
    },
    data() {
      return {
        loading: false,
        apis: {
          summary: "/api/monitor/rubbish/summary",
        },
        summaryData: null,
        seriesData: [0, 0, 0, 0, 0, 0],
        seriesPrecentage: ["", "", "", "", "", ""],
        total: 0,
      };
    },
    mounted() {
      this.$nextTick(() => {
        window.addEventListener("resize", this.handleResize);
      });
    },
    methods: {
      // 监听窗口变化
      handleResize() {
        var myChart = echarts.init(document.getElementById("run"));
        if (myChart) {
          myChart.dispose();
        }
        this.init();
      },
      initData() {
        this.getRecord();
      },
      async getRecord() {
        try {
          let res = await getInfoApiFunByParams({ channelId: this.channelId }, this.apis.summary);
          this.summaryData = res.data;
          this.seriesData = [
            this.summaryData.infectiousWaste,
            this.summaryData.damaginWaste,
            this.summaryData.chemicalWaste,
            this.summaryData.pharmaceuticalWaste,
            this.summaryData.pathologicalWaste,
            this.summaryData.sludge,
          ];
          this.seriesPrecentage = [
            this.summaryData.infectiousWastePrecentage,
            this.summaryData.damaginWastePrecentage,
            this.summaryData.chemicalWastePrecentage,
            this.summaryData.pharmaceuticalWastePrecentage,
            this.summaryData.pathologicalWastePrecentage,
            this.summaryData.sludgePrecentage,
          ];
          this.total = this.summaryData.total;
          this.init();
        } catch (error) {
          console.log(error);
        }
      },
      init() {
        const chartData = [
          {
            name: "感染性废物         ",
            value: this.seriesData[0],
            precentage: this.seriesPrecentage[0],
            color: "#1A9CFF",
          },
          {
            name: "感染性废物(污泥)",
            value: this.seriesData[5],
            precentage: this.seriesPrecentage[5],
            color: "#9FD4FD",
          },
          {
            name: "损伤性废物          ",
            value: this.seriesData[1],
            precentage: this.seriesPrecentage[1],
            color: "#FABB28",
          },
          {
            name: "化学性废物          ",
            value: this.seriesData[2],
            precentage: this.seriesPrecentage[2],
            color: "#33D1C9",
          },
          {
            name: "药物性废物          ",
            value: this.seriesData[3],
            precentage: this.seriesPrecentage[3],
            color: "#5CC78A",
          },
          {
            name: "病理性废物          ",
            value: this.seriesData[4],
            precentage: this.seriesPrecentage[4],
            color: "#7372E9",
          },
        ];
        const sum = chartData.reduce((a, b) => a + b.value, 0);
        // 自定义lengend的图标
        const legendData = [
          {
            name: "感染性废物         ",
            icon: "rect",
          },
          {
            name: "感染性废物(污泥)",
            icon: "rect",
          },
          {
            name: "损伤性废物          ",
            icon: "rect",
          },
          {
            name: "化学性废物          ",
            icon: "rect",
          },
          {
            name: "药物性废物          ",
            icon: "rect",
          },
          {
            name: "病理性废物          ",
            icon: "rect",
          },
        ];
        const pieSeries = [];
        chartData.forEach((v, i) => {
          pieSeries.push({
            name: "",
            type: "pie",
            emphasis: {
              // 取消放大效果
              scale: 0,
              // 可以设置其他悬停时的样式，比如标签显示
              label: {
                show: false,
                fontSize: "14",
                fontWeight: "bold",
              },
            },
            clockwise: false,
            radius: [75 - i * 10 + "%", 80 - i * 10 + "%"],
            center: ["30%", "50%"],
            startAngle: 90,
            label: {
              show: false,
            },

            data: [
              {
                value: v.value,
                name: v.name,
                itemStyle: {
                  color: v.color,
                },
              },
              {
                value: sum === 0 ? 0.001 : sum - v.value,
                name: "",
                itemStyle: {
                  color: "rgba(0,0,0,0)",
                },
              },
            ],
          });
          pieSeries.push({
            name: "",
            type: "pie",
            silent: false,
            emphasis: {
              // 取消放大效果
              scale: 0,
              // 可以设置其他悬停时的样式，比如标签显示
              label: {
                show: false,
                fontSize: "14",
                fontWeight: "bold",
              },
            },
            z: 1,
            clockwise: false, // 顺时加载
            radius: [75 - i * 10 + "%", 80 - i * 10 + "%"],
            center: ["30%", "50%"],
            label: {
              show: false,
            },
            itemStyle: {
              borderCap: "round",
              borderJoin: "round",
            },
            data: [
              {
                value: sum,
                itemStyle: {
                  color: "#F7F8FA",
                },
              },
            ],
          });
        });
        var myChart = echarts.init(document.getElementById("run"));
        myChart.setOption(
          {
            title: {
              text: "今日收运量统计",
              textStyle: {
                color: "#414D5A",
                fontStyle: "normal",
                fontWeight: "bold",
                fontFamily: "Microsoft YaHei",
                fontSize: "14",
                lineHeight: "20",
              },
              left: "center",
            },
            legend: {
              orient: "vertical",
              right: 12,
              top: "center",
              itemWidth: 8,
              itemHeight: 8,
              data: legendData,
              formatter: function (val) {
                const item = chartData.find((chartDataItem) => chartDataItem.name === val);
                const valStr = val.toString();
                const valueStr = item.value.toString();
                const percentageStr = item.precentage.toString();
                return (
                  "{title|" + valStr + "}" + "{value|" + valueStr + "}" + "（kg）" + "{value|" + percentageStr + "}"
                );
              },
              textStyle: {
                rich: {
                  title: {
                    fontSize: 12,
                    color: "#414D5A",
                  },
                  value: {
                    fontSize: 12,
                    color: "#414D5A",
                    padding: [0, 0, 0, 10],
                  },
                },
              },
            },
            series: [
              ...pieSeries,
              {
                center: ["30%", "50%"],
                type: "pie",
                selectedMode: "single",
                radius: ["0%", "20%"],
                silent: true,
                clockwise: false,
                label: {
                  position: "center",
                  formatter: `{value|${this.total}}{beside|（kg）}\n {name|总重量} `,
                  rich: {
                    name: {
                      fontSize: 10,
                    },
                    value: {
                      fontSize: 12,
                      fontWeight: "bold",
                      padding: [10, 0, 0, 0],
                      fontFamily: "PangMenZhengDao",
                    },
                    beside: {
                      fontSize: 8,
                      lineHeight: 8,
                      padding: [10, 0, 0, 0],
                    },
                  },
                },
                itemStyle: {
                  normal: {
                    color: "#fff",
                  },
                },
                labelLine: {
                  show: false,
                },
                data: [{ value: 0 }],
                right: "0%",
              },
            ],
          },
          true,
        );
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-record {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .record-content {
    width: 100%;
    flex: 1;
    overflow: auto;
    padding-bottom: 20px;
  }
  .record-footer {
    padding-top: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #efefef;
  }
  .header {
    text-align: center;
    font-size: 24px;
    font-weight: bold;
  }
  #run {
    width: 100%;
    height: 100%;
  }
  ::v-deep .el-dialog {
    height: calc(100vh - 400px);
    position: relative;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
  }
  ::v-deep .el-dialog__body {
    padding: 20px;
    flex: 1;
    overflow: hidden;
  }
</style>
