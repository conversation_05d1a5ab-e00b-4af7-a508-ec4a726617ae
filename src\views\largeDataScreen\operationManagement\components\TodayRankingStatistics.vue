<template>
  <div class="ranking">
    <main-title title="各区点位数量统计"></main-title>
    <div class="chart-list">
      <div class="chart-data">
        <Echart :chartData="chartData" chartType="pie" key="char999991"></Echart>
      </div>
    </div>
    <div class="today-kg-info">
      <ul class="data-list">
        <li class="data-item" :class="'data-item' + index" v-for="(item, index) in areaPointTotalData" :key="index">
          <div class="title name">
            <!-- <div class="tips" :style="{ backgroundColor: item.color }"></div> -->
            {{ item.areaName }}
          </div>
          <div class="title">{{ item.percentage }}%</div>
          <div class="nums">{{ item.quantity }}</div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
  import MainTitle from "./MainTitle.vue";
  import Echart from "@/components/echart";
  export default {
    components: {
      MainTitle,
      Echart,
    },
    props: {
      areaPointTotalData: {
        type: Array,
        default: () => [],
      },
    },

    data() {
      return {
        iconData: {
          numbg: require("@/assets/images/numbg.png"),
        },
        echartData: [
          {
            name: "从化区",
            value: "200",
            color: "#FF844A",
            percentage: "10%",
          },
          {
            name: "花都区",
            value: "200",
            color: "#B98EFB",
            percentage: "10%",
          },
          {
            name: "白云区",
            value: "200",
            color: "#1A9CFF",
            percentage: "10%",
          },
          {
            name: "越秀区",
            value: "200",
            color: "#0064FF",
            percentage: "10%",
          },
          {
            name: "海珠区",
            value: "200",
            color: "#FABB28",
            percentage: "10%",
          },
          {
            name: "荔湾区",
            value: "200",
            color: "#5EFFBF",
            percentage: "10%",
          },
          {
            name: "南沙区",
            value: "200",
            color: "#623FFF",
            percentage: "10%",
          },
          {
            name: "番禺区",
            value: "200",
            color: "#00D7FF",
            percentage: "10%",
          },
        ],
        color: ["#FF844A", "#B98EFB", "#1A9CFF", "#0064FF", "#FABB28", "#5EFFBF", "#623FFF", "#00D7FF"],
        chartData: {
          title: {
            text: "0\n\n总数量",
            top: "30%",
            left: "center",
            textStyle: {
              color: "#fff",
              fontSize: 16,
              align: "center",
            },
          },
          tooltip: {
            show: true,
            trigger: "item",
          },
          series: [
            {
              type: "pie",
              center: ["50%", "40%"],
              radius: ["50%", "70%"],
              labelLine: {
                show: true,
                emphasis: {
                  show: true,
                },
              },
              data: [],
            },
          ],
          color: this.color,
        },
      };
    },
    mounted() {
      this.initChart();
    },

    watch: {
      areaPointTotalData: {
        handler(n) {
          if (n && n.length) {
            this.chartData.series[0].data = n.map((i) => ({
              value: i.quantity,
              name: i.areaName,
            }));
            let total = n.reduce((pre, next) => {
              return pre + next.quantity;
            }, 0);

            this.chartData.title.text = `${total}\n\n总数量`;
          }
        },
        deep: true,
        immediate: true,
      },
    },

    methods: {
      initChart() {},
    },
  };
</script>

<style lang="scss" scoped>
  .ranking {
    width: 480px;
    position: absolute;
    top: 130px;
    right: 40px;
    z-index: 1;
  }
  .chart-list {
    padding-top: 20px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
  }
  .chart-data {
    position: relative;
    width: 220px;
    height: 220px;
  }
  .today-kg-info::-webkit-scrollbar {
    display: none;
  }
  .today-kg-info {
    height: 400px;
    overflow: auto;
    width: 100%;
    font-size: 16px;
    line-height: 22px;
    font-weight: 400;
    color: #ffffff;
    margin-top: -24px;
    scrollbar-width: none;
    .data-list {
      .data-item {
        display: flex;
        padding: 7px 16px;
        margin-bottom: 10px;
        background: linear-gradient(270deg, rgba(93, 255, 203, 0) 0%, rgba(93, 255, 203, 0.1) 100%);
        .name {
          display: flex;
          align-items: center;
        }
        .title {
          flex: 1;
        }
        .tips {
          width: 8px;
          height: 8px;
          margin-right: 4px;
        }
      }
    }
  }
</style>
