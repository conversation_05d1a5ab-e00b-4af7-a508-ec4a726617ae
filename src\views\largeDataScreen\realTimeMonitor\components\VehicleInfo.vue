<template>
  <div class="container">
    <div class="box">
      <div class="label">收运车辆：</div>
      <div class="value">{{ carData?.plateNumber }}</div>
      <div class="button" @click="monitor">视频监控</div>
    </div>
    <div class="box">
      <div class="label">收运司机：</div>
      <div class="value">
        <div>{{ carData?.defaultDriverDossierName }}</div>
        <div class="phone">{{ carData?.defaultDriverDossierPhone }}</div>
      </div>
    </div>
    <div class="box">
      <div class="label">收运押运工：</div>
      <div class="value">
        <div>{{ carData?.supercargoDossierOneName }}</div>
        <div class="phone">{{ carData?.supercargoDossierOnePhone }}</div>
      </div>
    </div>
    <div class="box">
      <div class="label">收运押运工2：</div>
      <div class="value">
        <div>{{ carData?.supercargoDossierTwoName }}</div>
        <div class="phone">{{ carData?.supercargoDossierTwoPhone }}</div>
      </div>
    </div>
  </div>
</template>

<script>
  import emitter from "@/utils/mitt";
  export default {
    props: ["carData"],
    computed: {
      plateNumber() {
        return this.$store.state.plateNumber;
      },
    },
    methods: {
      monitor() {
        emitter.emit("open-dialog", "showMonitorDialog");
      },
    },
  };
</script>

<style lang="scss" scoped>
  .container {
    margin: 30px 0;
    padding-left: 60px;
    pointer-events: auto;
  }
  .box {
    display: grid;
    grid-gap: 40px;
    grid-template-columns: repeat(3, 1fr);
    margin-bottom: 40px;
  }
  .label {
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    line-height: 18px;
  }
  .value {
    font-size: 20px;
    font-weight: bold;
    color: #fff;
    line-height: 20px;
    .phone {
      font-size: 14px;
      color: #fff;
    }
  }
  .button {
    width: 80px;
    font-size: 16px;
    color: #fff;
    font-weight: bold;
    background-color: #d9001b;
    padding: 6px;
    border-radius: 10px;
    text-align: center;
    margin-top: -4px;
    cursor: pointer;
  }
</style>
