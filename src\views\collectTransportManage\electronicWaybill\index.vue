<template>
  <div class="micro-app-sctmp_base full-height" v-loading="loading">
    <defaultPage>
      <record v-if="recordType === 1" :recordId="recordId" :recordForm="recordForm" @closeRecord="closeRecord"></record>
      <temporary
        v-else-if="recordType === 2"
        :recordForm="recordForm"
        @closeRecord="closeRecord"
        @refreshList="initData"
      ></temporary>
      <div class="main-index" v-else>
        <header class="header">
          <div class="header-left">
            <el-input
              class="w400"
              v-model="keyword"
              placeholder="请输入收运单编号/路线编号/路线名称"
              clearable
            ></el-input>
          </div>
          <div class="filter-box">
            <el-badge :value="filterNumber" class="filter-badge">
              <el-button type="primary" @click="showFilter = !showFilter">
                <span class="iconfont icon-shaixuan-o mr-5" style="font-size: 12px"></span>
                筛选
                <span :class="[showFilter ? 'el-icon-caret-top' : 'el-icon-caret-bottom']"></span>
              </el-button>
            </el-badge>
          </div>
          <el-button @click="resetFilter">重置</el-button>
          <el-button type="primary" @click="searchFilter">查询</el-button>
          <div class="header-right">
            <!-- <el-button size="small" type="primary" @click="handleExport">
              <span class="sctmp-iconfont icon-ic_daochu"></span>
              <span class="ml-5">导出</span>
            </el-button> -->
            <el-popover class="ml-10" placement="bottom" width="240" trigger="click" :append-to-body="false">
              <div class="reveal-box">
                <div class="reveal-header">
                  <div>展示全部</div>
                  <el-switch
                    :value="allItemChecked"
                    active-text="是"
                    inactive-text="否"
                    @change="changeAllReveal"
                  ></el-switch>
                </div>
                <ul class="reveal-list">
                  <li class="reveal-item" v-for="(item, index) in itemList" :key="index">
                    <el-checkbox :value="item.value" :disabled="index === 0" @change="changeReveal($event, item)">{{
                      item.label
                    }}</el-checkbox>
                  </li>
                </ul>
              </div>
              <el-button slot="reference">
                <div class="btn-inner">
                  <span class="el-icon-s-grid"></span>
                  <span>显示列</span>
                  <span class="el-icon-caret-bottom"></span>
                </div>
              </el-button>
            </el-popover>
          </div>
        </header>
        <FilterContent label-width="160px" v-show="showFilter">
          <InterviewChannel
            :value.sync="filterForm.channelId"
            :record.sync="channelRecord"
            :needCol="true"
            :colNum="12"
          ></InterviewChannel>
          <el-col :span="12">
            <el-form-item label="驾驶司机">
              <el-input
                class="w400"
                v-model="filterForm.defaultDriverDossierName"
                placeholder="请输入司机姓名"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="车辆车牌号">
              <el-select
                v-model="filterForm.defaultVehiclePlateNumber"
                placeholder="请选择车辆车牌号"
                clearable
                filterable
              >
                <el-option v-for="item in carOptions" :key="item.name" :label="item.name" :value="item.name">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收运单生效日期范围">
              <el-date-picker
                v-model="filterForm.effectiveDate"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </FilterContent>
        <main class="main">
          <el-table
            :data="tableData"
            :header-cell-style="{ background: '#F5F7F9' }"
            height="100%"
            border
            ref="tableRef"
          >
            <el-table-column type="index" align="center"></el-table-column>
            <el-table-column
              prop="waybillCode"
              label="收运单编号"
              align="center"
              min-width="100"
              v-if="itemList[0].value"
            ></el-table-column>
            <el-table-column
              prop="effectiveDate"
              label="收运单生效日期"
              align="center"
              min-width="160"
              v-if="itemList[1].value"
            ></el-table-column>
            <el-table-column prop="isTemp" label="临时收运单" align="center" min-width="100" v-if="itemList[2].value">
              <template #default="{ row }">{{ IS_TEMPORARY[row.isTemp] }}</template>
            </el-table-column>
            <el-table-column prop="name" label="路线名称" align="center" v-if="itemList[3].value">
              <template #default="{ row }">
                <el-link type="primary" @click="toUpdateRoute(row)">{{ row.name }}</el-link>
              </template>
            </el-table-column>
            <el-table-column
              prop="districtName"
              label="所属区域"
              align="center"
              v-if="itemList[4].value"
            ></el-table-column>
            <el-table-column prop="type" label="路线属性" align="center" min-width="120" v-if="itemList[5].value">
              <template #default="{ row }">{{ ROUTE_PROPERTY[row.type] }}</template>
            </el-table-column>
            <el-table-column
              prop="defaultVehiclePlateNumber"
              label="收运车辆"
              align="center"
              min-width="120"
              v-if="itemList[6].value"
            ></el-table-column>
            <el-table-column
              prop="defaultDriverDossierName"
              label="驾驶司机"
              align="center"
              v-if="itemList[7].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierOneName"
              label="押运工"
              align="center"
              v-if="itemList[8].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierTwoName"
              label="押运工2"
              align="center"
              v-if="itemList[9].value"
            ></el-table-column>
            <el-table-column prop="waybillType" label="收运方式" align="center" v-if="itemList[10].value">
              <template #default="{ row }">{{ WAYBILL_TYPE[row.waybillType] }}</template>
            </el-table-column>
            <el-table-column
              prop="pointNumber"
              label="点位数量"
              align="center"
              v-if="itemList[11].value"
            ></el-table-column>
            <el-table-column
              prop="completeCount"
              label="已收运点位数"
              align="center"
              v-if="itemList[12].value"
            ></el-table-column>
            <el-table-column
              prop="code"
              label="路线编号"
              align="center"
              min-width="120"
              v-if="itemList[13].value"
            ></el-table-column>
            <el-table-column
              prop="defaultDriverDossierPhone"
              label="司机联系方式"
              align="center"
              min-width="120"
              v-if="itemList[14].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierOnePhone"
              label="押运工联系方式"
              align="center"
              min-width="120"
              v-if="itemList[15].value"
            ></el-table-column>
            <el-table-column
              prop="supercargoDossierTwoPhone"
              label="押运工2联系方式"
              align="center"
              min-width="140"
              v-if="itemList[16].value"
            ></el-table-column>
            <el-table-column
              prop="versionNumber"
              label="路线版本号"
              align="center"
              min-width="100"
              v-if="itemList[17].value"
            >
              <template #default="{ row }">v{{ row.versionNumber.toFixed(1) }}</template>
            </el-table-column>
            <el-table-column
              prop="updateFullname"
              label="最近修改人"
              align="center"
              min-width="140"
              v-if="itemList[18].value"
            ></el-table-column>
            <el-table-column
              prop="updateTime"
              label="最近修改时间"
              align="center"
              min-width="160"
              v-if="itemList[19].value"
            ></el-table-column>
            <el-table-column
              prop="totalWeighingWeight"
              label="地磅核实重量（kg）"
              align="center"
              min-width="140px"
              v-if="itemList[20].value"
            ></el-table-column>
            <el-table-column
              prop="emptyBucketCount"
              label="空桶数量"
              align="center"
              v-if="itemList[21].value"
            ></el-table-column>
            <el-table-column prop="channelId" label="渠道名称" align="center" v-if="itemList[22].value">
              <template #default="{ row }">{{ channelRecord[row.channelId] }}</template>
            </el-table-column>
            <el-table-column label="操作" align="center" fixed="right" min-width="200">
              <template #default="{ row }">
                <el-link class="mr-10" type="primary" @click="editRecord(row, 1)">详情</el-link>
                <el-link
                  type="primary"
                  @click="editRecord(row, 2)"
                  v-if="row.isOperateTemp === 0 && row.pointNumber !== row.completeCount"
                  >临时调整</el-link
                >
              </template>
            </el-table-column>
          </el-table>
        </main>
        <Pagination :page="page" @pageChange="pageChange"></Pagination>
      </div>
    </defaultPage>
  </div>
</template>

<script>
  import defaultPage from "@/components/defaultPage";
  import { getListPageApiFun, BASE_API_URL, createApiFun } from "@/api/base";
  import record from "./components/record.vue";
  import temporary from "./components/temporary.vue";
  import { IS_TEMPORARY, ROUTE_PROPERTY, ROUTE_STATUS, WAYBILL_TYPE } from "@/enums";
  import { exportFile } from "@/utils/request";
  import { createDownloadEvent } from "@/utils/download";
  export default {
    components: {
      defaultPage,
      record,
      temporary,
    },
    data() {
      return {
        filterForm: {},
        IS_TEMPORARY,
        ROUTE_PROPERTY,
        ROUTE_STATUS,
        WAYBILL_TYPE,
        page: {
          pageNo: 1,
          pageSize: 10,
          total: 0,
        },
        tableData: [],
        apis: {
          listPage: "/api/waybill/listPage",
          export: "",
          carList: "/api/vehicle/dossier/list",
        },
        recordType: 0,
        recordId: "",
        loading: false,
        recordForm: {},
        showFilter: false,
        keyword: "",
        itemList: [
          { label: "收运单编号", value: true },
          { label: "收运单生效日期", value: true },
          { label: "临时收运单", value: true },
          { label: "路线名称", value: true },
          { label: "所属区域", value: true },
          { label: "路线属性", value: true },
          { label: "收运车辆", value: true },
          { label: "驾驶司机", value: true },
          { label: "押运工", value: true },
          { label: "押运工2", value: true },
          { label: "收运方式", value: true },
          { label: "点位数量", value: true },
          { label: "已收运点位数", value: true },
          { label: "路线编号", value: false },
          { label: "司机联系方式", value: false },
          { label: "押运工联系方式", value: false },
          { label: "押运工2联系方式", value: false },
          { label: "路线版本号", value: false },
          { label: "最近修改人", value: false },
          { label: "最近修改时间", value: false },
          { label: "地磅核实重量（kg）", value: true },
          { label: "空桶数量", value: true },
          { label: "渠道名称", value: true },
        ],
        allItemChecked: false,
        carOptions: [],
        channelRecord: {},
      };
    },
    computed: {
      filterNumber() {
        let paramsArray = Object.values(this.filterForm);
        let num = paramsArray.filter((arr) => arr || arr === 0).length;
        return num == 0 ? null : num;
      },
    },
    activated() {
      this.$nextTick(() => {
        if (this.$refs.tableRef) {
          this.$refs.tableRef.doLayout();
        }
      });
    },
    mounted() {
      this.getOptions();
      this.initData();
    },
    methods: {
      // 获取数据列表
      async getOptions() {
        let res = await createApiFun({ statusList: [0, 1] }, this.apis.carList);
        this.carOptions = res.data;
      },
      async initData() {
        let params = {
          pageNo: this.page.pageNo,
          pageSize: this.page.pageSize,
          issueStatus: 1,
          keyword: this.keyword,
          defaultDriverDossierName: this.filterForm.defaultDriverDossierName,
          defaultVehiclePlateNumber: this.filterForm.defaultVehiclePlateNumber,
          effectiveBeginTime: this.filterForm.effectiveDate ? this.filterForm.effectiveDate[0] : "",
          effectiveEndTime: this.filterForm.effectiveDate ? this.filterForm.effectiveDate[1] : "",
          channelId: this.filterForm.channelId || "",
        };
        let res = await getListPageApiFun(params, this.apis.listPage);
        if (res.success) {
          this.tableData = res.data.datas.map((item) => {
            return {
              ...item,
              defaultDriverDossierPhone: item.defaultDriverDossierPhone
                ? this.$sm2Decrypt(item.defaultDriverDossierPhone)
                : "",
              supercargoDossierOnePhone: item.supercargoDossierOnePhone
                ? this.$sm2Decrypt(item.supercargoDossierOnePhone)
                : "",
              supercargoDossierTwoPhone: item.supercargoDossierTwoPhone
                ? this.$sm2Decrypt(item.supercargoDossierTwoPhone)
                : "",
            };
          });
          this.page.total = res.data.total;
        }
      },
      pageChange(val) {
        this.page.pageNo = val.pageNo;
        this.page.pageSize = val.pageSize;
        this.initData();
      },
      // 编辑
      editRecord(row, type) {
        this.recordType = type;
        this.recordId = row.id;
        this.recordForm = row;
      },
      closeRecord() {
        this.recordType = 0;
      },
      // 重置筛选
      resetFilter() {
        this.filterForm = {};
        this.keyword = "";
        this.page.pageNo = 1;
        this.initData();
      },
      // 查询
      searchFilter() {
        this.page.pageNo = 1;
        this.initData();
      },
      // 导出
      handleExport() {
        this.$confirm(`确认是否以当前筛选条件导出数据`, "提示", {
          confirmButtonText: "确认",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            this.loading = true;
            try {
              let res = await exportFile(BASE_API_URL + this.apis.export, {
                keyword: this.keyword,
                defaultDriverDossierName: this.filterForm.defaultDriverDossierName,
                defaultVehiclePlateNumber: this.filterForm.defaultVehiclePlateNumber,
                effectiveBeginTime: this.filterForm.effectiveDate ? this.filterForm.effectiveDate[0] : "",
                effectiveEndTime: this.filterForm.effectiveDate ? this.filterForm.effectiveDate[1] : "",
              });
              if (res.success) {
                createDownloadEvent(`电子收运单${Date.now()}.xlsx`, [res.data]);
                window.ELEMENT.Message.success("导出成功");
              }
              this.loading = false;
            } catch (error) {
              this.loading = false;
              console.warn(error);
            }
          })
          .catch(() => {});
      },
      // 修改所有显示列
      changeAllReveal(value) {
        this.allItemChecked = value;
        this.itemList.slice(1, this.itemList.length).forEach((item) => (item.value = value));
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 列点击事件
      changeReveal(value, item) {
        item.value = value;
        let uncheckList = this.itemList.slice(1, this.itemList.length).filter((list) => !list.value);
        if (uncheckList.length === 0) {
          this.allItemChecked = true;
        } else {
          this.allItemChecked = false;
        }
        this.$nextTick(() => {
          this.$refs.tableRef.doLayout();
        });
      },
      // 跳转编辑路线档案
      toUpdateRoute(row) {
        this.$router.push({ name: "receiveRoute", params: { pickupPathId: row.pickupPathId } });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .main-index {
    flex: 1;
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
  .header {
    display: flex;
    align-items: center;
    margin-top: 20px;
    .header-right {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
    }
  }
  .main {
    flex: 1;
    margin-top: 20px;
    overflow: auto;
  }
  .w400 {
    width: 400px;
  }
  .filter-box {
    margin: 0 10px;
  }
  .el-col {
    margin-top: 10px;
  }
  .dropdown-button {
    margin: 0 10px;
  }
  ::v-deep .filter-content .el-form-item {
    margin-bottom: 0;
  }
  ::v-deep .el-date-editor .el-range-separator {
    width: 10%;
  }
</style>
